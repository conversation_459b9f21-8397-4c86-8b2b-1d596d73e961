/*
 * @File: 6_housekeeping_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 11/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 16/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestHousekeeping(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		// AddHousekeeping
		var houseId string
		apiUrl := "/api/v3/api-asker-vn/add-housekeeping"
		log.Println("==================================== AddHousekeeping")

		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"houseNumber": "123",
				"contactName": "Test Name",
				"phoneNumber": "01234567812",
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"roomTypes":   []string{"test1", "test2"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Housekeeping", func() {
				service.NewRouter().ServeHTTP(resp, req)

				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				houseId = respResult["_id"].(string)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["name"], ShouldEqual, "Test Housekeeping")
				So(respResult["contactName"], ShouldEqual, "Test Name")
				So(respResult["homeType"], ShouldEqual, "type test")
				So(respResult["houseNumber"], ShouldEqual, "123")
				So(respResult["userId"], ShouldEqual, "0834567890")
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["status"], ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				So(respResultM["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(respResultM["taskPlace"]["city"], ShouldEqual, "Bình Dương")
				So(respResultM["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
			})

			Convey("Then check Database Add Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], houseId, bson.M{}, &result)
				So(result.Name, ShouldEqual, "Test Housekeeping")
				So(result.ContactName, ShouldEqual, "Test Name")
				So(result.HomeType, ShouldEqual, "type test")
				So(result.HouseNumber, ShouldEqual, "123")
				So(result.UserId, ShouldEqual, "0834567890")
				So(result.IsoCode, ShouldEqual, local.ISO_CODE)
				So(result.Status, ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				So(result.TaskPlace.Address, ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(result.TaskPlace.City, ShouldEqual, "Bình Dương")
				So(result.TaskPlace.Country, ShouldEqual, local.ISO_CODE)
				So(result.CountryCode, ShouldEqual, body["countryCode"])
				So(result.PhoneNumber, ShouldEqual, body["phoneNumber"])
			})
		})
	})

	t.Run("1.1", func(t *testing.T) {
		// AddHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-housekeeping"
		log.Println("==================================== Validate AddHousekeeping")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check the response if name is empty", func() {
				body := map[string]interface{}{
					"name": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NAME_REQUIRED.Message)
			})
			Convey("Check the response if address is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ADDRESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ADDRESS_REQUIRED.Message)
			})
			Convey("Check the response if task place is nil", func() {
				body := map[string]interface{}{
					"name":      "123123",
					"address":   "123123",
					"taskPlace": nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_PLACE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_PLACE_REQUIRED.Message)
			})
			Convey("Check the response if home type is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_HOME_TYPE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_HOME_TYPE_REQUIRED.Message)
			})
			Convey("Check the response if house number is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_HOUSE_NUMBER_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_HOUSE_NUMBER_REQUIRED.Message)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "123",
					"userId":      "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check the response if isoCode is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "123",
					"userId":      "123",
					"isoCode":     "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check the response if isoCode is incorrect", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "123",
					"userId":      "123",
					"isoCode":     "HA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId":   123123123123,
					"isoCode":  local.ISO_CODE,
					"name":     12312312123,
					"address":  "quan 1, HCM",
					"homeType": "type test",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"houseNumber": "123",
					"contactName": "Test Name",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("1.2", func(t *testing.T) {
		// AddHousekeeping
		var houseId string
		apiUrl := "/api/v3/api-asker-vn/add-housekeeping"
		log.Println("==================================== AddHousekeeping")

		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"rooms": []map[string]interface{}{
					{
						"area": 50.0,
						"name": "room 1",
						"note": "chu y gam giuong",
						"images": []string{
							"https://toanphambucket.s3.amazonaws.com/hostel%2FAsker+01%2Ffasfasdf.%2F%2Fimages%2Fhostel-tmtcjj7zcd9wr9l",
							"https://toanphambucket.s3.amazonaws.com/hostel%2FAsker+01%2Ffasfasdf.%2F%2Fimages%2Fhostel-w93lzjv4wbncgjn",
							"https://toanphambucket.s3.amazonaws.com/hostel%2FAsker+01%2Ffasfasdf.%2F%2Fimages%2Fhostel-kl2bsr52uwqhakd",
							"https://toanphambucket.s3.amazonaws.com/hostel%2FAsker+01%2Ffasfasdf.%2F%2Fimages%2Fhostel-rcwvgkomrmnbi6h",
						},
					},
				},
				"houseNumber": "123",
				"contactName": "Test Name",
				"phoneNumber": "01234567812",
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"roomTypes":   []string{"test1", "test2"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Housekeeping", func() {
				service.NewRouter().ServeHTTP(resp, req)

				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				var respResultArrM map[string][]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				json.Unmarshal(bytes, &respResultArrM)
				houseId = respResult["_id"].(string)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["name"], ShouldEqual, "Test Housekeeping")
				So(respResult["contactName"], ShouldEqual, "Test Name")
				So(respResult["homeType"], ShouldEqual, "type test")
				So(respResult["houseNumber"], ShouldEqual, "123")
				So(respResult["userId"], ShouldEqual, "0834567890")
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["status"], ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				So(respResultM["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(respResultM["taskPlace"]["city"], ShouldEqual, "Bình Dương")
				So(respResultM["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
				So(respResultArrM["rooms"][0]["Id"], ShouldNotEqual, "")
				So(respResultArrM["rooms"][0]["area"], ShouldEqual, 50.0)
				So(respResultArrM["rooms"][0]["name"], ShouldEqual, "room 1")
				So(respResultArrM["rooms"][0]["note"], ShouldEqual, "chu y gam giuong")
			})

			Convey("Then check Database Add Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], houseId, bson.M{}, &result)
				So(result.Name, ShouldEqual, "Test Housekeeping")
				So(result.ContactName, ShouldEqual, "Test Name")
				So(result.HomeType, ShouldEqual, "type test")
				So(result.HouseNumber, ShouldEqual, "123")
				So(result.UserId, ShouldEqual, "0834567890")
				So(result.IsoCode, ShouldEqual, local.ISO_CODE)
				So(result.Status, ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				So(result.TaskPlace.Address, ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(result.TaskPlace.City, ShouldEqual, "Bình Dương")
				So(result.TaskPlace.Country, ShouldEqual, local.ISO_CODE)
				So(result.CountryCode, ShouldEqual, body["countryCode"])
				So(result.PhoneNumber, ShouldEqual, body["phoneNumber"])
				So(result.Rooms[0].Id, ShouldNotEqual, "")
				So(result.Rooms[0].Area, ShouldEqual, 50.0)
				So(result.Rooms[0].Name, ShouldEqual, "room 1")
				So(result.Rooms[0].Note, ShouldEqual, "chu y gam giuong")
				So(len(result.Rooms[0].Images), ShouldEqual, 4)
				So(result.Rooms[0].CreatedAt, ShouldNotBeNil)
			})
		})
	})

	t.Run("1.3", func(t *testing.T) {
		// AddHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-housekeeping"
		log.Println("==================================== Validate AddHousekeeping")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check the response if name is empty", func() {
				body := map[string]interface{}{
					"name": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NAME_REQUIRED.Message)
			})
			Convey("Check the response if address is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ADDRESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ADDRESS_REQUIRED.Message)
			})
			Convey("Check the response if task place is nil", func() {
				body := map[string]interface{}{
					"name":      "123123",
					"address":   "123123",
					"taskPlace": nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_PLACE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_PLACE_REQUIRED.Message)
			})
			Convey("Check the response if home type is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_HOME_TYPE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_HOME_TYPE_REQUIRED.Message)
			})
			Convey("Check the response if house number is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_HOUSE_NUMBER_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_HOUSE_NUMBER_REQUIRED.Message)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "123",
					"userId":      "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check the response if isoCode is empty", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "123",
					"userId":      "123",
					"isoCode":     "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check the response if isoCode is incorrect", func() {
				body := map[string]interface{}{
					"name":    "123123",
					"address": "123123",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"homeType":    "type test",
					"houseNumber": "123",
					"userId":      "123",
					"isoCode":     "HA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId":   123123123123,
					"isoCode":  local.ISO_CODE,
					"name":     12312312123,
					"address":  "quan 1, HCM",
					"homeType": "type test",
					"taskPlace": map[string]interface{}{
						"lat":      10.9824936,
						"lng":      106.673495,
						"country":  local.ISO_CODE,
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
						"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
					},
					"houseNumber": "123",
					"contactName": "Test Name",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		// UpdateHousekeeping
		apiUrl := "/api/v3/api-asker-vn/update-housekeeping"

		ResetData()

		log.Println("==================================== Validation UpdateHousekeeping")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when id nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		// UpdateHousekeeping
		apiUrl := "/api/v3/api-asker-vn/update-housekeeping"

		ResetData()
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"contactName": "Test Name",
			},
		})
		log.Println("==================================== UpdateHousekeeping")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"_id":      ids[0],
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping 01",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "HỒ CHÍ MINH",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, HỒ CHÍ MINH, Việt Nam",
				},
				"houseNumber": "1234",
				"contactName": "Test Name 01",
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Housekeeping", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check Database Update Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], ids[0], bson.M{}, &result)
				So(result.Name, ShouldEqual, "Test Housekeeping 01")
				So(result.ContactName, ShouldEqual, "Test Name 01")
				So(result.HomeType, ShouldEqual, "type test")
				So(result.HouseNumber, ShouldEqual, "1234")
				So(result.UserId, ShouldEqual, "0834567890")
				So(result.IsoCode, ShouldEqual, local.ISO_CODE)
				So(result.Status, ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				So(result.TaskPlace.Address, ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, HỒ CHÍ MINH, Việt Nam")
				So(result.TaskPlace.City, ShouldEqual, "HỒ CHÍ MINH")
				So(result.TaskPlace.Country, ShouldEqual, local.ISO_CODE)
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		// GetHousekeepingList
		apiUrl := "/api/v3/api-asker-vn/get-housekeeping-list"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			}, {
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping 02",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
					"address":  "191 Phú Lợi, Quận 1, Hồ Chí Minh, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})

		log.Println("==================================== Validate GetHousekeepingList")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":  123123123,
					"isoCode": 123123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		// GetHousekeepingList
		apiUrl := "/api/v3/api-asker-vn/get-housekeeping-list"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			}, {
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping 02",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
					"address":  "191 Phú Lợi, Quận 1, Hồ Chí Minh, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})

		log.Println("==================================== GetHousekeepingList")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get List Housekeeping", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)

				for _, v := range respResult {
					So(v["userId"], ShouldEqual, "0834567890")
					So(v["isoCode"], ShouldEqual, local.ISO_CODE)
					So(v["status"], ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				}
				So(len(respResult), ShouldEqual, 2)
				So(respResult[0]["name"], ShouldEqual, "Test Housekeeping")
				So(respResult[0]["contactName"], ShouldEqual, "Test Name")
				So(respResult[0]["homeType"], ShouldEqual, "type test")
				So(respResultM[0]["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(respResultM[0]["taskPlace"]["city"], ShouldEqual, "Bình Dương")
				So(respResultM[0]["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
				So(respResult[1]["name"], ShouldEqual, "Test Housekeeping 02")
				So(respResult[1]["contactName"], ShouldEqual, "Test Name")
				So(respResult[1]["homeType"], ShouldEqual, "type test")
				So(respResultM[1]["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResultM[1]["taskPlace"]["city"], ShouldEqual, "Hồ Chí Minh")
				So(respResultM[1]["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
			})
		})
	})

	t.Run("5.1", func(t *testing.T) {
		// GetHousekeepingList
		apiUrl := "/api/v3/api-asker-vn/get-housekeeping-list"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			}, {
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping 02",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
					"address":  "191 Phú Lợi, Quận 1, Hồ Chí Minh, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})

		log.Println("==================================== GetHousekeepingList")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get List Housekeeping", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)

				for _, v := range respResult {
					So(v["userId"], ShouldEqual, "0834567890")
					So(v["isoCode"], ShouldEqual, local.ISO_CODE)
					So(v["status"], ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				}
				So(len(respResult), ShouldEqual, 2)
				So(respResult[0]["name"], ShouldEqual, "Test Housekeeping")
				So(respResult[0]["contactName"], ShouldEqual, "Test Name")
				So(respResult[0]["homeType"], ShouldEqual, "type test")
				So(respResultM[0]["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(respResultM[0]["taskPlace"]["city"], ShouldEqual, "Bình Dương")
				So(respResultM[0]["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
				So(respResult[1]["name"], ShouldEqual, "Test Housekeeping 02")
				So(respResult[1]["contactName"], ShouldEqual, "Test Name")
				So(respResult[1]["homeType"], ShouldEqual, "type test")
				So(respResultM[1]["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResultM[1]["taskPlace"]["city"], ShouldEqual, "Hồ Chí Minh")
				So(respResultM[1]["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		// GetHousekeeping
		apiUrl := "/api/v3/api-asker-vn/get-housekeeping"
		log.Println("==================================== GetHousekeeping")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"_id": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Housekeeping", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["userId"], ShouldEqual, "0834567890")
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["status"], ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_ACTIVE)
				So(respResult["name"], ShouldEqual, "Test Housekeeping")
				So(respResult["contactName"], ShouldEqual, "Test Name")
				So(respResult["homeType"], ShouldEqual, "type test")
				So(respResultM["taskPlace"]["address"], ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(respResultM["taskPlace"]["city"], ShouldEqual, "Bình Dương")
				So(respResultM["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
			})
		})
	})
	t.Run("6.1", func(t *testing.T) {
		// GetHousekeeping
		apiUrl := "/api/v3/api-asker-vn/get-housekeeping"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"_id": 123123123123,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response if params is invalid", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("6.2", func(t *testing.T) {
		// GetHousekeeping
		apiUrl := "/api/v3/api-asker-vn/get-housekeeping"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"_id": "",
			}

			Convey("Then check the response if params is invalid", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		// RemoveHousekeeping
		apiUrl := "/api/v3/api-asker-vn/remove-housekeeping"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})
		log.Println("==================================== Validate RemoveHousekeeping")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"_id":    ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"_id":    "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})

			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
					"_id":    "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		log.Println("==================================== RemoveHousekeeping")
		// RemoveHousekeeping
		apiUrl := "/api/v3/api-asker-vn/remove-housekeeping"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"_id":    ids[0],
				"userId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Add Housekeeping", func() {
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check Database Update Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], ids[0], bson.M{}, &result)
				So(result.Name, ShouldEqual, "Test Housekeeping")
				So(result.ContactName, ShouldEqual, "Test Name")
				So(result.HomeType, ShouldEqual, "type test")
				So(result.UserId, ShouldEqual, "0834567890")
				So(result.IsoCode, ShouldEqual, local.ISO_CODE)
				So(result.Status, ShouldEqual, globalConstant.HOUSEKEEPING_STATUS_DELETED)
				So(result.TaskPlace.Address, ShouldEqual, "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam")
				So(result.TaskPlace.City, ShouldEqual, "Bình Dương")
				So(result.TaskPlace.Country, ShouldEqual, local.ISO_CODE)
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("==================================== Validate AddRoomHousekeeping")
		// AddRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-room-housekeeping"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when housekeepingId nil", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"name":   "347",
					"area":   30,
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})

			Convey("Check request when userId nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"name":           "347",
					"area":           30,
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when name nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"area":           30,
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NAME_REQUIRED.Message)
			})

			Convey("Check request when area nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"name":           "347",
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_AREA_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_AREA_REQUIRED.Message)
			})

			Convey("Check request when images nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"name":           "347",
					"area":           30,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.Message)
			})

			Convey("Check request when images not slice", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"name":           "347",
					"area":           30,
					"images":         "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.Message)
			})
		})
	})

	t.Run("9.1", func(t *testing.T) {
		log.Println("==================================== Validate AddRoomHousekeeping")
		// AddRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-room-housekeeping"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when housekeepingId nil", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"name":   "347",
					"area":   30,
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})

			Convey("Check request when userId nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"name":           "347",
					"area":           30,
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when name nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"area":           30,
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NAME_REQUIRED.Message)
			})

			Convey("Check request when area nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"name":           "347",
					"images": []string{
						"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_AREA_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_AREA_REQUIRED.Message)
			})

			Convey("Check request when images nil", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"name":           "347",
					"area":           30,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.Message)
			})

			Convey("Check request when images not slice", func() {
				body := map[string]interface{}{
					"housekeepingId": "abc",
					"userId":         "0834567890",
					"name":           "347",
					"area":           30,
					"images":         "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_IMAGES_NOT_VALID.Message)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("==================================== AddRoomHousekeeping")
		// AddRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"housekeepingId": ids[0],
				"userId":         "0834567890",
				"name":           "347",
				"area":           30,
				"roomType":       "test",
				"note":           "abc",
				"images": []string{
					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Add Room Housekeeping", func() {
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["area"], ShouldEqual, 30)
				So(respResult["name"], ShouldEqual, "347")
			})

			Convey("Then check Database Add Room Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], ids[0], bson.M{}, &result)
				So(result.Rooms[0].Area, ShouldEqual, 30)
				So(result.Rooms[0].Name, ShouldEqual, "347")
			})
		})
	})

	t.Run("10.1", func(t *testing.T) {
		log.Println("==================================== AddRoomHousekeeping")
		// AddRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"housekeepingId": ids[0],
				"userId":         "0834567890",
				"name":           "347",
				"area":           30,
				"note":           "abc",
				"images": []string{
					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Add Room Housekeeping", func() {
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["area"], ShouldEqual, 30)
				So(respResult["name"], ShouldEqual, "347")
			})

			Convey("Then check Database Add Room Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], ids[0], bson.M{}, &result)
				So(result.Rooms[0].Area, ShouldEqual, 30)
				So(result.Rooms[0].Name, ShouldEqual, "347")
			})
		})
	})

	t.Run("10.2", func(t *testing.T) {
		log.Println("==================================== AddRoomHousekeeping")
		// AddRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/add-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"housekeepingId": "!23S",
				"userId":         "0834567890",
				"name":           "347",
				"area":           30,
				"images": []string{
					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
				},
				"roomType": "test",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Add Room Housekeeping", func() {
				bytes, _ := io.ReadAll(resp.Body)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_HOUSEKEEPING_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_HOUSEKEEPING_NOT_FOUND.Message)
			})
		})
	})

	t.Run("11", func(t *testing.T) {
		log.Println("==================================== Validate UpdateRoomHousekeeping")
		// UpdateRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/update-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
				"rooms": []map[string]interface{}{
					{
						"id":   "u4mdjv686si8ejsz9pxd",
						"name": "347",
						"area": 30,
						"note": "",
						"images": []string{
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when housekeepingId nil", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"roomId": "u4mdjv686si8ejsz9pxd",
					"data": []map[string]interface{}{
						{
							"name": "246",
							"area": 20,
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})

			Convey("Check request when userId nil", func() {
				body := map[string]interface{}{
					"housekeepingId": ids[0],
					"roomId":         "u4mdjv686si8ejsz9pxd",
					"data": []map[string]interface{}{
						{
							"name": "246",
							"area": 20,
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when roomId nil", func() {
				body := map[string]interface{}{
					"housekeepingId": ids[0],
					"userId":         "0834567890",
					"data": []map[string]interface{}{
						{
							"name": "246",
							"area": 20,
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ROOM_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ROOM_ID_REQUIRED.Message)
			})

			Convey("Check request when area nil", func() {
				body := map[string]interface{}{
					"housekeepingId": ids[0],
					"userId":         "0834567890",
					"roomId":         "u4mdjv686si8ejsz9pxd",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.Message)
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		log.Println("==================================== UpdateRoomHousekeeping")
		// UpdateRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/update-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
				"rooms": []map[string]interface{}{
					{
						"id":   "u4mdjv686si8ejsz9pxd",
						"name": "347",
						"area": 30,
						"note": "",
						"images": []string{
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"housekeepingId": ids[0],
				"userId":         "0834567890",
				"roomId":         "u4mdjv686si8ejsz9pxd",
				"data": map[string]interface{}{
					"name": "246",
					"area": 20,
				},
			}
			Convey("Then check the response to test Update Room Housekeeping", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check Database Update Room Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], ids[0], bson.M{"rooms": 1}, &result)
				So(result.Rooms[0].Area, ShouldEqual, 20)
				So(result.Rooms[0].Name, ShouldEqual, "246")
			})
		})
	})

	t.Run("12.1", func(t *testing.T) {
		log.Println("==================================== UpdateRoomHousekeeping")
		// UpdateRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/update-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"housekeepingId": "ids[0]",
				"userId":         "0834567890",
				"roomId":         "u4mdjv686si8ejsz9pxd",
				"data": map[string]interface{}{
					"name": "246",
					"area": 20,
				},
			}
			Convey("Then check the response to test Update Room Housekeeping", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ROOM_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ROOM_NOT_FOUND.Message)
			})
		})
	})

	t.Run("13", func(t *testing.T) {
		log.Println("==================================== Validate RemoveRoomHousekeeping")
		// RemoveRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/remove-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
				"rooms": []map[string]interface{}{
					{
						"id":   "u4mdjv686si8ejsz9pxd",
						"name": "347",
						"area": 30,
						"note": "",
						"images": []string{
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when housekeepingId nil", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"roomId": "u4mdjv686si8ejsz9pxd",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})

			Convey("Check request when userId nil", func() {
				body := map[string]interface{}{
					"housekeepingId": ids[0],
					"roomId":         "u4mdjv686si8ejsz9pxd",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when roomId nil", func() {
				body := map[string]interface{}{
					"housekeepingId": ids[0],
					"userId":         "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ROOM_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ROOM_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("14", func(t *testing.T) {
		log.Println("==================================== RemoveRoomHousekeeping")
		// RemoveRoomHousekeeping
		apiUrl := "/api/v3/api-asker-vn/remove-room-housekeeping"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Housekeeping
		ids := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
				"rooms": []map[string]interface{}{
					{
						"id":   "u4mdjv686si8ejsz9pxd",
						"name": "347",
						"area": 30,
						"note": "",
						"images": []string{
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"housekeepingId": ids[0],
				"userId":         "0834567890",
				"roomId":         "u4mdjv686si8ejsz9pxd",
			}
			Convey("Then check the response to test Remove Room Housekeeping", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check Database Remove Room Housekeeping", func() {
				var result *modelHousekeeping.Housekeeping
				globalDataAccess.GetOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], ids[0], bson.M{"rooms": 1}, &result)
				So(len(result.Rooms), ShouldEqual, 0)
			})
		})
	})

	//============== ROOM ===============
	t.Run("15", func(t *testing.T) {
		log.Println("===================== Validate UpdateTaskRoom")
		apiUrl := subPath + "/update-task-rooms-housekeeping"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when taskId empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
			})
			Convey("Check request when userId empty", func() {
				body := map[string]interface{}{
					"taskId": "123123",
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when roomId = nil", func() {
				body := map[string]interface{}{
					"taskId": "123123",
					"userId": "1123",
					"roomId": nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ROOM_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ROOM_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("16", func(t *testing.T) {
		log.Println("===================== UpdateTaskRoom house keeping not found")
		apiUrl := subPath + "/update-task-rooms-housekeeping"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"detailHostel": map[string]interface{}{
					"rooms": []map[string]interface{}{
						{
							"area": 200.0,
							"id":   "",
							"name": "",
						},
					},
					"hostelId":   "",
					"hostelName": "",
					"totalArea":  200.0,
				},
				"voiceCallHistory": []map[string]interface{}{
					{
						"to":     "W86An6yApbA8WQYQ7",
						"status": "Incoming",
						"type":   "audio",
						"createdAt": map[string]interface{}{
							"seconds": 1619580713.0,
							"nanos":   764000000.0,
						},
						"callId": "call-vn-1-QPUZ5PW3MB-1618421911543",
						"from":   "h5xvuZZanyPkBzgdh",
					},
					{
						"callId": "call-vn-1-QPUZ5PW3MB-1618421912269",
						"from":   "h5xvuZZanyPkBzgdh",
						"to":     "W86An6yApbA8WQYQ7",
						"status": "Incoming",
						"type":   "audio",
					},
				},
				"costDetail": map[string]interface{}{
					"baseCost":  240000.0,
					"cost":      258000.0,
					"finalCost": 258000.0,
					"duration":  4.0,
					"currency":  "VND",
				},
			},
		})

		//houseIds := CreateHousekeeping([]map[string]interface{}{
		//	{
		//		"userId":   "0834567890",
		//		"isoCode":  local.ISO_CODE,
		//		"name":     "Test Housekeeping",
		//		"address":  "quan 1, HCM",
		//		"homeType": "type test",
		//		"taskPlace": map[string]interface{}{
		//			"lat":      10.9824936,
		//			"lng":      106.673495,
		//			"country":  local.ISO_CODE,
		//			"city":     "Bình Dương",
		//			"district": "Thủ Dầu Một",
		//			"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
		//		},
		//		"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
		//		"contactName": "Test Name",
		//		"rooms": []map[string]interface{}{
		//			{
		//				"id":   "u4mdjv686si8ejsz9pxd",
		//				"name": "347",
		//				"area": 30,
		//				"note": "",
		//				"images": []string{
		//					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
		//					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
		//					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
		//					"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
		//				},
		//			},
		//		},
		//	},
		//})
		asker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567896"}, bson.M{"_id": 1})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId":  ids[0],
				"userId":  asker.XId,
				"roomIds": []string{"123123s"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When request handled", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Check the response", func() {
					So(resp.Code, ShouldEqual, 404)
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_HOUSEKEEPING_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	t.Run("17", func(t *testing.T) {
		log.Println("===================== UpdateTaskRoom house keeping not found")
		apiUrl := subPath + "/update-task-rooms-housekeeping"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		houseIds := CreateHousekeeping([]map[string]interface{}{
			{
				"userId":   "0834567890",
				"isoCode":  local.ISO_CODE,
				"name":     "Test Housekeeping",
				"address":  "quan 1, HCM",
				"homeType": "type test",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"status":      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
				"contactName": "Test Name",
				"rooms": []map[string]interface{}{
					{
						"id":   "u4mdjv686si8ejsz9pxd",
						"name": "347",
						"area": 30,
						"note": "",
						"images": []string{
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
							"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
						},
					},
				},
			},
		})

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_HOUSEKEEPING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"detailHostel": map[string]interface{}{
					"rooms": []map[string]interface{}{
						{
							"id":   "u4mdjv686si8ejsz9pxd",
							"name": "347",
							"area": 30,
							"note": "",
							"images": []string{
								"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-x4b9npe6bvkk7qu",
								"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-xe04941238h2f9m",
								"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-a7ubeknean7n1ce",
								"https://btaskee-stag.s3.amazonaws.com/hostel%2FB%E1%BA%A3o+Uy%C3%AAn%2F347%2Fimages%2Fhostel-i1c9u7tfzsml0vf",
							},
						},
					},
					"hostelId":   houseIds[0],
					"hostelName": "Test Housekeeping",
					"totalArea":  200.0,
				},
			},
		})

		asker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567896"}, bson.M{"_id": 1})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId":  ids[0],
				"userId":  asker.XId,
				"roomIds": []string{"u4mdjv686si8ejsz9pxd"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When request handled", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Check the response", func() {
					So(resp.Code, ShouldEqual, 200)
					var respResult map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(respResult, ShouldBeNil)
					//Check DB
					var task modelTask.Task
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], ids[0], bson.M{"viewedTaskers": 1, "acceptedTasker": 1, "status": 1}, &task)
					So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(task.ViewedTaskers, ShouldBeNil)
					So(task.AcceptedTasker, ShouldBeNil)

					var notificaion modelNotification.Notification
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{}, &notificaion)
					So(notificaion.Type, ShouldEqual, 25)
					So(notificaion.Description, ShouldEqual, "Lưu ý: Khách hàng thay đổi buồng, phòng, và mục 'XÁC NHẬN' của bạn không còn công việc đó nữa. Hãy tìm trong 'VIỆC MỚI' những công việc phù hợp khác.")
					So(notificaion.TaskId, ShouldEqual, task.XId)
					So(notificaion.NavigateTo, ShouldEqual, "TaskDetail")
				})
			})
		})
	})
}
