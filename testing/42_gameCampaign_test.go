package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func Test_GameCampaign(t *testing.T) {
	// ================= GET RUNNING GAME CAMPAIGN

	// Not any game running in this time -> no need to login
	t.Run("1", func(t *testing.T) {
		ResetData()

		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Not any game running in this time -> no need to login")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldBeNil)
				})
			})
		})
	})

	// Success game running (not need to login)
	t.Run("2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Success game running")
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldNotBeNil)
					So(result["_id"], ShouldNotBeEmpty)
					So(result["name"], ShouldEqual, "LUCKY_DRAW")
					resultShowEventFrom := globalLib.ParseDateFromString(result["showEventFrom"].(string), local.TimeZone)
					So(resultShowEventFrom.Format("2006-01-02 15:04"), ShouldEqual, showEventFrom.Format("2006-01-02 15:04"))
					resultStartDate := globalLib.ParseDateFromString(result["startDate"].(string), local.TimeZone)
					So(resultStartDate.Format("2006-01-02 15:04"), ShouldEqual, startDate.Format("2006-01-02 15:04"))
					resultEndDate := globalLib.ParseDateFromString(result["endDate"].(string), local.TimeZone)
					So(resultEndDate.Format("2006-01-02 15:04"), ShouldEqual, endDate.Format("2006-01-02 15:04"))
					So(result["giftCountToOpen"], ShouldEqual, 0)
					So(result["config"], ShouldResemble, config)
					So(result["text"], ShouldResemble, map[string]interface{}{
						"en": "Quay thưởng nhận quà",
						"id": "Quay thưởng nhận quà",
						"ko": "Quay thưởng nhận quà",
						"th": "Quay thưởng nhận quà",
						"vi": "Quay thưởng nhận quà",
					})
					So(result["rewards"], ShouldNotBeNil)
					rewards := []map[string]interface{}{}
					rewardsData, _ := json.Marshal(result["rewards"])
					json.Unmarshal(rewardsData, &rewards)
					So(len(rewards), ShouldEqual, 2)
					So(rewards[0]["rewardKey"], ShouldEqual, 1)
					So(rewards[0]["title"], ShouldResemble, map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					})
					So(rewards[0]["image"], ShouldEqual, "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg")
					So(rewards[0]["thumbnail"], ShouldEqual, "https://")
					So(rewards[0]["segment"], ShouldEqual, 1)
					So(rewards[1]["rewardKey"], ShouldEqual, 2)
					So(rewards[1]["title"], ShouldResemble, map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					})
					So(rewards[1]["image"], ShouldEqual, "https://")
					So(rewards[1]["thumbnail"], ShouldEqual, "https://")
					So(rewards[1]["segment"], ShouldEqual, 2)
					So(result["cacheImages"], ShouldResemble, []interface{}{
						"https://i.imgur.com/KcOAOXx.png",
						"https://i.imgur.com/baxKD6O.png",
						"https://i.imgur.com/ozvNyCw.png",
						"https://i.imgur.com/PKsIIyB.png",
						"https://i.imgur.com/A2fn00Z.png",
						"https://i.imgur.com/5hpKcY6.png",
						"https://i.imgur.com/xjZjjdm.png",
						"https://i.imgur.com/x7688Sa.png",
						"https://i.imgur.com/I122XDr.png",
						"https://i.imgur.com/BJjfeZv.png",
						"xx",
						"https://i.imgur.com/mmnNBFK.png",
						"https://i.imgur.com/bqi1AdT.png",
						"https://i.imgur.com/JXKSzsG.png",
						"https://i.imgur.com/NCATT2q.png",
						"https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
						"https://",
					})
				})
			})
		})
	})

	// Success game running (user logged in but not have any spin yet)
	t.Run("3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Success game running (user logged in but not have any spin yet)")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldNotBeNil)
					So(result["_id"], ShouldNotBeEmpty)
					So(result["name"], ShouldEqual, "LUCKY_DRAW")
					resultShowEventFrom := globalLib.ParseDateFromString(result["showEventFrom"].(string), local.TimeZone)
					So(resultShowEventFrom.Format("2006-01-02 15:04"), ShouldEqual, showEventFrom.Format("2006-01-02 15:04"))
					resultStartDate := globalLib.ParseDateFromString(result["startDate"].(string), local.TimeZone)
					So(resultStartDate.Format("2006-01-02 15:04"), ShouldEqual, startDate.Format("2006-01-02 15:04"))
					resultEndDate := globalLib.ParseDateFromString(result["endDate"].(string), local.TimeZone)
					So(resultEndDate.Format("2006-01-02 15:04"), ShouldEqual, endDate.Format("2006-01-02 15:04"))
					So(result["giftCountToOpen"], ShouldEqual, 0)
					So(result["config"], ShouldResemble, config)
					So(result["text"], ShouldResemble, map[string]interface{}{
						"en": "Quay thưởng nhận quà",
						"id": "Quay thưởng nhận quà",
						"ko": "Quay thưởng nhận quà",
						"th": "Quay thưởng nhận quà",
						"vi": "Quay thưởng nhận quà",
					})
					So(result["rewards"], ShouldNotBeNil)
					rewards := []map[string]interface{}{}
					rewardsData, _ := json.Marshal(result["rewards"])
					json.Unmarshal(rewardsData, &rewards)
					So(len(rewards), ShouldEqual, 2)
					So(rewards[0]["rewardKey"], ShouldEqual, 1)
					So(rewards[0]["title"], ShouldResemble, map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					})
					So(rewards[0]["image"], ShouldEqual, "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg")
					So(rewards[0]["thumbnail"], ShouldEqual, "https://")
					So(rewards[0]["segment"], ShouldEqual, 1)
					So(rewards[1]["rewardKey"], ShouldEqual, 2)
					So(rewards[1]["title"], ShouldResemble, map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					})
					So(rewards[1]["image"], ShouldEqual, "https://")
					So(rewards[1]["thumbnail"], ShouldEqual, "https://")
					So(rewards[1]["segment"], ShouldEqual, 2)
					So(result["cacheImages"], ShouldNotBeNil)
					So(result["cacheImages"], ShouldNotResemble, []interface{}{})
				})
			})
		})
	})

	// Success game running (user logged)
	t.Run("4", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Success game running (user logged in)")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"minVersion":    "3.1.1",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldNotBeNil)
					So(result["_id"], ShouldNotBeEmpty)
					So(result["name"], ShouldEqual, "LUCKY_DRAW")
					resultShowEventFrom := globalLib.ParseDateFromString(result["showEventFrom"].(string), local.TimeZone)
					So(resultShowEventFrom.Format("2006-01-02 15:04"), ShouldEqual, showEventFrom.Format("2006-01-02 15:04"))
					resultStartDate := globalLib.ParseDateFromString(result["startDate"].(string), local.TimeZone)
					So(resultStartDate.Format("2006-01-02 15:04"), ShouldEqual, startDate.Format("2006-01-02 15:04"))
					resultEndDate := globalLib.ParseDateFromString(result["endDate"].(string), local.TimeZone)
					So(resultEndDate.Format("2006-01-02 15:04"), ShouldEqual, endDate.Format("2006-01-02 15:04"))
					So(result["giftCountToOpen"], ShouldEqual, 10)
					So(result["config"], ShouldResemble, config)
					So(result["text"], ShouldResemble, map[string]interface{}{
						"en": "Quay thưởng nhận quà",
						"id": "Quay thưởng nhận quà",
						"ko": "Quay thưởng nhận quà",
						"th": "Quay thưởng nhận quà",
						"vi": "Quay thưởng nhận quà",
					})
					So(result["rewards"], ShouldNotBeNil)
					rewards := []map[string]interface{}{}
					rewardsData, _ := json.Marshal(result["rewards"])
					json.Unmarshal(rewardsData, &rewards)
					So(len(rewards), ShouldEqual, 2)
					So(rewards[0]["rewardKey"], ShouldEqual, 1)
					So(rewards[0]["title"], ShouldResemble, map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					})
					So(rewards[0]["image"], ShouldEqual, "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg")
					So(rewards[0]["thumbnail"], ShouldEqual, "https://")
					So(rewards[0]["segment"], ShouldEqual, 1)
					So(rewards[1]["rewardKey"], ShouldEqual, 2)
					So(rewards[1]["title"], ShouldResemble, map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					})
					So(rewards[1]["image"], ShouldEqual, "https://")
					So(rewards[1]["thumbnail"], ShouldEqual, "https://")
					So(rewards[1]["segment"], ShouldEqual, 2)
					So(result["cacheImages"], ShouldNotBeNil)
					So(result["cacheImages"], ShouldNotResemble, []interface{}{})
				})
			})
		})
	})

	// Normal user cannot get testing game campaign
	t.Run("5", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Normal user cannot get testing game campaign")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
			"isTesting": true,
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldBeNil)
				})
			})
		})
	})

	// Tester user cannot get normal game campaign
	t.Run("6", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Tester user cannot get normal game campaign")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
			},
		})

		PushUserToTester(local.ISO_CODE, []string{"0834567890"})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				PullUserToTester(local.ISO_CODE, []string{"0834567890"})

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldBeNil)
				})
			})
		})
	})

	// Tester can get testing game
	t.Run("7", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Tester can get testing game")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"minVersion":    "3.1.1",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
			"isTesting": true,
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
			},
		})

		PushUserToTester(local.ISO_CODE, []string{"0834567890"})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				PullUserToTester(local.ISO_CODE, []string{"0834567890"})

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldNotBeNil)
					So(result["_id"], ShouldNotBeEmpty)
					So(result["name"], ShouldEqual, "LUCKY_DRAW")
					resultShowEventFrom := globalLib.ParseDateFromString(result["showEventFrom"].(string), local.TimeZone)
					So(resultShowEventFrom.Format("2006-01-02 15:04"), ShouldEqual, showEventFrom.Format("2006-01-02 15:04"))
					resultStartDate := globalLib.ParseDateFromString(result["startDate"].(string), local.TimeZone)
					So(resultStartDate.Format("2006-01-02 15:04"), ShouldEqual, startDate.Format("2006-01-02 15:04"))
					resultEndDate := globalLib.ParseDateFromString(result["endDate"].(string), local.TimeZone)
					So(resultEndDate.Format("2006-01-02 15:04"), ShouldEqual, endDate.Format("2006-01-02 15:04"))
					So(result["giftCountToOpen"], ShouldEqual, 10)
					So(result["config"], ShouldResemble, config)
					So(result["text"], ShouldResemble, map[string]interface{}{
						"en": "Quay thưởng nhận quà",
						"id": "Quay thưởng nhận quà",
						"ko": "Quay thưởng nhận quà",
						"th": "Quay thưởng nhận quà",
						"vi": "Quay thưởng nhận quà",
					})
					So(result["rewards"], ShouldNotBeNil)
					rewards := []map[string]interface{}{}
					rewardsData, _ := json.Marshal(result["rewards"])
					json.Unmarshal(rewardsData, &rewards)
					So(len(rewards), ShouldEqual, 2)
					So(rewards[0]["rewardKey"], ShouldEqual, 1)
					So(rewards[0]["title"], ShouldResemble, map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					})
					So(rewards[0]["image"], ShouldEqual, "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg")
					So(rewards[0]["thumbnail"], ShouldEqual, "https://")
					So(rewards[0]["segment"], ShouldEqual, 1)
					So(rewards[1]["rewardKey"], ShouldEqual, 2)
					So(rewards[1]["title"], ShouldResemble, map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					})
					So(rewards[1]["image"], ShouldEqual, "https://")
					So(rewards[1]["thumbnail"], ShouldEqual, "https://")
					So(rewards[1]["segment"], ShouldEqual, 2)
					So(result["cacheImages"], ShouldNotBeNil)
					So(result["cacheImages"], ShouldNotResemble, []interface{}{})
				})
			})
		})
	})

	// Check version
	t.Run("8", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Check version")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"minVersion":    "3.1.1",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
			"isTesting": true,
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
			},
		})

		PushUserToTester(local.ISO_CODE, []string{"0834567890"})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.0",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				PullUserToTester(local.ISO_CODE, []string{"0834567890"})

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldNotBeNil)
					So(result["isRequiredUpdate"], ShouldEqual, true)
				})
			})
		})
	})

	// Check version
	t.Run("9", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Check version")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now
		startDate := now.AddDate(0, 0, 5)
		endDate := now.AddDate(0, 0, 20)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
			},
			"luckyDraw": map[string]interface{}{
				"header": map[string]interface{}{
					"ratio1x0_5": map[string]interface{}{
						"vi": "https://i.imgur.com/ozvNyCw.png",
						"en": "https://i.imgur.com/ozvNyCw.png",
						"ko": "https://i.imgur.com/ozvNyCw.png",
						"th": "https://i.imgur.com/ozvNyCw.png",
						"id": "https://i.imgur.com/ozvNyCw.png",
					},
				},
				"footer": map[string]interface{}{
					"ratio1x1_3": map[string]interface{}{
						"vi": "https://i.imgur.com/PKsIIyB.png",
						"en": "https://i.imgur.com/PKsIIyB.png",
						"ko": "https://i.imgur.com/PKsIIyB.png",
						"th": "https://i.imgur.com/PKsIIyB.png",
						"id": "https://i.imgur.com/PKsIIyB.png",
					},
				},
				"rewardList": map[string]interface{}{
					"vi": "https://i.imgur.com/A2fn00Z.png",
					"en": "https://i.imgur.com/A2fn00Z.png",
					"ko": "https://i.imgur.com/A2fn00Z.png",
					"th": "https://i.imgur.com/A2fn00Z.png",
					"id": "https://i.imgur.com/A2fn00Z.png",
				},
				"wheel": map[string]interface{}{
					"vi": "https://i.imgur.com/5hpKcY6.png",
					"en": "https://i.imgur.com/5hpKcY6.png",
					"ko": "https://i.imgur.com/5hpKcY6.png",
					"th": "https://i.imgur.com/5hpKcY6.png",
					"id": "https://i.imgur.com/5hpKcY6.png",
				},
				"wheelFrame": map[string]interface{}{
					"vi": "https://i.imgur.com/xjZjjdm.png",
					"en": "https://i.imgur.com/xjZjjdm.png",
					"ko": "https://i.imgur.com/xjZjjdm.png",
					"th": "https://i.imgur.com/xjZjjdm.png",
					"id": "https://i.imgur.com/xjZjjdm.png",
				},
				"needle": map[string]interface{}{
					"vi": "https://i.imgur.com/x7688Sa.png",
					"en": "https://i.imgur.com/x7688Sa.png",
					"ko": "https://i.imgur.com/x7688Sa.png",
					"th": "https://i.imgur.com/x7688Sa.png",
					"id": "https://i.imgur.com/x7688Sa.png",
				},
				"iconBack":        "https://i.imgur.com/I122XDr.png",
				"iconNext":        "https://i.imgur.com/BJjfeZv.png",
				"iconHistory":     "xx",
				"iconReward":      "https://i.imgur.com/mmnNBFK.png",
				"iconSpin":        "https://i.imgur.com/bqi1AdT.png",
				"iconShare":       "https://i.imgur.com/JXKSzsG.png",
				"primaryColor":    "#CE121A",
				"secondaryColor":  "#3CBD1B",
				"socialLinkShare": "https://www.facebook.com/btaskee/posts/4781554121905071",
				"introduction": map[string]interface{}{
					"icon": "https://i.imgur.com/NCATT2q.png",
					"link": map[string]interface{}{
						"vi": "https://www.btaskee.com/en/",
						"en": "https://www.btaskee.com/en/",
						"ko": "https://www.btaskee.com/en/",
						"th": "https://www.btaskee.com/en/",
						"id": "https://www.btaskee.com/en/",
					},
					"text": map[string]interface{}{
						"en": "abc",
						"id": "abc",
						"ko": "abc",
						"th": "abc",
						"vi": "abc",
					},
				},
				"numberOfSegments": 12.0,
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "LUCKY_DRAW",
			"minVersion":    "3.1.1",
			"rewards": []map[string]interface{}{
				{
					"type":      "GIFT",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "IPhone 15 Promax",
						"th": "IPhone 15 Promax",
						"vi": "IPhone 15 Promax",
						"ko": "IPhone 15 Promax",
						"id": "IPhone 15 Promax",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] IPhone 15 Promax",
							"th": "[XMAS GIFT] IPhone 15 Promax",
							"vi": "[XMAS GIFT] IPhone 15 Promax",
							"ko": "[XMAS GIFT] IPhone 15 Promax",
							"id": "[XMAS GIFT] IPhone 15 Promax",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment": 1,
				},
				{
					"type":      "PROMOTION_CODE",
					"name":      "Voucher 50,000 from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Voucher giảm 50,000đ",
						"vi": "Voucher giảm 50,000đ",
						"ko": "Voucher giảm 50,000đ",
						"id": "Voucher giảm 50,000đ",
						"th": "Voucher giảm 50,000đ",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"vi": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"ko": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"id": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
							"th": "[XMAS GIFT] Voucher giảm 50,000đ cho tất cả dịch vụ của bTaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment": 2,
				},
			},
			"rewardRateBySpin": []map[string]interface{}{
				{
					"spinFrom": 1.0,
					"spinTo":   1.0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 2.0,
							"quantity":  12195.0,
						},
					},
				},
				{
					"spinFrom": 2.0,
					"spinTo":   0,
					"rewards": []map[string]interface{}{
						{
							"rewardKey": 1.0,
							"quantity":  602.0,
						},
						{
							"rewardKey": 2.0,
							"quantity":  1279.0,
						},
					},
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
			"isTesting": true,
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
			},
		})

		PushUserToTester(local.ISO_CODE, []string{"0834567890"})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				PullUserToTester(local.ISO_CODE, []string{"0834567890"})

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result, ShouldNotBeNil)
					So(result["_id"], ShouldNotBeEmpty)
				})
			})
		})
	})

	// ==== GAME CAMPAIGN NOEL
	setUpData := func() string {
		now := globalLib.GetCurrentTime(local.TimeZone)
		showEventFrom := now.AddDate(0, 0, -7)
		startDate := now.AddDate(0, 0, -7)
		endDate := now.AddDate(0, 0, 7)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"bannerByLocale": map[string]interface{}{
					"vi": "https://i.imgur.com/va94AV0.png1",
					"en": "https://i.imgur.com/va94AV0.png1",
					"ko": "https://i.imgur.com/va94AV0.png1",
					"th": "https://i.imgur.com/va94AV0.png1",
					"id": "https://i.imgur.com/va94AV0.png1",
				},
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
				"isHideNumberSpinOfUser": true,
				"missionList": map[string]interface{}{
					"titleImage": map[string]interface{}{
						"vi": "https://i.imgur.com/1ECzMbM.png",
						"en": "https://i.imgur.com/1ECzMbM.png",
						"ko": "https://i.imgur.com/1ECzMbM.png",
						"th": "https://i.imgur.com/1ECzMbM.png",
						"id": "https://i.imgur.com/1ECzMbM.png",
					},
					"mission": []map[string]interface{}{
						{
							"name": "ROLL_CALL",
							"text": map[string]interface{}{
								"vi": "Điểm danh",
								"en": "Điểm danh",
								"ko": "Điểm danh",
								"th": "Điểm danh",
								"id": "Điểm danh",
							},
							"description": map[string]interface{}{
								"vi": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"en": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"ko": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"th": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"id": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
							},
							"quantity": 1,
						},
						{
							"name": "REFERRAL",
							"text": map[string]interface{}{
								"vi": "Săn quà giới thiệu",
								"en": "Săn quà giới thiệu",
								"ko": "Săn quà giới thiệu",
								"th": "Săn quà giới thiệu",
								"id": "Săn quà giới thiệu",
							},
							"description": map[string]interface{}{
								"vi": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"en": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"ko": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"th": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"id": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
							},
							"quantity": 1,
						},
						{
							"name": "DONE_TASK",
							"text": map[string]interface{}{
								"vi": "Hoàn thành công việc bất kỳ",
								"en": "Hoàn thành công việc bất kỳ",
								"ko": "Hoàn thành công việc bất kỳ",
								"th": "Hoàn thành công việc bất kỳ",
								"id": "Hoàn thành công việc bất kỳ",
							},
							"description": map[string]interface{}{
								"vi": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"en": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"ko": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"th": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"id": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
							},
							"quantity": 1,
						},
						{
							"name":       "WELLCOME",
							"navigateTo": "HOME",
							"text": map[string]interface{}{
								"vi": "Chào mừng bạn mới",
								"en": "Chào mừng bạn mới",
								"ko": "Chào mừng bạn mới",
								"th": "Chào mừng bạn mới",
								"id": "Chào mừng bạn mới",
							},
							"description": map[string]interface{}{
								"vi": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"en": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"ko": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"th": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"id": "Hoàn thành công việc bất kỳ lần đầu tiên",
							},
							"quantity": 2.0,
						},
					},
					"background": "https://imgur.com/XabkRPj.png",
				},
			},
			"presentUnboxing": map[string]interface{}{
				"iconChecked":  "https://i.imgur.com/vuUJgTU.png",
				"background":   "https://i.imgur.com/vuUJgTU.png",
				"treeImage":    "https://i.imgur.com/LDh8oHA.png",
				"footerImage":  "https://i.imgur.com/MdtrpnB.png",
				"iconBack":     "https://www.btaskee.com/wp-content/uploads/2023/11/icon_back.png",
				"iconNext":     "https://www.btaskee.com/wp-content/uploads/2023/11/icon_next-update.png",
				"iconHistory":  "https://img.upanh.tv/2023/11/29/icon_next-2.png",
				"iconReward":   "https://i.imgur.com/IzE64EY.png",
				"iconClose":    "https://www.btaskee.com/wp-content/uploads/2023/11/icon_close.png",
				"iconMute":     "https://i.imgur.com/Bhi7GfR.png",
				"iconMuteOn":   "https://i.imgur.com/XVus0Fp.png",
				"iconMuteOff":  "https://i.imgur.com/Bhi7GfR.png",
				"primaryColor": "#ffffff",
				"introduction": map[string]interface{}{
					"icon": "https://www.btaskee.com/wp-content/uploads/2023/11/icon_info.png",
					"text": map[string]interface{}{
						"en": "abc markdown",
						"id": "abc markdown",
						"ko": "abc markdown",
						"th": "abc markdown",
						"vi": "abc markdown",
					},
					"titleImage": map[string]interface{}{
						"en": "https://i.imgur.com/AsejaXl.png",
						"id": "https://i.imgur.com/AsejaXl.png",
						"ko": "https://i.imgur.com/AsejaXl.png",
						"th": "https://i.imgur.com/AsejaXl.png",
						"vi": "https://i.imgur.com/AsejaXl.png",
					},
					"background": "https://i.imgur.com/gztnLBj.png",
					"contents": map[string]interface{}{
						"info": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/va94AV0.png",
								"en": "https://i.imgur.com/va94AV0.png",
								"ko": "https://i.imgur.com/va94AV0.png",
								"th": "https://i.imgur.com/va94AV0.png",
								"id": "https://i.imgur.com/va94AV0.png",
							},
							"contents": []map[string]interface{}{
								{
									"icon": "https://i.imgur.com/Se8D5J3.png",
									"text": map[string]interface{}{
										"vi": "Mỗi ngày từ 01/12/2024, khi mở app bTaskee quý khách hàng sẽ được nhận ngay 1 lượt mở quà. Đặc biệt, khi mở app bTaskee vào ngày 25/12/2024 khách hàng sẽ nhận được 2 lượt mở quà.",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
								{
									"icon": "https://i.imgur.com/sYbQoSE.png",
									"text": map[string]interface{}{
										"vi": "Càng đặt lịch và hoàn thành công việc quý khách sẽ càng có nhiều lượt mở quà. Số món quà tích lũy sẽ xuất hiện dưới gốc cây thông tại phần giao diện chương trình Giáng Sinh trên app bTaskee.",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
								{
									"icon": "https://i.imgur.com/g0KYage.png",
									"text": map[string]interface{}{
										"vi": "Giải thưởng sẽ được thông báo ngay sau khi bạn mở thưởng thành công.",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
								{
									"icon": "https://i.imgur.com/Vq2x51g.png",
									"text": map[string]interface{}{
										"vi": "Tích cực đặt lịch và mở quà để nhận các phần quà Giáng Sinh siêu bự từ bTaskee thôi!",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
							},
						},
						"rule": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/RMl3hDF.png",
								"en": "https://i.imgur.com/RMl3hDF.png",
								"ko": "https://i.imgur.com/RMl3hDF.png",
								"th": "https://i.imgur.com/RMl3hDF.png",
								"id": "https://i.imgur.com/RMl3hDF.png",
							},
							"contents": []map[string]interface{}{
								{
									"icon": "https://i.imgur.com/Jzy1xDd.png",
									"title": map[string]interface{}{
										"vi": "Mở ứng dụng",
										"en": "Mở ứng dụng",
										"ko": "Mở ứng dụng",
										"th": "Mở ứng dụng",
										"id": "Mở ứng dụng",
									},
									"subTitle": map[string]interface{}{
										"vi": "Mỗi ngày 1 lần",
										"en": "Mỗi ngày 1 lần",
										"ko": "Mỗi ngày 1 lần",
										"th": "Mỗi ngày 1 lần",
										"id": "Mỗi ngày 1 lần",
									},
									"missions": []map[string]interface{}{
										{
											"text": map[string]interface{}{
												"vi": "Từ 01/12/2024 - 31/12/2024",
												"en": "Từ 01/12/2024 - 31/12/2024",
												"ko": "Từ 01/12/2024 - 31/12/2024",
												"th": "Từ 01/12/2024 - 31/12/2024",
												"id": "Từ 01/12/2024 - 31/12/2024",
											},
											"giftCountToOpen": 1,
										},
										{
											"text": map[string]interface{}{
												"vi": "25/12/2024",
												"en": "25/12/2024",
												"ko": "25/12/2024",
												"th": "25/12/2024",
												"id": "25/12/2024",
											},
											"giftCountToOpen": 2,
										},
									},
								},
							},
						},
						"reward": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/G6UppYs.png11",
								"en": "https://i.imgur.com/G6UppYs.png11",
								"ko": "https://i.imgur.com/G6UppYs.png11",
								"th": "https://i.imgur.com/G6UppYs.png11",
								"id": "https://i.imgur.com/G6UppYs.png11",
							},
						},
						"note": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/G6UppYs.png12",
								"en": "https://i.imgur.com/G6UppYs.png12",
								"ko": "https://i.imgur.com/G6UppYs.png12",
								"th": "https://i.imgur.com/G6UppYs.png12",
								"id": "https://i.imgur.com/G6UppYs.png12",
							},
						},
					},
				},
				"primaryBackgroundButtonSmall":         "https://www.btaskee.com/wp-content/uploads/2023/11/button_xmasRed-fix.png",
				"primaryBackgroundButtonBig":           "https://www.btaskee.com/wp-content/uploads/2023/11/button_xmasRed-fix.png",
				"primaryBackgroundButtonSmallDisabled": "https://i.imgur.com/TmR57Tb.png",
				"primaryBackgroundButtonBigDisabled":   "https://i.imgur.com/tl6tbIv.png",
				"secondaryBackgroundButtonSmall":       "https://i.imgur.com/tl6tbIv.png1",
				"secondaryBackgroundButtonBig":         "https://i.imgur.com/tl6tbIv.png2",
				"share": map[string]interface{}{
					"icon": "https://www.btaskee.com/wp-content/uploads/2023/11/icon_share.png",
					"text": map[string]interface{}{
						"en": "Share text",
						"id": "Share text",
						"ko": "Share text",
						"th": "Share text",
						"vi": "Share text",
					},
				},
				"history": map[string]interface{}{
					"titleImage": map[string]interface{}{
						"en": "https://i.imgur.com/zsLQUVZ.png",
						"id": "https://i.imgur.com/zsLQUVZ.png",
						"ko": "https://i.imgur.com/zsLQUVZ.png",
						"th": "https://i.imgur.com/zsLQUVZ.png",
						"vi": "https://i.imgur.com/zsLQUVZ.png",
					},
					"background": "https://i.imgur.com/XabkRPj.png",
				},
				"backgroundItem": "https://imgur.com/XabkRPj.png",
				"checkIn": map[string]interface{}{
					"titleImage": map[string]interface{}{
						"en": "https://i.imgur.com/mRwUL1Q.png",
						"id": "https://i.imgur.com/mRwUL1Q.png",
						"ko": "https://i.imgur.com/mRwUL1Q.png",
						"th": "https://i.imgur.com/mRwUL1Q.png",
						"vi": "https://i.imgur.com/mRwUL1Q.png",
					},
					"background":               "https://i.imgur.com/x4BzkVn.png",
					"backgroundNormalDayFail":  "https://i.imgur.com/pgB2ueE.png",
					"backgroundNormalDay":      "https://i.imgur.com/x5oSSvY.png",
					"backgroundSpecialDayFail": "https://i.imgur.com/Kpwm97s.png",
					"backgroundSpecialDay":     "https://i.imgur.com/3c4YLpR.png",
				},
				"boxes": []map[string]interface{}{
					{
						"_id":    "1",
						"image":  "https://i.imgur.com/CDxG22o.png",
						"x":      10,
						"y":      5,
						"zIndex": 20,
						"width":  100,
						"height": 100,
					},
					{
						"_id":    "2",
						"image":  "https://i.imgur.com/C0uRkm7.png",
						"x":      -45,
						"y":      0,
						"zIndex": 21,
						"width":  65,
						"height": 100,
					},
				},
				"receiveGift": map[string]interface{}{
					"background":          "https://i.imgur.com/Lkxw8eU.png",
					"backgroundHighlight": "https://i.imgur.com/ihn4j7e.png",
				},
				"deeplink": map[string]interface{}{
					"en": "https://link.deeplink.com",
					"id": "https://link.deeplink.com",
					"ko": "https://link.deeplink.com",
					"th": "https://link.deeplink.com",
					"vi": "https://link.deeplink.com",
				},
				"tutorialImage": "https://i.imgur.com/tl6tbIv.png4",
			},
		}

		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":           "65656344be3a390bf5b38671",
			"startDate":     startDate,
			"endDate":       endDate,
			"showEventFrom": showEventFrom,
			"name":          "NOEL_CAMPAIGN",
			"minVersion":    "3.1.1",
			"rewards": []map[string]interface{}{
				{
					"type":      "VOUCHER Giảm giá",
					"rewardKey": 1.0,
					"title": map[string]interface{}{
						"en": "VOUCHER Giảm giá",
						"th": "VOUCHER Giảm giá",
						"vi": "VOUCHER Giảm giá",
						"ko": "VOUCHER Giảm giá",
						"id": "VOUCHER Giảm giá",
					},
					"image":     "https://cdn.dienthoaigiakho.vn/photos/1694577894457-iPhone15Pr-BL3.jpg",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Voucher giảm giá 100.000vnđ",
							"th": "[XMAS GIFT] Voucher giảm giá 100.000vnđ",
							"vi": "[XMAS GIFT] Voucher giảm giá 100.000vnđ",
							"ko": "[XMAS GIFT] Voucher giảm giá 100.000vnđ",
							"id": "[XMAS GIFT] Voucher giảm giá 100.000vnđ",
						},
						"note": map[string]interface{}{
							"en": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"th": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"vi": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"ko": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
							"id": "Điều khoản sử dụng:\n- bTaskee sẽ chủ động liên hệ với khách hàng để thông báo trúng thưởng, địa điểm, thời gian, thủ tục nhận giải thưởng.\n- bTaskee sẽ trao quà cho khách hàng  từ 01/01/2024 đến muộn nhất vào 15/01/2024.\n- Khách hàng khi nhận giải thưởng phải xuất trình: CMND/Hộ chiếu, thông tin tài khoản bTaskee, hoặc giấy ủy quyền nếu có người nhận thay.\n- Nếu được sự đồng ý của Khách hàng trúng thưởng, bTaskee sẽ sử dụng các hình ảnh của Khách hàng trúng giải cho mục đích quảng cáo chương trình.\n- Phần thưởng không có giá trị quy đổi tiền mặt.",
						},
						"content": map[string]interface{}{},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM_WITH_PARTNER",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"prefix":    "XM2301",
					},
					"segment":    1,
					"navigateTo": "MyRewards",
					"background": "https://imgur.com/XabkRPj.png",
				},
				{
					"type":      "BPOINT",
					"name":      "Cộng 200 điểm bpoint from bTaskee",
					"rewardKey": 2.0,
					"title": map[string]interface{}{
						"en": "Cộng 200 điểm bpoint",
						"vi": "Cộng 200 điểm bpoint",
						"ko": "Cộng 200 điểm bpoint",
						"id": "Cộng 200 điểm bpoint",
						"th": "Cộng 200 điểm bpoint",
					},
					"image":     "https://",
					"thumbnail": "https://",
					"giftInfo": map[string]interface{}{
						"title": map[string]interface{}{
							"en": "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee",
							"vi": "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee",
							"ko": "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee",
							"id": "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee",
							"th": "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee",
						},
						"content": map[string]interface{}{
							"en": "Content",
							"vi": "Content",
							"ko": "Content",
							"id": "Content",
							"th": "Content",
						},
						"note": map[string]interface{}{
							"en": "Note",
							"vi": "Note",
							"ko": "Note",
							"id": "Note",
							"th": "Note",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
								"vi": "bTaskee",
								"ko": "bTaskee",
								"id": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://play-lh.googleusercontent.com/prffR8y1RoDBFJlSnNMFdsPBdGLY8w0MpaDGLTw2P7r-s19xoQtQkjFlmFoIGzchOFr-=w240-h480-rw",
						},
						"source":    "SYSTEM",
						"expiredAt": now.AddDate(0, 0, 100),
						"image":     "https://",
						"value": map[string]interface{}{
							"type":     "MONEY",
							"value":    50000.0,
							"maxValue": 0.0,
						},
						"prefix": "XM2350",
					},
					"segment":    2,
					"navigateTo": "UserBpay",
					"screen":     "Tab_Transaction",
					"background": "https://imgur.com/XabkRPj.png",
				},
			},
			"config": config,
			"text": map[string]interface{}{
				"vi": "Quay thưởng nhận quà",
				"en": "Quay thưởng nhận quà",
				"ko": "Quay thưởng nhận quà",
				"th": "Quay thưởng nhận quà",
				"id": "Quay thưởng nhận quà",
			},
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "LOGIN",
					"numberOfSpin": 1,
					"applyFor":     "NEW",
				},
				{
					"action":       "LOGIN",
					"numberOfSpin": 2,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 2,
					"applyFor":     "NEW",
				},
				{
					"action":       "DONE_TASK",
					"numberOfSpin": 1,
					"applyFor":     "CURRENT",
				},
				{
					"action":       "REFERRAL",
					"numberOfSpin": 3,
					"applyFor":     "BOTH",
				},
			},
			// "isTesting": true,
		})

		var serviceLaundry *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_LAUNDRY}, bson.M{"_id": 1, "text": 1}, &serviceLaundry)

		var serviceHomeMoving *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_MOVING}, bson.M{"_id": 1, "text": 1}, &serviceHomeMoving)

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
				"accumulateSpinHistory": []map[string]interface{}{
					{
						"action":    "DONE_TASK",
						"spin":      2,
						"createdAt": now,
						"taskId":    "x199819992000",
						"serviceId": serviceLaundry.XId,
					},
					{
						"action":    "DONE_TASK",
						"spin":      3,
						"createdAt": now.AddDate(0, 0, -1),
						"taskId":    "x199819992002",
						"serviceId": serviceHomeMoving.XId,
					},
					{
						"action":    "DONE_TASK_REFERRAL",
						"spin":      2,
						"createdAt": now.AddDate(0, 0, -2),
						"taskId":    "x199819992004",
					},
					{
						"action":    "REFERRAL",
						"spin":      1,
						"createdAt": now.AddDate(0, 0, -3),
					},
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": now.AddDate(0, 0, -4),
					},
					{
						"action":    "SIGN_UP",
						"spin":      1,
						"createdAt": now.AddDate(0, 0, -5),
					},
					{
						"action":    "DONE_TASK_FIRST",
						"spin":      2,
						"createdAt": now.AddDate(0, 0, -6),
					},
				},
				"rewards": []map[string]interface{}{
					{
						"rewardKey": 1,
						"createdAt": now.Add(-30 * time.Minute),
					},
					{
						"rewardKey": 2,
						"createdAt": now.Add(-20 * time.Minute),
					},
					{
						"rewardKey": 2,
						"createdAt": now.Add(-10 * time.Minute),
					},
				},
			},
		})

		return gameCampaignIds[0]
	}

	//============= Get History Receive Spin
	//Validate
	t.Run("10", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-history-receive-spin"
		log.Println("==================================== Validate")
		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request userId required", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
		})

		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request userId required", func() {
				body := map[string]interface{}{
					"userId":         "xxx",
					"gameCampaignId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_GAME_CAMPAIGN_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	//GetHistoryReceiveSpin
	t.Run("11", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-history-receive-spin"
		log.Println("==================================== Get History Reveice Spin")
		ResetData()
		gameCampaignId := setUpData()
		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request Get History Reveice Spin", func() {
				body := map[string]interface{}{
					"userId":         "0834567890",
					"gameCampaignId": gameCampaignId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make([]map[string]interface{}, 0)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(res.Code, ShouldEqual, 200)
					So(len(result), ShouldEqual, 7)
					for i, history := range result {
						switch i {
						case 0:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK", 2))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK", 2))
						case 1:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK", 3))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK", 3))
						case 2:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK_REFERRAL", 2))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK_REFERRAL", 2))
						case 3:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)

							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_SIGNUP_BY_REFERRAL", 1))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_SIGNUP_BY_REFERRAL", 1))
						case 4:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_ROLL_CALL", 1))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_ROLL_CALL", 1))
						case 5:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_NEW_SIGN_UP", 1))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_NEW_SIGN_UP", 1))
						case 6:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK_FIRST", 2))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK_FIRST", 2))
						}
					}
				})
			})
		})
	})

	//GetHistoryReceiveSpin - Birthday 2025
	t.Run("11.1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-history-receive-spin"
		log.Println("==================================== Get History Reveice Spin")
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		startDate := now.AddDate(0, 0, -1)
		endDate := now.AddDate(0, 0, 1)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"_id":        "65656344be3a390bf5b38671",
			"startDate":  startDate,
			"endDate":    endDate,
			"name":       "BTASKEE_9TH_BIRTHDAY",
			"minVersion": "3.1.1",
			"config": map[string]interface{}{
				"puzzleGame": map[string]interface{}{
					"iconReward":     "https://i.imgur.com/IzE64EY.png",
					"backgroundItem": "https://imgur.com/XabkRPj.png",
				},
			},
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
				"accumulateSpinHistory": []map[string]interface{}{
					{
						"action":    "DONE_TASK",
						"spin":      2,
						"createdAt": now,
						"taskId":    "x199819992000",
					},
					{
						"action":    "REFERRAL",
						"spin":      1,
						"createdAt": now.AddDate(0, 0, -3),
					},
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": now.AddDate(0, 0, -4),
					},
				},
			},
		})

		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request Get History Reveice Spin", func() {
				body := map[string]interface{}{
					"userId":         "0834567890",
					"gameCampaignId": gameCampaignIds[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make([]map[string]interface{}, 0)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(res.Code, ShouldEqual, 200)
					So(len(result), ShouldEqual, 3)
					for i, history := range result {
						So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
						So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")
						switch i {
						case 0:
							So(history["createdAt"], ShouldNotBeNil)

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_DONE_TASK", 2))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_DONE_TASK", 2))
						case 1:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)

							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_REFERRAL", 1))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_REFERRAL", 1))
						default:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://i.imgur.com/IzE64EY.png")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, localization.T("vi", "TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_ROLL_CALL", 1))
							So(title.GetEn(), ShouldEqual, localization.T("en", "TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_ROLL_CALL", 1))
						}
					}
				})
			})
		})
	})

	//============= Get History Receive Reward
	//Validate
	t.Run("12", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-history-receive-reward"
		log.Println("==================================== Validate Get History Receive Reward")
		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request userId required", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
		})

		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request userId required", func() {
				body := map[string]interface{}{
					"userId":         "xxx",
					"gameCampaignId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_GAME_CAMPAIGN_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	//getHistoryReceiveReward
	t.Run("13", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-history-receive-reward"
		log.Println("==================================== Get History Reveice Reward")
		ResetData()
		gameCampaignId := setUpData()
		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if request userId required", func() {
				body := map[string]interface{}{
					"userId":         "0834567890",
					"gameCampaignId": gameCampaignId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make([]map[string]interface{}, 0)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(len(result), ShouldEqual, 3)
					for i, history := range result {
						switch i {
						case 0:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")
							So(history["navigateTo"], ShouldEqual, "UserBpay")
							So(history["screen"], ShouldEqual, "Tab_Transaction")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee")
							So(title.GetEn(), ShouldEqual, "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee")
						case 1:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")
							So(history["navigateTo"], ShouldEqual, "UserBpay")
							So(history["screen"], ShouldEqual, "Tab_Transaction")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee")
							So(title.GetEn(), ShouldEqual, "[XMAS GIFT] Cộng 200 điểm bpoint vào tài khoản bpoint từ btaskee")
						case 2:
							So(history["createdAt"], ShouldNotBeNil)
							So(history["icon"], ShouldEqual, "https://")
							So(history["background"], ShouldEqual, "https://imgur.com/XabkRPj.png")
							So(history["navigateTo"], ShouldEqual, "MyRewards")

							title := &modelService.ServiceText{}
							b, _ := json.Marshal(history["title"])
							json.Unmarshal(b, &title)
							So(title.GetVi(), ShouldEqual, "[XMAS GIFT] Voucher giảm giá 100.000vnđ")
							So(title.GetEn(), ShouldEqual, "[XMAS GIFT] Voucher giảm giá 100.000vnđ")
						}
					}
				})
			})
		})
	})

	//getGameCampaignRunning
	t.Run("14", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Get Game Campaign Running")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		setUpData()
		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if Get Game Campaign Running", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]interface{}, 0)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(res.Code, ShouldEqual, 200)

					textMap := make(map[string]string)
					textData, _ := json.Marshal(result["text"])
					json.Unmarshal(textData, &textMap)
					So(textMap["vi"], ShouldEqual, "Quay thưởng nhận quà")

					So(result["startDate"], ShouldNotBeNil)
					So(result["endDate"], ShouldNotBeNil)
					So(result["name"].(string), ShouldEqual, "NOEL_CAMPAIGN")
					var config *modelGameCampaign.GameCampaignConfig
					configData, _ := json.Marshal(result["config"])
					json.Unmarshal(configData, &config)
					So(config.General.Banner, ShouldEqual, "https://i.imgur.com/KcOAOXx.png")
					So(config.General.FloatIcon, ShouldNotBeNil)
					So(config.General.MissionList, ShouldNotBeNil)
					So(config.General.MissionList.BackgroundItem, ShouldEqual, "https://imgur.com/XabkRPj.png")
					So(config.General.IsHideNumberSpinOfUser, ShouldEqual, true)
					So(config.PresentUnboxing, ShouldNotBeNil)
					So(config.PresentUnboxing.History, ShouldNotBeNil)
					So(config.PresentUnboxing.Introduction, ShouldNotBeNil)
					So(config.PresentUnboxing.Share, ShouldNotBeNil)
					So(config.PresentUnboxing.CheckIn, ShouldNotBeNil)
					So(config.PresentUnboxing.Boxes, ShouldNotBeNil)
					So(config.PresentUnboxing.ReceiveGift, ShouldNotBeNil)
					So(result["rewards"], ShouldNotBeNil)
					So(result["cacheImages"], ShouldNotBeNil)
					So(result["giftCountToOpen"].(float64), ShouldEqual, 10)
					So(len(config.General.MissionList.Mission), ShouldEqual, 4)
					for _, mission := range config.General.MissionList.Mission {
						So(mission.Name, ShouldBeIn, []string{"ROLL_CALL", "REFERRAL", "DONE_TASK", "WELLCOME"})
					}
					So(len(result["cacheImages"].([]interface{})), ShouldEqual, 48)

					So(config.PresentUnboxing.Introduction.Contents, ShouldNotBeNil)
					So(config.PresentUnboxing.Introduction.Contents.Info, ShouldNotBeNil)
					So(config.PresentUnboxing.Introduction.Contents.Rule, ShouldNotBeNil)
				})
			})
		})
	})

	//getGameCampaignRunning with done task
	t.Run("15", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== getGameCampaignRunning with done task")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_STATUS_DONE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
			},
		})

		setUpData()
		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if Get Game Campaign Running", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]interface{}, 0)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(res.Code, ShouldEqual, 200)

					textMap := make(map[string]string)
					textData, _ := json.Marshal(result["text"])
					json.Unmarshal(textData, &textMap)
					So(textMap["vi"], ShouldEqual, "Quay thưởng nhận quà")

					So(result["startDate"], ShouldNotBeNil)
					So(result["endDate"], ShouldNotBeNil)
					So(result["name"].(string), ShouldEqual, "NOEL_CAMPAIGN")
					var config *modelGameCampaign.GameCampaignConfig
					configData, _ := json.Marshal(result["config"])
					json.Unmarshal(configData, &config)
					So(config.General.Banner, ShouldEqual, "https://i.imgur.com/KcOAOXx.png")
					So(config.General.FloatIcon, ShouldNotBeNil)
					So(config.General.MissionList, ShouldNotBeNil)
					So(config.General.MissionList.BackgroundItem, ShouldEqual, "https://imgur.com/XabkRPj.png")
					So(config.General.IsHideNumberSpinOfUser, ShouldEqual, true)
					So(config.PresentUnboxing, ShouldNotBeNil)
					So(config.PresentUnboxing.History, ShouldNotBeNil)
					So(config.PresentUnboxing.Introduction, ShouldNotBeNil)
					So(config.PresentUnboxing.Share, ShouldNotBeNil)
					So(config.PresentUnboxing.CheckIn, ShouldNotBeNil)
					So(config.PresentUnboxing.Boxes, ShouldNotBeNil)
					So(config.PresentUnboxing.ReceiveGift, ShouldNotBeNil)
					So(result["rewards"], ShouldNotBeNil)
					So(result["cacheImages"], ShouldNotBeNil)
					So(result["giftCountToOpen"].(float64), ShouldEqual, 10)
					So(len(config.General.MissionList.Mission), ShouldEqual, 3)
					for _, mission := range config.General.MissionList.Mission {
						So(mission.Name, ShouldBeIn, []string{"ROLL_CALL", "REFERRAL", "DONE_TASK"})
					}
				})
			})
		})
	})

	// Test campaign puzzle config.
	t.Run("16", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-running-game-campaign"
		log.Println("==================================== Test campaign puzzle config")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)

		config := map[string]interface{}{
			"general": map[string]interface{}{
				"bannerByLocale": map[string]interface{}{
					"vi": "https://i.imgur.com/va94AV0.png1",
					"en": "https://i.imgur.com/va94AV0.png1",
					"ko": "https://i.imgur.com/va94AV0.png1",
					"th": "https://i.imgur.com/va94AV0.png1",
					"id": "https://i.imgur.com/va94AV0.png1",
				},
				"banner": "https://i.imgur.com/KcOAOXx.png",
				"floatIcon": map[string]interface{}{
					"vi": "https://i.imgur.com/baxKD6O.png",
					"en": "https://i.imgur.com/baxKD6O.png",
					"ko": "https://i.imgur.com/baxKD6O.png",
					"th": "https://i.imgur.com/baxKD6O.png",
					"id": "https://i.imgur.com/baxKD6O.png",
				},
				"isHideNumberSpinOfUser": true,
				"missionList": map[string]interface{}{
					"titleImage": map[string]interface{}{
						"vi": "https://i.imgur.com/1ECzMbM.png",
						"en": "https://i.imgur.com/1ECzMbM.png",
						"ko": "https://i.imgur.com/1ECzMbM.png",
						"th": "https://i.imgur.com/1ECzMbM.png",
						"id": "https://i.imgur.com/1ECzMbM.png",
					},
					"mission": []map[string]interface{}{
						{
							"name": "ROLL_CALL",
							"text": map[string]interface{}{
								"vi": "Điểm danh",
								"en": "Điểm danh",
								"ko": "Điểm danh",
								"th": "Điểm danh",
								"id": "Điểm danh",
							},
							"description": map[string]interface{}{
								"vi": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"en": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"ko": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"th": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
								"id": "Điểm danh mỗi ngày khi truy cập vào sự kiện",
							},
							"quantity": 1,
						},
						{
							"name": "REFERRAL",
							"text": map[string]interface{}{
								"vi": "Săn quà giới thiệu",
								"en": "Săn quà giới thiệu",
								"ko": "Săn quà giới thiệu",
								"th": "Săn quà giới thiệu",
								"id": "Săn quà giới thiệu",
							},
							"description": map[string]interface{}{
								"vi": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"en": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"ko": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"th": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
								"id": "Giới thiệu bạn bè đăng ký tài khoản bTaskee",
							},
							"quantity": 1,
						},
						{
							"name": "DONE_TASK",
							"text": map[string]interface{}{
								"vi": "Hoàn thành công việc bất kỳ",
								"en": "Hoàn thành công việc bất kỳ",
								"ko": "Hoàn thành công việc bất kỳ",
								"th": "Hoàn thành công việc bất kỳ",
								"id": "Hoàn thành công việc bất kỳ",
							},
							"description": map[string]interface{}{
								"vi": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"en": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"ko": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"th": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
								"id": "Dọn dẹp buồng phòng\n Dọn dẹp nhà\n Nấu ăn",
							},
							"quantity": 1,
						},
						{
							"name":       "WELLCOME",
							"navigateTo": "HOME",
							"text": map[string]interface{}{
								"vi": "Chào mừng bạn mới",
								"en": "Chào mừng bạn mới",
								"ko": "Chào mừng bạn mới",
								"th": "Chào mừng bạn mới",
								"id": "Chào mừng bạn mới",
							},
							"description": map[string]interface{}{
								"vi": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"en": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"ko": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"th": "Hoàn thành công việc bất kỳ lần đầu tiên",
								"id": "Hoàn thành công việc bất kỳ lần đầu tiên",
							},
							"quantity": 2.0,
						},
					},
					"background": "https://imgur.com/XabkRPj.png",
				},
			},
			"presentUnboxing": map[string]interface{}{
				"iconChecked":  "https://i.imgur.com/vuUJgTU.png",
				"background":   "https://i.imgur.com/vuUJgTU.png",
				"treeImage":    "https://i.imgur.com/LDh8oHA.png",
				"footerImage":  "https://i.imgur.com/MdtrpnB.png",
				"iconBack":     "https://www.btaskee.com/wp-content/uploads/2023/11/icon_back.png",
				"iconNext":     "https://www.btaskee.com/wp-content/uploads/2023/11/icon_next-update.png",
				"iconHistory":  "https://img.upanh.tv/2023/11/29/icon_next-2.png",
				"iconReward":   "https://i.imgur.com/IzE64EY.png",
				"iconClose":    "https://www.btaskee.com/wp-content/uploads/2023/11/icon_close.png",
				"iconMute":     "https://i.imgur.com/Bhi7GfR.png",
				"iconMuteOn":   "https://i.imgur.com/XVus0Fp.png",
				"iconMuteOff":  "https://i.imgur.com/Bhi7GfR.png",
				"primaryColor": "#ffffff",
				"introduction": map[string]interface{}{
					"icon": "https://www.btaskee.com/wp-content/uploads/2023/11/icon_info.png",
					"text": map[string]interface{}{
						"en": "abc markdown",
						"id": "abc markdown",
						"ko": "abc markdown",
						"th": "abc markdown",
						"vi": "abc markdown",
					},
					"titleImage": map[string]interface{}{
						"en": "https://i.imgur.com/AsejaXl.png",
						"id": "https://i.imgur.com/AsejaXl.png",
						"ko": "https://i.imgur.com/AsejaXl.png",
						"th": "https://i.imgur.com/AsejaXl.png",
						"vi": "https://i.imgur.com/AsejaXl.png",
					},
					"background": "https://i.imgur.com/gztnLBj.png",
					"contents": map[string]interface{}{
						"info": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/va94AV0.png",
								"en": "https://i.imgur.com/va94AV0.png",
								"ko": "https://i.imgur.com/va94AV0.png",
								"th": "https://i.imgur.com/va94AV0.png",
								"id": "https://i.imgur.com/va94AV0.png",
							},
							"contents": []map[string]interface{}{
								{
									"icon": "https://i.imgur.com/Se8D5J3.png",
									"text": map[string]interface{}{
										"vi": "Mỗi ngày từ 01/12/2024, khi mở app bTaskee quý khách hàng sẽ được nhận ngay 1 lượt mở quà. Đặc biệt, khi mở app bTaskee vào ngày 25/12/2024 khách hàng sẽ nhận được 2 lượt mở quà.",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
								{
									"icon": "https://i.imgur.com/sYbQoSE.png",
									"text": map[string]interface{}{
										"vi": "Càng đặt lịch và hoàn thành công việc quý khách sẽ càng có nhiều lượt mở quà. Số món quà tích lũy sẽ xuất hiện dưới gốc cây thông tại phần giao diện chương trình Giáng Sinh trên app bTaskee.",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
								{
									"icon": "https://i.imgur.com/g0KYage.png",
									"text": map[string]interface{}{
										"vi": "Giải thưởng sẽ được thông báo ngay sau khi bạn mở thưởng thành công.",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
								{
									"icon": "https://i.imgur.com/Vq2x51g.png",
									"text": map[string]interface{}{
										"vi": "Tích cực đặt lịch và mở quà để nhận các phần quà Giáng Sinh siêu bự từ bTaskee thôi!",
										"en": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"ko": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"th": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
										"id": "Every day from 01/12/2024, when opening the bTaskee app, guests will receive a free ticket. Special, when opening the bTaskee app on 25/12/2024, guests will receive 2 free tickets.",
									},
								},
							},
						},
						"rule": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/RMl3hDF.png",
								"en": "https://i.imgur.com/RMl3hDF.png",
								"ko": "https://i.imgur.com/RMl3hDF.png",
								"th": "https://i.imgur.com/RMl3hDF.png",
								"id": "https://i.imgur.com/RMl3hDF.png",
							},
							"contents": []map[string]interface{}{
								{
									"icon": "https://i.imgur.com/Jzy1xDd.png",
									"title": map[string]interface{}{
										"vi": "Mở ứng dụng",
										"en": "Mở ứng dụng",
										"ko": "Mở ứng dụng",
										"th": "Mở ứng dụng",
										"id": "Mở ứng dụng",
									},
									"subTitle": map[string]interface{}{
										"vi": "Mỗi ngày 1 lần",
										"en": "Mỗi ngày 1 lần",
										"ko": "Mỗi ngày 1 lần",
										"th": "Mỗi ngày 1 lần",
										"id": "Mỗi ngày 1 lần",
									},
									"missions": []map[string]interface{}{
										{
											"text": map[string]interface{}{
												"vi": "Từ 01/12/2024 - 31/12/2024",
												"en": "Từ 01/12/2024 - 31/12/2024",
												"ko": "Từ 01/12/2024 - 31/12/2024",
												"th": "Từ 01/12/2024 - 31/12/2024",
												"id": "Từ 01/12/2024 - 31/12/2024",
											},
											"giftCountToOpen": 1,
										},
										{
											"text": map[string]interface{}{
												"vi": "25/12/2024",
												"en": "25/12/2024",
												"ko": "25/12/2024",
												"th": "25/12/2024",
												"id": "25/12/2024",
											},
											"giftCountToOpen": 2,
										},
									},
								},
							},
						},
						"reward": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/G6UppYs.png11",
								"en": "https://i.imgur.com/G6UppYs.png11",
								"ko": "https://i.imgur.com/G6UppYs.png11",
								"th": "https://i.imgur.com/G6UppYs.png11",
								"id": "https://i.imgur.com/G6UppYs.png11",
							},
						},
						"note": map[string]interface{}{
							"titleImage": map[string]interface{}{
								"vi": "https://i.imgur.com/G6UppYs.png12",
								"en": "https://i.imgur.com/G6UppYs.png12",
								"ko": "https://i.imgur.com/G6UppYs.png12",
								"th": "https://i.imgur.com/G6UppYs.png12",
								"id": "https://i.imgur.com/G6UppYs.png12",
							},
						},
					},
				},
				"primaryBackgroundButtonSmall":         "https://www.btaskee.com/wp-content/uploads/2023/11/button_xmasRed-fix.png",
				"primaryBackgroundButtonBig":           "https://www.btaskee.com/wp-content/uploads/2023/11/button_xmasRed-fix.png",
				"primaryBackgroundButtonSmallDisabled": "https://i.imgur.com/TmR57Tb.png",
				"primaryBackgroundButtonBigDisabled":   "https://i.imgur.com/tl6tbIv.png",
				"secondaryBackgroundButtonSmall":       "https://i.imgur.com/tl6tbIv.png1",
				"secondaryBackgroundButtonBig":         "https://i.imgur.com/tl6tbIv.png2",
				"share": map[string]interface{}{
					"icon": "https://www.btaskee.com/wp-content/uploads/2023/11/icon_share.png",
					"text": map[string]interface{}{
						"en": "Share text",
						"id": "Share text",
						"ko": "Share text",
						"th": "Share text",
						"vi": "Share text",
					},
				},
				"history": map[string]interface{}{
					"titleImage": map[string]interface{}{
						"en": "https://i.imgur.com/zsLQUVZ.png",
						"id": "https://i.imgur.com/zsLQUVZ.png",
						"ko": "https://i.imgur.com/zsLQUVZ.png",
						"th": "https://i.imgur.com/zsLQUVZ.png",
						"vi": "https://i.imgur.com/zsLQUVZ.png",
					},
					"background": "https://i.imgur.com/XabkRPj.png",
				},
				"backgroundItem": "https://imgur.com/XabkRPj.png",
				"checkIn": map[string]interface{}{
					"titleImage": map[string]interface{}{
						"en": "https://i.imgur.com/mRwUL1Q.png",
						"id": "https://i.imgur.com/mRwUL1Q.png",
						"ko": "https://i.imgur.com/mRwUL1Q.png",
						"th": "https://i.imgur.com/mRwUL1Q.png",
						"vi": "https://i.imgur.com/mRwUL1Q.png",
					},
					"background":               "https://i.imgur.com/x4BzkVn.png",
					"backgroundNormalDayFail":  "https://i.imgur.com/pgB2ueE.png",
					"backgroundNormalDay":      "https://i.imgur.com/x5oSSvY.png",
					"backgroundSpecialDayFail": "https://i.imgur.com/Kpwm97s.png",
					"backgroundSpecialDay":     "https://i.imgur.com/3c4YLpR.png",
				},
				"boxes": []map[string]interface{}{
					{
						"_id":    "1",
						"image":  "https://i.imgur.com/CDxG22o.png",
						"x":      10,
						"y":      5,
						"zIndex": 20,
						"width":  100,
						"height": 100,
					},
					{
						"_id":    "2",
						"image":  "https://i.imgur.com/C0uRkm7.png",
						"x":      -45,
						"y":      0,
						"zIndex": 21,
						"width":  65,
						"height": 100,
					},
				},
				"receiveGift": map[string]interface{}{
					"background":          "https://i.imgur.com/Lkxw8eU.png",
					"backgroundHighlight": "https://i.imgur.com/ihn4j7e.png",
				},
				"deeplink": map[string]interface{}{
					"en": "https://link.deeplink.com",
					"id": "https://link.deeplink.com",
					"ko": "https://link.deeplink.com",
					"th": "https://link.deeplink.com",
					"vi": "https://link.deeplink.com",
				},
				"tutorialImage": "https://i.imgur.com/tl6tbIv.png4",
			},
		}

		gameCampaignIds := CreateGameCampaign([]map[string]interface{}{
			{
				"status":    globalConstant.GAME_CAMPAIGN_STATUS_ACTIVE,
				"isTesting": false,
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"text": map[string]interface{}{
					"vi": "Ai là triệu phú",
					"en": "Ai là triệu phú",
					"ko": "Ai là triệu phú",
					"th": "Ai là triệu phú",
					"id": "Ai là triệu phú",
				},
				"config": config,
				"type":   globalConstant.GAME_CAMPAIGN_TYPE_CHALLENGE_GAME,
				"name":   "BTASKEE_9TH_BIRTHDAY",
				"challenges": []map[string]interface{}{
					{ // COMPLETED (Pass condition)
						"_id":  "challengeId1",
						"name": "C1",
						"type": globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_COMMUNITY_POST,
						"postData": map[string]interface{}{
							"tagId": "tagId1",
						},
						"order": 4,
						"conditions": []map[string]interface{}{
							{
								"type":       "DATE",
								"targetDate": now.AddDate(0, 0, -1),
							},
						},
						"requiredSpins": 0,
					},
					{ // COMPLETED (Pass condition date but not pass challenge completed require)
						"_id":   "challengeId2",
						"name":  "C2",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 0,
						"conditions": []map[string]interface{}{
							{
								"type":       "DATE",
								"targetDate": now.AddDate(0, 0, -1),
							},
						},
						"requiredSpins": 1,
					},
					{ // COMPLETED (Pass condition)
						"_id":   "challengeId3",
						"name":  "C3",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 1,
						"conditions": []map[string]interface{}{
							{
								"type":       "DATE",
								"targetDate": now.AddDate(0, 0, -1),
							},
						},
						"requiredSpins": 1,
					},
					{ // LOCKED (Not pass condition date)
						"_id":   "challengeId4",
						"name":  "C4",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 2,
						"conditions": []map[string]interface{}{
							{
								"type":       "DATE",
								"targetDate": now.AddDate(0, 0, 1),
							},
						},
						"requiredSpins": 1,
					},
					{ // LOCKED (Not pass condition)
						"_id":   "challengeId5",
						"name":  "C5",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 3,
						"conditions": []map[string]interface{}{
							{
								"type":         "CHALLENGE_COMPLETED_REQUIRE",
								"targetSArray": []string{"C1", "C4"},
							},
						},
						"requiredSpins": 1,
					},
					{ // OPEN (Pass condition)
						"_id":   "challengeId6",
						"name":  "C6",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 5,
						"conditions": []map[string]interface{}{
							{
								"type":         "CHALLENGE_COMPLETED_REQUIRE",
								"targetSArray": []string{"C1", "C2", "C3"},
							},
						},
						"requiredSpins": 1,
					},
					{ // OPEN (Pass condition)
						"_id":   "challengeId7",
						"name":  "C7",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 6,
						"conditions": []map[string]interface{}{
							{
								"type":         "CHALLENGE_COMPLETED_REQUIRE",
								"targetSArray": []string{"C1"},
							},
						},
						"requiredSpins": 1,
					},
					{ // OPEN (Pass condition)
						"_id":   "challengeId8",
						"name":  "C8",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 7,
						"conditions": []map[string]interface{}{
							{
								"type":         "CHALLENGE_COMPLETED_REQUIRE",
								"targetSArray": []string{"C1"},
							},
						},
						"requiredSpins": 1,
					},
					{ // LOCKED (Not enough spins)
						"_id":   "challengeId9",
						"name":  "C9",
						"type":  globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ,
						"order": 8,
						"conditions": []map[string]interface{}{
							{
								"type":         "CHALLENGE_COMPLETED_REQUIRE",
								"targetSArray": []string{"C1"},
							},
						},
						"requiredSpins": 2,
					},
				},
			},
		})

		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   1,
				"challengeHistories": []map[string]interface{}{
					{
						"challengeId":     "challengeId1",
						"challengeName":   "C1",
						"status":          "PASSED",
						"communityPostId": "xa6165dab94916d104d132037bb89d209",
					},
					{
						"challengeId":   "challengeId2",
						"challengeName": "C2",
						"quizId":        "quizId2",
						"answers": []string{
							"A",
						},
						"status": "PASSED",
					},
					{
						"challengeId":   "challengeId3",
						"challengeName": "C3",
						"quizId":        "quizId3",
						"answers": []string{
							"A",
						},
						"status": "PASSED",
					},
				},
			},
		})

		Convey("Given a HTTP request", t, func() {
			Convey("When service handle request if Get Game Campaign Running", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"version": "3.1.1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					result := make(map[string]interface{}, 0)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(res.Code, ShouldEqual, 200)

					textMap := make(map[string]string)
					textData, _ := json.Marshal(result["text"])
					json.Unmarshal(textData, &textMap)
					So(textMap["vi"], ShouldEqual, "Ai là triệu phú")

					So(result["startDate"], ShouldNotBeNil)
					So(result["endDate"], ShouldNotBeNil)
					So(result["name"].(string), ShouldEqual, "BTASKEE_9TH_BIRTHDAY")
					var config *modelGameCampaign.GameCampaignConfig
					configData, _ := json.Marshal(result["config"])
					json.Unmarshal(configData, &config)
					So(config.General.Banner, ShouldEqual, "https://i.imgur.com/KcOAOXx.png")
					So(config.General.FloatIcon, ShouldNotBeNil)
					So(config.General.MissionList, ShouldNotBeNil)
					So(config.General.MissionList.BackgroundItem, ShouldEqual, "https://imgur.com/XabkRPj.png")
					So(config.General.IsHideNumberSpinOfUser, ShouldEqual, true)

					challenges := []map[string]interface{}{}
					challengesData, _ := json.Marshal(result["challenges"])
					json.Unmarshal(challengesData, &challenges)
					So(len(challenges), ShouldEqual, 9)
					// check the fields: _id, name, type, order, status
					// sort by order
					So(challenges[0]["_id"], ShouldEqual, "challengeId2")
					So(challenges[0]["name"], ShouldEqual, "C2")
					So(challenges[0]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[0]["order"], ShouldEqual, 0)
					So(challenges[0]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED)
					So(challenges[0]["requiredSpins"], ShouldEqual, 1)

					So(challenges[1]["_id"], ShouldEqual, "challengeId3")
					So(challenges[1]["name"], ShouldEqual, "C3")
					So(challenges[1]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[1]["order"], ShouldEqual, 1)
					So(challenges[1]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED)
					So(challenges[1]["requiredSpins"], ShouldEqual, 1)

					So(challenges[2]["_id"], ShouldEqual, "challengeId4")
					So(challenges[2]["name"], ShouldEqual, "C4")
					So(challenges[2]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[2]["order"], ShouldEqual, 2)
					So(challenges[2]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_LOCKED)
					So(challenges[2]["requiredSpins"], ShouldEqual, 1)

					So(challenges[3]["_id"], ShouldEqual, "challengeId5")
					So(challenges[3]["name"], ShouldEqual, "C5")
					So(challenges[3]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[3]["order"], ShouldEqual, 3)
					So(challenges[3]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_LOCKED)
					So(challenges[3]["requiredSpins"], ShouldEqual, 1)

					So(challenges[4]["_id"], ShouldEqual, "challengeId1")
					So(challenges[4]["name"], ShouldEqual, "C1")
					So(challenges[4]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_COMMUNITY_POST)
					So(challenges[4]["order"], ShouldEqual, 4)
					So(challenges[4]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED)
					So(challenges[4]["requiredSpins"], ShouldEqual, 0)

					So(challenges[5]["_id"], ShouldEqual, "challengeId6")
					So(challenges[5]["name"], ShouldEqual, "C6")
					So(challenges[5]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[5]["order"], ShouldEqual, 5)
					So(challenges[5]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_OPEN)
					So(challenges[5]["requiredSpins"], ShouldEqual, 1)

					So(challenges[6]["_id"], ShouldEqual, "challengeId7")
					So(challenges[6]["name"], ShouldEqual, "C7")
					So(challenges[6]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[6]["order"], ShouldEqual, 6)
					So(challenges[6]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_OPEN)
					So(challenges[6]["requiredSpins"], ShouldEqual, 1)

					So(challenges[7]["_id"], ShouldEqual, "challengeId8")
					So(challenges[7]["name"], ShouldEqual, "C8")
					So(challenges[7]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[7]["order"], ShouldEqual, 7)
					So(challenges[7]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_OPEN)
					So(challenges[7]["requiredSpins"], ShouldEqual, 1)

					So(challenges[8]["_id"], ShouldEqual, "challengeId9")
					So(challenges[8]["name"], ShouldEqual, "C9")
					So(challenges[8]["type"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ)
					So(challenges[8]["order"], ShouldEqual, 8)
					So(challenges[8]["status"], ShouldEqual, globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_LOCKED)
					So(challenges[8]["requiredSpins"], ShouldEqual, 2)
				})
			})
		})
	})

}
