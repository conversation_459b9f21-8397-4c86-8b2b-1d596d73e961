/*
 * @File: 4_financial_account_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 27/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 16/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	"go.mongodb.org/mongo-driver/bson"
)

func TestFAccount(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetFAccount")
		// GetFAccount
		apiGetFAccount := "/api/v3/api-asker-vn/get-financial-account"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFAccount), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})

			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": "vq",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})

			Convey("Check request when id and isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  1231,
					"isoCode": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetFAccount")
		// GetFAccount
		apiGetFAccount := "/api/v3/api-asker-vn/get-financial-account"
		ResetData()

		//Create Financial Account
		ids := CreateFAccount([]map[string]interface{}{
			{
				"FMainAccount": 200000,
				"Promotion":    200000,
			}, {
				"FMainAccount": 250000,
				"Promotion":    300000,
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"name":       "Tasker 01",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": ids[0],
			}, {
				"phone":      "**********",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": ids[1],
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s with code VN", apiGetFAccount), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get FAccount with code VN", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["currency"], ShouldNotBeEmpty)
					So(respResult["currency"].(map[string]interface{})["code"], ShouldEqual, "VND")
					So(respResult["FMainAccount"], ShouldEqual, 200000)
					So(respResult["Promotion"], ShouldEqual, 200000)
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetFAccount")
		// GetFAccount
		apiGetFAccount := "/api/v3/api-asker-vn/get-financial-account"
		ResetData()

		//Create Financial Account
		ids := CreateFAccount([]map[string]interface{}{
			{
				"FMainAccount": 200000,
				"Promotion":    200000,
			}, {
				"FMainAccount": 250000,
				"Promotion":    300000,
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"name":       "Tasker 01",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": ids[0],
			}, {
				"phone":      "**********",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": ids[1],
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s with code VN", apiGetFAccount), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get FAccount with code VN", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["currency"], ShouldNotBeEmpty)
					So(respResult["currency"].(map[string]interface{})["code"], ShouldEqual, "VND")
					So(respResult["FMainAccount"], ShouldEqual, 250000)
					So(respResult["Promotion"], ShouldEqual, 300000)
				})
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetFAccount")
		// GetFAccount
		apiGetFAccount := "/api/v3/api-asker-vn/get-financial-account"
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s when user not found", apiGetFAccount), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get FAccount with code VN", func() {
					m := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(resp.Body)

					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 404)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== GetFAccount")
		// GetFAccount
		apiGetFAccount := "/api/v3/api-asker-vn/get-financial-account"
		ResetData()

		//Create Financial Account
		ids := CreateFAccount([]map[string]interface{}{
			{
				"FMainAccount": 200000,
				"Promotion":    200000,
			}, {
				"FMainAccount": 250000,
				"Promotion":    300000,
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"name":       "Tasker 01",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": ids[0],
			}, {
				"phone":      "**********",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": ids[1],
			},
		})

		var settingCountry *modelSettingCountry.SettingCountry
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"currency": 1}, &settingCountry)
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], settingCountry.XId, bson.M{"$unset": bson.M{"currency": 1}})
		Convey(fmt.Sprintf("Given a HTTP request for %s with code VN", apiGetFAccount), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFAccount, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response", func() {
					m := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(resp.Body)

					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 404)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_CURRENCY_NOT_FOUND.ErrorCode)
				})
			})
		})
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], settingCountry.XId, bson.M{"$set": bson.M{"currency": settingCountry.Currency}})
	})
}
