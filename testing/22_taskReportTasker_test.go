/*
* @File: 22_taskReportTasker_test.go
* @Description: Handler function, case test
* @CreatedAt: 19/11/2020
* @Author: ngoctb
* @UpdatedAt: 25/10/2020
* @UpdatedBy: ngoctb
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

func TestTaskReportTasker(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := subPath + "/get-tasker-report"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"userId": 123123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		apiURL := subPath + "/get-tasker-report"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateTaskReportTasker([]map[string]interface{}{
			{
				"phone":            "0834567890",
				"numberOfDoneTask": 2,
				"avgRating":        3,
				"totalIncome":      12000,
				"goodRating": map[string]interface{}{
					"total":    1,
					"feedback": []string{"Sach se"},
				},
			},
			{
				"phone":            "0834567890",
				"numberOfDoneTask": 5,
				"avgRating":        3,
				"totalIncome":      13000,
				"goodRating": map[string]interface{}{
					"total":    2,
					"feedback": []string{"Sach se", "Can than"},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := []map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m[0]["taskerId"], ShouldEqual, body["userId"])
				So(m[0]["numberOfDoneTask"], ShouldEqual, 5)
				So(m[0]["avgRating"], ShouldEqual, 3)
				So(m[0]["totalIncome"], ShouldEqual, 13000)
				b0, _ := json.Marshal(m[0]["goodRating"])
				m0 := map[string]interface{}{}
				json.Unmarshal(b0, &m0)
				So(m0["total"], ShouldEqual, 2)
				So(m0["feedback"], ShouldResemble, []interface{}{"Sach se", "Can than"})

				So(m[1]["taskerId"], ShouldEqual, body["userId"])
				So(m[1]["numberOfDoneTask"], ShouldEqual, 2)
				So(m[1]["avgRating"], ShouldEqual, 3)
				So(m[1]["totalIncome"], ShouldEqual, 12000)
				b1, _ := json.Marshal(m[1]["goodRating"])
				m1 := map[string]interface{}{}
				json.Unmarshal(b1, &m1)
				So(m1["total"], ShouldEqual, 1)
				So(m1["feedback"], ShouldResemble, []interface{}{"Sach se"})
			})
		})
	})
}
