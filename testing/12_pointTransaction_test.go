/*
 * @File: 12_pointTransaction_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 20/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

func TestPointTransaction(t *testing.T) {
	t.Run("1.1", func(t *testing.T) {
		log.Println("==================================== validate GetListPointTransactionUsed")
		//GetListPointTransactionUsed
		apiIGetListPointTransactionUsed := "/api/v3/api-asker-vn/get-list-point-transaction-used"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionUsed), t, func() {
			Convey("Then check the response when userId is empty", func() {
				var body = map[string]interface{}{
					"userId": "",
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIGetListPointTransactionUsed, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Then check the response when params invalid", func() {
				var body = map[string]interface{}{
					"userId": 123123,
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIGetListPointTransactionUsed, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionUsed")
		//GetListPointTransactionUsed
		apiIGetListPointTransactionUsed := "/api/v3/api-asker-vn/get-list-point-transaction-used"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd58",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd60",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp nhà vệ sinh",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd62",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,25",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd64",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd66",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,40",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionUsed), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    1,
				"limit":   10,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionUsed, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Used", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 4)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "C")
					}
					So(respResultM[0]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd62")
					So(respResultM[0]["source"]["text"], ShouldEqual, "Giặt ủi")
					So(respResult[0]["point"], ShouldEqual, 15.0)
					So(respResultM[1]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd60")
					So(respResultM[1]["source"]["text"], ShouldEqual, "Dọn dẹp nhà vệ sinh")
					So(respResult[1]["point"], ShouldEqual, 24.0)
					So(respResultM[2]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd58")
					So(respResult[2]["point"], ShouldEqual, 30.0)
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					So(respResult[1]["createdAt"], ShouldBeGreaterThan, respResult[2]["createdAt"])
				})
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionUsed")
		//GetListPointTransactionUsed
		apiIGetListPointTransactionUsed := "/api/v3/api-asker-vn/get-list-point-transaction-used"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd58",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd60",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp nhà vệ sinh",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd62",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd64",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd66",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,40",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionUsed), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    1,
				"limit":   2,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionUsed, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Used Check paging (page 1, limit 2)", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "C")
					}
					So(respResultM[0]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd62")
					So(respResultM[0]["source"]["text"], ShouldEqual, "Giặt ủi")
					So(respResult[0]["point"], ShouldEqual, 15.0)
					So(respResultM[1]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd60")
					So(respResultM[1]["source"]["text"], ShouldEqual, "Dọn dẹp nhà vệ sinh")
					So(respResult[1]["point"], ShouldEqual, 24.0)
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionUsed")
		//GetListPointTransactionUsed
		apiIGetListPointTransactionUsed := "/api/v3/api-asker-vn/get-list-point-transaction-used"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd58",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd60",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp nhà vệ sinh",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd62",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd64",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd66",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,40",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionUsed), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    2,
				"limit":   2,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionUsed, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Used Check paging (page 2, limit 2)", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "C")
					}
					So(respResultM[0]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd58")
					So(respResultM[0]["source"]["text"], ShouldEqual, "Dọn dẹp buồng phòng")
					So(respResult[0]["point"], ShouldEqual, 30.0)
					So(respResultM[1]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd64")
					So(respResultM[1]["source"]["text"], ShouldEqual, "Giặt ủi")
					So(respResult[1]["point"], ShouldEqual, 12.0)
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
				})
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionUsed")
		//GetListPointTransactionUsed
		apiIGetListPointTransactionUsed := "/api/v3/api-asker-vn/get-list-point-transaction-used"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd58",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd60",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp nhà vệ sinh",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd62",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd64",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd66",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,40",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionUsed), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    2,
				"limit":   3,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionUsed, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Used Check paging (page 2, limit 3)", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "C")
					}
					So(respResultM[0]["source"]["taskId"], ShouldEqual, "x5fa37ea3651af126ff4bdd64")
					So(respResultM[0]["source"]["text"], ShouldEqual, "Giặt ủi")
					So(respResult[0]["point"], ShouldEqual, 12.0)
				})
			})
		})
	})

	t.Run("5.1", func(t *testing.T) {
		log.Println("==================================== validate GetListPointTransactionUsed")
		//GetListPointTransactionUsed
		apiIGetListPointTransactionReceived := "/api/v3/api-asker-vn/get-list-point-transaction-received"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionReceived), t, func() {
			Convey("Then check the response when userId is empty", func() {
				var body = map[string]interface{}{
					"userId": "",
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIGetListPointTransactionReceived, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Then check the response when params invalid", func() {
				var body = map[string]interface{}{
					"userId": 123123,
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIGetListPointTransactionReceived, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionReceived")
		//GetListPointTransactionReceived
		apiIGetListPointTransactionReceived := "/api/v3/api-asker-vn/get-list-point-transaction-received"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im30",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC30",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im40",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC40",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im60",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC60",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im70",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC70",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd68",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,40",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionReceived), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    1,
				"limit":   10,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionReceived, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Received", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 4)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "D")
					}
					So(respResultM[0]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im50")
					So(respResultM[0]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC50")
					So(respResultM[0]["source"]["text"], ShouldEqual, "Đổi quà từ bTaskee")
					So(respResult[0]["point"], ShouldEqual, 15.0)
					So(respResultM[1]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im40")
					So(respResultM[1]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC40")
					So(respResultM[1]["source"]["text"], ShouldEqual, "Đổi quà từ bTaskee")
					So(respResult[1]["point"], ShouldEqual, 24.0)
					So(respResultM[2]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im30")
					So(respResultM[2]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC30")
					So(respResultM[2]["source"]["text"], ShouldEqual, "bTaskee")
					So(respResult[2]["point"], ShouldEqual, 30.0)
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					So(respResult[1]["createdAt"], ShouldBeGreaterThan, respResult[2]["createdAt"])
				})
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionReceived")
		//GetListPointTransactionReceived
		apiIGetListPointTransactionReceived := "/api/v3/api-asker-vn/get-list-point-transaction-received"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im30",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC30",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im40",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC40",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im60",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC60",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im70",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC70",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd68",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,40",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionReceived), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    1,
				"limit":   2,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionReceived, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Received Check paging (page 1, limit 2)", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "D")
					}
					So(respResultM[0]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im50")
					So(respResultM[0]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC50")
					So(respResult[0]["point"], ShouldEqual, 15.0)
					So(respResultM[1]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im40")
					So(respResultM[1]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC40")
					So(respResult[1]["point"], ShouldEqual, 24.0)
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionReceived")
		//GetListPointTransactionReceived
		apiIGetListPointTransactionReceived := "/api/v3/api-asker-vn/get-list-point-transaction-received"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im30",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC30",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im40",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC40",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im60",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC60",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im70",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC70",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd68",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,40",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionReceived), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    2,
				"limit":   2,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionReceived, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Received Check paging (page 2, limit 2)", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "D")
					}
					So(respResultM[0]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im30")
					So(respResultM[0]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC30")
					So(respResult[0]["point"], ShouldEqual, 30.0)
					So(respResultM[1]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im60")
					So(respResultM[1]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC60")
					So(respResult[1]["point"], ShouldEqual, 12.0)
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
				})
			})
		})
	})

	t.Run("8", func(t *testing.T) {
		log.Println("==================================== GetListPointTransactionReceived")
		//GetListPointTransactionReceived
		apiIGetListPointTransactionReceived := "/api/v3/api-asker-vn/get-list-point-transaction-received"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Point Transaction
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im30",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC30",
				},
				"userId":    "0834567890",
				"point":     30.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,15",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im40",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC40",
				},
				"userId":    "0834567890",
				"point":     24.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,20",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"userId":    "0834567890",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,25",
			},
			{
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im60",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC60",
				},
				"userId":    "0834567890",
				"point":     12.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,10",
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im70",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC70",
				},
				"userId":    "0834567810",
				"point":     15.0,
				"type":      "D",
				"createdAt": "2020,11,02,15,35",
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd68",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"userId":    "0834567890",
				"point":     26.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,40",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIGetListPointTransactionReceived), t, func() {
			var body = map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    2,
				"limit":   3,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIGetListPointTransactionReceived, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Point Transaction Received Check paging (page 2, limit 3)", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					for _, v := range respResult {
						So(v["type"], ShouldEqual, "D")
					}
					So(respResultM[0]["source"]["incentiveId"], ShouldEqual, "prdQgLqLwKRgN8im60")
					So(respResultM[0]["source"]["promotionCodeId"], ShouldEqual, "DMB2JxEN8Njkk7DCC60")
					So(respResult[0]["point"], ShouldEqual, 12.0)
				})
			})
		})
	})
}
