/*
 * @File: 5_gift_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 29/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 16/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestGift(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetGift")
		// apiGetGift
		apiGetGift := "/api/v3/api-asker-vn/get-gift"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetGift), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":    "",
					"serviceId": "testId",
					"isoCode":   local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when serviceId blank", func() {
				body := map[string]interface{}{
					"userId":    "0834567890",
					"serviceId": "",
					"isoCode":   local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":    "0834567890",
					"serviceId": "testId",
					"isoCode":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode is incorrect", func() {
				body := map[string]interface{}{
					"userId":    "0834567890",
					"serviceId": "testId",
					"isoCode":   "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":    123123,
					"serviceId": 123123,
					"isoCode":   123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		// apiGetGift
		apiGetGift := "/api/v3/api-asker-vn/get-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Gift
		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		log.Println("==================================== GetGift")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetGift), t, func() {
			body := map[string]interface{}{
				"userId":    "0834567890",
				"serviceId": result["_id"],
				"isoCode":   local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["promotionCode"], ShouldEqual, "hsztksv7")
					So(respResult[0]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[0]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					So(respResult[0]["_id"], ShouldEqual, ids[1])
					So(respResult[1]["_id"], ShouldEqual, ids[0])
				})
			})
		})
	})

	t.Run("2.1", func(t *testing.T) {
		log.Println("==================================== Validate GetGift")
		// apiGetGift
		apiGetGift := "/api/v3/api-asker-vn/get-gift-v2"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetGift), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":    "",
					"serviceId": "testId",
					"isoCode":   local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when serviceId blank", func() {
				body := map[string]interface{}{
					"userId":    "0834567890",
					"serviceId": "",
					"isoCode":   local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":    "0834567890",
					"serviceId": "testId",
					"isoCode":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode is incorrect", func() {
				body := map[string]interface{}{
					"userId":    "0834567890",
					"serviceId": "testId",
					"isoCode":   "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":    123123,
					"serviceId": 123123,
					"isoCode":   123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2.2", func(t *testing.T) {
		// apiGetGift
		apiGetGift := "/api/v3/api-asker-vn/get-gift-v2"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		// Tạo promotion code cho gifts
		taskStartDate := globalLib.GetCurrentTime(local.TimeZone)
		taskEndDate := taskStartDate.AddDate(0, 0, 4)
		homeCleaningService := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		promotionForGiftIds := CreatePromotionCode([]map[string]interface{}{
			{
				"code": "CODE0",
			},
			{
				"code": "CODE1",
			},
			{
				"code": "CODE2",
			},
			{
				"code": "CODE3",
			},
			{
				"code": "CODE4",
			},
			{
				"code":          "CODE5",
				"minOrderValue": 200000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"maxValue": 50000,
					"value":    0.5,
				},
			},
			{
				"code":          "CODE6",
				"minOrderValue": 100000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"maxValue": 60000,
					"value":    0.5,
				},
			},
			{
				"code":          "CODE7",
				"minOrderValue": 100000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskStartDate.AddDate(0, 0, 20),
				"serviceId":     homeCleaningService.XId,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 110000,
				},
			},
			{
				"code":          "CODE8",
				"minOrderValue": 100000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskStartDate.AddDate(0, 0, 20),
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 150000,
				},
			},
			{
				"code": "CODE9",
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 150000,
				},
				"paymentMethods": []string{"MOMO"},
			},
			{
				"code": "CODE10",
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 150000,
				},
				"paymentMethods": []string{"MOMO"},
				"locked":         true,
			},
		})

		//Create Gift
		giftIds := CreateGift([]map[string]interface{}{
			{ // 0. Không phải của user -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567891",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE0",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[0],
			},
			{ // 1. Của user đã sử dụng -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE1",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[1],
				"used":          true,
			},
			{ // 2. Của user đã hết hạn -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE2",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"expired":       "2020,11,02,15,15",
				"promotionId":   promotionForGiftIds[2],
			},
			// { // 3. Của user isoCode TH -> Không lấy
			// 	"title": map[string]interface{}{
			// 		"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
			// 		"en": "Enjoy 20% discount for Cleaning Service",
			// 		"ko": "청소 서비스 20% 할인 즐기기",
			// 	},
			// 	"askerPhone":    "0834567890",
			// 	"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			// 	"promotionCode": "CODE3",
			// 	"isoCode":       local.ISO_CODE,
			// 	"createdAt":     "2020,11,02,15,15",
			// 	"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			// 	"promotionId":   promotionForGiftIds[3],
			// },
			{ // 4. Của user source không phải SYSTEM -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE4",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"promotionId":   promotionForGiftIds[4],
			},
			{ // 5. Của user nhưng không đạt minOrderValue -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE5",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[5],
			},
			{ // 6. Của user nhưng không nằm trong điều kiện thời gian -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE6",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[6],
			},
			{ // 7. Của user nhưng không đúng service app gửi lên -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,25",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[7],
			},
			{ // 8. Của user đạt điều kiện -> Lấy vs quantity 2 CODE8
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE8",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[8],
				"from": map[string]interface{}{
					"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
					"userVoucherId": "VoucherId1",
				},
			},
			{ // 9. Của user không đạt điều kiện promotionCode -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE9",
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[9],
			},
			{ // 10. Của user đạt điều kiện nhưng promotionCode bị lock -> không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE10",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[10],
			},
			{ // 11. Của user đạt điều kiện -> Lấy vs quantity 2 CODE8
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE8",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[8],

				"from": map[string]interface{}{
					"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
					"userVoucherId": "VoucherId1",
				},
			},
		})

		// Tạo promotion cho marketing campaign type promotion
		CreatePromotionCode([]map[string]interface{}{
			{
				"code":          "TEST0",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"minOrderValue": 100000,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"value":    0.2,
					"maxValue": 130000,
				},
			},
			{
				"code":          "TEST1",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"minOrderValue": 100000,
				"limit":         100,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"value":    0.2,
					"maxValue": 120000,
				},
			},
			{
				"code":          "TEST2",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 100000,
				"limit":         2,
			},
			{
				"code":          "TEST3",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 100000,
				"limit":         100,
			},
			{
				"code":          "TEST4",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 500000,
				"limit":         100,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"maxValue": 80000,
					"value":    0.4,
				},
			},
			{
				"code":          "TEST5",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 100000,
				"limit":         100,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 90000,
				},
			},
			{
				"code":          "TEST6",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"minOrderValue": 100000,
				"limit":         100,
				"serviceId":     homeCleaningService.XId,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 100000,
				},
			},
			{
				"code":          "TEST7",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"limit":         100,
				"serviceId":     homeCleaningService.XId,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 100000,
				},
				"paymentMethods": []string{"MOMO"},
			},
		})

		CreatePromotionHistory([]map[string]interface{}{
			{
				"promotionCode": "TEST1",
			},
			{
				"promotionCode": "TEST2",
			},
			{
				"promotionCode": "TEST2",
			},
			{
				"promotionCode": "TEST3",
				"userId":        "0834567890",
			},
		})

		//Create Marketing Campaign
		createdAt0 := globalLib.GetCurrentTime(local.TimeZone)
		createdAt1 := globalLib.GetCurrentTime(local.TimeZone).Add(-1 * time.Minute)
		createdAt2 := globalLib.GetCurrentTime(local.TimeZone).Add(-2 * time.Minute)
		createdAt3 := globalLib.GetCurrentTime(local.TimeZone).Add(-3 * time.Minute)
		createdAt4 := globalLib.GetCurrentTime(local.TimeZone).Add(-4 * time.Minute)
		createdAt5 := globalLib.GetCurrentTime(local.TimeZone).Add(-5 * time.Minute)
		createdAt6 := globalLib.GetCurrentTime(local.TimeZone).Add(-6 * time.Minute)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 1, 0)
		marketingCampaignIds := CreateMarketingCampaign([]map[string]interface{}{
			{ // 0. Lấy do đủ điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt0.Year(), int(createdAt0.Month()), createdAt0.Day(), createdAt0.Hour(), createdAt0.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,10",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST0",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 1. Có limit -> Lấy kèm field quantity
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt1.Year(), int(createdAt1.Month()), createdAt1.Day(), createdAt1.Hour(), createdAt1.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,18",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST1",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 2. Có limit nhưng hết lượt sử dụng -> Không lấy
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt2.Year(), int(createdAt2.Month()), createdAt2.Day(), createdAt2.Hour(), createdAt2.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt2.Year(), int(createdAt2.Month()), createdAt2.Day(), createdAt2.Hour(), createdAt2.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST2",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 3. Có limit chưa hết lượt sử dụng nhưng user đã sử dụng code này -> Không lấy
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt3.Year(), int(createdAt3.Month()), createdAt3.Day(), createdAt3.Hour(), createdAt3.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt3.Year(), int(createdAt3.Month()), createdAt3.Day(), createdAt3.Hour(), createdAt3.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST3",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 4. Chưa đạt minOrderValue -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt4.Year(), int(createdAt4.Month()), createdAt4.Day(), createdAt4.Hour(), createdAt4.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,22",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST4",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 5. Không đạt điều kiện task Date -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt5.Year(), int(createdAt5.Month()), createdAt5.Day(), createdAt5.Hour(), createdAt5.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt5.Year(), int(createdAt5.Month()), createdAt5.Day(), createdAt5.Hour(), createdAt5.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST5",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 6. Không đạt điều kiện service -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt6.Year(), int(createdAt6.Month()), createdAt6.Day(), createdAt6.Hour(), createdAt6.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,23",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST6",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 7. Không đạt điều kiện payment method -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt6.Year(), int(createdAt6.Month()), createdAt6.Day(), createdAt6.Hour(), createdAt6.Minute()),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt": "2020,11,02,15,23",
				"status":    globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":   globalConstant.ISO_CODE_VN,
				"type":      globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST7",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		log.Println("==================================== GetGift")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetGift), t, func() {
			body := map[string]interface{}{
				"userId":        "0834567890",
				"serviceId":     result["_id"],
				"isoCode":       local.ISO_CODE,
				"taskCost":      150000,
				"taskDate":      taskEndDate.AddDate(0, 0, 1),
				"paymentMethod": "VNPAY",
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 11)

					// Thứ tự sort
					/*
						enable
						disable không đủ tiền
						disable không đúng thời gian task
						disable sai dịch vụ
					*/

					// From gift
					So(respResult[0]["promotionCode"], ShouldEqual, "CODE8")
					So(respResult[0]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[0]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					So(respResult[0]["_id"], ShouldEqual, giftIds[7])
					So(respResult[0]["type"], ShouldEqual, "GIFT")
					So(respResult[0]["quantity"], ShouldEqual, 2)
					So(respResult[0]["isDisabled"], ShouldBeNil)
					So(respResult[0]["disableReason"], ShouldBeNil)

					// From MKT Campaign
					So(respResult[1]["promotionCode"], ShouldEqual, "TEST0")
					So(respResult[1]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[1]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[1]["createdAt"], ShouldBeGreaterThan, respResult[5]["createdAt"])
					So(respResult[1]["_id"], ShouldEqual, marketingCampaignIds[0])
					So(respResult[1]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					So(respResult[1]["isDisabled"], ShouldBeNil)
					So(respResult[1]["disableReason"], ShouldBeNil)

					// From MKT Campaign
					So(respResult[2]["promotionCode"], ShouldEqual, "TEST1")
					So(respResult[2]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[2]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[2]["createdAt"], ShouldBeGreaterThan, respResult[6]["createdAt"])
					So(respResult[2]["_id"], ShouldEqual, marketingCampaignIds[1])
					So(respResult[2]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[2]["quantity"], ShouldEqual, 99)
					So(respResult[2]["isDisabled"], ShouldBeNil)
					So(respResult[2]["disableReason"], ShouldBeNil)

					// From MKT Campaign
					So(respResult[3]["promotionCode"], ShouldEqual, "TEST4")
					So(respResult[3]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[3]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[3]["createdAt"], ShouldBeGreaterThan, respResult[7]["createdAt"])
					So(respResult[3]["_id"], ShouldEqual, marketingCampaignIds[4])
					So(respResult[3]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[3]["quantity"], ShouldEqual, 100)
					So(respResult[3]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[3]["disableReason"])["en"], ShouldEqual, "This promotion code only applies to orders from 500,000₫.")

					// From gift
					So(respResult[4]["promotionCode"], ShouldEqual, "CODE5")
					So(respResult[4]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[4]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					So(respResult[4]["_id"], ShouldEqual, giftIds[4])
					So(respResult[4]["type"], ShouldEqual, "GIFT")
					// So(respResult[4]["quantity"], ShouldEqual, 1)
					So(respResult[4]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[4]["disableReason"])["en"], ShouldEqual, "This promotion code only applies to orders from 200,000₫.")

					// From MKT Campaign
					So(respResult[5]["promotionCode"], ShouldEqual, "TEST5")
					So(respResult[5]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[5]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[5]["createdAt"], ShouldBeGreaterThan, respResult[8]["createdAt"])
					So(respResult[5]["_id"], ShouldEqual, marketingCampaignIds[5])
					So(respResult[5]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[5]["quantity"], ShouldEqual, 100)
					So(respResult[5]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[5]["disableReason"])["en"], ShouldEqual, "The promotion code is not applicable during this time frame.")

					// From gift
					So(respResult[6]["promotionCode"], ShouldEqual, "CODE6")
					So(respResult[6]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[6]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[6]["createdAt"], ShouldBeGreaterThan, respResult[3]["createdAt"])
					So(respResult[6]["_id"], ShouldEqual, giftIds[5])
					So(respResult[6]["type"], ShouldEqual, "GIFT")
					// So(respResult[6]["quantity"], ShouldEqual, 1)
					So(respResult[6]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[6]["disableReason"])["en"], ShouldEqual, "The promotion code is not applicable during this time frame.")

					// From gift
					So(respResult[7]["promotionCode"], ShouldEqual, "CODE9")
					So(respResult[7]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[7]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[7]["createdAt"], ShouldBeGreaterThan, respResult[2]["createdAt"])
					So(respResult[7]["_id"], ShouldEqual, giftIds[8])
					So(respResult[7]["type"], ShouldEqual, "GIFT")
					So(respResult[7]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[7]["disableReason"])["vi"], ShouldEqual, "Ưu đãi này chỉ được sử dụng khi thanh toán bằng phương thức\n**Momo**")

					// From MKT Campaign
					So(respResult[8]["promotionCode"], ShouldEqual, "TEST7")
					So(respResult[8]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[8]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					So(respResult[8]["_id"], ShouldEqual, marketingCampaignIds[7])
					So(respResult[8]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[8]["quantity"], ShouldEqual, 100)
					So(respResult[8]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[8]["disableReason"])["vi"], ShouldEqual, "Ưu đãi này chỉ được sử dụng khi thanh toán bằng phương thức\n**Momo**")

					// From gift
					So(respResult[9]["promotionCode"], ShouldEqual, "CODE7")
					So(respResult[9]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[9]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[9]["createdAt"], ShouldBeGreaterThan, respResult[2]["createdAt"])
					So(respResult[9]["_id"], ShouldEqual, giftIds[6])
					So(respResult[9]["type"], ShouldEqual, "GIFT")
					So(respResult[9]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[9]["disableReason"])["en"], ShouldEqual, "The promotion code only applies to the Cleaning.")

					// From MKT Campaign
					So(respResult[10]["promotionCode"], ShouldEqual, "TEST6")
					So(respResult[10]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[10]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					So(respResult[10]["_id"], ShouldEqual, marketingCampaignIds[6])
					So(respResult[10]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[10]["quantity"], ShouldEqual, 100)
					So(respResult[10]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[10]["disableReason"])["en"], ShouldEqual, "The promotion code only applies to the Cleaning.")
				})
			})
		})
	})
	t.Run("2.3", func(t *testing.T) {
		// apiGetGift
		apiGetGift := "/api/v3/api-asker-vn/get-gift-v2"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		taskStartDate := globalLib.GetCurrentTime(local.TimeZone)
		taskEndDate := taskStartDate.AddDate(0, 0, 4)
		homeCleaningService := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		promotionForGiftIds := CreatePromotionCode([]map[string]interface{}{
			{
				"code": "CODE0",
			},
			{
				"code": "CODE1",
			},
			{
				"code": "CODE2",
			},
			// {
			// 	"code": "CODE3",
			// },
			{
				"code": "CODE4",
			},
			{
				"code":          "CODE5",
				"minOrderValue": 200000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"maxValue": 50000,
					"value":    0.5,
				},
			},
			{
				"code":          "CODE6",
				"minOrderValue": 100000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"maxValue": 60000,
					"value":    0.5,
				},
			},
			{
				"code":          "CODE7",
				"minOrderValue": 100000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskStartDate.AddDate(0, 0, 20),
				"serviceId":     homeCleaningService.XId,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 110000,
				},
			},
			{
				"code":          "CODE8",
				"minOrderValue": 100000,
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskStartDate.AddDate(0, 0, 20),
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 150000,
				},
				"taskPlace": map[string]interface{}{
					"city":     []string{"Hà Nội"},
					"district": []string{"Quận 7"},
				},
			},
		})

		//Create Gift
		giftIds := CreateGift([]map[string]interface{}{
			{ // 0. Không phải của user -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567891",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE0",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[0],
			},
			{ // 1. Của user đã sử dụng -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE1",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[1],
				"used":          true,
			},
			{ // 2. Của user đã hết hạn -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE2",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"expired":       "2020,11,02,15,15",
				"promotionId":   promotionForGiftIds[2],
			},
			// { // 3. Của user isoCode TH -> Không lấy
			// 	"title": map[string]interface{}{
			// 		"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
			// 		"en": "Enjoy 20% discount for Cleaning Service",
			// 		"ko": "청소 서비스 20% 할인 즐기기",
			// 	},
			// 	"askerPhone":    "0834567890",
			// 	"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			// 	"promotionCode": "CODE3",
			// 	"isoCode":       local.ISO_CODE,
			// 	"createdAt":     "2020,11,02,15,15",
			// 	"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			// 	"promotionId":   promotionForGiftIds[3],
			// },
			{ // 4. Của user source không phải SYSTEM -> Không lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE4",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"promotionId":   promotionForGiftIds[3],
			},
			{ // 5. Của user nhưng không đạt minOrderValue -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE5",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[4],
			},
			{ // 6. Của user nhưng không nằm trong điều kiện thời gian -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE6",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[5],
			},
			{ // 7. Của user nhưng không đúng service app gửi lên -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,25",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[6],
			},
			{ // 8. Của user đạt điều kiện -> Lấy
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"promotionCode": "CODE8",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[7],
			},
		})

		// Tạo promotion cho marketing campaign type promotion
		CreatePromotionCode([]map[string]interface{}{
			{
				"code":          "TEST0",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"minOrderValue": 100000,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"value":    0.2,
					"maxValue": 130000,
				},
				"taskPlace": map[string]interface{}{
					"city":     []string{"Hà Nội"},
					"district": []string{"Quận 7"},
				},
			},
			{
				"code":          "TEST1",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"minOrderValue": 100000,
				"limit":         100,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"value":    0.2,
					"maxValue": 120000,
				},
			},
			{
				"code":          "TEST2",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 100000,
				"limit":         2,
			},
			{
				"code":          "TEST3",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 100000,
				"limit":         100,
			},
			{
				"code":          "TEST4",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 500000,
				"limit":         100,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"maxValue": 80000,
					"value":    0.4,
				},
				"taskPlace": map[string]interface{}{
					"city":     []string{"Hà Nội"},
					"district": []string{"Quận 7"},
				},
			},
			{
				"code":          "TEST5",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate,
				"minOrderValue": 100000,
				"limit":         100,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 90000,
				},
			},
			{
				"code":          "TEST6",
				"taskStartDate": taskStartDate,
				"taskEndDate":   taskEndDate.AddDate(0, 0, 20),
				"minOrderValue": 100000,
				"limit":         100,
				"serviceId":     homeCleaningService.XId,
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 100000,
				},
				"taskPlace": map[string]interface{}{
					"city":     []string{"Hà Nội"},
					"district": []string{"Quận 7"},
				},
			},
		})

		CreatePromotionHistory([]map[string]interface{}{
			{
				"promotionCode": "TEST1",
			},
			{
				"promotionCode": "TEST2",
			},
			{
				"promotionCode": "TEST2",
			},
			{
				"promotionCode": "TEST3",
				"userId":        "0834567890",
			},
		})

		//Create Marketing Campaign
		createdAt0 := globalLib.GetCurrentTime(local.TimeZone)
		createdAt1 := globalLib.GetCurrentTime(local.TimeZone).Add(-1 * time.Minute)
		createdAt2 := globalLib.GetCurrentTime(local.TimeZone).Add(-2 * time.Minute)
		createdAt3 := globalLib.GetCurrentTime(local.TimeZone).Add(-3 * time.Minute)
		createdAt4 := globalLib.GetCurrentTime(local.TimeZone).Add(-4 * time.Minute)
		createdAt5 := globalLib.GetCurrentTime(local.TimeZone).Add(-5 * time.Minute)
		createdAt6 := globalLib.GetCurrentTime(local.TimeZone).Add(-6 * time.Minute)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 1, 0)
		marketingCampaignIds := CreateMarketingCampaign([]map[string]interface{}{
			{ // 0. Lấy do đủ điều kiện => không thuộc task place => không lấy
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt0.Year(), int(createdAt0.Month()), createdAt0.Day(), createdAt0.Hour(), createdAt0.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,10",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST0",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 1. Có limit -> Lấy kèm field quantity
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt1.Year(), int(createdAt1.Month()), createdAt1.Day(), createdAt1.Hour(), createdAt1.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,18",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST1",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 2. Có limit nhưng hết lượt sử dụng -> Không lấy
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt2.Year(), int(createdAt2.Month()), createdAt2.Day(), createdAt2.Hour(), createdAt2.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt2.Year(), int(createdAt2.Month()), createdAt2.Day(), createdAt2.Hour(), createdAt2.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST2",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 3. Có limit chưa hết lượt sử dụng nhưng user đã sử dụng code này -> Không lấy
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt3.Year(), int(createdAt3.Month()), createdAt3.Day(), createdAt3.Hour(), createdAt3.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt3.Year(), int(createdAt3.Month()), createdAt3.Day(), createdAt3.Hour(), createdAt3.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST3",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 4. Chưa đạt minOrderValue -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt4.Year(), int(createdAt4.Month()), createdAt4.Day(), createdAt4.Hour(), createdAt4.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,22",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST4",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 5. Không đạt điều kiện task Date -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt5.Year(), int(createdAt5.Month()), createdAt5.Day(), createdAt5.Hour(), createdAt5.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt5.Year(), int(createdAt5.Month()), createdAt5.Day(), createdAt5.Hour(), createdAt5.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST5",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // 6. Không đạt điều kiện service -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt6.Year(), int(createdAt6.Month()), createdAt6.Day(), createdAt6.Hour(), createdAt6.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", endDate.Year(), int(endDate.Month()), endDate.Day(), endDate.Hour(), endDate.Minute()),
				"createdAt":   "2020,11,02,15,23",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST6",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		log.Println("==================================== GetGift")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetGift), t, func() {
			body := map[string]interface{}{
				"userId":    "0834567890",
				"serviceId": result["_id"],
				"isoCode":   local.ISO_CODE,
				"taskCost":  150000,
				"taskDate":  taskEndDate.AddDate(0, 0, 1),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 5)

					// Thứ tự sort
					/*
						enable
						disable không đủ tiền
						disable không đúng thời gian task
						disable sai dịch vụ
					*/

					// From gift
					// So(respResult[0]["promotionCode"], ShouldEqual, "CODE8")
					// So(respResult[0]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					// So(respResultM[0]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// // So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					// So(respResult[0]["_id"], ShouldEqual, giftIds[8])
					// So(respResult[0]["type"], ShouldEqual, "GIFT")
					// So(respResult[0]["isDisabled"], ShouldBeNil)
					// So(respResult[0]["disableReason"], ShouldBeNil)

					// From MKT Campaign
					// So(respResult[1]["promotionCode"], ShouldEqual, "TEST0")
					// So(respResult[1]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					// So(respResultM[1]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// // So(respResult[1]["createdAt"], ShouldBeGreaterThan, respResult[2]["createdAt"])
					// So(respResult[1]["_id"], ShouldEqual, marketingCampaignIds[0])
					// So(respResult[1]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[1]["isDisabled"], ShouldBeNil)
					// So(respResult[1]["disableReason"], ShouldBeNil)

					// From MKT Campaign
					So(respResult[0]["promotionCode"], ShouldEqual, "TEST1")
					So(respResult[0]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[0]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[3]["createdAt"])
					So(respResult[0]["_id"], ShouldEqual, marketingCampaignIds[1])
					So(respResult[0]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[0]["quantity"], ShouldEqual, 99)
					So(respResult[0]["isDisabled"], ShouldBeNil)
					So(respResult[0]["disableReason"], ShouldBeNil)

					// From MKT Campaign
					// So(respResult[3]["promotionCode"], ShouldEqual, "TEST4")
					// So(respResult[3]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					// So(respResultM[3]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// // So(respResult[3]["createdAt"], ShouldBeGreaterThan, respResult[4]["createdAt"])
					// So(respResult[3]["_id"], ShouldEqual, marketingCampaignIds[4])
					// So(respResult[3]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[3]["quantity"], ShouldEqual, 100)
					// So(respResult[3]["isDisabled"], ShouldBeTrue)
					// So(cast.ToStringMap(respResult[3]["disableReason"])["en"], ShouldEqual, "This promotion code only applies to orders from 500,000₫.")

					// From gift
					So(respResult[1]["promotionCode"], ShouldEqual, "CODE5")
					So(respResult[1]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[1]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					So(respResult[1]["_id"], ShouldEqual, giftIds[4])
					So(respResult[1]["type"], ShouldEqual, "GIFT")
					So(respResult[1]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[1]["disableReason"])["en"], ShouldEqual, "This promotion code only applies to orders from 200,000₫.")

					// From MKT Campaign
					So(respResult[2]["promotionCode"], ShouldEqual, "TEST5")
					So(respResult[2]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[2]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[2]["createdAt"], ShouldBeGreaterThan, respResult[8]["createdAt"])
					So(respResult[2]["_id"], ShouldEqual, marketingCampaignIds[5])
					So(respResult[2]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[2]["quantity"], ShouldEqual, 100)
					So(respResult[2]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[2]["disableReason"])["en"], ShouldEqual, "The promotion code is not applicable during this time frame.")

					// From gift
					So(respResult[3]["promotionCode"], ShouldEqual, "CODE6")
					So(respResult[3]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[3]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[3]["createdAt"], ShouldBeGreaterThan, respResult[3]["createdAt"])
					So(respResult[3]["_id"], ShouldEqual, giftIds[5])
					So(respResult[3]["type"], ShouldEqual, "GIFT")
					So(respResult[3]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[3]["disableReason"])["en"], ShouldEqual, "The promotion code is not applicable during this time frame.")

					// From gift
					So(respResult[4]["promotionCode"], ShouldEqual, "CODE7")
					So(respResult[4]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[4]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[4]["createdAt"], ShouldBeGreaterThan, respResult[2]["createdAt"])
					So(respResult[4]["_id"], ShouldEqual, giftIds[6])
					So(respResult[4]["type"], ShouldEqual, "GIFT")
					So(respResult[4]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[4]["disableReason"])["en"], ShouldEqual, "The promotion code only applies to the Cleaning.")

					// From MKT Campaign
					// So(respResult[8]["promotionCode"], ShouldEqual, "TEST6")
					// So(respResult[8]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					// So(respResultM[8]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					// So(respResult[8]["_id"], ShouldEqual, marketingCampaignIds[6])
					// So(respResult[8]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					// So(respResult[8]["quantity"], ShouldEqual, 100)
					// So(respResult[8]["isDisabled"], ShouldBeTrue)
					// So(cast.ToStringMap(respResult[8]["disableReason"])["en"], ShouldEqual, "The promotion code only applies to the Cleaning.")
				})
			})
		})
	})

	t.Run("2.4", func(t *testing.T) {
		// apiGetGift
		apiGetGift := "/api/v3/api-asker-vn/get-gift-v2"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone)
		weekday := int(date.Weekday())
		if weekday == 0 {
			weekday = 7 // Set Sunday as the 7th day of the week
		}
		startOfWeek := date.AddDate(0, 0, -weekday+1)
		promotionForGiftIds := CreatePromotionCode([]map[string]interface{}{
			{
				"code":          "CODE0",
				"minOrderValue": 100000,
				"taskStartDate": date.AddDate(0, 0, -10),
				"taskEndDate":   date.AddDate(0, 0, 10),
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 150000,
				},
				"hourRanges": []map[string]interface{}{
					{
						"from": "00:00",
						"to":   "10:00",
					},
					{
						"from": "12:00",
					},
					{
						"weekDays": []int32{2, 3, 4, 5, 6, 7},
					},
					{
						"from":     "00:00",
						"to":       "10:00",
						"weekDays": []int32{3},
					},
					{
						"from":     "00:00",
						"to":       "12:00",
						"weekDays": []int32{2, 3, 4, 5, 6, 7},
					},
				},
			},
			{
				"code":          "CODE1",
				"minOrderValue": 100000,
				"taskStartDate": date.AddDate(0, 0, -10),
				"taskEndDate":   date.AddDate(0, 0, 10),
				"value": map[string]interface{}{
					"type":  globalConstant.PROMOTION_TYPE_MONEY,
					"value": 150000,
				},
				"hourRanges": []map[string]interface{}{
					{
						"from": "00:00",
						"to":   "10:00",
					},
					{
						"from":     "00:00",
						"to":       "12:00",
						"weekDays": []int32{1, 2, 3, 4, 5, 6, 7},
					},
				},
			},
		})
		giftIds := CreateGift([]map[string]interface{}{
			{ //Của user nhưng task date không nằm trong hour ranges  -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE0",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[0],
			},
			{ //Của user nhưng task date không nằm trong hour ranges  -> Lấy kèm điều kiện
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "CODE1",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				"promotionId":   promotionForGiftIds[1],
			},
		})
		// Tạo promotion cho marketing campaign type promotion
		CreatePromotionCode([]map[string]interface{}{
			{
				"code":          "TEST0",
				"taskStartDate": date.AddDate(0, 0, -10),
				"taskEndDate":   date.AddDate(0, 0, 10),
				"minOrderValue": 100000,
				"value": map[string]interface{}{
					"type":     globalConstant.PROMOTION_TYPE_PERCENTAGE,
					"value":    0.2,
					"maxValue": 130000,
				},
				"hourRanges": []map[string]interface{}{
					{
						"from": "00:00",
						"to":   "10:00",
					},
					{
						"from": "12:00",
					},
					{
						"weekDays": []int32{2, 3, 4, 5, 6, 7},
					},
					{
						"from":     "00:00",
						"to":       "10:00",
						"weekDays": []int32{3},
					},
					{
						"from":     "00:00",
						"to":       "12:00",
						"weekDays": []int32{2, 3, 4, 5, 6, 7},
					},
				},
			},
		})
		marketingCampaignIds := CreateMarketingCampaign([]map[string]interface{}{
			{
				// Không đạt điều kiện task date in hour ranges -> Lấy kèm điều kiện
				"img": "Img Test 0",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), int(date.Month()), date.Day()-10, date.Hour(), date.Minute()),
				"endDate":     fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), int(date.Month()), date.Day()+10, date.Hour(), date.Minute()),
				"createdAt":   "2020,11,02,15,15",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "TEST0",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
				"isHidingOnHomePage": true,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		taskDate := time.Date(startOfWeek.Year(), startOfWeek.Month(), startOfWeek.Day(), 10, 30, 0, 0, local.TimeZone)
		log.Println("==================================== GetGift")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetGift), t, func() {
			body := map[string]interface{}{
				"userId":    "0834567890",
				"isoCode":   local.ISO_CODE,
				"serviceId": result["_id"],
				"taskCost":  150000,
				"taskDate":  taskDate,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 3)
					// From gift
					So(respResult[0]["promotionCode"], ShouldEqual, "CODE1")
					So(respResult[0]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[0]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[3]["createdAt"])
					So(respResult[0]["_id"], ShouldEqual, giftIds[1])
					So(respResult[0]["type"], ShouldEqual, "GIFT")
					So(respResult[0]["isDisabled"], ShouldBeNil)

					// From gift
					So(respResult[1]["promotionCode"], ShouldEqual, "CODE0")
					So(respResult[1]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[1]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					// So(respResult[1]["createdAt"], ShouldBeGreaterThan, respResult[3]["createdAt"])
					So(respResult[1]["_id"], ShouldEqual, giftIds[0])
					So(respResult[1]["type"], ShouldEqual, "GIFT")
					So(respResult[1]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[1]["disableReason"])["en"], ShouldEqual, "The promotion code is not valid at this time")

					// From MKT Campaign
					So(respResult[2]["promotionCode"], ShouldEqual, "TEST0")
					So(respResult[2]["expired"], ShouldBeGreaterThanOrEqualTo, now.Format(time.RFC3339))
					So(respResultM[2]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
					So(respResult[2]["_id"], ShouldEqual, marketingCampaignIds[0])
					So(respResult[2]["type"], ShouldEqual, "MARKETING_CAMPAIGN")
					So(respResult[2]["isDisabled"], ShouldBeTrue)
					So(cast.ToStringMap(respResult[1]["disableReason"])["en"], ShouldEqual, "The promotion code is not valid at this time")
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Validate GetUserGift")
		// GetUserGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-user-gift"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			// Convey("Check request when isoCode blank", func() {
			// 	body := map[string]interface{}{
			// 		"userId":  "0834567890",
			// 		"isoCode": "",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 400)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			// })
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  132123,
					"isoCode": 12321312,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

		})
	})
	t.Run("4", func(t *testing.T) {
		// GetUserGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-user-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 50% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 50% discount for Cleaning Service",
					"ko": "청소 서비스 50% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "haslqwe",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
			},
			// {
			// 	"askerPhone":    "0834567890",
			// 	"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			// 	"promotionCode": "j123klhsad",
			// 	"isoCode":       local.ISO_CODE,
			// 	"createdAt":     "2020,11,02,15,25",
			// },
			{
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah1234",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah2345",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,35",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah3456",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,40",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah5678",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,45",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah6789",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,50",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah7890",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,55",
			},
		})
		log.Println("==================================== GetUserGift")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["promotionCode"], ShouldEqual, "haslqwe")
					So(respResultM[0]["title"]["en"], ShouldEqual, "Enjoy 50% discount for Cleaning Service")
					So(respResult[1]["promotionCode"], ShouldEqual, "hsztksv7")
					So(respResultM[1]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					So(respResult[0]["_id"], ShouldEqual, ids[1])
					So(respResult[1]["_id"], ShouldEqual, ids[0])
				})
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		// GetUserGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-user-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 50% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 50% discount for Cleaning Service",
					"ko": "청소 서비스 50% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "haslqwe",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
			}, {
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "j123klhsad",
				"isoCode":       globalConstant.ISO_CODE_TH,
				"createdAt":     "2020,11,02,15,25",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah1234",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah2345",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,35",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah3456",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,40",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah5678",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,45",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah6789",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,50",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah7890",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,55",
			},
		})
		log.Println("==================================== GetUserGift")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			Convey("Check request with params page/limit case page = 1, limit = 4", func() {
				body := map[string]interface{}{
					"userId":  "0834569520",
					"isoCode": local.ISO_CODE,
					"limit":   4,
					"page":    1,
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Gift", func() {
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 4)
						So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
						So(respResult[2]["createdAt"], ShouldBeGreaterThan, respResult[3]["createdAt"])
						So(respResult[0]["_id"], ShouldEqual, ids[8])
						So(respResult[2]["_id"], ShouldEqual, ids[6])
					})
				})
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		// GetUserGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-user-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 50% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 50% discount for Cleaning Service",
					"ko": "청소 서비스 50% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "haslqwe",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,20",
			}, {
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "j123klhsad",
				"isoCode":       globalConstant.ISO_CODE_TH,
				"createdAt":     "2020,11,02,15,25",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah1234",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,30",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah2345",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,35",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah3456",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,40",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah5678",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,45",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah6789",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,50",
			}, {
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "ah7890",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,55",
			},
		})
		log.Println("==================================== GetUserGift")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			Convey("Check request with params page/limit case page = 2, limit = 3", func() {
				body := map[string]interface{}{
					"userId":  "0834569520",
					"isoCode": local.ISO_CODE,
					"limit":   4,
					"page":    2,
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Gift", func() {
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 2)
						So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					})
				})
			})
		})
	})

	// get user gift case has barcode
	t.Run("6.1", func(t *testing.T) {
		// GetUserGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-user-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834569520",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"cart": map[string]interface{}{
					"cartNo": "9660995",
					"code_link_gift": []map[string]interface{}{
						{
							"code_image": `https://barcode.tec-it.com/barcode.ashx?data=UB100KC66C3ED&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default`,
						},
					},
				},
			},
		})
		log.Println("==================================== GetUserGift")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			Convey("Check request with params page/limit case page = 2, limit = 3", func() {
				body := map[string]interface{}{
					"userId":  "0834569520",
					"isoCode": local.ISO_CODE,
					"limit":   4,
					"page":    1,
				}

				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Gift", func() {
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 1)
						So(respResult[0]["barCode"], ShouldEqual, "https://barcode.tec-it.com/barcode.ashx?data=UB100KC66C3ED&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default")
					})
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		log.Println("===================== Redeem Gift")
		apiURL := subPath + "/redeem-gift"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId": 1231232131,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if giftId is empty", func() {
				body := map[string]interface{}{
					"userId": "12123412323",
					"giftId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_GIFT_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": "12121212",
					"giftId": "12312312312",
					"data":   nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
			})
		})
	})
	// t.Run("7.5", func(t *testing.T) {
	// 	log.Println("===================== Redeem Gift is PAUSE")
	// 	ResetData()
	// 	UpdateSettings(map[string]interface{}{
	// 		"$set": map[string]interface{}{"giftSetting.rankingCycle.isResetbPointRunning": true},
	// 	})
	// 	CreateUser([]map[string]interface{}{
	// 		{
	// 			"phone": "0834567890",
	// 			"name":  "Asker 01",
	// 			"type":  globalConstant.USER_TYPE_ASKER,
	// 		},
	// 	})
	// 	apiURL := subPath + "/redeem-gift"
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
	// 		Convey("Check the response if data is nil", func() {
	// 			body := map[string]interface{}{
	// 				"userId": "0834567890",
	// 				"giftId": "12312312312",
	// 				"data": map[string]interface{}{
	// 					"mockData": 123,
	// 				},
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 			res := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(res, req)
	// 			b, _ := io.ReadAll(res.Body)
	// 			m := map[string]map[string]interface{}{}
	// 			json.Unmarshal(b, &m)
	// 			So(res.Code, ShouldEqual, 500)
	// 			So(m["error"]["code"], ShouldEqual, lib.ERROR_REDEEM_GIFT_IS_PAUSE.ErrorCode)
	// 			UpdateSettings(map[string]interface{}{
	// 				"$unset": map[string]interface{}{"giftSetting.rankingCycle.isResetbPointRunning": 1},
	// 			})
	// 		})
	// 	})
	// })
	t.Run("8", func(t *testing.T) {
		log.Println("===================== Redeem Gift User Not Found")
		apiURL := subPath + "/redeem-gift"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": "12121212",
					"giftId": "12312312312",
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("===================== Redeem Gift User Not Found")
		apiURL := subPath + "/redeem-gift"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": "12121212",
					"giftId": "12312312312",
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("===================== Redeem Gift User Not Found")
		apiURL := subPath + "/redeem-gift"
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": "12312312312",
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_INCENTIVE_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("11", func(t *testing.T) {
		log.Println("===================== Redeem Gift user not enough point")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(4 * time.Hour)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image": "test1",
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_ENOUGH_POINTS.ErrorCode)
				incentive, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{"status": 1, "data": 1})
				So(incentive["status"], ShouldEqual, globalConstant.TRANSACTION_STATUS_ERROR)
				So(incentive["data"], ShouldEqual, lib.ERROR_USER_NOT_ENOUGH_POINTS.ErrorCode)
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive has stopped")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{"tester": []string{"01265644493", "12ZASEAS"}},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image": "test1",
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_INACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_INCENTIVE_HAS_STOPPED.ErrorCode)
				incentive, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{"status": 1, "data": 1})
				So(incentive["status"], ShouldEqual, globalConstant.TRANSACTION_STATUS_ERROR)
				So(incentive["data"], ShouldEqual, lib.ERROR_INCENTIVE_HAS_STOPPED.ErrorCode)
			})
		})
		UpdateSettings(bson.M{
			"$unset": bson.M{"tester": 1},
		})
	})
	t.Run("13", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive has expired")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		y, m, d := startDate.Date()
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 2)
		yEnd, mEnd, dEnd := endDate.Date()
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, 0, 0),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, 0, 0),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_INCENTIVE_EXPIRED.ErrorCode)
				incentive, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{"status": 1, "data": 1})
				So(incentive["status"], ShouldEqual, globalConstant.TRANSACTION_STATUS_ERROR)
				So(incentive["data"], ShouldEqual, lib.ERROR_INCENTIVE_EXPIRED.ErrorCode)
			})
		})
	})
	t.Run("14", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m["data"], ShouldNotBeEmpty)
				So(m["code"], ShouldNotBeEmpty)

				// Check gift
				var gift *modelGift.Gift
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{"applyFor": 1, "promotionCode": 1}, &gift)
				So(gift.ApplyFor.IsSharePublic, ShouldBeTrue)

				// Check promotion code
				var promotionCode *modelPromotionCode.PromotionCode
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"code": gift.PromotionCode}, bson.M{"userIds": 1, "source": 1}, &promotionCode)
				So(promotionCode.UserIds, ShouldBeEmpty)
				So(promotionCode.Source, ShouldEqual, globalConstant.PROMOTION_CODE_SOURCE_BREWARDS)
			})
		})
	})
	t.Run("16", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER=btaskee")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"partner":      "",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeList": []map[string]interface{}{
					{
						"code":   "BTASK1111",
						"isUsed": false,
					},
					{
						"code":   "KEEE12312",
						"isUsed": false,
					},
					{
						"code":   "KAHE12312",
						"isUsed": false,
					},
				},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
				So(m["data"], ShouldNotBeEmpty)

				//Check user point after point transaction
				updatedUser, _ := modelUser.GetOneById(local.ISO_CODE, user.XId, bson.M{"point": 1})
				So(updatedUser.Point, ShouldEqual, 500)

				//Check point transaction
				var insertedPointTransaction *modelPointTransaction.PointTransaction
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{}, &insertedPointTransaction)
				So(insertedPointTransaction.Point, ShouldEqual, 500)
				So(insertedPointTransaction.OldPoint, ShouldEqual, 1000)
				So(insertedPointTransaction.Source.ServiceText, ShouldNotBeNil)
			})
		})
	})
	t.Run("17", func(t *testing.T) {
		log.Println("==================== validate getGiftDetail")
		apiURL := subPath + "/get-gift-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"_id": 1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				mapResult := map[string]map[string]interface{}{}
				json.Unmarshal(b, &mapResult)
				So(res.Code, ShouldEqual, 400)
				So(mapResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if incentiveId is empty", func() {
				body := map[string]interface{}{
					"_id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				mapResult := map[string]map[string]interface{}{}
				json.Unmarshal(b, &mapResult)
				So(res.Code, ShouldEqual, 400)
				So(mapResult["error"]["code"], ShouldEqual, lib.ERROR_GIFT_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is empty", func() {
				body := map[string]interface{}{
					"_id":     "231123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				mapResult := map[string]map[string]interface{}{}
				json.Unmarshal(b, &mapResult)
				So(res.Code, ShouldEqual, 400)
				So(mapResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("18", func(t *testing.T) {
		log.Println("=================== getGiftDetail")
		apiURL := subPath + "/get-gift-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"incentiveId":   "ABC",
				"social": map[string]interface{}{
					"facebook":  "facbook.com",
					"instagram": "instagram.com",
					"ios":       "ios.com",
					"android":   "android.com",
					"website":   "website.com",
				},
				"redeemLink": "http://redeem.com",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"_id":     ids[0],
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			So(res.Code, ShouldEqual, 200)

			var gift *modelGift.Gift
			b, _ := io.ReadAll(res.Body)
			json.Unmarshal(b, &gift)
			So(gift, ShouldNotBeNil)
			So(gift.IncentiveId, ShouldEqual, "ABC")
			So(gift.Social, ShouldNotBeNil)
			So(gift.Social.Facebook, ShouldEqual, "facbook.com")
			So(gift.Social.Website, ShouldEqual, "website.com")
			So(gift.RedeemLink, ShouldEqual, "http://redeem.com")
		})
	})

	// Get gift detail has promotionCode with payment method
	t.Run("18.1", func(t *testing.T) {
		log.Println("=================== getGiftDetail")
		apiURL := subPath + "/get-gift-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)

		CreatePromotionCode([]map[string]interface{}{
			{
				"code":           "hsztksv7",
				"paymentMethods": []string{"MOMO", "VNPAY"},
			},
		})

		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"incentiveId":   "ABC",
				"social": map[string]interface{}{
					"facebook":  "facbook.com",
					"instagram": "instagram.com",
					"ios":       "ios.com",
					"android":   "android.com",
					"website":   "website.com",
				},
				"redeemLink": "http://redeem.com",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"_id":     ids[0],
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			So(res.Code, ShouldEqual, 200)

			var gift *model.GiftDetailWithPaymentMethod
			b, _ := io.ReadAll(res.Body)
			json.Unmarshal(b, &gift)
			So(gift, ShouldNotBeNil)
			So(gift.IncentiveId, ShouldEqual, "ABC")
			So(gift.Social, ShouldNotBeNil)
			So(gift.Social.Facebook, ShouldEqual, "facbook.com")
			So(gift.Social.Website, ShouldEqual, "website.com")
			So(gift.RedeemLink, ShouldEqual, "http://redeem.com")
			So(gift.PaymentMethods, ShouldResemble, []string{"MOMO", "VNPAY"})
		})
	})

	t.Run("19", func(t *testing.T) {
		log.Println("========================== validate getNewGift")
		apiURL := subPath + "/get-new-gift"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userId": 13213,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				mapResult := map[string]map[string]interface{}{}
				json.Unmarshal(b, &mapResult)
				So(res.Code, ShouldEqual, 400)
				So(mapResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				mapResult := map[string]map[string]interface{}{}
				json.Unmarshal(b, &mapResult)
				So(res.Code, ShouldEqual, 400)
				So(mapResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			// Convey("Check the response if isoCode is empty", func() {
			// 	body := map[string]interface{}{
			// 		"userId":  "123123",
			// 		"isoCode": "",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			// 	res := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(res, req)
			// 	b, _ := io.ReadAll(res.Body)
			// 	mapResult := map[string]map[string]interface{}{}
			// 	json.Unmarshal(b, &mapResult)
			// 	So(res.Code, ShouldEqual, 400)
			// 	So(mapResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			// })
		})
	})
	t.Run("20", func(t *testing.T) {
		log.Println("========================= getNewGift")
		apiURL := subPath + "/get-new-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+2),
				"incentiveId":   "ABC",
				"used":          false,
				"createdAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"incentiveId":   "*********",
				"used":          false,
				"createdAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			b, _ := io.ReadAll(res.Body)
			var results []*modelGift.Gift
			json.Unmarshal(b, &results)
			So(len(results), ShouldEqual, 2)
			So(results[0].IncentiveId, ShouldEqual, "ABC")
			So(results[0].Used, ShouldBeFalse)
			So(results[0].IsoCode, ShouldEqual, local.ISO_CODE)
			So(results[0].UserId, ShouldEqual, body["userId"])
			So(len(results[0].ApplyFor.Service), ShouldNotBeNil)
		})
	})

	t.Run("20.1", func(t *testing.T) {
		log.Println("========================= getNewGift")
		apiURL := subPath + "/get-new-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+2),
				"incentiveId":   "ABC",
				"used":          false,
				"createdAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 30% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 30% discount for Cleaning Service",
					"ko": "청소 서비스 30% 할인 즐기기",
				},
				"askerPhone":        "0834567890",
				"serviceName":       globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode":     "hsztksv7",
				"isoCode":           local.ISO_CODE,
				"incentiveId":       "*********",
				"used":              false,
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"expiredIsInfinite": true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			b, _ := io.ReadAll(res.Body)
			var results []*modelGift.Gift
			json.Unmarshal(b, &results)
			So(len(results), ShouldEqual, 2)
			So(results[0].IncentiveId, ShouldEqual, "ABC")
			So(results[0].Used, ShouldBeFalse)
			So(results[0].IsoCode, ShouldEqual, local.ISO_CODE)
			So(results[0].UserId, ShouldEqual, body["userId"])
			So(len(results[0].ApplyFor.Service), ShouldNotBeNil)

		})
	})

	t.Run("21", func(t *testing.T) {
		log.Println("==================================== Validate GetUsedGift")
		// GetUsedGift
		apiGetUsedGift := "/api/v3/api-asker-vn/get-used-gift"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUsedGift), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUsedGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			// Convey("Check request when isoCode blank", func() {
			// 	body := map[string]interface{}{
			// 		"userId":  "0834567890",
			// 		"isoCode": "",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiGetUsedGift, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 400)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			// })
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123123,
					"isoCode": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUsedGift, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("22", func(t *testing.T) {
		log.Println("==================================== GetUsedGift")
		// apiGetGift
		apiGetUsedGift := "/api/v3/api-asker-vn/get-used-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Gift
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"used":          true,
			},
		})
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUsedGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetUsedGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Used Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["promotionCode"], ShouldEqual, "hsztksv7")
					So(respResultM[0]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
				})
			})
		})
	})

	t.Run("23", func(t *testing.T) {
		log.Println("==================================== GetUsedGift")
		// apiGetGift
		apiGetUsedGift := "/api/v3/api-asker-vn/get-used-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Gift
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"expired":       "2020,11,02,15,15",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUsedGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetUsedGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Used Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["promotionCode"], ShouldEqual, "hsztksv7")
					So(respResult[0]["expired"], ShouldBeLessThan, now.Format(time.RFC3339))
					So(respResultM[0]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
				})
			})
		})
	})

	// Case has barcode
	t.Run("23.1", func(t *testing.T) {
		log.Println("==================================== GetUsedGift")
		// apiGetGift
		apiGetUsedGift := "/api/v3/api-asker-vn/get-used-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"cart": map[string]interface{}{
					"cartNo": "9660995",
					"code_link_gift": []map[string]interface{}{
						{
							"code_image": `https://barcode.tec-it.com/barcode.ashx?data=UB100KC66C3ED&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default`,
						},
					},
				},
				"used":    true,
				"expired": "2020,11,02,15,15",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUsedGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetUsedGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Used Gift", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["barCode"], ShouldEqual, `https://barcode.tec-it.com/barcode.ashx?data=UB100KC66C3ED&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default`)
				})
			})
		})
	})

	t.Run("24", func(t *testing.T) {
		log.Println("==================================== GetNewGift with gift not field expired")
		// GetNewGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-new-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Gift
		now := globalLib.GetCurrentTime(local.TimeZone)
		createdAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
		createdAt2 := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, -1)
		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), createdAt.Month(), createdAt.Day(), createdAt.Hour(), 30),
				"expiredIsNil":  true,
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			},
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 50% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 50% discount for Cleaning Service",
					"ko": "청소 서비스 50% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "haslqwe",
				"isoCode":       local.ISO_CODE,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), 10),
				"expiredIsNil":  true,
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			},
			// {
			// 	"askerPhone":    "0834567890",
			// 	"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			// 	"promotionCode": "j123klhsad",
			// 	"isoCode":       local.ISO_CODE,
			// 	"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), 45),
			// 	"expiredIsNil":  true,
			// 	"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			// },
			{
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "j123klhsad",
				"isoCode":       local.ISO_CODE,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", createdAt2.Year(), createdAt2.Month(), createdAt2.Day(), createdAt2.Hour(), 45),
				"expiredIsNil":  true,
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["expired"], ShouldNotBeNil)
					expiredExpected := globalLib.ParseDateFromString(respResult[0]["createdAt"].(string), local.TimeZone).AddDate(0, 1, 0).Format("2006-01-02T15:04:05Z")
					expiredActual := globalLib.ParseDateFromString(respResult[0]["expired"].(string), local.TimeZone).Format("2006-01-02T15:04:05Z")
					So(expiredActual, ShouldEqual, expiredExpected)
					So(respResult[0]["promotionCode"], ShouldEqual, "haslqwe")
					So(respResultM[0]["title"]["en"], ShouldEqual, "Enjoy 50% discount for Cleaning Service")
					So(respResult[1]["promotionCode"], ShouldEqual, "hsztksv7")
					So(respResultM[1]["title"]["vi"], ShouldEqual, "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà")
					So(respResult[0]["createdAt"], ShouldBeGreaterThan, respResult[1]["createdAt"])
					So(respResult[0]["_id"], ShouldEqual, ids[1])
					So(respResult[1]["_id"], ShouldEqual, ids[0])
					So(respResult[1]["expired"], ShouldNotBeNil)
					expiredExpected = globalLib.ParseDateFromString(respResult[1]["createdAt"].(string), local.TimeZone).AddDate(0, 1, 0).Format("2006-01-02T15:04:05Z")
					expiredActual = globalLib.ParseDateFromString(respResult[1]["expired"].(string), local.TimeZone).Format("2006-01-02T15:04:05Z")
					So(expiredActual, ShouldEqual, expiredExpected)
				})
			})
		})
	})

	t.Run("24.1", func(t *testing.T) {
		log.Println("==================================== GetNewGift with isApplyForAllService")
		// GetNewGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-new-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Gift
		now := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 50% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 50% discount for Cleaning Service",
					"ko": "청소 서비스 50% 할인 즐기기",
				},
				"askerPhone":                    "0834567890",
				"serviceName":                   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode":                 "haslqwe",
				"isoCode":                       local.ISO_CODE,
				"createdAt":                     fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), 10),
				"expiredIsNil":                  true,
				"source":                        globalConstant.GIFT_SOURCE_SYSTEM,
				"applyFor.isApplyForAllService": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User Gift", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["promotionCode"], ShouldEqual, "haslqwe")
					So(respResultM[0]["title"]["en"], ShouldEqual, "Enjoy 50% discount for Cleaning Service")
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					// So(respResultM[0]["applyFor"]["isApplyForAllService"], ShouldEqual, true)
				})
			})
		})
	})

	// case has barCode
	t.Run("24.2", func(t *testing.T) {
		log.Println("==================================== GetNewGift has barcode")
		// GetNewGift
		apiGetUserGift := "/api/v3/api-asker-vn/get-new-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Gift
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"cart": map[string]interface{}{
					"cartNo": "9660995",
					"code_link_gift": []map[string]interface{}{
						{
							"code_image": `https://barcode.tec-it.com/barcode.ashx?data=UB100KC66C3ED&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default`,
						},
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User Gift", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["barCode"], ShouldEqual, `https://barcode.tec-it.com/barcode.ashx?data=UB100KC66C3ED&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Fit&dpi=96&imagetype=Gif&rotation=0&color=%23000000&bgcolor=%23ffffff&qunit=Mm&quiet=0&dmsize=Default`)
					// So(respResultM[0]["applyFor"]["isApplyForAllService"], ShouldEqual, true)
				})
			})
		})
	})

	// Share gift
	t.Run("25", func(t *testing.T) {
		apiURL := subPath + "/share-gift"
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("========================= Share gift: userId required")

			body := map[string]interface{}{
				"userId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 400)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("========================= Share gift: new gift owner id required")

			body := map[string]interface{}{
				"userId":       "123123",
				"sharedUserId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 400)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_SHARED_USER_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		log.Println("========================= Share gift: new gift id required")
		apiURL := subPath + "/share-gift"
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "123123123",
				"sharedUserId": "2223123",
				"giftId":       "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 400)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_GIFT_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	t.Run("27", func(t *testing.T) {
		log.Println("========================= Share gift: user not found")
		apiURL := subPath + "/share-gift"
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "0834567890",
				"sharedUserId": "2223123",
				"giftId":       "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 404)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("28", func(t *testing.T) {
		log.Println("========================= Share gift: new gift owner not found")
		apiURL := subPath + "/share-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "0834567890",
				"sharedUserId": "2223123",
				"giftId":       "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 404)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_SHARED_USER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("29", func(t *testing.T) {
		log.Println("========================= Share gift: gift not found")
		apiURL := subPath + "/share-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			}, {
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			}, {
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+2),
				"incentiveId":   "ABC",
				"used":          false,
				"createdAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "0834567891",
				"sharedUserId": "0834567892",
				"giftId":       giftIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 404)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_GIFT_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("30", func(t *testing.T) {
		log.Println("========================= Share gift: gift cannot share because Gift.applyFor.isAllUsers = false")
		apiURL := subPath + "/share-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			}, {
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"expiredAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+2),
				"incentiveId":   "ABC",
				"used":          false,
				"createdAt":     fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "0834567891",
				"sharedUserId": "0834567890",
				"giftId":       giftIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 500)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_GIFT_CAN_NOT_SHARE.ErrorCode)
				})
			})
		})
	})

	t.Run("31", func(t *testing.T) {
		log.Println("========================= Share gift: new user isoCode not match")
		apiURL := subPath + "/share-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			}, {
				"phone":   "0834567891",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"point":   1000,
				"isoCode": globalConstant.ISO_CODE_TH,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":          "0834567890",
				"serviceName":         globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode":       "hsztksv7",
				"isoCode":             local.ISO_CODE,
				"expiredAt":           fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+2),
				"incentiveId":         "ABC",
				"used":                false,
				"createdAt":           fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"applyFor.isAllUsers": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "0834567891",
				"sharedUserId": "0834567890",
				"giftId":       giftIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response ", func() {
					b, _ := io.ReadAll(res.Body)
					resultMap := make(map[string]map[string]interface{})
					json.Unmarshal(b, &resultMap)

					So(res.Code, ShouldEqual, 500)
					So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_USER_ISO_CODE_NOT_MATCH.ErrorCode)
				})
			})
		})
	})

	t.Run("32", func(t *testing.T) {
		log.Println("========================= Share gift: success")
		apiURL := subPath + "/share-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			}, {
				"phone": "0834567891",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(10 * time.Hour)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":          "0834567890",
				"serviceName":         globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode":       "hsztksv7",
				"isoCode":             local.ISO_CODE,
				"expiredAt":           fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+2),
				"incentiveId":         "ABC",
				"used":                false,
				"createdAt":           fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"applyFor.isAllUsers": true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId":       "0834567891",
				"sharedUserId": "0834567890",
				"giftId":       giftIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response and database", func() {
					// Check the response
					So(res.Code, ShouldEqual, 200)

					// Check the database
					var newOwnerGifts []*modelGift.Gift
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"userId": "0834567891"}, bson.M{}, &newOwnerGifts)
					So(len(newOwnerGifts), ShouldEqual, 1)
					So(newOwnerGifts[0].PromotionCode, ShouldEqual, "hsztksv7")
					So(newOwnerGifts[0].IsoCode, ShouldEqual, local.ISO_CODE)
					So(newOwnerGifts[0].IncentiveId, ShouldEqual, "ABC")
					So(newOwnerGifts[0].Used, ShouldBeFalse)

					var oldOwnerGifts []*modelGift.Gift
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{"_id": 1}, &oldOwnerGifts)
					So(len(oldOwnerGifts), ShouldEqual, 0)

				})
			})
		})
	})

	// Redeem-gift from partner (incentive has codeFromPartner)
	// limit = 10
	// numberOfUsed = 1
	t.Run("33", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER=btaskee (incentive has codeFromPartner)")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"partner":      "",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeFromPartner": map[string]interface{}{
					"code":         "abc123",
					"limit":        10,
					"numberOfUsed": 1,
				},
				"social": map[string]interface{}{
					"facebook":  "facbook.com",
					"instagram": "instagram.com",
					"ios":       "ios.com",
					"android":   "android.com",
					"website":   "website.com",
					"youtube":   "http://Youtube.com",
					"tiktok":    "http://Tiktok.com",
				},
				"redeemLink":     "redeemLink",
				"paymentMethods": []string{"MOMO"},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
				So(m["data"], ShouldNotBeEmpty)
				So(m["paymentMethods"], ShouldResemble, []interface{}{"MOMO"})

				//Check user point after point transaction
				updatedUser, _ := modelUser.GetOneById(local.ISO_CODE, user.XId, bson.M{"point": 1})
				So(updatedUser.Point, ShouldEqual, 500)

				//Check point transaction
				var insertedPointTransaction *modelPointTransaction.PointTransaction
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{}, &insertedPointTransaction)
				So(insertedPointTransaction.Point, ShouldEqual, 500)
				So(insertedPointTransaction.OldPoint, ShouldEqual, 1000)
				So(insertedPointTransaction.Source.ServiceText, ShouldNotBeNil)
				var incentive *modelIncentive.Incentive
				globalDataAccess.GetOneById(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], ids[0], bson.M{"codeFromPartner": 1}, &incentive)
				So(incentive.CodeFromPartner.NumberOfUsed, ShouldEqual, 2)

				var gift *modelGift.Gift
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"incentiveId": ids[0]}, bson.M{"social": 1, "redeemLink": 1}, &gift)
				So(gift.Social.Facebook, ShouldEqual, "facbook.com")
				So(gift.Social.Website, ShouldEqual, "website.com")
				So(gift.Social.Youtube, ShouldEqual, "http://Youtube.com")
				So(gift.Social.Tiktok, ShouldEqual, "http://Tiktok.com")
				So(gift.RedeemLink, ShouldEqual, "redeemLink")
			})
		})
	})

	// Redeem-gift from partner (incentive has codeFromPartner)
	// limit = 0
	// numberOfUsed = 2
	t.Run("34", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER=btaskee (incentive has codeFromPartner)")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"partner":      "",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeFromPartner": map[string]interface{}{
					"code":         "abc123",
					"limit":        0,
					"numberOfUsed": 2,
				},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
				So(m["data"], ShouldNotBeEmpty)

				//Check user point after point transaction
				updatedUser, _ := modelUser.GetOneById(local.ISO_CODE, user.XId, bson.M{"point": 1})
				So(updatedUser.Point, ShouldEqual, 500)

				//Check point transaction
				var insertedPointTransaction *modelPointTransaction.PointTransaction
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{}, &insertedPointTransaction)
				So(insertedPointTransaction.Point, ShouldEqual, 500)
				So(insertedPointTransaction.OldPoint, ShouldEqual, 1000)
				So(insertedPointTransaction.Source.ServiceText, ShouldNotBeNil)
				var incentive *modelIncentive.Incentive
				globalDataAccess.GetOneById(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], ids[0], bson.M{"codeFromPartner": 1}, &incentive)
				So(incentive.CodeFromPartner.NumberOfUsed, ShouldEqual, 3)
			})
		})
	})

	// Redeem-gift from partner (incentive has codeFromPartner)
	// limit = 10
	// numberOfUsed = 10
	t.Run("35", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER=btaskee (incentive has codeFromPartner)")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"partner":      "",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeFromPartner": map[string]interface{}{
					"code":         "abc123",
					"limit":        10,
					"numberOfUsed": 10,
				},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				So(res.Code, ShouldEqual, 500)
				json.Unmarshal(b, &m)
				So(m["error"]["code"], ShouldEqual, "CODE_IS_USED_UP")

				var incentive *modelIncentive.Incentive
				globalDataAccess.GetOneById(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], ids[0], bson.M{"status": 1}, &incentive)
				So(incentive.Status, ShouldEqual, globalConstant.INCENTIVE_STATUS_INACTIVE)
			})
		})
	})
	// Redeem-gift from partner (incentive has codeFromPartner)
	// limit = 2
	// numberOfUsed = 1
	t.Run("36", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER=btaskee (incentive has codeFromPartner)")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"partner":      "",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeFromPartner": map[string]interface{}{
					"code":         "abc123",
					"limit":        2,
					"numberOfUsed": 1,
				},
				"codeList": []map[string]interface{}{
					{
						"code":   "hsztksv6",
						"isUsed": false,
					}, {
						"code":   "hsztksv7",
						"isUsed": false,
					},
				},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
			})
		})
	})

	// Like case 24 but another api and return the total gifts only
	t.Run("37", func(t *testing.T) {
		log.Println("==================================== CountNewGift with gift not field expired. Like case 24 but another api and return the total gifts only")
		// GetNewGift
		apiGetUserGift := "/api/v3/api-asker-vn/count-new-gift"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834569520",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Gift
		now := globalLib.GetCurrentTime(local.TimeZone)
		createdAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
		createdAt2 := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, -1)
		CreateGift([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), createdAt.Month(), createdAt.Day(), createdAt.Hour(), 30),
				"expiredIsNil":  true,
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			},
			{
				"title": map[string]interface{}{
					"vi": "Ưu đãi 50% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 50% discount for Cleaning Service",
					"ko": "청소 서비스 50% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "haslqwe",
				"isoCode":       local.ISO_CODE,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), 10),
				"expiredIsNil":  true,
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			},
			// {
			// 	"askerPhone":    "0834567890",
			// 	"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			// 	"promotionCode": "j123klhsad",
			// 	"isoCode":       local.ISO_CODE,
			// 	"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), 45),
			// 	"expiredIsNil":  true,
			// 	"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			// },
			{
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "j123klhsad",
				"isoCode":       local.ISO_CODE,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", createdAt2.Year(), createdAt2.Month(), createdAt2.Day(), createdAt2.Hour(), 45),
				"expiredIsNil":  true,
				"source":        globalConstant.GIFT_SOURCE_SYSTEM,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUserGift), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserGift, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Then check the response to test Get User Gift", func() {
					So(res.Code, ShouldEqual, 200)
					var result map[string]interface{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["totalNewGifts"], ShouldEqual, 2)
				})
			})
		})
	})

	t.Run("37.1", func(t *testing.T) {
		log.Println("==================================== Get my reward v2 with marketing campaign type promotion can be use")
		apiUrl := "/api/v3/api-asker-vn/count-new-gift-v2"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":    "0834567890",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"language": globalConstant.LANG_VI,
			}, {
				"phone":    "0834567891",
				"name":     "Asker 02",
				"type":     globalConstant.USER_TYPE_ASKER,
				"language": globalConstant.LANG_VI,
			},
		})
		CreateGift([]map[string]interface{}{
			{
				"userId": "0834567890",
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "brand.image",
					"text": map[string]interface{}{
						"vi": "Grab",
					},
				},
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2022,11,02,15,15",
				"promotionCode": "gift01",
			},
			{
				"userId": "0834567891",
				"title": map[string]interface{}{
					"vi": "Incentive 2",
				},
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2021,11,02,15,15",
				"promotionCode": "gift02",
			},
			{
				"userId": "0834567890",
				"title": map[string]interface{}{
					"vi": "Incentive 3",
				},
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"promotionCode": "gift03",
			},
			{
				"userId": "0834567890",
				"title": map[string]interface{}{
					"vi": "Incentive 4",
				},
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"promotionCode": "gift04",
			},
			{
				"userId": "0834567890",
				"title": map[string]interface{}{
					"vi": "Incentive 5",
				},
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2020,11,02,15,15",
				"promotionCode": "gift05",
			},
		})

		// Create marketing campaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "ABC123",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 2",
					"en": "Happy Vietnam's Women Day 2",
					"ko": "Happy Vietnam's Women Day 2",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ) 3",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_INFO,
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // has limit
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "ABC124",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // has limit but not enough quantity
				"img": "Img Test 4",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "ABC125",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
			{ // enough quantity but user used it before
				"img": "Img Test 5",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"promotion": map[string]interface{}{
					"code": "ABC126",
				},
				"applyForUser": map[string]interface{}{
					"target": "BOTH",
				},
			},
		})

		CreatePromotionCode([]map[string]interface{}{
			{
				"code": "ABC123",
			},
			{
				"code":  "ABC124",
				"limit": 100,
			},
			{
				"code":  "ABC125",
				"limit": 2,
			},
			{
				"code":  "ABC126",
				"limit": 200,
			}, {
				"code": "gift01",
			}, {
				"code": "gift02",
			}, {
				"code": "gift03",
			}, {
				"code":   "gift04",
				"locked": true,
			},
		})

		CreatePromotionHistory([]map[string]interface{}{
			{
				"promotionCode": "ABC124",
			}, {
				"promotionCode": "ABC124",
			}, {
				"promotionCode": "ABC125",
			}, {
				"promotionCode": "ABC125",
			}, {
				"promotionCode": "ABC126",
				"userId":        "0834567890",
			},
		})

		Convey("Check the response", t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			result := map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["totalNewGifts"], ShouldEqual, 4)

			// Đây là response của api get-my-rewards-v2.
			// resultM := []map[string]interface{}{}
			// resultMM := []map[string]map[string]interface{}{}
			// json.Unmarshal(b, &resultM)
			// json.Unmarshal(b, &resultMM)
			// So(len(resultM), ShouldEqual, 4)
			// So(len(resultMM), ShouldEqual, 4)
			// So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			// So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			// So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")

			// So(resultMM[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			// So(resultMM[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")

			// // Check marketing campaign
			// So(resultMM[2]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
			// So(resultMM[2]["brandInfo"]["image"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/sEFHixKrmjNcp9jaw")
			// So(resultMM[2]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
			// So(resultM[2]["quantity"], ShouldBeNil)

			// So(resultMM[3]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
			// So(resultMM[3]["brandInfo"]["image"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/sEFHixKrmjNcp9jaw")
			// So(resultMM[3]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
			// So(resultM[3]["quantity"], ShouldEqual, 98)
		})
	})

	// Can not redeem incentive khi rank user chưa đủ
	t.Run("38", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER=btaskee (incentive has codeFromPartner)")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
				"rankName": map[string]interface{}{
					"name": "GOLD",
				},
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"partner":      "",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeFromPartner": map[string]interface{}{
					"code":         "abc123",
					"limit":        2,
					"numberOfUsed": 1,
				},
				"codeList": []map[string]interface{}{
					{
						"code":   "hsztksv6",
						"isUsed": false,
					}, {
						"code":   "hsztksv7",
						"isUsed": false,
					},
				},
				"rankRequire": 4,
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				//Check the http response
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_YOUR_RANK_CAN_NOT_REDEEM_GIFT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_YOUR_RANK_CAN_NOT_REDEEM_GIFT.Message)
			})
		})
	})
	// Redeem partner incentive flash sale
	t.Run("39", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive flash sale")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()

		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM_WITH_PARTNER",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"codeList": []map[string]interface{}{
					{
						"code":   "BTASK1111",
						"isUsed": false,
					},
					{
						"code":   "KEEE12312",
						"isUsed": false,
					},
					{
						"code":   "KAHE12312",
						"isUsed": false,
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           ids[0],
						"originalPoint": 500,
						"point":         250,
					},
				},
			},
		})

		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
				So(m["data"], ShouldNotBeEmpty)

				//Check user point after point transaction
				updatedUser, _ := modelUser.GetOneById(local.ISO_CODE, user.XId, bson.M{"point": 1})
				So(updatedUser.Point, ShouldEqual, 750)

				//Check point transaction
				var insertedPointTransaction *modelPointTransaction.PointTransaction
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{}, &insertedPointTransaction)
				So(insertedPointTransaction.Point, ShouldEqual, 250)
				So(insertedPointTransaction.OldPoint, ShouldEqual, 1000)
				So(insertedPointTransaction.Source.ServiceText, ShouldNotBeNil)
			})
		})
	})
	// Redeem Gift incentive from SYSTEM flash sale
	t.Run("40", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive from SYSTEM_WITH_PARTNER flash sale")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           ids[0],
						"originalPoint": 500,
						"point":         250,
					},
				},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
				So(m["data"], ShouldNotBeEmpty)

				//Check user point after point transaction
				updatedUser, _ := modelUser.GetOneById(local.ISO_CODE, user.XId, bson.M{"point": 1})
				So(updatedUser.Point, ShouldEqual, 750)

				//Check point transaction
				var insertedPointTransaction *modelPointTransaction.PointTransaction
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{}, &insertedPointTransaction)
				So(insertedPointTransaction.Point, ShouldEqual, 250)
				So(insertedPointTransaction.OldPoint, ShouldEqual, 1000)
				So(insertedPointTransaction.Source.ServiceText, ShouldNotBeNil)
			})
		})
	})

	// Redeem Gift incentive on time
	t.Run("41", func(t *testing.T) {
		log.Println("===================== Redeem Gift incentive on time")
		apiURL := subPath + "/redeem-gift"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := now.Date()
		hh, mm := now.Hour(), now.Minute()

		end := now.Add(15 * time.Minute)
		yEnd, mEnd, dEnd := end.Date()
		hhEnd, mmEnd := end.Hour(), end.Minute()
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", y, int(m), d, hh, mm),
				"endDate":   fmt.Sprintf("%d,%d,%d,%d,%d", yEnd, int(mEnd), dEnd, hhEnd, mmEnd),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              500,
				},
				"from":            "SYSTEM",
				"categoryName":    "ABC",
				"status":          globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":       "vinhnt",
				"isoCode":         local.ISO_CODE,
				"createdAt":       fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"isRedeemOneTime": true,
			},
		})
		CreateRedeemGiftTransaction([]map[string]interface{}{
			{
				"userId": "0834567890",
				"incentiveInfo": map[string]interface{}{
					"incentiveId": ids[0],
				},
			},
		})

		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": user.XId,
					"giftId": ids[0],
					"data": map[string]interface{}{
						"mockData": 123,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				//Check the http response
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(res.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_INCENTIVE_JUST_REDEEM_ONE_TIME.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_INCENTIVE_JUST_REDEEM_ONE_TIME.Message)
			})
		})
	})
}
