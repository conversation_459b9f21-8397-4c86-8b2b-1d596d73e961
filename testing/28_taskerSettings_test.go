/*
* @File: 28_taskerSettings_test.go
* @Description: Handler function, case test
* @CreatedAt: 08/12/2020
* @Author: vinhnt
* @UpdatedAt: 16/12/2020
* @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"go.mongodb.org/mongo-driver/bson"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
)

func TestT_Setting(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-safe-working-process"
		ResetData()

		log.Println("==================================== Validate GetSafeWorkingProcess")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})

			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"isoCode": "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-safe-working-process"
		ResetData()
		UpdateTaskerSettings(map[string]interface{}{
			"$set": map[string]interface{}{"supportPhoneDailyWork": "0314151617",
				"safeWorkingProcessLink": "test.com"},
		})
		log.Println("==================================== GetSafeWorkingProcess")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)

			var respResult map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["status"], ShouldEqual, "success")
			So(respResult["link"], ShouldEqual, "test.com")
		})

		UpdateTaskerSettings(bson.M{"$unset": bson.M{"supportPhoneDailyWork": 1, "safeWorkingProcessLink": 1}})
	})

	t.Run("3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-safe-working-process"
		ResetData()
		log.Println("==================================== GetSafeWorkingProcess")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)

			var respResult map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["status"], ShouldEqual, "error")
			So(respResult["reason"], ShouldEqual, "Link not found")
		})
	})
}
