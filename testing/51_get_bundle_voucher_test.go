package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func TestGetListBundleVoucher(t *testing.T) {
	apiGetBundleVoucher := "/api/v3/api-asker-vn/get-list-bundle-voucher"
	t.Run("2", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher is expired ")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 1)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 0)
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher is not redeemed yet ")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 2),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 1)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 0)
				})
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher's rank required higher than asker rank")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 2),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 1)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 0)
				})
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list without sort ")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 1)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 1)
					result1 := result[0].(map[string]interface{})
					So(result1["image"], ShouldEqual, "abcxyz")
					So(result1["type"], ShouldEqual, "SECRET_BOX")
					title := result1["title"].(map[string]interface{})
					So(title["vi"], ShouldEqual, "Gói voucher bí ẩn")
					content := result1["content"].(map[string]interface{})
					So(content["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition := result1["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange := result1["exchange"].(map[string]interface{})
					So(exchange["requiredPoint"], ShouldEqual, 30)
				})
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list without SORT_BY_LATEST ")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn 2",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -3),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
			"sortBy": "LATEST",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 2)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 2)
					result1 := result[0].(map[string]interface{})
					So(result1["image"], ShouldEqual, "abcxyz")
					So(result1["type"], ShouldEqual, "SECRET_BOX")
					title := result1["title"].(map[string]interface{})
					So(title["vi"], ShouldEqual, "Gói voucher bí ẩn")
					content := result1["content"].(map[string]interface{})
					So(content["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition := result1["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange := result1["exchange"].(map[string]interface{})
					So(exchange["requiredPoint"], ShouldEqual, 30)

					result2 := result[1].(map[string]interface{})
					So(result2["image"], ShouldEqual, "abcxyz")
					So(result2["type"], ShouldEqual, "SECRET_BOX")
					title2 := result2["title"].(map[string]interface{})
					So(title2["vi"], ShouldEqual, "Gói voucher bí ẩn 2")
					content2 := result2["content"].(map[string]interface{})
					So(content2["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition2 := result2["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition2["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange2 := result2["exchange"].(map[string]interface{})
					So(exchange2["requiredPoint"], ShouldEqual, 30)
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list without SORT_BY_LOW_TO_HIGH")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 20,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -3),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn 2",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
			"sortBy": "LOW_TO_HIGH",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 2)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 2)
					result1 := result[0].(map[string]interface{})
					So(result1["image"], ShouldEqual, "abcxyz")
					So(result1["type"], ShouldEqual, "SECRET_BOX")
					title := result1["title"].(map[string]interface{})
					So(title["vi"], ShouldEqual, "Gói voucher bí ẩn")
					content := result1["content"].(map[string]interface{})
					So(content["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition := result1["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange := result1["exchange"].(map[string]interface{})
					So(exchange["requiredPoint"], ShouldEqual, 20)

					result2 := result[1].(map[string]interface{})
					So(result2["image"], ShouldEqual, "abcxyz")
					So(result2["type"], ShouldEqual, "SECRET_BOX")
					title2 := result2["title"].(map[string]interface{})
					So(title2["vi"], ShouldEqual, "Gói voucher bí ẩn 2")
					content2 := result2["content"].(map[string]interface{})
					So(content2["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition2 := result2["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition2["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange2 := result2["exchange"].(map[string]interface{})
					So(exchange2["requiredPoint"], ShouldEqual, 30)
				})
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list without SORT_BY_HIGH_TO_LOW")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -3),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn 2",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 20,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
			"sortBy": "HIGH_TO_LOW",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 2)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 2)
					result1 := result[0].(map[string]interface{})
					So(result1["image"], ShouldEqual, "abcxyz")
					So(result1["type"], ShouldEqual, "SECRET_BOX")
					title := result1["title"].(map[string]interface{})
					So(title["vi"], ShouldEqual, "Gói voucher bí ẩn")
					content := result1["content"].(map[string]interface{})
					So(content["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition := result1["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange := result1["exchange"].(map[string]interface{})
					So(exchange["requiredPoint"], ShouldEqual, 30)

					result2 := result[1].(map[string]interface{})
					So(result2["image"], ShouldEqual, "abcxyz")
					So(result2["type"], ShouldEqual, "SECRET_BOX")
					title2 := result2["title"].(map[string]interface{})
					So(title2["vi"], ShouldEqual, "Gói voucher bí ẩn 2")
					content2 := result2["content"].(map[string]interface{})
					So(content2["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition2 := result2["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition2["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange2 := result2["exchange"].(map[string]interface{})
					So(exchange2["requiredPoint"], ShouldEqual, 20)
				})
			})
		})
	})

	t.Run("9", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list without login ")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 1)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 1)
					result1 := result[0].(map[string]interface{})
					So(result1["image"], ShouldEqual, "abcxyz")
					So(result1["type"], ShouldEqual, "SECRET_BOX")
					title := result1["title"].(map[string]interface{})
					So(title["vi"], ShouldEqual, "Gói voucher bí ẩn")
					content := result1["content"].(map[string]interface{})
					So(content["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition := result1["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange := result1["exchange"].(map[string]interface{})
					So(exchange["requiredPoint"], ShouldEqual, 30)
				})
			})
		})
	})

	t.Run("10", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list with asker turn")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucher1",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -3),

				"isUnlimitRedeem": true,
			},
			{
				"_id":       "bundleVoucher2",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn 2",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 20,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire":     1,
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": false,
				"askerTurn":       3,
				"excludeUserIds":  []string{"0823456720"},
			},
		})
		CreateBundleVoucherHistory([]map[string]interface{}{
			{
				"bundleVoucherId": "bundleVoucher2",
				"userId":          "0823456720",
				"totalRedeem":     3,
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 1)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 1)
					result1 := result[0].(map[string]interface{})
					So(result1["image"], ShouldEqual, "abcxyz")
					So(result1["type"], ShouldEqual, "SECRET_BOX")
					title := result1["title"].(map[string]interface{})
					So(title["vi"], ShouldEqual, "Gói voucher bí ẩn")
					content := result1["content"].(map[string]interface{})
					So(content["vi"], ShouldEqual, "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà")
					termsAndCondition := result1["termsAndCondition"].(map[string]interface{})
					So(termsAndCondition["vi"], ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
					exchange := result1["exchange"].(map[string]interface{})
					So(exchange["requiredPoint"], ShouldEqual, 30)
				})
			})
		})
	})

	t.Run("11", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list with isOnlyDealsCanBuy")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 40,
				"rankInfo": map[string]interface{}{
					"point":    40,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 10,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 20,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 50,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 100,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId":            "0823456720",
			"isOnlyDealsCanBuy": true,
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 0)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 3)
				})
			})
		})
	})

	t.Run("12", func(t *testing.T) {
		ResetData()
		log.Println("==================================== Get list with voucher has no stock")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 40,
				"rankInfo": map[string]interface{}{
					"point":    40,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"stock":       0,
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 10,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"stock":       0,
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 20,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 50,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 100,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId":            "0823456720",
			"isOnlyDealsCanBuy": true,
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					result := make([]interface{}, 0)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 1)
				})
			})
		})
	})
}
