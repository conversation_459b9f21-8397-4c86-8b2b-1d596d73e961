/*
 * @File: 35_refund_request_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 10/11/2021
 * @Author: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	getListComboVoucherTransactionHistories "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getListTransactionHistories"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getListUserComboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getUserComboVoucherDetail"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucherTransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelUserComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func TestComboVoucher(t *testing.T) {

	// Case user not login
	// User can get only ACTIVE combo voucher (status == ACTIVE && isTesting != true && target == BOTH)
	t.Run("1", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image":    "combo1-1",
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"image": "xxx",
						"from":  "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":  "combo2.jpg",
				"status": "ACTIVE",
				"cost":   200000,
				"price":  150000,
				"title": map[string]interface{}{
					"en": "Title2",
				},
				"content": map[string]interface{}{
					"en": "Tiết kiệm tới 50000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image": "xxx",
						"promotion": map[string]interface{}{
							"type":  "MONEY",
							"value": 50000,
						},
						"from": "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":  "combo4.jpg",
				"status": "ACTIVE",
				"cost":   200000,
				"price":  150000,
				"title": map[string]interface{}{
					"en": "Title4",
				},
				"content": map[string]interface{}{
					"en": "Tiết kiệm tới 50000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image": "xxx",
						"promotion": map[string]interface{}{
							"type":  "MONEY",
							"value": 50000,
						},
						"from": "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})
		body := map[string]interface{}{}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				combo1 := respResult[0]

				So(combo1.Currency.Code, ShouldEqual, "VND")
				So(combo1.Thumbnail, ShouldEqual, "thumbnail1.jpg")
				So(combo1.Image, ShouldEqual, "combo1.jpg")
				So(combo1.Cost, ShouldEqual, 100000)
				So(combo1.Price, ShouldEqual, 80000)
				So(combo1.Title.En, ShouldEqual, "Title1")
				So(combo1.Content.En, ShouldEqual, "Tiết kiệm tới 20000 vnđ")
				So(combo1.StartDate, ShouldNotBeNil)
				So(combo1.EndDate, ShouldNotBeNil)

				combo2 := respResult[1]
				So(combo2.Currency.Code, ShouldEqual, "VND")
				So(combo2.Image, ShouldEqual, "combo2.jpg")
				So(combo2.Cost, ShouldEqual, 200000)
				So(combo2.Price, ShouldEqual, 150000)
				So(combo2.Title.En, ShouldEqual, "Title2")
				So(combo2.Content.En, ShouldEqual, "Tiết kiệm tới 50000 vnđ")
				So(combo2.StartDate, ShouldNotBeNil)
				So(combo2.EndDate, ShouldNotBeNil)
			})
		})
	})

	// Case new user -> can get BOTH && NEW combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	t.Run("2", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":    "0823456720",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"taskDone": 0,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"isTesting": true,
				"cost":      300000,
				"price":     200000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo4.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_NEW,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for _, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo4.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					}
				}
			})
		})
	})

	// Case old user -> can get BOTH && CURRENT combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	t.Run("2.1", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"isTesting": true,
				"cost":      300000,
				"price":     200000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo4.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_NEW,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for _, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo5.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					}
				}
			})
		})
	})

	// Case user testing -> can get all ACTIVE combo voucher
	// Check isUserTester
	t.Run("3", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"isTesting": true,
				"cost":      300000,
				"price":     200000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":          "combo4.jpg",
				"status":         "ACTIVE",
				"isTesting":      true,
				"cost":           300000,
				"price":          200000,
				"createdAt":      currentTime,
				"startDate":      currentTime.AddDate(0, 0, -1),
				"endDate":        currentTime.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			},
		})
		UpdateSettings(map[string]interface{}{
			"$set": bson.M{"tester": []string{"0823456720"}},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				UpdateSettings(map[string]interface{}{"$unset": bson.M{"tester": 1}})
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 1)
				combo1 := respResult[0]
				So(combo1.Currency.Code, ShouldEqual, "VND")
				So(combo1.Image, ShouldEqual, "combo3.jpg")
				So(combo1.Cost, ShouldEqual, 300000)
				So(combo1.Price, ShouldEqual, 200000)
				So(combo1.StartDate, ShouldNotBeNil)
				So(combo1.EndDate, ShouldNotBeNil)
			})
		})
	})

	// Case sort multiple field: startDate: 1 && createdAt: -1
	t.Run("3.1", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo0.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"createdAt": now.AddDate(0, 0, -2),
				"startDate": now.AddDate(0, 0, -2),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": now.AddDate(0, 0, -2),
				"startDate": now.AddDate(0, 0, -3),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      300000,
				"price":     200000,
				"createdAt": now.AddDate(0, 0, -1),
				"startDate": now.AddDate(0, 0, -2),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": now.AddDate(0, 0, -1),
				"startDate": now.AddDate(0, 0, -3),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo4.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": now.AddDate(0, 0, -3),
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": now.AddDate(0, 0, -2),
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 6)
				// Expect sort result: sort list combovoucher by startDate ASC and createdAt DESC
				/*
					combo1
					combo2
					combo0
					combo3
					cmobo5
					combo4
				*/
				So(respResult[0].Image, ShouldEqual, "combo5.jpg")
				So(respResult[1].Image, ShouldEqual, "combo4.jpg")
				So(respResult[2].Image, ShouldEqual, "combo2.jpg")
				So(respResult[3].Image, ShouldEqual, "combo0.jpg")
				So(respResult[4].Image, ShouldEqual, "combo3.jpg")
				So(respResult[5].Image, ShouldEqual, "combo1.jpg")
			})
		})
	})

	// Case old user -> can get BOTH && CURRENT combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	// Include free voucher
	t.Run("3.2", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for _, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo5.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 0)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					}
				}
			})
		})
	})

	// Case old user -> can get BOTH && CURRENT combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	// Cannot see free voucher if got this before
	t.Run("3.3", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboVoucherIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})

		CreateVNComboVoucherTransaction([]map[string]interface{}{
			{
				"userId":         "0823456720",
				"comboVoucherId": comboVoucherIds[1],
				"amount":         0,
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 1)
				So(respResult[0].Currency.Code, ShouldEqual, "VND")
				So(respResult[0].Currency.Code, ShouldEqual, "VND")
				So(respResult[0].Image, ShouldEqual, "combo2.jpg")
				So(respResult[0].Cost, ShouldEqual, 200000)
				So(respResult[0].Price, ShouldEqual, 150000)
				So(respResult[0].StartDate, ShouldNotBeNil)
				So(respResult[0].EndDate, ShouldNotBeNil)
			})
		})
	})
	// Case test user in userIds, old user -> can get BOTH && CURRENT combo voucher, if user in userIds then can get voucher and vice versa
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	// Include free voucher
	t.Run("3.4", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"userIds":   []string{"0823456720"}, // user in userIds
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"userIds":   []string{"0823456721"}, // user not in userIds
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for _, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo5.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 0)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					}
				}
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Validate GetComboVoucherDetail")
		// CreateRefundRequest
		apiURL := "/api/v3/api-asker-vn/get-combo-voucher-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		// getComboVoucherDetail
		apiURL := "/api/v3/api-asker-vn/get-combo-voucher-detail"
		log.Println("==================================== getComboVoucherDetail")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":  "combo1.jpg",
				"status": "ACTIVE",
				"cost":   100000,
				"price":  80000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"content": map[string]interface{}{
							"vi": "content-voucher1",
							"en": "content-voucher1",
							"ko": "content-voucher1",
							"th": "content-voucher1",
							"id": "content-voucher1",
						},
						"note": map[string]interface{}{
							"vi": "note-voucher1",
							"en": "note-voucher1",
							"ko": "note-voucher1",
							"th": "note-voucher1",
							"id": "note-voucher1",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"vi": "bTaskee",
								"en": "bTaskee",
								"ko": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"image": "xxx",
						"title": map[string]interface{}{
							"vi": "title-voucher2",
							"en": "title-voucher2",
							"ko": "title-voucher2",
							"th": "title-voucher2",
							"id": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":  "MONEY",
							"value": 50000,
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"vi": "bTaskee",
								"en": "bTaskee",
								"ko": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{"HCM"},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    true,
							"isSharePublic": true,
						},
						"from":     "SYSTEM",
						"quantity": 4,
					},
				},
			},
		})
		body := map[string]interface{}{
			"comboVoucherId": comboIds[0],
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var combo1 *modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &combo1)
				So(resp.Code, ShouldEqual, 200)

				So(combo1.Currency.Code, ShouldEqual, "VND")
				So(combo1.Image, ShouldEqual, "combo1.jpg")
				So(combo1.Cost, ShouldEqual, 100000)
				So(combo1.Price, ShouldEqual, 80000)
				So(combo1.Title.En, ShouldEqual, "Title1")
				So(combo1.Content.En, ShouldEqual, "Tiết kiệm tới 20000 vnđ")
				So(combo1.TermsAndCondition.Th, ShouldEqual, "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.")
				So(combo1.StartDate, ShouldNotBeNil)
				So(combo1.EndDate, ShouldNotBeNil)

				var combo map[string][]map[string]interface{}
				json.Unmarshal(bytes, &combo)
				vouchers := combo["vouchers"]
				So(len(vouchers), ShouldEqual, 2)
				So(vouchers[0]["title"], ShouldNotBeNil)
				So(vouchers[0]["price"], ShouldEqual, 30000)
				So(vouchers[0]["quantity"], ShouldEqual, 2)
				So(vouchers[0]["image"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az")
				So(vouchers[0]["serviceIds"], ShouldBeEmpty)

				So(vouchers[1]["title"], ShouldNotBeNil)
				So(vouchers[1]["price"], ShouldEqual, 50000)
				So(vouchers[1]["quantity"], ShouldEqual, 4)
				So(vouchers[1]["image"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az")
				So(vouchers[1]["serviceIds"], ShouldResemble, []any{"pcZRQ6PqmjrAPe5gt"})
			})
		})
	})
	// Get combo isTesting = true
	t.Run("6", func(t *testing.T) {
		// getComboVoucherDetail
		apiURL := "/api/v3/api-asker-vn/get-combo-voucher-detail"
		log.Println("==================================== getComboVoucherDetail")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"image":     "combo1.jpg",
				"isTesting": true,
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
			},
		})
		body := map[string]interface{}{
			"comboVoucherId": comboIds[0],
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				So(resp.Code, ShouldEqual, 404)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_NOT_FOUND.Message)
			})
		})
	})
	// Get combo with user tester
	t.Run("7", func(t *testing.T) {
		// getComboVoucherDetail
		apiURL := "/api/v3/api-asker-vn/get-combo-voucher-detail"
		log.Println("==================================== getComboVoucherDetail")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"image":     "combo1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
			},
		})
		UpdateSettings(map[string]interface{}{
			"$set": bson.M{"tester": []string{"0823456720"}},
		})

		body := map[string]interface{}{
			"comboVoucherId": comboIds[0],
			"userId":         "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				UpdateSettings(map[string]interface{}{
					"$unset": bson.M{"tester": 1},
				})

				So(resp.Code, ShouldEqual, 404)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_NOT_FOUND.Message)
			})
		})
	})
	// =================== API GET FREE COMBO VOUCHER VN =====================
	// Validate
	t.Run("17", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-free-combo-voucher"
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"userId": 122,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})
			Convey("When service handle request if userId required", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if comboVoucherId required", func() {
				body := map[string]interface{}{
					"userId":         "xx",
					"comboVoucherId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	// User not found
	t.Run("18", func(t *testing.T) {
		// getComboVoucherDetail
		apiUrl := "/api/v3/api-asker-vn/get-free-combo-voucher"
		log.Println("==================================== User not found")
		ResetData()

		body := map[string]interface{}{
			"comboVoucherId": "xxx",
			"userId":         "xxx",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				result := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &result)

				So(resp.Code, ShouldEqual, 404)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})

	// Combo not found
	t.Run("19", func(t *testing.T) {
		// getComboVoucherDetail
		apiUrl := "/api/v3/api-asker-vn/get-free-combo-voucher"
		log.Println("==================================== Combo not found")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"comboVoucherId": "xxx",
			"userId":         "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				result := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &result)

				So(resp.Code, ShouldEqual, 404)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_NOT_FOUND.ErrorCode)
			})
		})
	})

	// Combo is not free
	t.Run("20", func(t *testing.T) {
		// getComboVoucherDetail
		apiUrl := "/api/v3/api-asker-vn/get-free-combo-voucher"
		log.Println("==================================== Combo is not free")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboVoucherIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image":    "combo1-1",
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"image": "xxx",
						"from":  "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_BOTH,
			},
		})

		body := map[string]interface{}{
			"comboVoucherId": comboVoucherIds[0],
			"userId":         "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				result := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &result)

				So(resp.Code, ShouldEqual, 500)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_NOT_FREE.ErrorCode)
			})
		})
	})

	// Free Combo can get once
	t.Run("21", func(t *testing.T) {
		// getComboVoucherDetail
		apiUrl := "/api/v3/api-asker-vn/get-free-combo-voucher"
		log.Println("==================================== Free Combo can get once")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboVoucherIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     0,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image":    "combo1-1",
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"image": "xxx",
						"from":  "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_BOTH,
			},
		})

		CreateVNComboVoucherTransaction([]map[string]interface{}{
			{
				"comboVoucherId": comboVoucherIds[0],
				"userId":         "0823456720",
			},
		})

		body := map[string]interface{}{
			"comboVoucherId": comboVoucherIds[0],
			"userId":         "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				result := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &result)

				So(resp.Code, ShouldEqual, 500)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_CAN_GET_ONCE.ErrorCode)
			})
		})
	})

	// Create combo success
	t.Run("22", func(t *testing.T) {
		// getComboVoucherDetail
		apiUrl := "/api/v3/api-asker-vn/get-free-combo-voucher"
		log.Println("==================================== Create combo success")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone":    "0823456720",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"language": "en",
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboVoucherIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     0,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"content": map[string]interface{}{
							"vi": "content-voucher1",
							"en": "content-voucher1",
							"ko": "content-voucher1",
							"th": "content-voucher1",
							"id": "content-voucher1",
						},
						"note": map[string]interface{}{
							"vi": "note-voucher1",
							"en": "note-voucher1",
							"ko": "note-voucher1",
							"th": "note-voucher1",
							"id": "note-voucher1",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"vi": "bTaskee",
								"en": "bTaskee",
								"ko": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
							"prefix":             "ABC123",
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"image": "xxx",
						"title": map[string]interface{}{
							"en": "title-voucher2",
						},
						"content": map[string]interface{}{
							"en": "content-voucher2",
						},
						"note": map[string]interface{}{
							"en": "note-voucher2",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"en": "bTaskee",
							},
							"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
						},
						"promotion": map[string]interface{}{
							"type":               "MONEY",
							"value":              30000,
							"numberOfDayDueDate": 30,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    false,
							"isSharePublic": false,
						},
						"from":     "SYSTEM",
						"quantity": 1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"comboVoucherId": comboVoucherIds[0],
			"userId":         "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				result := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &result)

				So(resp.Code, ShouldEqual, 200)
				So(result["detail"]["currency"], ShouldResemble, map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				})
				So(result["detail"]["title"], ShouldNotBeNil)
				So(result["detail"]["content"], ShouldNotBeNil)
				So(result["detail"]["price"], ShouldEqual, 0)
				So(result["detail"]["cost"], ShouldEqual, 100000)

				// Check promotion Code
				var promotionCodes []*promotioncode.PromotionCode
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{}, bson.M{}, &promotionCodes)
				So(len(promotionCodes), ShouldEqual, 3)
				numberVoucherType1 := 0
				numberVoucherType2 := 0
				codes := []string{}
				mapCodeByPromotionId := map[string]string{}
				for _, v := range promotionCodes {
					codes = append(codes, v.Code)
					mapCodeByPromotionId[v.Code] = v.XId
					if v.Description == "title-voucher1" {
						numberVoucherType1++
						So(v.Value, ShouldNotBeNil)
						So(v.Value.Type, ShouldEqual, "PERCENTAGE")
						So(v.Value.Value, ShouldEqual, 0.2)
						So(v.Value.MaxValue, ShouldEqual, 30000)
						So(v.UserIds, ShouldResemble, []string{"0823456720"})
						So(v.Limit, ShouldEqual, 1)
						So(v.Code, ShouldNotBeNil)
						So(len(v.Code), ShouldEqual, 12)
						So(v.Code[:6], ShouldEqual, "ABC123")
						So(v.IsoCode, ShouldEqual, local.ISO_CODE)
						So(v.Target, ShouldEqual, globalConstant.USER_TYPE_ASKER)
						So(v.Description, ShouldEqual, "title-voucher1")
						So(v.TypeOfPromotion, ShouldEqual, globalConstant.PROMOTION_APPLY_BOTH)
						startDate := globalLib.ParseDateFromTimeStamp(v.StartDate, local.TimeZone)
						So(startDate.String()[:13], ShouldEqual, currentTime.String()[:13])
						endDate := globalLib.ParseDateFromTimeStamp(v.EndDate, local.TimeZone)
						So(endDate.String()[:13], ShouldEqual, currentTime.AddDate(0, 0, 30).String()[:13])
						So(v.ServiceId, ShouldBeEmpty)
						So(v.TaskPlace, ShouldBeNil)
					} else {
						numberVoucherType2++
						So(v.Description, ShouldEqual, "title-voucher2")
						So(v.Value, ShouldNotBeNil)
						So(v.Value.Type, ShouldEqual, "MONEY")
						So(v.Value.Value, ShouldEqual, 30000)
						So(v.UserIds, ShouldResemble, []string{"0823456720"})
						So(v.Limit, ShouldEqual, 1)
						So(v.Code, ShouldNotBeNil)
						So(len(v.Code), ShouldEqual, 6)
						So(v.IsoCode, ShouldEqual, local.ISO_CODE)
						So(v.Target, ShouldEqual, globalConstant.USER_TYPE_ASKER)
						So(v.TypeOfPromotion, ShouldEqual, globalConstant.PROMOTION_APPLY_BOTH)
						startDate := globalLib.ParseDateFromTimeStamp(v.StartDate, local.TimeZone)
						So(startDate.String()[:13], ShouldEqual, currentTime.String()[:13])
						endDate := globalLib.ParseDateFromTimeStamp(v.EndDate, local.TimeZone)
						So(endDate.String()[:13], ShouldEqual, currentTime.AddDate(0, 0, 30).String()[:13])
						So(v.ServiceId, ShouldEqual, "pcZRQ6PqmjrAPe5gt")
						So(v.TaskPlace, ShouldBeNil)
					}
				}
				So(numberVoucherType1, ShouldEqual, 2)
				So(numberVoucherType2, ShouldEqual, 1)

				// Check gift
				var gifts []*gift.Gift
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{}, bson.M{}, &gifts)
				So(len(gifts), ShouldEqual, 3)
				giftIds := []string{}
				for _, v := range gifts {
					giftIds = append(giftIds, v.XId)
					So(v.PromotionCode, ShouldBeIn, codes)
					So(v.Source, ShouldEqual, "SYSTEM")
					So(v.IsoCode, ShouldEqual, local.ISO_CODE)
					So(v.UserId, ShouldEqual, "0823456720")
					So(v.CreatedAt, ShouldNotBeNil)
					So(v.PromotionId, ShouldEqual, mapCodeByPromotionId[v.PromotionCode])
					So(v.BrandInfo.Image, ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az")
					So(v.BrandInfo.Name, ShouldEqual, "bTaskee")
					So(v.BrandInfo.Text.En, ShouldEqual, "bTaskee")

					if v.Title.En == "title-voucher1" {
						So(v.Image, ShouldEqual, "combo1-1")
						So(v.Note.En, ShouldEqual, "note-voucher1")
						So(v.Content.En, ShouldEqual, "content-voucher1")
					} else {
						So(v.Image, ShouldEqual, "xxx")
						So(v.Note.En, ShouldEqual, "note-voucher2")
						So(v.Content.En, ShouldEqual, "content-voucher2")
					}
				}

				// Check notification
				var notifications []*notification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{}, bson.M{}, &notifications)
				So(len(notifications), ShouldEqual, 1)
				So(notifications[0].UserId, ShouldEqual, "0823456720")
				So(notifications[0].Type, ShouldEqual, 25)
				So(notifications[0].Description, ShouldEqual, "Your vouchers are here! Please open the app to check and enjoy your discount coupons.")
				So(notifications[0].IsForce, ShouldBeTrue)
				So(notifications[0].Title, ShouldEqual, "Message")
				So(notifications[0].NavigateTo, ShouldEqual, "MyRewards")

				// Check combo voucher transaction
				var cbTransaction []*comboVoucherTransaction.ComboVoucherTransaction
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{}, bson.M{}, &cbTransaction)
				So(len(cbTransaction), ShouldEqual, 1)
				So(cbTransaction[0].UserId, ShouldEqual, "0823456720")
				So(cbTransaction[0].PaymentMethod, ShouldEqual, globalConstant.PAYMENT_METHOD_CASH)
				So(cbTransaction[0].Status, ShouldEqual, "PAID")
				So(cbTransaction[0].ComboVoucherId, ShouldEqual, comboVoucherIds[0])
				So(cbTransaction[0].Amount, ShouldEqual, 0)
				So(len(cbTransaction[0].GiftIds), ShouldEqual, 3)
				for _, v := range cbTransaction[0].GiftIds {
					So(v, ShouldBeIn, giftIds)
				}
			})
		})
	})
	// ================= 23. Get list user combo voucher =================
	apiGetListUserComboVoucher := "/api/v3/api-asker-vn/get-list-user-combo-voucher"
	// validate
	t.Run("23.1", func(t *testing.T) {
		log.Println("==================================== validate")
		ResetData()

		Convey("When the request is handled by the with body missing userId", t, func() {
			body := map[string]interface{}{}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListUserComboVoucher, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
		Convey("When the request is handled by the with userId not exist", t, func() {
			body := map[string]interface{}{
				"userId": "xxx",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListUserComboVoucher, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	// get list user combo voucher
	t.Run("23.2", func(t *testing.T) {
		log.Println("==================================== get list user combo voucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image": "combo2.jpg",
				"title": map[string]interface{}{
					"vi": "Title2",
					"en": "Title2",
					"ko": "Title2",
					"th": "Title2",
					"id": "Title2",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 30000 vnđ",
					"en": "Tiết kiệm tới 30000 vnđ",
					"ko": "Tiết kiệm tới 30000 vnđ",
					"th": "Tiết kiệm tới 30000 vnđ",
					"id": "Tiết kiệm tới 30000 vnđ",
				},
				"status":    "ACTIVE",
				"cost":      300000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image": "combo3.jpg",
				"title": map[string]interface{}{
					"vi": "Title3",
					"en": "Title3",
					"ko": "Title3",
					"th": "Title3",
					"id": "Title3",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 30000 vnđ",
					"en": "Tiết kiệm tới 30000 vnđ",
					"ko": "Tiết kiệm tới 30000 vnđ",
					"th": "Tiết kiệm tới 30000 vnđ",
					"id": "Tiết kiệm tới 30000 vnđ",
				},
				"status":         "ACTIVE",
				"isTesting":      true,
				"cost":           300000,
				"price":          200000,
				"createdAt":      currentTime,
				"startDate":      currentTime.AddDate(0, 0, -1),
				"endDate":        currentTime.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image": "combo4.jpg",
				"title": map[string]interface{}{
					"vi": "Title4",
					"en": "Title4",
					"ko": "Title4",
					"th": "Title4",
					"id": "Title4",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 40000 vnđ",
					"en": "Tiết kiệm tới 40000 vnđ",
					"ko": "Tiết kiệm tới 40000 vnđ",
					"th": "Tiết kiệm tới 40000 vnđ",
					"id": "Tiết kiệm tới 40000 vnđ",
				},
				"status":         "ACTIVE",
				"isTesting":      true,
				"cost":           400000,
				"price":          200000,
				"createdAt":      currentTime,
				"startDate":      currentTime.AddDate(0, 0, -1),
				"endDate":        currentTime.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			},
		})

		CreateUserComboVoucher([]map[string]interface{}{
			{
				"userId":         "0823456720",
				"comboVoucherId": comboIds[0],
				"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE,
				"expiredDate":    currentTime.AddDate(0, 0, 31),
			}, {
				"userId":         "0823456720",
				"comboVoucherId": comboIds[1],
				"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_EXPIRED,
				"expiredDate":    currentTime.AddDate(0, 0, -1),
			}, {
				"userId":         "0823456720",
				"comboVoucherId": comboIds[2],
				"isSubscription": true,
				"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE,
				"expiredDate":    currentTime.AddDate(0, 0, 30),
			}, {
				"userId":         "0823456720",
				"comboVoucherId": comboIds[3],
				"isSubscription": true,
				"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_EXPIRED,
				"expiredDate":    currentTime.AddDate(0, 0, -1),
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetListUserComboVoucher, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult []*getListUserComboVoucher.UserComboVoucherDetail
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				tested := map[string]bool{}
				for _, v := range respResult {
					switch v.Title.Vi {
					case "Title1":
						So(v.Content.Vi, ShouldEqual, "Tiết kiệm tới 20000 vnđ")
						So(v.Status, ShouldEqual, globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE)
						So(v.Type, ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)
						So(v.Thumbnail, ShouldEqual, "thumbnail1.jpg")
						So(v.ExpiredDate, ShouldNotBeNil)
						tested[v.Title.Vi] = true
					case "Title3":
						So(v.Content.Vi, ShouldEqual, "Tiết kiệm tới 30000 vnđ")
						So(v.Status, ShouldEqual, globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE)
						So(v.Type, ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION)
						So(v.ExpiredDate, ShouldNotBeNil)
						tested[v.Title.Vi] = true
					}
				}
				So(len(tested), ShouldEqual, 2)
			})
		})
	})
	// ================= 24. Get list user combo voucher =================
	apiGetUserComboVoucherDetail := "/api/v3/api-asker-vn/get-user-combo-voucher-detail"
	// validate
	t.Run("24.1", func(t *testing.T) {
		log.Println("==================================== validate")
		ResetData()

		Convey("When the request is handled by the with body missing userComboVoucherId", t, func() {
			body := map[string]interface{}{}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserComboVoucherDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_COMBO_VOUCHER_ID_REQUIRED.ErrorCode)
			})
		})
		Convey("When the request is handled by the with body missing userId", t, func() {
			body := map[string]interface{}{
				"userComboVoucherId": "xxx",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserComboVoucherDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
		Convey("When the request is handled by the with userId not exist", t, func() {
			body := map[string]interface{}{
				"userComboVoucherId": "xxx",
				"userId":             "xxx",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserComboVoucherDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
		Convey("When the request is handled by the with userComboVoucherId not exist", t, func() {
			CreateUser([]map[string]interface{}{
				{
					"phone": "0823456720",
					"name":  "Asker 01",
					"type":  globalConstant.USER_TYPE_ASKER,
				},
			})
			body := map[string]interface{}{
				"userComboVoucherId": "xxx",
				"userId":             "0823456720",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserComboVoucherDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_COMBO_VOUCHER_NOT_FOUND.ErrorCode)
			})
		})
		Convey("When the request is handled by the with comboVoucher not exist", t, func() {
			ids := CreateUserComboVoucher([]map[string]interface{}{
				{
					"userId":         "0823456720",
					"comboVoucherId": "abc",
					"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE,
				},
			})

			body := map[string]interface{}{
				"userComboVoucherId": ids[0],
				"userId":             "0823456720",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUserComboVoucherDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_NOT_FOUND.ErrorCode)
			})
		})
	})
	// get user combo voucher detail
	t.Run("24.2", func(t *testing.T) {
		log.Println("==================================== get user combo voucher detail")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"usageInstruction": map[string]interface{}{
					"vi": "Hướng dẫn sử dụng",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"_id":   "voucher1",
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"content": map[string]interface{}{
							"vi": "content-voucher1",
							"en": "content-voucher1",
							"ko": "content-voucher1",
							"th": "content-voucher1",
							"id": "content-voucher1",
						},
						"note": map[string]interface{}{
							"vi": "note-voucher1",
							"en": "note-voucher1",
							"ko": "note-voucher1",
							"th": "note-voucher1",
							"id": "note-voucher1",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"vi": "bTaskee",
								"en": "bTaskee",
								"ko": "bTaskee",
								"th": "bTaskee",
							},
							"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"_id":   "voucher2",
						"image": "xxx",
						"title": map[string]interface{}{
							"vi": "title-voucher2",
							"en": "title-voucher2",
							"ko": "title-voucher2",
							"th": "title-voucher2",
							"id": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":  "MONEY",
							"value": 50000,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{"HCM"},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    true,
							"isSharePublic": true,
						},
						"from":     "SYSTEM",
						"quantity": 4,
					},
				},
				"isSubscription": true,
				"cancellationReasons": []map[string]interface{}{
					{
						"content": map[string]interface{}{
							"vi": "Khong mua nua",
							"en": "Khong mua nua",
						},
						"isRequireAdditionalReason": true,
					},
					{
						"content": map[string]interface{}{
							"vi": "Khong thich",
							"en": "Khong thich",
						},
						"isRequireAdditionalReason": false,
					},
				},
				"allowedPaymentMethods": []string{"MOMO", "CREDIT"},
			},
		})

		userComboIds := CreateUserComboVoucher([]map[string]interface{}{
			{
				"userId":         "0823456720",
				"comboVoucherId": comboIds[0],
				"isSubscription": true,
				"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE,
				"expiredDate":    currentTime.AddDate(0, 0, 31),
				"createdAt":      currentTime,
				"extendedHistories": []interface{}{
					currentTime,
				},
				"vouchers": []map[string]interface{}{
					{
						"_id":            "voucher1",
						"voucherId":      "voucher1",
						"quantity":       2,
						"promotionCodes": []string{"code1"},
						"image":          "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"serviceIds": []string{"pcZRQ6PqmjrAPe5gt"},
					}, {
						"_id":            "voucher2",
						"voucherId":      "voucher2",
						"quantity":       4,
						"promotionCodes": []string{"code5"},
						"image":          "xxx",
						"title": map[string]interface{}{
							"vi": "title-voucher2",
							"en": "title-voucher2",
							"ko": "title-voucher2",
							"th": "title-voucher2",
							"id": "title-voucher2",
						},
						"serviceIds": []string{"pcZRQ6PqmjrAPe5gt"},
					},
				},
				"isCancelled": true,
			},
		})

		CreatePromotionHistory([]map[string]interface{}{
			{
				"_id":           "ph1",
				"promotionCode": "code1",
				"userId":        "0823456720",
			}, {
				"_id":           "ph2",
				"promotionCode": "code5",
				"userId":        "0823456720",
			},
		})

		body := map[string]interface{}{
			"userId":             "0823456720",
			"userComboVoucherId": userComboIds[0],
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUserComboVoucherDetail, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var detail *getUserComboVoucherDetail.UserComboVoucherDetail
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &detail)
				So(resp.Code, ShouldEqual, 200)

				So(detail.XId, ShouldEqual, userComboIds[0])
				So(detail.ComboVoucherId, ShouldEqual, comboIds[0])
				So(detail.Status, ShouldEqual, globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE)
				So(detail.ExpiredDate, ShouldNotBeNil)
				So(detail.CreatedAt, ShouldNotBeNil)
				So(len(detail.ExtendedHistories), ShouldEqual, 1)
				So(detail.ExtendedHistories[0], ShouldNotBeNil)
				So(detail.Image, ShouldEqual, "combo1.jpg")
				So(detail.Cost, ShouldEqual, 100000)
				So(detail.Price, ShouldEqual, 80000)
				So(detail.Title.Vi, ShouldEqual, "Title1")
				So(detail.Content.Vi, ShouldEqual, "Tiết kiệm tới 20000 vnđ")
				So(detail.TermsAndCondition.Vi, ShouldEqual, "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.")
				So(detail.UsageInstruction.Vi, ShouldEqual, "Hướng dẫn sử dụng")
				So(detail.Type, ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION)
				So(detail.IsCancelled, ShouldEqual, true)
				So(len(detail.CancellationReasons), ShouldEqual, 2)
				So(detail.CancellationReasons[0].Content.Vi, ShouldEqual, "Khong mua nua")
				So(detail.CancellationReasons[0].IsRequireAdditionalReason, ShouldEqual, true)
				So(detail.CancellationReasons[1].Content.Vi, ShouldEqual, "Khong thich")
				So(detail.CancellationReasons[1].IsRequireAdditionalReason, ShouldEqual, false)
				So(len(detail.VoucherDetails), ShouldEqual, 2)
				tested := map[string]bool{}
				for _, voucherDetail := range detail.VoucherDetails {
					switch voucherDetail.VoucherId {
					case "voucher1":
						So(voucherDetail.Image, ShouldEqual, "combo1-1")
						So(voucherDetail.Title.Vi, ShouldEqual, "title-voucher1")
						So(voucherDetail.Quantity, ShouldEqual, 2)
						So(voucherDetail.RemainQuantity, ShouldEqual, 1)
						So(voucherDetail.ServiceIds, ShouldEqual, []string{"pcZRQ6PqmjrAPe5gt"})
						tested[voucherDetail.VoucherId] = true
					case "voucher2":
						So(voucherDetail.Image, ShouldEqual, "xxx")
						So(voucherDetail.Title.Vi, ShouldEqual, "title-voucher2")
						So(voucherDetail.Quantity, ShouldEqual, 4)
						So(voucherDetail.RemainQuantity, ShouldEqual, 3)
						So(voucherDetail.ServiceIds, ShouldEqual, []string{"pcZRQ6PqmjrAPe5gt"})
						tested[voucherDetail.VoucherId] = true
					}
				}
				So(len(tested), ShouldEqual, 2)
			})
		})
	})
	// ================= 25. Get list combo voucher V2=================
	// Case user not login
	// User can get only ACTIVE combo voucher (status == ACTIVE && isTesting != true && target == BOTH)
	t.Run("25.1", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"thumbnail": "thumbnail1.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
					"ko": "Tiết kiệm tới 20000 vnđ",
					"th": "Tiết kiệm tới 20000 vnđ",
					"id": "Tiết kiệm tới 20000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
					"ko": "- 프로모션 코드는 가사도우미 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다.\n- 프로모션 코드는 현금으로 교환할 수 없습니다.\n- 프로그램은 타 프로모션과 중복 사용이 불가합니다.\n- 프로모션 코드는 포인트를 교환한 날로부터 30일 동안만 유효합니다.",
					"th": "- ส่วนลดสามารถใช้ได้เฉพาะบริการจองทำความสะอาดทั่วไปเท่านั้น\n- โค้ดโปรโมชันนี้สามารถใช้ได้เพียง 1 ครั้งเท่านั้น.\n- โปรโมชันนี้ไม่สามารถใช้ร่วมกับโปรโมชันอื่นๆ ได้.\n- ไม่สามารถแลกเปลี่ยนหรือขอคืนเงินได้.\n- ส่วนลดมีอายุการใช้งาน 30 วันหลังจากกดแลกคะแนน.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image":    "combo1-1",
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"image": "xxx",
						"from":  "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":  "combo2.jpg",
				"status": "ACTIVE",
				"cost":   200000,
				"price":  150000,
				"title": map[string]interface{}{
					"en": "Title2",
				},
				"content": map[string]interface{}{
					"en": "Tiết kiệm tới 50000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image": "xxx",
						"promotion": map[string]interface{}{
							"type":  "MONEY",
							"value": 50000,
						},
						"from": "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":  "combo4.jpg",
				"status": "ACTIVE",
				"cost":   200000,
				"price":  150000,
				"title": map[string]interface{}{
					"en": "Title4",
				},
				"content": map[string]interface{}{
					"en": "Tiết kiệm tới 50000 vnđ",
				},
				"termsAndCondition": map[string]interface{}{
					"en": "- Only applied to booking Cleaning service.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions.\n- Not refundable and exchangeable.\n- Promotion code only has an expiry date of 30 days since redemption.",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"image": "xxx",
						"promotion": map[string]interface{}{
							"type":  "MONEY",
							"value": 50000,
						},
						"from": "SYSTEM",
					},
				},
				"target": globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})
		body := map[string]interface{}{}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				combo1 := respResult[0]

				So(combo1.Currency.Code, ShouldEqual, "VND")
				So(combo1.Thumbnail, ShouldEqual, "thumbnail1.jpg")
				So(combo1.Image, ShouldEqual, "combo1.jpg")
				So(combo1.Cost, ShouldEqual, 100000)
				So(combo1.Price, ShouldEqual, 80000)
				So(combo1.Title.En, ShouldEqual, "Title1")
				So(combo1.Content.En, ShouldEqual, "Tiết kiệm tới 20000 vnđ")
				So(combo1.StartDate, ShouldNotBeNil)
				So(combo1.EndDate, ShouldNotBeNil)

				combo2 := respResult[1]
				So(combo2.Currency.Code, ShouldEqual, "VND")
				So(combo2.Image, ShouldEqual, "combo2.jpg")
				So(combo2.Cost, ShouldEqual, 200000)
				So(combo2.Price, ShouldEqual, 150000)
				So(combo2.Title.En, ShouldEqual, "Title2")
				So(combo2.Content.En, ShouldEqual, "Tiết kiệm tới 50000 vnđ")
				So(combo2.StartDate, ShouldNotBeNil)
				So(combo2.EndDate, ShouldNotBeNil)
			})
		})
	})

	// Case new user -> can get BOTH && NEW combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	t.Run("25.2", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":    "0823456720",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"taskDone": 0,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"isTesting": true,
				"cost":      300000,
				"price":     200000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo4.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_NEW,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for _, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo4.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					}
				}
			})
		})
	})

	// Case old user -> can get BOTH && CURRENT combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	t.Run("25.3", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"isTesting": true,
				"cost":      300000,
				"price":     200000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo4.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_NEW,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for _, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo5.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
					}
				}
			})
		})
	})

	// Case user testing -> can get all ACTIVE combo voucher
	// Check isUserTester
	t.Run("25.4", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "INACTIVE",
				"isTesting": true,
				"cost":      100000,
				"price":     80000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"isTesting": true,
				"cost":      300000,
				"price":     200000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":          "combo4.jpg",
				"status":         "ACTIVE",
				"isTesting":      true,
				"cost":           300000,
				"price":          200000,
				"createdAt":      currentTime,
				"startDate":      currentTime.AddDate(0, 0, -1),
				"endDate":        currentTime.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			},
		})
		UpdateSettings(map[string]interface{}{
			"$set": bson.M{"tester": []string{"0823456720"}},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				UpdateSettings(map[string]interface{}{"$unset": bson.M{"tester": 1}})
				var respResult []*modelComboVoucher.ComboVoucher
				var respResultM []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				combo1 := respResult[0]
				combo1M := respResultM[0]
				So(combo1.Currency.Code, ShouldEqual, "VND")
				So(combo1.Image, ShouldEqual, "combo4.jpg")
				So(combo1.Cost, ShouldEqual, 300000)
				So(combo1.Price, ShouldEqual, 200000)
				So(combo1.StartDate, ShouldNotBeNil)
				So(combo1.EndDate, ShouldNotBeNil)
				So(combo1M["type"], ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION)
				combo2 := respResult[1]
				combo2M := respResultM[1]
				So(combo2.Currency.Code, ShouldEqual, "VND")
				So(combo2.Image, ShouldEqual, "combo3.jpg")
				So(combo2.Cost, ShouldEqual, 300000)
				So(combo2.Price, ShouldEqual, 200000)
				So(combo2.StartDate, ShouldNotBeNil)
				So(combo2.EndDate, ShouldNotBeNil)
				So(combo2M["type"], ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)
			})
		})
	})

	// Case sort multiple field: startDate: 1 && createdAt: -1
	t.Run("25.5", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo0.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     80000,
				"createdAt": now.AddDate(0, 0, -1),
				"startDate": now.AddDate(0, 0, -2),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo1.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": now.AddDate(0, 0, -3),
				"startDate": now.AddDate(0, 0, -3),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":          "combo2.jpg",
				"status":         "ACTIVE",
				"cost":           300000,
				"price":          200000,
				"createdAt":      now.AddDate(0, 0, -5),
				"startDate":      now.AddDate(0, 0, -2),
				"endDate":        now.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": now.AddDate(0, 0, -2),
				"startDate": now.AddDate(0, 0, -3),
				"endDate":   now.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":          "combo4.jpg",
				"status":         "ACTIVE",
				"cost":           200000,
				"price":          150000,
				"createdAt":      now.AddDate(0, 0, -3),
				"startDate":      now.AddDate(0, 0, -1),
				"endDate":        now.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":          "combo5.jpg",
				"status":         "ACTIVE",
				"cost":           200000,
				"price":          150000,
				"createdAt":      now.AddDate(0, 0, -2),
				"startDate":      now.AddDate(0, 0, -1),
				"endDate":        now.AddDate(0, 0, 31),
				"target":         globalConstant.PROMOTION_APPLY_BOTH,
				"isSubscription": true,
			},
		})
		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 6)
				// Expect sort result: sort list combovoucher by type SUBSCRIPTION > COMBO_VOUCHER and createdAt DESC
				/*
					combo1
					combo2
					combo0
					combo3
					cmobo5
					combo4
				*/
				So(respResult[0].Image, ShouldEqual, "combo5.jpg")
				So(respResult[1].Image, ShouldEqual, "combo4.jpg")
				So(respResult[2].Image, ShouldEqual, "combo2.jpg")
				So(respResult[3].Image, ShouldEqual, "combo0.jpg")
				So(respResult[4].Image, ShouldEqual, "combo3.jpg")
				So(respResult[5].Image, ShouldEqual, "combo1.jpg")
			})
		})
	})

	// Case old user -> can get BOTH && CURRENT combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	// Include free voucher
	t.Run("25.6", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				var respResultM []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for i, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
						So(respResultM[i]["type"], ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo5.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 0)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
						So(respResultM[i]["type"], ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)
					}
				}
			})
		})
	})

	// Case old user -> can get BOTH && CURRENT combo voucher
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	// Cannot see free voucher if got this before
	t.Run("25.7", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboVoucherIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo5.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_CURRENT,
			},
		})

		CreateVNComboVoucherTransaction([]map[string]interface{}{
			{
				"userId":         "0823456720",
				"comboVoucherId": comboVoucherIds[1],
				"amount":         0,
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 1)
				So(respResult[0].Currency.Code, ShouldEqual, "VND")
				So(respResult[0].Currency.Code, ShouldEqual, "VND")
				So(respResult[0].Image, ShouldEqual, "combo2.jpg")
				So(respResult[0].Cost, ShouldEqual, 200000)
				So(respResult[0].Price, ShouldEqual, 150000)
				So(respResult[0].StartDate, ShouldNotBeNil)
				So(respResult[0].EndDate, ShouldNotBeNil)
			})
		})
	})
	// Case test user in userIds, old user -> can get BOTH && CURRENT combo voucher, if user in userIds then can get voucher and vice versa
	// User can get only ACTIVE combo voucher (status == ACTIVE && not testing)
	// Include free voucher
	t.Run("25.8", func(t *testing.T) {
		// getListComboVoucher
		apiURL := "/api/v3/api-asker-vn/get-list-combo-voucher-v2"
		log.Println("==================================== getListComboVoucher")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNComboVoucher([]map[string]interface{}{
			{
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo2.jpg",
				"status":    "ACTIVE",
				"cost":      200000,
				"price":     150000,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"target":    globalConstant.PROMOTION_APPLY_BOTH,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":          "combo5.jpg",
				"status":         "ACTIVE",
				"cost":           200000,
				"price":          0,
				"createdAt":      currentTime,
				"startDate":      currentTime.AddDate(0, 0, -1),
				"endDate":        currentTime.AddDate(0, 0, 31),
				"userIds":        []string{"0823456720"}, // user in userIds
				"isSubscription": true,
			}, {
				"currency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"image":     "combo3.jpg",
				"status":    "ACTIVE",
				"cost":      100000,
				"price":     0,
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"userIds":   []string{"0823456721"}, // user not in userIds
			},
		})

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []*modelComboVoucher.ComboVoucher
				var respResultM []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				for i, v := range respResult {
					if v.Image == "combo2.jpg" {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo2.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 150000)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
						So(respResultM[i]["type"], ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)
					} else {
						So(v.Currency.Code, ShouldEqual, "VND")
						So(v.Image, ShouldEqual, "combo5.jpg")
						So(v.Cost, ShouldEqual, 200000)
						So(v.Price, ShouldEqual, 0)
						So(v.StartDate, ShouldNotBeNil)
						So(v.EndDate, ShouldNotBeNil)
						So(respResultM[i]["type"], ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION)
					}
				}
			})
		})
	})
	// ================= 26. Get list combo voucher transaction histories =================
	apiGetListComboVoucherTransactionHistories := "/api/v3/api-asker-vn/get-list-combo-voucher-transaction-histories"
	// validate
	t.Run("26.1", func(t *testing.T) {
		log.Println("==================================== validate")
		ResetData()

		Convey("When the request is handled by the with body missing userId", t, func() {
			body := map[string]interface{}{}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListComboVoucherTransactionHistories, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	// list combo voucher transaction histories
	t.Run("26.2", func(t *testing.T) {
		log.Println("==================================== list combo voucher transaction histories")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		comboIds := CreateVNComboVoucher([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
					"ko": "Title1",
					"th": "Title1",
					"id": "Title1",
				},
			}, {
				"title": map[string]interface{}{
					"vi": "Title2",
					"en": "Title2",
					"ko": "Title2",
					"th": "Title2",
					"id": "Title2",
				},
				"isSubscription": true,
			},
		})

		CreateVNComboVoucherTransaction([]map[string]interface{}{
			{
				"userId":         "0823456720",
				"paymentMethod":  globalConstant.PAYMENT_METHOD_MOMO,
				"transactionId":  "1234",
				"status":         globalConstant.TRANSACTION_STATUS_PAID,
				"comboVoucherId": comboIds[0],
				"amount":         100000,
				"createdAt":      currentTime,
			}, {
				"userId":         "0823456720",
				"paymentMethod":  globalConstant.PAYMENT_METHOD_CREDIT,
				"transactionId":  "2345",
				"status":         globalConstant.TRANSACTION_STATUS_WAITING,
				"comboVoucherId": comboIds[0],
				"amount":         200000,
				"createdAt":      currentTime.AddDate(0, 0, -1),
			}, {
				"userId":         "0823456720",
				"paymentMethod":  globalConstant.PAYMENT_METHOD_SHOPEE_PAY,
				"transactionId":  "098999",
				"status":         globalConstant.TRANSACTION_STATUS_WAITING,
				"comboVoucherId": comboIds[1],
				"amount":         300000,
				"createdAt":      currentTime.AddDate(0, 0, 1),
			}, {
				"userId":         "ccc",
				"paymentMethod":  globalConstant.PAYMENT_METHOD_CREDIT,
				"transactionId":  "2345",
				"status":         globalConstant.TRANSACTION_STATUS_WAITING,
				"comboVoucherId": comboIds[0],
				"amount":         200000,
				"createdAt":      currentTime.AddDate(0, 0, -1),
			},
		})

		body := map[string]interface{}{
			"userId": "0823456720",
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetListComboVoucherTransactionHistories, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var result []*getListComboVoucherTransactionHistories.TransactionHistory
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &result)
				So(resp.Code, ShouldEqual, 200)

				So(len(result), ShouldEqual, 3)

				So(result[0].TransactionId, ShouldEqual, "098999")
				So(result[0].PaymentMethod, ShouldEqual, globalConstant.PAYMENT_METHOD_SHOPEE_PAY)
				So(result[0].Status, ShouldEqual, globalConstant.TRANSACTION_STATUS_WAITING)
				So(result[0].Amount, ShouldEqual, 300000)
				So(result[0].CreatedAt, ShouldNotBeNil)
				So(result[0].Title.En, ShouldEqual, "Title2")
				So(result[0].Type, ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION)

				So(result[1].TransactionId, ShouldEqual, "1234")
				So(result[1].PaymentMethod, ShouldEqual, globalConstant.PAYMENT_METHOD_MOMO)
				So(result[1].Status, ShouldEqual, globalConstant.TRANSACTION_STATUS_PAID)
				So(result[1].Amount, ShouldEqual, 100000)
				So(result[1].CreatedAt, ShouldNotBeNil)
				So(result[1].Title.En, ShouldEqual, "Title1")
				So(result[1].Type, ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)

				So(result[2].TransactionId, ShouldEqual, "2345")
				So(result[2].PaymentMethod, ShouldEqual, globalConstant.PAYMENT_METHOD_CREDIT)
				So(result[2].Status, ShouldEqual, globalConstant.TRANSACTION_STATUS_WAITING)
				So(result[2].Amount, ShouldEqual, 200000)
				So(result[2].CreatedAt, ShouldNotBeNil)
				So(result[2].Title.En, ShouldEqual, "Title1")
				So(result[2].Type, ShouldEqual, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER)
			})
		})
	})
	// ================= 27. cancel subscription =================
	apiCancelSubscription := "/api/v3/api-asker-vn/cancel-combo-voucher-subscription"
	// validate
	t.Run("27.1", func(t *testing.T) {
		log.Println("==================================== validate")
		ResetData()

		Convey("When the request is handled by the with body missing userId", t, func() {
			body := map[string]interface{}{}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
		Convey("When the request is handled by the with body missing userComboVoucherId", t, func() {
			body := map[string]interface{}{
				"userId": "xxx",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_COMBO_VOUCHER_ID_REQUIRED.ErrorCode)
			})
		})
		Convey("When the request is handled by the with body missing reason", t, func() {
			body := map[string]interface{}{
				"userId":             "xxx",
				"userComboVoucherId": "xxx",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_REASON_REQUIRED.ErrorCode)
			})
		})
	})
	// user combo voucher invalid
	t.Run("27.2", func(t *testing.T) {
		log.Println("==================================== user combo voucher invalid")
		ResetData()

		Convey("When the request is handled", t, func() {
			body := map[string]interface{}{
				"userId":             "123456",
				"userComboVoucherId": "xxx",
				"reason": map[string]interface{}{
					"vi": "Khong thich dang ky nua",
					"en": "I don't like to register anymore",
				},
				"additionalReason": "Het tien",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_COMBO_VOUCHER_NOT_FOUND.ErrorCode)
			})
		})
		Convey("When the request is handled", t, func() {
			ResetData()
			ids := CreateUserComboVoucher([]map[string]interface{}{
				{
					"userId":         "123456",
					"isSubscription": true,
				},
			})
			body := map[string]interface{}{
				"userId":             "1234567",
				"userComboVoucherId": ids[0],
				"reason": map[string]interface{}{
					"vi": "Khong thich dang ky nua",
					"en": "I don't like to register anymore",
				},
				"additionalReason": "Het tien",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION.ErrorCode)
			})
		})
		Convey("When the request is handled", t, func() {
			ResetData()
			ids := CreateUserComboVoucher([]map[string]interface{}{
				{
					"userId": "123456",
				},
			})
			body := map[string]interface{}{
				"userId":             "123456",
				"userComboVoucherId": ids[0],
				"reason": map[string]interface{}{
					"vi": "Khong thich dang ky nua",
					"en": "I don't like to register anymore",
				},
				"additionalReason": "Het tien",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COMBO_VOUCHER_IS_NOT_SUBSCRIPTION.ErrorCode)
			})
		})
		Convey("When the request is handled", t, func() {
			ResetData()
			ids := CreateUserComboVoucher([]map[string]interface{}{
				{
					"userId":         "123456",
					"isSubscription": true,
					"isCancelled":    true,
				},
			})
			body := map[string]interface{}{
				"userId":             "123456",
				"userComboVoucherId": ids[0],
				"reason": map[string]interface{}{
					"vi": "Khong thich dang ky nua",
					"en": "I don't like to register anymore",
				},
				"additionalReason": "Het tien",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_IS_CANCELLED.ErrorCode)
			})
		})
	})
	// cancel success
	t.Run("27.3", func(t *testing.T) {
		log.Println("==================================== cancel success")
		ResetData()

		Convey("When the request is handled", t, func() {
			ids := CreateUserComboVoucher([]map[string]interface{}{
				{
					"userId":         "123456",
					"isSubscription": true,
				},
			})
			body := map[string]interface{}{
				"userId":             "123456",
				"userComboVoucherId": ids[0],
				"reason": map[string]interface{}{
					"vi": "Khong thich dang ky nua",
					"en": "I don't like to register anymore",
				},
				"additionalReason": "Het tien",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response", func() {
				So(resp.Code, ShouldEqual, 200)

				var userComboVoucher *modelUserComboVoucher.UserComboVoucher
				globalDataAccess.GetOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], ids[0], bson.M{}, &userComboVoucher)
				So(userComboVoucher.IsCancelled, ShouldEqual, true)
				So(userComboVoucher.CancelledAt, ShouldNotBeNil)
				So(userComboVoucher.CancellationReason.Vi, ShouldEqual, "Khong thich dang ky nua")
				So(userComboVoucher.AdditionalCancellationReason, ShouldEqual, "Het tien")
			})
		})
	})
}
