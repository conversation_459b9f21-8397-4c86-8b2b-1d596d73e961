package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountVN"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func TestLixiTasker(t *testing.T) {
	apiURL := "/api/v3/api-asker-vn/lixi-tasker"

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})
			Convey("When service handle request if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if giftId is empty", func() {
				body := map[string]interface{}{
					"userId": "xxxx",
					"giftId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_GIFT_ID_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if taskId && taskerId are both empty", func() {
				body := map[string]interface{}{
					"userId":   "xxxx",
					"giftId":   "xxx",
					"taskId":   "",
					"taskerId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	// GIFT NOT FOUND
	t.Run("2", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"userId":   "**********",
			"giftId":   "xxx",
			"taskerId": "xxx",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 404)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_GIFT_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	// ERROR_GIFT_IS_USED
	t.Run("3", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId":  "**********",
				"image":   "test1",
				"expired": now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"used":          true,
			},
		})

		body := map[string]interface{}{
			"userId":   "**********",
			"giftId":   giftIds[0],
			"taskerId": "xxx",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_GIFT_IS_USED.ErrorCode)
				})
			})
		})
	})

	// ERROR_GIFT_INVALID
	t.Run("4", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, -2).Format("2006,1,2,15,4"),
			},
		})

		body := map[string]interface{}{
			"userId":   "**********",
			"giftId":   giftIds[0],
			"taskerId": "xxx",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_GIFT_IS_EXPIRED.ErrorCode)
				})
			})
		})
	})

	// ERROR_GIFT_IS_EXPIRED
	t.Run("5", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				// "shareGiftInfo": map[string]interface{}{
				// 	"type":  "MONEY",
				// 	"value": 50000.0,
				// },
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
			},
		})

		body := map[string]interface{}{
			"userId":   "**********",
			"giftId":   giftIds[0],
			"taskerId": "xxx",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_GIFT_INVALID.ErrorCode)
				})
			})
		})
	})

	// ERROR_TASK_NOT_FOUND
	t.Run("6", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
			},
		})

		body := map[string]interface{}{
			"userId": "**********",
			"giftId": giftIds[0],
			"taskId": "xxx",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 404)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	// ERROR_CAN_NOT_LIXI_TO_COMPANY
	t.Run("7", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        date.Format("2006,1,2,15,4"),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "**********",
						"companyId": "xxx",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"userId": "**********",
			"giftId": giftIds[0],
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_LIXI_TO_COMPANY.ErrorCode)
				})
			})
		})
	})

	// ERROR_TASKER_NOT_FOUND
	t.Run("8", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			// {
			// 	"phone": "**********",
			// 	"name":  "Tasker 01",
			// 	"type":  globalConstant.USER_TYPE_TASKER,
			// },
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        date.Format("2006,1,2,15,4"),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"userId": "**********",
			"giftId": giftIds[0],
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 404)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASKER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	// SUCCESS for 1 favourite taskers
	t.Run("9", func(t *testing.T) {
		ResetData()

		fAccountIds := CreateFAccount([]map[string]interface{}{
			{
				"FMainAccount": 0,
				"Promotion":    0,
			},
		})

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone":      "**********",
				"name":       "Tasker 01",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": fAccountIds[0],
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        date.Format("2006,1,2,15,4"),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		conversationIds := CreateChatConversation(local.ISO_CODE, []map[string]interface{}{
			{
				"askerPhone":  "**********",
				"taskerPhone": "**********",
				"members": []map[string]interface{}{
					{
						"_id": "**********",
					},
					{
						"_id": "**********",
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId": "**********",
			"giftId": giftIds[0],
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check result
					So(result["promotionAmount"], ShouldEqual, 50000.0)
					So(result["taskers"], ShouldResemble, []interface{}{
						map[string]interface{}{
							"_id":  "**********",
							"name": "Tasker 01",
						},
					})

					// Check gift is used
					var gift *modelGift.Gift
					globalDataAccess.GetOneById(globalCollection.COLLECTION_GIFT[local.ISO_CODE],
						giftIds[0],
						bson.M{"used": 1, "usedAt": 1, "shareGiftInfo.receivers": 1},
						&gift,
					)
					So(gift.Used, ShouldBeTrue)
					So(gift.UsedAt, ShouldNotBeNil)
					So(gift.ShareGiftInfo.Receivers[0].UserId, ShouldEqual, "**********")
					So(gift.ShareGiftInfo.Receivers[0].Name, ShouldEqual, "Tasker 01")

					// Check tasker account
					var taskerFAccount *financialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE],
						fAccountIds[0],
						bson.M{"FMainAccount": 1, "Promotion": 1},
						&taskerFAccount,
					)
					So(taskerFAccount.FMainAccount, ShouldEqual, 0)
					So(taskerFAccount.Promotion, ShouldEqual, 50000)

					// Check tasker transaction
					var taskerTransactions []*fatransaction.FinancialAccountTransaction
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&taskerTransactions,
					)
					So(len(taskerTransactions), ShouldEqual, 1)
					So(taskerTransactions[0].Amount, ShouldEqual, 50000)
					So(taskerTransactions[0].Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER)
					So(taskerTransactions[0].Source.Value, ShouldEqual, "**********")
					So(taskerTransactions[0].Source.GiftId, ShouldEqual, giftIds[0])
					So(taskerTransactions[0].Type, ShouldEqual, "D")
					So(taskerTransactions[0].AccountType, ShouldEqual, "P")
					So(taskerTransactions[0].Date, ShouldNotBeNil)
					So(taskerTransactions[0].CreatedAt, ShouldNotBeNil)

					// Check tasker notification
					var taskerNotifications []*notification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&taskerNotifications,
					)
					So(len(taskerNotifications), ShouldEqual, 1)
					So(taskerNotifications[0].Description, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_NOTIFICATION_TITLE", "Asker 01").Vi)

					// Check tasker chat conversation
					var chatMessages []*chatMessage.ChatMessageMessages
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE],
						bson.M{"chatId": conversationIds[0]},
						bson.M{},
						&chatMessages,
					)
					So(len(chatMessages), ShouldEqual, 2)

					for _, v := range chatMessages {
						So(v.XId, ShouldNotBeNil)
						So(v.CreatedAt, ShouldNotBeNil)
						So(v.From, ShouldEqual, globalConstant.CHAT_MESSAGE_FROM_SYSTEM)
						So(v.MessageBySystem, ShouldNotBeNil)
						So(v.MessageBySystem.Image, ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/dJpdupQCq7cc2Mqi6")
						if v.MessageBySystem.SendTo == globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER {
							So(v.MessageBySystem.Title.Vi, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_TITLE").Vi)
							So(v.MessageBySystem.Text.Vi, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_BODY", "50,000", globalConstant.CURRENCY_SIGN_VN.Code).Vi)
						} else {
							So(v.MessageBySystem.SendTo, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER)
							So(v.MessageBySystem.Title.Vi, ShouldEqual, localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_TITLE").Vi)
							So(v.MessageBySystem.Text.Vi, ShouldEqual, localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_BODY", "50,000", globalConstant.CURRENCY_SIGN_VN.Code).Vi)
						}
					}
				})
			})
		})
	})

	// SUCCESS for 3 taskers in task but only 2 taskers has chat with asker
	t.Run("10", func(t *testing.T) {
		ResetData()

		fAccountIds := CreateFAccount([]map[string]interface{}{
			{
				"FMainAccount": 0,
				"Promotion":    0,
			},
			{
				"FMainAccount": 0,
				"Promotion":    10000,
			},
			{
				"FMainAccount": 0,
				"Promotion":    30000,
			},
		})

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone":      "**********",
				"name":       "Tasker 01",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": fAccountIds[0],
			},
			{
				"phone":      "**********",
				"name":       "Tasker 02",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": fAccountIds[1],
			},
			{
				"phone":      "**********",
				"name":       "Tasker 03",
				"type":       globalConstant.USER_TYPE_TASKER,
				"fAccountId": fAccountIds[2],
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		giftIds := CreateGift([]map[string]interface{}{
			{
				"userId": "**********",
				"image":  "test1",
				"title": map[string]interface{}{
					"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
					"en": "eGift Cash - Kichi Kichi - 100.000VND",
					"ko": "현금 eGift - Kichi Kichi - 100.000VND",
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
					"isSharePublic": true,
				},
				"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
				"promotionCode": "oMcom5M",
				"shareGiftInfo": map[string]interface{}{
					"type":  "MONEY",
					"value": 50000.0,
				},
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
				"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				"expired":       now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        date.Format("2006,1,2,15,4"),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"isLeader": true,
					}, {
						"taskerId": "**********",
					}, {
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		conversationIds := CreateChatConversation(local.ISO_CODE, []map[string]interface{}{
			{
				"askerPhone":  "**********",
				"taskerPhone": "**********",
				"members": []map[string]interface{}{
					{
						"_id": "**********",
					},
					{
						"_id": "**********",
					},
				},
			},
			{
				"askerPhone":  "**********",
				"taskerPhone": "**********",
				"members": []map[string]interface{}{
					{
						"_id": "**********",
					},
					{
						"_id": "**********",
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId": "**********",
			"giftId": giftIds[0],
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check result
					So(result["promotionAmount"], ShouldEqual, 50000.0)
					So(result["taskers"], ShouldResemble, []interface{}{
						map[string]interface{}{
							"_id":  "**********",
							"name": "Tasker 01",
						},
						map[string]interface{}{
							"_id":  "**********",
							"name": "Tasker 02",
						},
						map[string]interface{}{
							"_id":  "**********",
							"name": "Tasker 03",
						},
					})

					// Check gift is used
					var gift *modelGift.Gift
					globalDataAccess.GetOneById(globalCollection.COLLECTION_GIFT[local.ISO_CODE],
						giftIds[0],
						bson.M{"used": 1, "usedAt": 1, "shareGiftInfo.receivers": 1},
						&gift,
					)
					So(gift.Used, ShouldBeTrue)
					So(gift.UsedAt, ShouldNotBeNil)
					So(len(gift.ShareGiftInfo.Receivers), ShouldEqual, 3)
					So(gift.ShareGiftInfo.Receivers[0].UserId, ShouldEqual, "**********")
					So(gift.ShareGiftInfo.Receivers[0].Name, ShouldEqual, "Tasker 01")
					So(gift.ShareGiftInfo.Receivers[1].UserId, ShouldEqual, "**********")
					So(gift.ShareGiftInfo.Receivers[1].Name, ShouldEqual, "Tasker 02")
					So(gift.ShareGiftInfo.Receivers[2].UserId, ShouldEqual, "**********")
					So(gift.ShareGiftInfo.Receivers[2].Name, ShouldEqual, "Tasker 03")

					// Check tasker account
					var tasker0FAccount *financialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE],
						fAccountIds[0],
						bson.M{"FMainAccount": 1, "Promotion": 1},
						&tasker0FAccount,
					)
					So(tasker0FAccount.FMainAccount, ShouldEqual, 0)
					So(tasker0FAccount.Promotion, ShouldEqual, 18000)

					// Check tasker account
					var tasker1FAccount *financialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE],
						fAccountIds[1],
						bson.M{"FMainAccount": 1, "Promotion": 1},
						&tasker1FAccount,
					)
					So(tasker1FAccount.FMainAccount, ShouldEqual, 0)
					So(tasker1FAccount.Promotion, ShouldEqual, 10000+16000)

					// Check tasker account
					var tasker2FAccount *financialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE],
						fAccountIds[2],
						bson.M{"FMainAccount": 1, "Promotion": 1},
						&tasker2FAccount,
					)
					So(tasker2FAccount.FMainAccount, ShouldEqual, 0)
					So(tasker2FAccount.Promotion, ShouldEqual, 30000+16000)

					// Check tasker transaction
					var tasker0Transactions []*fatransaction.FinancialAccountTransaction
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&tasker0Transactions,
					)
					So(len(tasker0Transactions), ShouldEqual, 1)
					So(tasker0Transactions[0].Amount, ShouldEqual, 18000)
					So(tasker0Transactions[0].Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER)
					So(tasker0Transactions[0].Source.Value, ShouldEqual, "**********")
					So(tasker0Transactions[0].Source.GiftId, ShouldEqual, giftIds[0])
					So(tasker0Transactions[0].Type, ShouldEqual, "D")
					So(tasker0Transactions[0].AccountType, ShouldEqual, "P")
					So(tasker0Transactions[0].Date, ShouldNotBeNil)
					So(tasker0Transactions[0].CreatedAt, ShouldNotBeNil)

					// Check tasker transaction
					var tasker1Transactions []*fatransaction.FinancialAccountTransaction
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&tasker1Transactions,
					)
					So(len(tasker1Transactions), ShouldEqual, 1)
					So(tasker1Transactions[0].Amount, ShouldEqual, 16000)
					So(tasker1Transactions[0].Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER)
					So(tasker1Transactions[0].Source.Value, ShouldEqual, "**********")
					So(tasker1Transactions[0].Source.GiftId, ShouldEqual, giftIds[0])
					So(tasker1Transactions[0].Type, ShouldEqual, "D")
					So(tasker1Transactions[0].AccountType, ShouldEqual, "P")
					So(tasker1Transactions[0].Date, ShouldNotBeNil)
					So(tasker1Transactions[0].CreatedAt, ShouldNotBeNil)

					// Check tasker transaction
					var tasker2Transactions []*fatransaction.FinancialAccountTransaction
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&tasker2Transactions,
					)
					So(len(tasker2Transactions), ShouldEqual, 1)
					So(tasker2Transactions[0].Amount, ShouldEqual, 16000)
					So(tasker2Transactions[0].Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER)
					So(tasker2Transactions[0].Source.Value, ShouldEqual, "**********")
					So(tasker2Transactions[0].Source.GiftId, ShouldEqual, giftIds[0])
					So(tasker2Transactions[0].Type, ShouldEqual, "D")
					So(tasker2Transactions[0].AccountType, ShouldEqual, "P")
					So(tasker2Transactions[0].Date, ShouldNotBeNil)
					So(tasker2Transactions[0].CreatedAt, ShouldNotBeNil)

					// Check tasker notification
					var taskerNotifications []*notification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&taskerNotifications,
					)
					So(len(taskerNotifications), ShouldEqual, 1)
					So(taskerNotifications[0].Description, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_NOTIFICATION_TITLE", "Asker 01").Vi)

					// Check tasker notification
					var tasker1Notifications []*notification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&tasker1Notifications,
					)
					So(len(tasker1Notifications), ShouldEqual, 1)
					So(tasker1Notifications[0].Description, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_NOTIFICATION_TITLE", "Asker 01").Vi)

					// Check tasker notification
					var tasker2Notifications []*notification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
						bson.M{"userId": "**********"},
						bson.M{},
						&tasker2Notifications,
					)
					So(len(tasker2Notifications), ShouldEqual, 1)
					So(tasker2Notifications[0].Description, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_NOTIFICATION_TITLE", "Asker 01").Vi)

					// Check tasker chat conversation
					var chatMessages0 []*chatMessage.ChatMessageMessages
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE],
						bson.M{"chatId": conversationIds[0]},
						bson.M{},
						&chatMessages0,
					)
					So(len(chatMessages0), ShouldEqual, 2)

					for _, v := range chatMessages0 {
						So(v.XId, ShouldNotBeNil)
						So(v.CreatedAt, ShouldNotBeNil)
						So(v.From, ShouldEqual, globalConstant.CHAT_MESSAGE_FROM_SYSTEM)
						So(v.MessageBySystem, ShouldNotBeNil)
						So(v.MessageBySystem.Image, ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/dJpdupQCq7cc2Mqi6")
						if v.MessageBySystem.SendTo == globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER {
							So(v.MessageBySystem.Title.Vi, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_TITLE").Vi)
							So(v.MessageBySystem.Text.Vi, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_BODY", "18,000", globalConstant.CURRENCY_SIGN_VN.Code).Vi)
						} else {
							So(v.MessageBySystem.SendTo, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER)
							So(v.MessageBySystem.Title.Vi, ShouldEqual, localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_TITLE").Vi)
							So(v.MessageBySystem.Text.Vi, ShouldEqual, localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_BODY", "18,000", globalConstant.CURRENCY_SIGN_VN.Code).Vi)
						}
					}

					// Check tasker chat conversation
					var chatMessages1 []*chatMessage.ChatMessageMessages
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE],
						bson.M{"chatId": conversationIds[1]},
						bson.M{},
						&chatMessages1,
					)
					So(len(chatMessages1), ShouldEqual, 2)

					for _, v := range chatMessages1 {
						So(v.XId, ShouldNotBeNil)
						So(v.CreatedAt, ShouldNotBeNil)
						So(v.From, ShouldEqual, globalConstant.CHAT_MESSAGE_FROM_SYSTEM)
						So(v.MessageBySystem, ShouldNotBeNil)
						So(v.MessageBySystem.Image, ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/dJpdupQCq7cc2Mqi6")
						if v.MessageBySystem.SendTo == globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER {
							So(v.MessageBySystem.Title.Vi, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_TITLE").Vi)
							So(v.MessageBySystem.Text.Vi, ShouldEqual, localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_BODY", "16,000", globalConstant.CURRENCY_SIGN_VN.Code).Vi)
						} else {
							So(v.MessageBySystem.SendTo, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER)
							So(v.MessageBySystem.Title.Vi, ShouldEqual, localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_TITLE").Vi)
							So(v.MessageBySystem.Text.Vi, ShouldEqual, localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_BODY", "16,000", globalConstant.CURRENCY_SIGN_VN.Code).Vi)
						}
					}
				})
			})
		})
	})

}
