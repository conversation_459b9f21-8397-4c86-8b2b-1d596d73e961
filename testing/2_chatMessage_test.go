/*
 * @File: 2_chatMessage_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 20/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 23/03/2021
 * @UpdatedBy: ngoctb3
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func TestChatMessage(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== Validate InitConversation")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123123,
					"taskId":  taskIds[0],
					"message": 123132,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"taskId":  taskIds[0],
					"message": "test chat",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"taskId":  "",
					"message": "test chat",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
			})

			Convey("Check request when message blank", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"taskId":  taskIds[0],
					"message": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_MESSAGE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_MESSAGE_REQUIRED.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})
		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567810",
				"taskId":  taskIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[0]}, bson.M{"askerId": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0834567890")
				So(result.TaskerId, ShouldEqual, "0834567810")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0834567810")
				So(result.Messages[0].Message, ShouldEqual, "Test chat")
				So(result.Messages[0].UserName, ShouldEqual, "Tasker 01")
			})
		})
	})
	t.Run("2.1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})
		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567810",
				"taskId":  taskIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[0]}, bson.M{"askerId": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0834567890")
				So(result.TaskerId, ShouldEqual, "0834567810")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0834567810")
				So(result.Messages[0].Message, ShouldEqual, "Test chat")
				So(result.Messages[0].UserName, ShouldEqual, "Tasker 01")
			})
		})
	})
	t.Run("2.2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s task not found", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567810",
				"taskId":  "xxxxxx",
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				m := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &m)

				So(resp.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("2.3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s task_not_comfirmed", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567810",
				"taskId":  taskIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				m := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &m)

				So(resp.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_CONFIRMED.ErrorCode)
			})
		})
	})
	t.Run("2.4", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567820",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
						"companyId": "123",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0*********",
				"taskId":  taskIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"taskId": taskIds[0]}, bson.M{"askerId": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(result, ShouldBeNil)
			})
		})
	})

	t.Run("2.5", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567820",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"askerId":     "0834567100",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0*********",
				"taskId":  taskIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"taskId": taskIds[0]}, bson.M{"askerId": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(result, ShouldBeNil)
			})
		})
	})

	t.Run("2.6", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567820",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
				"contactName": "contactName 01",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567810",
				"taskId":   taskIds[0],
				"message":  "Test chat",
				"sendFrom": globalConstant.USER_TYPE_ASKER,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"askerId": "0*********"}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0*********")
				So(result.TaskerId, ShouldEqual, "0834567820")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0*********")
				So(result.Messages[0].Message, ShouldEqual, "Test chat")
				So(result.Messages[0].UserName, ShouldEqual, "Asker 01")
				So(result.AskerName, ShouldEqual, "contactName 01")
			})
		})
	})

	t.Run("2.7", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567820",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567810",
				"taskId":   taskIds[0],
				"message":  "Test chat",
				"sendFrom": globalConstant.USER_TYPE_ASKER,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"askerId": "0*********"}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0*********")
				So(result.TaskerId, ShouldEqual, "0834567820")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0*********")
				So(result.Messages[0].Message, ShouldEqual, "Test chat")
				So(result.Messages[0].UserName, ShouldEqual, "Asker 01")
			})
		})
	})

	t.Run("2.8", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})
		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "123",
				"taskId":  taskIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[0]}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0834567890")
			})
		})
	})

	t.Run("2.10", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		log.Println("==================================== InitConversation")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})
		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case existsChat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567890",
				"taskId":   taskIds[0],
				"message":  "Test chat",
				"sendFrom": globalConstant.USER_TYPE_ASKER,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[0]}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0834567890")
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case New chat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567820",
				"taskId":  taskIds[1],
				"message": "Test chat 02",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"taskId": taskIds[1]}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[1])
				So(result.AskerId, ShouldEqual, "0*********")
				So(result.TaskerId, ShouldEqual, "0834567820")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0834567820")
				So(result.Messages[0].Message, ShouldEqual, "Test chat 02")
				So(result.Messages[0].UserName, ShouldEqual, "Tasker 02")
			})
		})
	})
	t.Run("3.1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/init-conversation"

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567820",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
						"isLeader":  true,
					},
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case New chat", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567820",
				"taskId":  taskIds[1],
				"message": "Test chat 02",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"taskId": taskIds[1]}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1})
				So(result, ShouldNotBeNil)
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[1])
				So(result.AskerId, ShouldEqual, "0*********")
				So(result.TaskerId, ShouldEqual, "0834567820")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0834567820")
				So(result.Messages[0].Message, ShouldEqual, "Test chat 02")
				So(result.Messages[0].UserName, ShouldEqual, "Tasker 02")
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Validate MessageNewTasker")
		// MessageNewTasker
		apiUrl := "/api/v3/api-asker-vn/message-new-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567830",
						"name":      "Tasker 03",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 01",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567820",
					"taskId":      taskIds[1],
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"taskId": taskIds[0],
					"chatId": messIds[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"taskId": "",
					"chatId": messIds[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
			})

			Convey("Check request when message blank", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"taskId": taskIds[0],
					"chatId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.Message)
			})

			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123123,
					"taskId": taskIds[0],
					"chatId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== MessageNewTasker")
		// MessageNewTasker
		apiUrl := "/api/v3/api-asker-vn/message-new-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567830",
						"name":      "Tasker 03",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 01",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567820",
					"taskId":      taskIds[1],
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567830",
				"taskId":  taskIds[0],
				"chatId":  messIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Message New Tasker", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Message New Tasker", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[0]}, bson.M{})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0834567890")
				So(result.TaskerId, ShouldEqual, "0834567830")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0834567830")
				So(result.Messages[0].UserName, ShouldEqual, "Tasker 03")
				So(result.Messages[0].Message, ShouldEqual, "Test chat")
				So(result.ChangedHistory[0].OldTaskerId, ShouldEqual, "0834567810")
				So(result.ChangedHistory[0].NewTaskerId, ShouldEqual, "0834567830")
			})
		})
	})
	t.Run("5.1", func(t *testing.T) {
		log.Println("==================================== MessageNewTasker")
		// MessageNewTasker
		apiUrl := "/api/v3/api-asker-vn/message-new-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567830",
						"name":      "Tasker 03",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 01",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567820",
					"taskId":      taskIds[1],
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567830",
				"taskId":  taskIds[0],
				"chatId":  messIds[0],
				"message": "Test chat",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Message New Tasker", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Message New Tasker", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[0]}, bson.M{"askerId": 1, "askerName": 1, "taskerId": 1, "taskId": 1, "messages": 1, "changedHistory": 1})
				So(len(result.Messages), ShouldBeGreaterThan, 0)
				So(result.TaskId, ShouldEqual, taskIds[0])
				So(result.AskerId, ShouldEqual, "0834567890")
				So(result.TaskerId, ShouldEqual, "0834567830")
				So(result.Messages[0].From, ShouldEqual, globalConstant.USER_TYPE_TASKER)
				So(result.Messages[0].UserId, ShouldEqual, "0834567830")
				So(result.Messages[0].UserName, ShouldEqual, "Tasker 03")
				So(result.Messages[0].Message, ShouldEqual, "Test chat")
				So(result.ChangedHistory[0].OldTaskerId, ShouldEqual, "0834567810")
				So(result.ChangedHistory[0].NewTaskerId, ShouldEqual, "0834567830")
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== MessageNewTasker")
		// MessageNewTasker
		apiUrl := "/api/v3/api-asker-vn/message-new-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567830",
						"name":      "Tasker 03",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 01",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})

		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567820",
					"taskId":      taskIds[1],
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s Case TaskId != UserId", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567830",
				"taskId": taskIds[1],
				"chatId": messIds[1],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[1]}, bson.M{})
				So(result, ShouldNotBeNil)
				So(len(result.ChangedHistory), ShouldEqual, 0)
				So(result.TaskId, ShouldEqual, taskIds[1])
				So(result.AskerId, ShouldEqual, "0*********")
				So(result.TaskerId, ShouldEqual, "0834567820")
			})
		})
	})

	t.Run("6.1", func(t *testing.T) {
		log.Println("==================================== MessageNewTasker")
		// MessageNewTasker
		apiUrl := "/api/v3/api-asker-vn/message-new-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567830",
						"name":      "Tasker 03",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 01",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
						"companyId": "0834567810",
					},
				},
			},
		})

		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567820",
					"taskId":      taskIds[1],
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s Case TaskId != UserId", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567830",
				"taskId": taskIds[1],
				"chatId": messIds[1],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": messIds[1]}, bson.M{})
				So(result, ShouldNotBeNil)
				So(len(result.ChangedHistory), ShouldEqual, 0)
				So(result.TaskId, ShouldEqual, taskIds[1])
				So(result.AskerId, ShouldEqual, "0*********")
				So(result.TaskerId, ShouldEqual, "0834567820")
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		apiURL := subPath + "/translate-message"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if chatId is empty", func() {
				body := map[string]interface{}{
					"chatId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if text is empty", func() {
				body := map[string]interface{}{
					"chatId": "123",
					"text":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TEXT_REQUIRED.ErrorCode)
			})
			Convey("Check the response if createdAt is nil", func() {
				body := map[string]interface{}{
					"chatId":    "123123",
					"text":      "123",
					"messageId": nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_MESSAGE_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if param is invalid", func() {
				body := map[string]interface{}{
					"chatId":    123123123,
					"text":      123123123,
					"messageId": nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		apiURL := subPath + "/translate-message"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567830",
						"name":      "Tasker 03",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567810",
						"name":      "Tasker 01",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		//Created ChatMessage
		messIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567810",
					"taskId":      taskIds[0],
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567820",
					"taskId":      taskIds[1],
					"messages": []map[string]interface{}{
						{
							"_id":       "123",
							"from":      "TASKER",
							"message":   "Hello, goodmorning",
							"userId":    "RWpQhDTjLThjNnaPe",
							"isRead":    false,
							"createdAt": now,
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if param is invalid", func() {
				body := map[string]interface{}{
					"chatId":    messIds[1],
					"text":      "Hello, goodmorning",
					"messageId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m["messageId"], ShouldEqual, "123")
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("==================================== Validate GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
					"taskId": "234",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"taskId": "234",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "123",
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("11", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567720",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567111",
						"name":      "Võ Thị Hồng Hà",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
						"isLeader":  true,
					},
					{
						"taskerId":  "0834567120",
						"name":      "Trần Thị Thanh Hồng",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/WpH39swRihgN9gDkj",
						"avgRating": 4.96000003814697,
						"taskDone":  396,
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			},
		})

		//CreateChatMessage
		CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567710",
					"taskId":      ids[0],
					"messages": []map[string]interface{}{
						{
							"from":           "TASKER",
							"message":        "Test ABC",
							"userId":         "0834567710",
							"isRead":         true,
							"translatedText": "kiểm tra 123",
						},
					},
				}, {
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567720",
					"taskId":      ids[1],
					"messages": []map[string]interface{}{
						{
							"from":    "ASKER",
							"message": "Test 123",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567730",
					"taskId":      ids[2],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0*********",
							"isRead":  true,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResultR := make(map[string]interface{})
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultR)
					So(resp.Code, ShouldEqual, 200)
					So(respResultR["_id"], ShouldNotBeBlank)
					So(respResultM["messages"][0]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][0]["message"], ShouldEqual, "Test ABC")
					So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][0]["userId"], ShouldEqual, "0834567710")
					So(respResultM["messages"][0]["translatedText"], ShouldEqual, "kiểm tra 123")
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["task"]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Vệ sinh máy lạnh")
					So(respResult["task"]["duration"], ShouldEqual, 2)
					So(respResult["task"]["status"], ShouldEqual, "CONFIRMED")
					So(respResult["task"]["date"], ShouldNotBeNil)
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567710")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Tasker 01")
					So(respResult["taskerInfo"]["language"], ShouldEqual, globalConstant.LANG_VI)
				})
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567720",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567111",
						"name":      "Võ Thị Hồng Hà",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
						"isLeader":  true,
					},
					{
						"taskerId":  "0834567120",
						"name":      "Trần Thị Thanh Hồng",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/WpH39swRihgN9gDkj",
						"avgRating": 4.96000003814697,
						"taskDone":  396,
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			},
		})

		//CreateChatMessage
		CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567710",
					"taskId":      ids[0],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test ABC",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567720",
					"taskId":      ids[1],
					"messages": []map[string]interface{}{
						{
							"from":    "ASKER",
							"message": "Test 123",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567730",
					"taskId":      ids[2],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0*********",
							"isRead":  true,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": ids[1],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					// So(respResultM["messages"][0]["from"], ShouldEqual, "ASKER")
					// So(respResultM["messages"][0]["message"], ShouldEqual, "Test 123")
					// So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					// So(respResultM["messages"][0]["userId"], ShouldEqual, "0834567710")
					// So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					// So(respResult["taskerInfo"]["name"], ShouldEqual, "Tasker 02")
				})
			})
		})
	})
	t.Run("13", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567720",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567111",
						"name":      "Võ Thị Hồng Hà",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
						"isLeader":  true,
					},
					{
						"taskerId":  "0834567120",
						"name":      "Trần Thị Thanh Hồng",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/WpH39swRihgN9gDkj",
						"avgRating": 4.96000003814697,
						"taskDone":  396,
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			},
		})

		//CreateChatMessage
		CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567710",
					"taskId":      ids[0],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test ABC",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567720",
					"taskId":      ids[1],
					"messages": []map[string]interface{}{
						{
							"from":    "ASKER",
							"message": "Test 123",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567730",
					"taskId":      ids[2],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0*********",
							"isRead":  true,
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[2],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["messages"][0]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][0]["message"], ShouldEqual, "Test BCD")
					So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][0]["userId"], ShouldEqual, "0*********")
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[2])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")
					So(respResult["taskerInfo"]["language"], ShouldEqual, globalConstant.LANG_VI)
				})
			})
		})
	})

	t.Run("14", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
				"contactName": "contactName 01",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")

					respResultMessage := map[string][]map[string]interface{}{}
					json.Unmarshal(bytes, &respResultMessage)
					So(len(respResultMessage["messages"]), ShouldEqual, 1)

					// Check the database
					chatMessage, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{}, bson.M{})
					So(chatMessage, ShouldNotBeNil)
					So(chatMessage.TaskerName, ShouldEqual, "Company 01")
					So(chatMessage.TaskerId, ShouldEqual, "0834567740")
					So(chatMessage.TaskId, ShouldEqual, ids[0])
					So(chatMessage.AskerId, ShouldEqual, "0*********")
					So(chatMessage.AskerName, ShouldEqual, "contactName 01")
					So(len(chatMessage.Messages), ShouldEqual, 1)
					So(chatMessage.Messages[0].From, ShouldEqual, globalConstant.CHAT_MESSAGE_FROM_SYSTEM)
					So(chatMessage.Messages[0].MessageBySystem.Title.Vi, ShouldEqual, "Xin chào contactName 01,")
					So(chatMessage.Messages[0].MessageBySystem.Text.Vi, ShouldEqual, "Công việc của bạn đã có người nhận việc. Tasker sẽ có mặt đúng giờ.")
				})
			})
		})
	})

	t.Run("15", func(t *testing.T) {
		log.Println("==================================== GetChatList Task done < +30p")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
				})
			})
		})
	})

	t.Run("16", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": "123",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 500)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
				})
			})
		})
	})

	t.Run("17", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 404)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COMPANY_NOT_FOUND.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_COMPANY_NOT_FOUND.Message)
				})
			})
		})
	})

	t.Run("18", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone":  "0834567740",
				"name":   "Company 01",
				"type":   globalConstant.USER_TYPE_TASKER,
				"avatar": "http://avatar",
			},
		})

		//Create Task
		taskDate := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult["messages"]), ShouldEqual, 0)
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")
					So(respResult["taskerInfo"]["avatar"], ShouldEqual, "http://avatar")

				})
			})
		})
	})

	t.Run("19", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-7 * 24 * time.Hour).Add(-3 * time.Hour)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"duration":    2,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 500)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
				})
			})
		})
	})

	// Asker old version can only get chat if task not DONE
	t.Run("19.1", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-6 * 24 * time.Hour)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"duration":    2,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 500)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
				})
			})
		})
	})

	t.Run("20", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567720",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567111",
						"name":      "Võ Thị Hồng Hà",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
						"isLeader":  true,
					},
					{
						"taskerId":  "0834567120",
						"name":      "Trần Thị Thanh Hồng",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/WpH39swRihgN9gDkj",
						"avgRating": 4.96000003814697,
						"taskDone":  396,
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567710",
					"taskId":      ids[0],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test ABC",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567720",
					"taskId":      ids[1],
					"messages": []map[string]interface{}{
						{
							"from":    "ASKER",
							"message": "Test 123",
							"userId":  "0834567710",
							"isRead":  true,
						},
					},
				}, {
					"askerPhone":  "0*********",
					"taskerPhone": "0834567730",
					"taskId":      ids[2],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0*********",
							"isRead":  true,
						}, {
							"from":   "TASKER",
							"userId": "0*********",
							"isRead": true,
							"video":  "videotest.com",
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"chatId": chatIds[2],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["messages"][0]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][0]["message"], ShouldEqual, "Test BCD")
					So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][0]["userId"], ShouldEqual, "0*********")
					So(respResultM["messages"][1]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][1]["userId"], ShouldEqual, "0*********")
					So(respResultM["messages"][1]["video"], ShouldEqual, "videotest.com")
					So(respResultM["messages"][1]["from"], ShouldEqual, "TASKER")

					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[2])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")
				})
			})
		})
	})

	// Asker new version can get chat within 7 days after task done
	t.Run("20.1", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567720",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -6)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0*********",
					"taskerPhone": "0834567730",
					"taskId":      ids[0],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0*********",
							"isRead":  true,
						}, {
							"from":   "TASKER",
							"userId": "0*********",
							"isRead": true,
							"video":  "videotest.com",
						}, {
							"from": "SYSTEM",
							"messageBySystem": map[string]interface{}{
								"key":    "ALERT_TASK_START",
								"sendTo": "BOTH",
								"title": map[string]interface{}{
									"vi": "Công việc sẽ bắt đầu sau 30 phút",
								},
								"text": map[string]interface{}{
									"vi": "Công việc của bạn sẽ được bắt đầu trong vòng 30 phút nữa. Vui lòng giữ liên lạc với các CTV hoặc Đối tác để được phục vụ tốt nhất.",
								},
								"actions": []map[string]interface{}{
									{
										"type": "PRIMARY",
										"title": map[string]interface{}{
											"vi": "Tổng đài",
											"en": "Hotline",
										},
										"link": "tel:${0834567890}",
									}, {
										"type": "SECONDARY",
										"title": map[string]interface{}{
											"vi": "Zalo",
											"en": "Zalo",
										},
										"link": "https://zalo.me/0834567890",
									},
								},
							},
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"chatId":  chatIds[0],
				"version": "V2",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["messages"][0]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][0]["message"], ShouldEqual, "Test BCD")
					So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][0]["userId"], ShouldEqual, "0*********")
					So(respResultM["messages"][1]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][1]["userId"], ShouldEqual, "0*********")
					So(respResultM["messages"][1]["video"], ShouldEqual, "videotest.com")
					So(respResultM["messages"][1]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][2]["isRead"], ShouldBeNil)
					So(respResultM["messages"][2]["from"], ShouldEqual, "SYSTEM")
					messageBySystem := respResultM["messages"][2]["messageBySystem"].(map[string]interface{})
					So(messageBySystem["key"], ShouldEqual, "ALERT_TASK_START")
					So(messageBySystem["sendTo"], ShouldEqual, "BOTH")
					So(messageBySystem["title"].(map[string]interface{})["vi"], ShouldEqual, "Công việc sẽ bắt đầu sau 30 phút")
					So(messageBySystem["text"].(map[string]interface{})["vi"], ShouldEqual, "Công việc của bạn sẽ được bắt đầu trong vòng 30 phút nữa. Vui lòng giữ liên lạc với các CTV hoặc Đối tác để được phục vụ tốt nhất.")
					actions := messageBySystem["actions"].([]interface{})
					So(actions[0].(map[string]interface{})["type"], ShouldEqual, "PRIMARY")
					So(actions[0].(map[string]interface{})["link"], ShouldEqual, "tel:${0834567890}")
					So(actions[0].(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldEqual, "Tổng đài")
					So(actions[1].(map[string]interface{})["type"], ShouldEqual, "SECONDARY")
					So(actions[1].(map[string]interface{})["link"], ShouldEqual, "https://zalo.me/0834567890")
					So(actions[1].(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldEqual, "Zalo")

					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")
				})
			})
		})
	})

	t.Run("21", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567720",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567740",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"chatId": "123123",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 404)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CHAT_MESSAGE_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("22", func(t *testing.T) {
		log.Println("==================================== Validate GetChatHistory")
		apiUrl := "/api/v3/api-asker-vn/get-chat-history"
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s taskId blank", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId":  "",
				"isoCode": "VN",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 400)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				})
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s chat not found", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId":  "123",
				"isoCode": "VN",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})

	t.Run("23", func(t *testing.T) {
		log.Println("==================================== GetChatHistory")
		apiUrl := "/api/v3/api-asker-vn/get-chat-history"
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//CreateChatMessage
		CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      "x617a670140771ec0d4482730",
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
						{
							"isRead":    false,
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265235",
							"from":      "TASKER",
							"message":   "[System message] Our Taskers are required to get at least one dose of COVID-19 vaccine to accept tasks and to follow the 5K procedure when working.",
							"userId":    "2JCJp3E6CyQMHr3k8",
						},
						{
							"isRead":    false,
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265236",
							"from":      "TASKER",
							"message":   "[System message] In case you might require a Tasker who gets fully vaccinated or tests negative for COVID-19 please do note your requirement before posting your task or contact us.",
							"userId":    "2JCJp3E6CyQMHr3k8",
						},
						{
							"isRead":    false,
							"from":      "TASKER",
							"message":   "xin chào",
							"createdAt": currentTime,
							"_id":       "x617a6c1921ae1872e408110e",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"userName":  "Bùi Mộng Tuyền",
						},
					},
					"updatedAt": currentTime,
					"changedHistory": []map[string]interface{}{
						{
							"oldTaskerId": "dm8AonwZigKCBjhxy",
							"newTaskerId": "2JCJp3E6CyQMHr3k8",
							"messages": []map[string]interface{}{
								{
									"from":      "TASKER",
									"message":   "Test",
									"userId":    "dm8AonwZigKCBjhxy",
									"createdAt": currentTime.AddDate(0, 0, -2),
									"_id":       "x617a6a2624bdb0810e265206",
								},
								{
									"from":      "TASKER",
									"userId":    "dm8AonwZigKCBjhxy",
									"message":   "[System message] Our Taskers are required to get at least one dose of COVID-19 vaccine to accept tasks and to follow the 5K procedure when working.",
									"createdAt": currentTime.AddDate(0, 0, -2),
									"_id":       "x617a6a2624bdb0810e265207",
								},
								{
									"from":      "TASKER",
									"userId":    "dm8AonwZigKCBjhxy",
									"message":   "[System message] In case you might require a Tasker who gets fully vaccinated or tests negative for COVID-19 please do note your requirement before posting your task or contact us.",
									"createdAt": currentTime.AddDate(0, 0, -2),
									"_id":       "x617a6a2624bdb0810e265208",
								},
							},
							"changedAt":     currentTime.AddDate(0, 0, -2),
							"oldTaskerName": "Trần Thị Quốc Lệ",
						}, {
							"oldTaskerId": "dm8AonwZigKCBjhxy",
							"newTaskerId": "2JCJp3E6CyQMHr3k8",
							"messages": []map[string]interface{}{
								{
									"from":      "TASKER",
									"message":   "Test",
									"userId":    "dm8AonwZigKCBjhxy",
									"createdAt": currentTime.AddDate(0, 0, -1),
									"_id":       "x617a6a2624bdb0810e265206",
								},
								{
									"from":      "TASKER",
									"userId":    "dm8AonwZigKCBjhxy",
									"message":   "[System message] Our Taskers are required to get at least one dose of COVID-19 vaccine to accept tasks and to follow the 5K procedure when working.",
									"createdAt": currentTime.AddDate(0, 0, -1),
									"_id":       "x617a6a2624bdb0810e265207",
								},
								{
									"from":      "TASKER",
									"userId":    "dm8AonwZigKCBjhxy",
									"message":   "[System message] In case you might require a Tasker who gets fully vaccinated or tests negative for COVID-19 please do note your requirement before posting your task or contact us.",
									"createdAt": currentTime.AddDate(0, 0, -1),
									"_id":       "x617a6a2624bdb0810e265208",
								},
							},
							"changedAt":     currentTime.AddDate(0, 0, -1),
							"oldTaskerName": "Trần Thị Quốc Lệ A",
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId":  "x617a670140771ec0d4482730",
				"isoCode": "VN",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 3)
					So(respResult[0]["askerName"], ShouldEqual, "Ánh")
					So(respResult[0]["taskerName"], ShouldEqual, "Bùi Mộng Tuyền")
					So(len(respResult[0]["messages"].([]interface{})), ShouldEqual, 4)

					So(respResult[1]["askerName"], ShouldEqual, "Ánh")
					So(respResult[1]["taskerName"], ShouldEqual, "Trần Thị Quốc Lệ A")
					So(len(respResult[1]["messages"].([]interface{})), ShouldEqual, 3)

					So(respResult[2]["askerName"], ShouldEqual, "Ánh")
					So(respResult[2]["taskerName"], ShouldEqual, "Trần Thị Quốc Lệ")
					So(len(respResult[2]["messages"].([]interface{})), ShouldEqual, 3)
				})
			})
		})
	})

	t.Run("23.1", func(t *testing.T) {
		log.Println("==================================== GetChatHistory from history collection")
		apiUrl := "/api/v3/api-asker-vn/get-chat-history"
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//CreateChatMessage
		CreateHistoryChatMessage([]map[string]interface{}{
			{
				"taskId":      "x617a670140771ec0d4482730",
				"askerPhone":  "0834567890",
				"askerName":   "Ánh",
				"taskerPhone": "0834567740",
				"taskerName":  "Bùi Mộng Tuyền",
				"messages": []map[string]interface{}{
					{
						"createdAt": currentTime,
						"_id":       "x617a6c0724bdb0810e265234",
						"from":      "TASKER",
						"message":   "Test",
						"userId":    "2JCJp3E6CyQMHr3k8",
						"isRead":    false,
					},
					{
						"isRead":    false,
						"createdAt": currentTime,
						"_id":       "x617a6c0724bdb0810e265235",
						"from":      "TASKER",
						"message":   "[System message] Our Taskers are required to get at least one dose of COVID-19 vaccine to accept tasks and to follow the 5K procedure when working.",
						"userId":    "2JCJp3E6CyQMHr3k8",
					},
					{
						"isRead":    false,
						"createdAt": currentTime,
						"_id":       "x617a6c0724bdb0810e265236",
						"from":      "TASKER",
						"message":   "[System message] In case you might require a Tasker who gets fully vaccinated or tests negative for COVID-19 please do note your requirement before posting your task or contact us.",
						"userId":    "2JCJp3E6CyQMHr3k8",
					},
					{
						"isRead":    false,
						"from":      "TASKER",
						"message":   "xin chào",
						"createdAt": currentTime,
						"_id":       "x617a6c1921ae1872e408110e",
						"userId":    "2JCJp3E6CyQMHr3k8",
						"userName":  "Bùi Mộng Tuyền",
					},
				},
				"updatedAt": currentTime,
				"changedHistory": []map[string]interface{}{
					{
						"oldTaskerId": "dm8AonwZigKCBjhxy",
						"newTaskerId": "2JCJp3E6CyQMHr3k8",
						"messages": []map[string]interface{}{
							{
								"from":      "TASKER",
								"message":   "Test",
								"userId":    "dm8AonwZigKCBjhxy",
								"createdAt": currentTime.AddDate(0, 0, -2),
								"_id":       "x617a6a2624bdb0810e265206",
							},
							{
								"from":      "TASKER",
								"userId":    "dm8AonwZigKCBjhxy",
								"message":   "[System message] Our Taskers are required to get at least one dose of COVID-19 vaccine to accept tasks and to follow the 5K procedure when working.",
								"createdAt": currentTime.AddDate(0, 0, -2),
								"_id":       "x617a6a2624bdb0810e265207",
							},
							{
								"from":      "TASKER",
								"userId":    "dm8AonwZigKCBjhxy",
								"message":   "[System message] In case you might require a Tasker who gets fully vaccinated or tests negative for COVID-19 please do note your requirement before posting your task or contact us.",
								"createdAt": currentTime.AddDate(0, 0, -2),
								"_id":       "x617a6a2624bdb0810e265208",
							},
						},
						"changedAt":     currentTime.AddDate(0, 0, -2),
						"oldTaskerName": "Trần Thị Quốc Lệ",
					}, {
						"oldTaskerId": "dm8AonwZigKCBjhxy",
						"newTaskerId": "2JCJp3E6CyQMHr3k8",
						"messages": []map[string]interface{}{
							{
								"from":      "TASKER",
								"message":   "Test",
								"userId":    "dm8AonwZigKCBjhxy",
								"createdAt": currentTime.AddDate(0, 0, -1),
								"_id":       "x617a6a2624bdb0810e265206",
							},
							{
								"from":      "TASKER",
								"userId":    "dm8AonwZigKCBjhxy",
								"message":   "[System message] Our Taskers are required to get at least one dose of COVID-19 vaccine to accept tasks and to follow the 5K procedure when working.",
								"createdAt": currentTime.AddDate(0, 0, -1),
								"_id":       "x617a6a2624bdb0810e265207",
							},
							{
								"from":      "TASKER",
								"userId":    "dm8AonwZigKCBjhxy",
								"message":   "[System message] In case you might require a Tasker who gets fully vaccinated or tests negative for COVID-19 please do note your requirement before posting your task or contact us.",
								"createdAt": currentTime.AddDate(0, 0, -1),
								"_id":       "x617a6a2624bdb0810e265208",
							},
						},
						"changedAt":     currentTime.AddDate(0, 0, -1),
						"oldTaskerName": "Trần Thị Quốc Lệ A",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId":  "x617a670140771ec0d4482730",
				"isoCode": "VN",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 3)
					So(respResult[0]["askerName"], ShouldEqual, "Ánh")
					So(respResult[0]["taskerName"], ShouldEqual, "Bùi Mộng Tuyền")
					So(len(respResult[0]["messages"].([]interface{})), ShouldEqual, 4)

					So(respResult[1]["askerName"], ShouldEqual, "Ánh")
					So(respResult[1]["taskerName"], ShouldEqual, "Trần Thị Quốc Lệ A")
					So(len(respResult[1]["messages"].([]interface{})), ShouldEqual, 3)

					So(respResult[2]["askerName"], ShouldEqual, "Ánh")
					So(respResult[2]["taskerName"], ShouldEqual, "Trần Thị Quốc Lệ")
					So(len(respResult[2]["messages"].([]interface{})), ShouldEqual, 3)
				})
			})
		})
	})

	// SEND CHAT MESSAGE
	SEND_CHAT_MESSAGE_API_URL := "/api/v3/api-asker-vn/send-chat-message"
	t.Run("24", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"chatId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})

			Convey("When service handle request if request chatId is empty", func() {
				body := map[string]interface{}{
					"chatId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if request data is empty", func() {
				body := map[string]interface{}{
					"chatId": "123",
					"data":   map[string]interface{}{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if request messageTo is empty", func() {
				body := map[string]interface{}{
					"chatId": "123",
					"data": map[string]interface{}{
						"image": "urkl",
					},
					"messageTo": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MESSAGE_TO_REQUIRED.ErrorCode)
				})
			})
		})
	})

	t.Run("25", func(t *testing.T) {
		log.Println("==================================== Send chat message success")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    "chatIds[0]",
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 404)
					result := map[string]map[string]interface{}{}
					b, _ := io.ReadAll(resp.Body)
					json.Unmarshal(b, &result)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CHAT_MESSAGE_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		log.Println("==================================== Send chat message success")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      "x617a670140771ec0d4482730",
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    chatIds[0],
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 404)
					b, _ := io.ReadAll(resp.Body)
					result := map[string]map[string]interface{}{}
					json.Unmarshal(b, &result)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("27", func(t *testing.T) {
		log.Println("==================================== Error cannot send chat after 30 minutes from done task time")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-3 * time.Hour)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567740",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567740",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
				"duration": 2,
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      taskIds[0],
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    chatIds[0],
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := io.ReadAll(resp.Body)
					json.Unmarshal(b, &result)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
				})
			})
		})
	})

	t.Run("28", func(t *testing.T) {
		log.Println("==================================== Error can send chat after 30 minutes from done task time")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-2 * time.Hour).Add(-25 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567740",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567740",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
				"duration": 2,
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      taskIds[0],
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    chatIds[0],
				"userId":    "0834567890",
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 200)

					chat, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chat, ShouldNotBeNil)
					So(len(chat.Messages), ShouldEqual, 2)
					So(chat.Messages[1].From, ShouldEqual, "ASKER")
					So(chat.Messages[1].IsRead, ShouldBeFalse)
					So(chat.Messages[1].Message, ShouldEqual, "Xin chào! Hãy đến đúng giờ.")
					So(chat.Messages[1].UserId, ShouldEqual, "0834567890")
					So(chat.Messages[1].UserName, ShouldEqual, "0834567890")
					So(chat.Messages[1].Image, ShouldEqual, "url")
					So(chat.Messages[1].Location, ShouldNotBeNil)
					So(chat.Messages[1].Location.Latitude, ShouldEqual, 12.34981)
					So(chat.Messages[1].Location.Longitude, ShouldEqual, 4.123)
				})
			})
		})
	})

	// HOME_MOVING task can send chat because task not DONE && not pass 24h after task done time
	t.Run("28.1", func(t *testing.T) {
		log.Println("==================================== HOME_MOVING task can send chat because task not DONE && not pass 24h after task done time")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-22 * time.Hour).Add(-25 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567740",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567740",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
				"duration": 1,
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      taskIds[0],
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    chatIds[0],
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 200)

					chat, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chat, ShouldNotBeNil)
					So(len(chat.Messages), ShouldEqual, 2)
					So(chat.Messages[1].From, ShouldEqual, "ASKER")
					So(chat.Messages[1].IsRead, ShouldBeFalse)
					So(chat.Messages[1].Message, ShouldEqual, "Xin chào! Hãy đến đúng giờ.")
					So(chat.Messages[1].UserId, ShouldEqual, "0834567890")
					So(chat.Messages[1].UserName, ShouldEqual, "0834567890")
					So(chat.Messages[1].Image, ShouldEqual, "url")
					So(chat.Messages[1].Location, ShouldNotBeNil)
					So(chat.Messages[1].Location.Latitude, ShouldEqual, 12.34981)
					So(chat.Messages[1].Location.Longitude, ShouldEqual, 4.123)
				})
			})
		})
	})

	// HOME_MOVING task can not send chat because task DONE although not pass 24h after task done time
	t.Run("28.2", func(t *testing.T) {
		log.Println("==================================== HOME_MOVING task can not send chat because task DONE although not pass 24h after task done time")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-22 * time.Hour).Add(-25 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567740",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567740",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
				"duration": 1,
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      taskIds[0],
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    chatIds[0],
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 500)
					var respResult map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
				})
			})
		})
	})

	// HOME_MOVING task can not send chat because not pass 24h after task done time although task not DONE
	t.Run("28.3", func(t *testing.T) {
		log.Println("==================================== HOME_MOVING task can not send chat because task DONE although not pass 24h after task done time")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Ánh",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567740",
				"name":  "Bùi Mộng Tuyền",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-25 * time.Hour).Add(-25 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567740",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567740",
						"name":      "Tasker 02",
						"avatar":    "/avatars/avatarDefault.png",
						"avgRating": 8.0,
						"taskDone":  39,
					},
				},
				"duration": 1,
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
			},
		})

		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"taskId":      taskIds[0],
					"askerPhone":  "0834567890",
					"askerName":   "Ánh",
					"taskerPhone": "0834567740",
					"taskerName":  "Bùi Mộng Tuyền",
					"messages": []map[string]interface{}{
						{
							"createdAt": currentTime,
							"_id":       "x617a6c0724bdb0810e265234",
							"from":      "TASKER",
							"message":   "Test",
							"userId":    "2JCJp3E6CyQMHr3k8",
							"isRead":    false,
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", SEND_CHAT_MESSAGE_API_URL), t, func() {
			body := map[string]interface{}{
				"chatId":    chatIds[0],
				"messageTo": []string{"0834567740"},
				"data": map[string]interface{}{
					"userId":   "0834567890",
					"userName": "0834567890",
					"isRead":   false,
					"from":     "ASKER",
					"message":  "Xin chào! Hãy đến đúng giờ.",
					"image":    "url",
					"location": map[string]interface{}{
						"latitude":  12.34981,
						"longitude": 4.123,
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", SEND_CHAT_MESSAGE_API_URL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Chat List", func() {
					So(resp.Code, ShouldEqual, 500)
					var respResult map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
				})
			})
		})
	})

	t.Run("29", func(t *testing.T) {
		log.Println("==================================== Validate GetChatHistoryByTask")
		apiUrl := "/api/v3/api-asker-vn/get-chat-history-by-task"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
					"taskId": "234",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"taskId": "234",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "123",
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
			})
			Convey("Check request when not permission task CONFIRMED", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone": "0834567890",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
					}, {
						"phone": "0834567710",
						"name":  "Tasker 01",
						"type":  globalConstant.USER_TYPE_TASKER,
					},
				})

				//Create Task
				ids := CreateTask([]map[string]interface{}{
					{
						"askerPhone":  "0834567890",
						"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
						"description": "Desc Test",
						"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
						"status":      globalConstant.TASK_STATUS_CONFIRMED,
						"acceptedTasker": []map[string]interface{}{
							{
								"taskerId":  "0834567710",
								"name":      "Tasker 01",
								"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
								"avgRating": 4.96999979019165,
								"taskDone":  938,
							},
						},
					}},
				)
				body := map[string]interface{}{
					"userId": "0834567890",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.StatusCode)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
			})
			Convey("Check request when not permission task done so long", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone": "0834567890",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
					}, {
						"phone": "0834567710",
						"name":  "Tasker 01",
						"type":  globalConstant.USER_TYPE_TASKER,
					},
				})
				taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -8)
				//Create Task
				ids := CreateTask([]map[string]interface{}{
					{
						"askerPhone":  "0834567890",
						"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
						"description": "Desc Test",
						"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
						"date":        fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
						"status":      globalConstant.TASK_STATUS_DONE,
						"acceptedTasker": []map[string]interface{}{
							{
								"taskerId":  "0834567710",
								"name":      "Tasker 01",
								"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
								"avgRating": 4.96999979019165,
								"taskDone":  938,
							},
						},
					}},
				)
				body := map[string]interface{}{
					"userId": "0834567890",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.StatusCode)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT.Message)
			})
		})
	})
	t.Run("30", func(t *testing.T) {
		log.Println("==================================== GetChatHistoryByTask")
		apiUrl := "/api/v3/api-asker-vn/get-chat-history-by-task"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"askerId":     "0834567890",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
					},
				},
			}})

		//CreateChatMessage
		CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567710",
					"taskId":      ids[0],
					"askerId":     "0834567890",
					"messages": []map[string]interface{}{
						{
							"from":           "TASKER",
							"message":        "Test ABC",
							"userId":         "0834567710",
							"isRead":         true,
							"translatedText": "kiểm tra 123",
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResultR := make(map[string]interface{})
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultR)
					So(resp.Code, ShouldEqual, 200)
					So(respResultR["_id"], ShouldNotBeBlank)
					So(respResultM["messages"][0]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][0]["message"], ShouldEqual, "Test ABC")
					So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][0]["userId"], ShouldEqual, "0834567710")
					So(respResultM["messages"][0]["translatedText"], ShouldEqual, "kiểm tra 123")
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567710")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Tasker 01")
					So(respResult["taskerInfo"]["language"], ShouldEqual, globalConstant.LANG_VI)
				})
			})
		})
	})
	t.Run("31", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-list-chat"

		log.Println("==================================== Validate getLisChat")
		Convey("Given a HTTP request for user Id required", t, func() {
			ResetData()
			body := map[string]interface{}{
				"userId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				m := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, lib.ERROR_USER_ID_REQUIRED.StatusCode)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
		Convey("Given a HTTP request for isoCode required", t, func() {
			ResetData()
			body := map[string]interface{}{
				"userId": "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				m := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.StatusCode)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
		})
		Convey("Given a HTTP request for user not found", t, func() {
			ResetData()
			body := map[string]interface{}{
				"userId":  "123",
				"isoCode": "VN",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				m := make(map[string]map[string]interface{})
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, lib.ERROR_USER_NOT_FOUND.StatusCode)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("32", func(t *testing.T) {
		log.Println("==================================== GetListChat")
		apiUrl := "/api/v3/api-asker-vn/get-list-chat"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone":           "0*********",
				"name":            "Asker 02",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0834567710", "0834567730"},
			}, {
				"phone":           "0834567710",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"avatar":          "Tasker01.png",
				"isPremiumTasker": true,
			}, {
				"phone":  "0834567720",
				"name":   "Tasker 02",
				"type":   globalConstant.USER_TYPE_TASKER,
				"avatar": "Tasker02.png",
			}, {
				"phone":  "0834567730",
				"name":   "Tasker 03",
				"type":   globalConstant.USER_TYPE_TASKER,
				"avatar": "Tasker03.png",
			}, {
				"phone":  "0834567740",
				"name":   "Company 01",
				"type":   globalConstant.USER_TYPE_TASKER,
				"avatar": "Tasker04.png",
			},
		})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567111",
						"name":      "Võ Thị Hồng Hà",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
						"isLeader":  true,
					},
					{
						"taskerId":  "0834567120",
						"name":      "Trần Thị Thanh Hồng",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/WpH39swRihgN9gDkj",
						"avgRating": 4.96000003814697,
						"taskDone":  396,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"duration":    3,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 05",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 06",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 05",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			}, {
				"askerPhone":  "0*********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 05",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567720",
						"name":      "Tasker 02",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
					},
				},
			},
		})
		//CreateChatMessage
		chatIds := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567710",
					"taskerName":  "Tasker 01",
					"taskId":      ids[0],
					"messages": []map[string]interface{}{
						{
							"from":     "TASKER",
							"video":    "Test ABC",
							"userId":   "0834567710",
							"isRead":   true,
							"userName": "Tasker 01",
						}, {
							"from":     "ASKER",
							"video":    "Test 123",
							"userId":   "0*********",
							"userName": "Asker 02",
						},
					},
				}, {
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567720",
					"taskerName":  "Tasker 02",
					"taskId":      ids[1],
					"messages": []map[string]interface{}{
						{
							"from":    "ASKER",
							"message": "Test 123",
							"userId":  "0834567710",
							"isRead":  true,
						}, {
							"from":     "TASKER",
							"image":    "Test 123",
							"userId":   "0834567710",
							"isRead":   true,
							"userName": "Tasker 02",
						},
					},
				}, {
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567730",
					"taskerName":  "Tasker 03",
					"taskId":      ids[2],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0834567730",
							"isRead":  true,
						}, {
							"from":    "TASKER",
							"message": "Test 1BCD",
							"userId":  "0834567730",
						}, {
							"from":     "TASKER",
							"message":  "Test 21BCD",
							"userId":   "0834567730",
							"userName": "Tasker 03",
						},
					},
				}, {
					"askerPhone":  "0834567890",
					"askerName":   "Asker 01",
					"taskerPhone": "0834567720",
					"taskerName":  "Tasker 02",
					"taskId":      ids[4],
					"messages": []map[string]interface{}{
						{
							"from":    "ASKER",
							"message": "Test 123",
							"userId":  "0834567710",
							"isRead":  true,
						}, {
							"from":   "ASKER",
							"image":  "Test 123",
							"userId": "0834567710",
							"isRead": true,
						},
					},
				}, {
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567730",
					"taskerName":  "Tasker 03",
					"taskId":      ids[5],
					"messages": []map[string]interface{}{
						{
							"from":    "TASKER",
							"message": "Test BCD",
							"userId":  "0834567730",
							"isRead":  true,
						}, {
							"from":    "TASKER",
							"message": "Test 1BCD",
							"userId":  "0834567730",
						}, {
							"from": "SYSTEM",
							"messageBySystem": map[string]interface{}{
								"key":    "ALERT_TASK_START",
								"sendTo": "TASKER",
							},
						}, {
							"from": "SYSTEM",
							"messageBySystem": map[string]interface{}{
								"key":    "ALERT_TASK_START",
								"sendTo": "BOTH",
								"title": map[string]interface{}{
									"vi": "Công việc sẽ bắt đầu sau 30 phút",
								},
								"text": map[string]interface{}{
									"vi": "Công việc của bạn sẽ được bắt đầu trong vòng 30 phút nữa. Vui lòng giữ liên lạc với các CTV hoặc Đối tác để được phục vụ tốt nhất.",
								},
							},
						},
					},
				}, {
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567730",
					"taskerName":  "Tasker 03",
					"taskId":      ids[6],
					"messages": []map[string]interface{}{
						{
							"from": "SYSTEM",
							"status": map[string]interface{}{
								"0*********": "seen",
							},
							"messageBySystem": map[string]interface{}{
								"isAskerRead": true,
								"key":         "ALERT_TASK_START",
								"sendTo":      "BOTH",
								"title": map[string]interface{}{
									"vi": "Công việc sẽ bắt đầu sau 30 phút",
								},
								"text": map[string]interface{}{
									"vi": "Công việc của bạn sẽ được bắt đầu trong vòng 30 phút nữa. Vui lòng giữ liên lạc với các CTV hoặc Đối tác để được phục vụ tốt nhất.",
								},
							},
						},
					},
				}, {
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567730",
					"taskerName":  "Tasker 03",
					"taskId":      ids[7],
					"messages": []map[string]interface{}{
						{
							"from": "TASKER",
							"type": globalConstant.CHAT_MESSAGE_REQUEST_TYPE_INCREASE_DURATION,
						},
					},
				}, {
					"askerPhone":  "0*********",
					"askerName":   "Asker 02",
					"taskerPhone": "0834567730",
					"taskerName":  "Tasker 03",
					"taskId":      ids[8],
					"messages": []map[string]interface{}{
						{
							"from":   "ASKER",
							"userId": "0*********",
							"type":   globalConstant.CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DATE_TIME,
						},
					},
				},
			})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0*********",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					var respResult []map[string]interface{}
					// respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 7)

					So(respResult[0]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[0]["chatId"], ShouldEqual, chatIds[0])
					So(respResult[0]["countUnreadMessage"], ShouldEqual, 0)
					So(respResult[0]["date"], ShouldNotBeNil)
					So(respResult[0]["duration"], ShouldEqual, 2)
					So(respResult[0]["isFavouriteTasker"], ShouldBeTrue)
					So(respResult[0]["isPremiumTasker"], ShouldBeTrue)
					So(respResult[0]["isRead"], ShouldBeTrue)
					So(respResult[0]["taskId"], ShouldEqual, ids[0])
					So(respResult[0]["taskerAvatar"], ShouldEqual, "Tasker01.png")
					So(respResult[0]["taskerId"], ShouldEqual, "0834567710")
					So(respResult[0]["taskerName"], ShouldEqual, "Tasker 01")
					So(respResult[0]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "ASKER")
					So(respResult[0]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "[Video]")
					So(respResult[0]["lastChatMessage"].(map[string]interface{})["userName"], ShouldEqual, "Asker 02")
					So(respResult[0]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Vệ sinh máy lạnh")

					So(respResult[1]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[1]["chatId"], ShouldEqual, chatIds[1])
					So(respResult[1]["countUnreadMessage"], ShouldEqual, 0)
					So(respResult[1]["date"], ShouldNotBeNil)
					So(respResult[1]["duration"], ShouldEqual, 2)
					So(respResult[1]["isFavouriteTasker"], ShouldBeFalse)
					So(respResult[1]["isPremiumTasker"], ShouldBeFalse)
					So(respResult[1]["isRead"], ShouldBeTrue)
					So(respResult[1]["taskId"], ShouldEqual, ids[1])
					So(respResult[1]["taskerAvatar"], ShouldEqual, "Tasker02.png")
					So(respResult[1]["taskerId"], ShouldEqual, "0834567720")
					So(respResult[1]["taskerName"], ShouldEqual, "Tasker 02")
					So(respResult[1]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "TASKER")
					So(respResult[1]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "[Hình ảnh]")
					So(respResult[1]["lastChatMessage"].(map[string]interface{})["userName"], ShouldEqual, "Tasker 02")
					So(respResult[1]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Dọn dẹp nhà")

					So(respResult[2]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[2]["chatId"], ShouldEqual, chatIds[2])
					So(respResult[2]["countUnreadMessage"], ShouldEqual, 2)
					So(respResult[2]["date"], ShouldNotBeNil)
					So(respResult[2]["duration"], ShouldEqual, 3)
					So(respResult[2]["isFavouriteTasker"], ShouldBeTrue)
					So(respResult[2]["isPremiumTasker"], ShouldBeFalse)
					So(respResult[2]["isRead"], ShouldBeFalse)
					So(respResult[2]["taskId"], ShouldEqual, ids[2])
					So(respResult[2]["taskerAvatar"], ShouldEqual, "Tasker03.png")
					So(respResult[2]["taskerId"], ShouldEqual, "0834567730")
					So(respResult[2]["taskerName"], ShouldEqual, "Tasker 03")
					So(respResult[2]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "TASKER")
					So(respResult[2]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "Test 21BCD")
					So(respResult[2]["lastChatMessage"].(map[string]interface{})["userName"], ShouldEqual, "Tasker 03")
					So(respResult[2]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Dọn dẹp nhà")

					So(respResult[3]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[3]["chatId"], ShouldEqual, chatIds[4])
					So(respResult[3]["countUnreadMessage"], ShouldEqual, 3)
					So(respResult[3]["date"], ShouldNotBeNil)
					So(respResult[3]["duration"], ShouldEqual, 2)
					So(respResult[3]["isFavouriteTasker"], ShouldBeTrue)
					So(respResult[3]["isPremiumTasker"], ShouldBeFalse)
					So(respResult[3]["isRead"], ShouldBeFalse)
					So(respResult[3]["taskId"], ShouldEqual, ids[5])
					So(respResult[3]["taskerAvatar"], ShouldEqual, "Tasker03.png")
					So(respResult[3]["taskerId"], ShouldEqual, "0834567730")
					So(respResult[3]["taskerName"], ShouldEqual, "Tasker 03")
					So(respResult[3]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "SYSTEM")
					So(respResult[3]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "[Tin nhắn từ bTaskee]")
					So(respResult[3]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Dọn dẹp nhà")

					So(respResult[4]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[4]["chatId"], ShouldEqual, chatIds[5])
					So(respResult[4]["countUnreadMessage"], ShouldEqual, 0)
					So(respResult[4]["date"], ShouldNotBeNil)
					So(respResult[4]["duration"], ShouldEqual, 2)
					So(respResult[4]["isFavouriteTasker"], ShouldBeTrue)
					So(respResult[4]["isPremiumTasker"], ShouldBeFalse)
					So(respResult[4]["isRead"], ShouldBeTrue)
					So(respResult[4]["taskId"], ShouldEqual, ids[6])
					So(respResult[4]["taskerAvatar"], ShouldEqual, "Tasker03.png")
					So(respResult[4]["taskerId"], ShouldEqual, "0834567730")
					So(respResult[4]["taskerName"], ShouldEqual, "Tasker 03")
					So(respResult[4]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "SYSTEM")
					So(respResult[4]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "[Tin nhắn từ bTaskee]")
					So(respResult[4]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Dọn dẹp nhà")

					So(respResult[5]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[5]["chatId"], ShouldEqual, chatIds[6])
					So(respResult[5]["countUnreadMessage"], ShouldEqual, 1)
					So(respResult[5]["date"], ShouldNotBeNil)
					So(respResult[5]["duration"], ShouldEqual, 2)
					So(respResult[5]["isFavouriteTasker"], ShouldBeTrue)
					So(respResult[5]["isPremiumTasker"], ShouldBeFalse)
					So(respResult[5]["isRead"], ShouldBeFalse)
					So(respResult[5]["taskId"], ShouldEqual, ids[7])
					So(respResult[5]["taskerAvatar"], ShouldEqual, "Tasker03.png")
					So(respResult[5]["taskerId"], ShouldEqual, "0834567730")
					So(respResult[5]["taskerName"], ShouldEqual, "Tasker 03")
					So(respResult[5]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "TASKER")
					So(respResult[5]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "[Yêu cầu thêm giờ làm]")
					So(respResult[5]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Dọn dẹp nhà")

					So(respResult[6]["askerName"], ShouldEqual, "Asker 02")
					So(respResult[6]["chatId"], ShouldEqual, chatIds[7])
					So(respResult[6]["countUnreadMessage"], ShouldEqual, 0)
					So(respResult[6]["date"], ShouldNotBeNil)
					So(respResult[6]["duration"], ShouldEqual, 2)
					So(respResult[6]["isFavouriteTasker"], ShouldBeTrue)
					So(respResult[6]["isPremiumTasker"], ShouldBeFalse)
					So(respResult[6]["isRead"], ShouldBeTrue)
					So(respResult[6]["taskId"], ShouldEqual, ids[8])
					So(respResult[6]["taskerAvatar"], ShouldEqual, "Tasker03.png")
					So(respResult[6]["taskerId"], ShouldEqual, "0834567730")
					So(respResult[6]["taskerName"], ShouldEqual, "Tasker 03")
					So(respResult[6]["lastChatMessage"].(map[string]interface{})["from"], ShouldEqual, "ASKER")
					So(respResult[6]["lastChatMessage"].(map[string]interface{})["message"], ShouldEqual, "[Yêu cầu thay đổi giờ làm]")
					So(respResult[6]["serviceText"].(map[string]interface{})["vi"], ShouldEqual, "Dọn dẹp nhà")
				})
			})
		})
	})
	t.Run("33", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/set-is-read-chat-message"
		ResetData()

		Convey("Check the response if chatId is empty", t, func() {
			body := map[string]interface{}{
				"chatId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.ErrorCode)
		})
		Convey("Check the response if isoCode is empty", t, func() {
			body := map[string]interface{}{
				"chatId": "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
		})
	})
	t.Run("34", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/set-is-read-chat-message"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone":    "0*********",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"avatar":   "http://avatar1",
				"language": globalConstant.LANG_VI,
				"gender":   globalConstant.GENDER_MALE,
			},
			{
				"phone": "0834567890",
				"name":  "Tasker 00",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		ids := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0*********",
					"taskerPhone": "0834567890",
					"messages": []map[string]interface{}{
						{
							"from":            globalConstant.USER_TYPE_ASKER,
							"preparedMessage": "PREPARED_MESSAGE_THANK",
							"createdAt":       globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
							"userId":          "0*********",
						}, {
							"from":            globalConstant.USER_TYPE_TASKER,
							"preparedMessage": "PREPARED_MESSAGE_THANK",
							"createdAt":       globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
							"userId":          "0834567890",
						}, {
							"from":      globalConstant.USER_TYPE_ASKER,
							"message":   "Hello",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
							"userId":    "0*********",
						}, {
							"from":            globalConstant.USER_TYPE_TASKER,
							"preparedMessage": "PREPARED_MESSAGE_THANK",
							"createdAt":       globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
							"userId":          "0834567890",
						}, {
							"from": "SYSTEM",
							"messageBySystem": map[string]interface{}{
								"key":    "ALERT_TASK_START",
								"sendTo": "BOTH",
								"title": map[string]interface{}{
									"vi": "Công việc sẽ bắt đầu sau 30 phút",
								},
								"text": map[string]interface{}{
									"vi": "Công việc của bạn sẽ được bắt đầu trong vòng 30 phút nữa. Vui lòng giữ liên lạc với các CTV hoặc Đối tác để được phục vụ tốt nhất.",
								},
							},
						},
					},
					"updatedAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
				},
			})

		body := map[string]interface{}{
			"chatId":  ids[0],
			"isoCode": "VN",
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					chat, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": ids[0]}, bson.M{})
					for _, m := range chat.Messages {
						if m.From == globalConstant.USER_TYPE_TASKER {
							So(m.IsRead, ShouldBeTrue)
						}
						if m.From == "SYSTEM" {
							So(m.MessageBySystem.IsAskerRead, ShouldBeTrue)
						}
					}
				})
			})
		})
	})
	t.Run("35", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/set-is-read-chat-message"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone":    "0*********",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"avatar":   "http://avatar1",
				"language": globalConstant.LANG_VI,
				"gender":   globalConstant.GENDER_MALE,
			},
			{
				"phone": "0834567890",
				"name":  "Tasker 00",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		ids := CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0*********",
					"taskerPhone": "0834567890",
					"messages": []map[string]interface{}{
						{
							"from":            globalConstant.USER_TYPE_TASKER,
							"preparedMessage": "PREPARED_MESSAGE_THANK",
							"createdAt":       globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
							"userId":          "0834567890",
						}, {
							"from":            globalConstant.USER_TYPE_ASKER,
							"preparedMessage": "PREPARED_MESSAGE_THANK",
							"createdAt":       globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
							"userId":          "0*********",
						},
					},
					"updatedAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
				},
			})

		body := map[string]interface{}{
			"chatId":  ids[0],
			"isoCode": "VN",
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					chat, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": ids[0]}, bson.M{})
					for _, m := range chat.Messages {
						if m.From == globalConstant.USER_TYPE_TASKER {
							So(m.IsRead, ShouldBeTrue)
						} else {
							So(m.IsRead, ShouldBeFalse)
						}
					}
				})
			})
		})
	})
	t.Run("36", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone":  "0834567740",
				"name":   "Company 01",
				"type":   globalConstant.USER_TYPE_TASKER,
				"avatar": "http://avatar",
			},
		})

		//Create Task
		taskDate := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0*********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
				"detailHomeMoving": map[string]interface{}{
					"oldHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "from Address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  "VN",
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"newHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "newHomeDetail address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  "VN",
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"furniture": []map[string]interface{}{
						{
							"name": "removableFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ dùng có thể tháo rời",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "solidFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ nội thất nguyên khối",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "electronic",
							"text": map[string]interface{}{
								"vi": "Thiết bị điện tử",
							},
							"quantity": 10,
							"images":   []string{"xxx"},
							"options": []map[string]interface{}{
								{
									"name": "airConditioner",
									"text": map[string]interface{}{
										"vi": "Tháo lắp máy lạnh",
									},
									"quantity": 1,
								},
								{
									"name": "waterHeater",
									"text": map[string]interface{}{
										"vi": "Máy nước nóng",
									},
									"options": []map[string]interface{}{
										{
											"name": "tanklessWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng trực tiếp",
											},
											"quantity": 1,
										},
										{
											"name": "traditionalWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng gián tiếp",
											},
											"quantity": 1,
										},
									},
									"quantity": 2,
								},
							},
						},
					},
					"completedStep": 2.0,
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult["messages"]), ShouldEqual, 0)
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")
					So(respResult["taskerInfo"]["avatar"], ShouldEqual, "http://avatar")

					taskDetailHomeMoving := cast.ToStringMap(respResult["task"]["homeMovingDetail"])
					So(taskDetailHomeMoving["fromAddress"], ShouldEqual, "from Address")
					So(taskDetailHomeMoving["toAddress"], ShouldEqual, "newHomeDetail address")
					So(taskDetailHomeMoving["stepInProgress"], ShouldBeNil)

					taskHomeMoving := map[string]map[string]map[string][]map[string]interface{}{}
					json.Unmarshal(bytes, &taskHomeMoving)
					So(len(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"]), ShouldEqual, 5)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][0]["step"], ShouldEqual, 1)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][0]["status"], ShouldBeNil)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][1]["step"], ShouldEqual, 2)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][1]["status"], ShouldBeNil)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][2]["step"], ShouldEqual, 3)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][2]["status"], ShouldBeNil)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][3]["step"], ShouldEqual, 4)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][3]["status"], ShouldBeNil)
				})
			})
		})
	})

	t.Run("37", func(t *testing.T) {
		log.Println("==================================== GetChatList")
		apiUrl := "/api/v3/api-asker-vn/get-chat-list-chat-message"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0*********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567730",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone":  "0834567740",
				"name":   "Company 01",
				"type":   globalConstant.USER_TYPE_TASKER,
				"avatar": "http://avatar",
			},
		})

		//Create Task
		taskDate := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0*********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567730",
						"name":      "Tasker 03",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/hypqjKgYLEFipkq4x",
						"avgRating": 4.8600001335144,
						"taskDone":  639,
						"companyId": "0834567740",
					},
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"detailHomeMoving": map[string]interface{}{
					"oldHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "from Address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  "VN",
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"newHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "newHomeDetail address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  "VN",
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"furniture": []map[string]interface{}{
						{
							"name": "removableFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ dùng có thể tháo rời",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "solidFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ nội thất nguyên khối",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "electronic",
							"text": map[string]interface{}{
								"vi": "Thiết bị điện tử",
							},
							"quantity": 10,
							"images":   []string{"xxx"},
							"options": []map[string]interface{}{
								{
									"name": "airConditioner",
									"text": map[string]interface{}{
										"vi": "Tháo lắp máy lạnh",
									},
									"quantity": 1,
								},
								{
									"name": "waterHeater",
									"text": map[string]interface{}{
										"vi": "Máy nước nóng",
									},
									"options": []map[string]interface{}{
										{
											"name": "tanklessWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng trực tiếp",
											},
											"quantity": 1,
										},
										{
											"name": "traditionalWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng gián tiếp",
											},
											"quantity": 1,
										},
									},
									"quantity": 2,
								},
							},
						},
					},
					"completedStep": 2.0,
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0*********",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult["messages"]), ShouldEqual, 0)
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567740")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Company 01")
					So(respResult["taskerInfo"]["employeeName"], ShouldEqual, "Tasker 03")
					So(respResult["taskerInfo"]["avatar"], ShouldEqual, "http://avatar")

					taskDetailHomeMoving := cast.ToStringMap(respResult["task"]["homeMovingDetail"])
					So(taskDetailHomeMoving["fromAddress"], ShouldEqual, "from Address")
					So(taskDetailHomeMoving["toAddress"], ShouldEqual, "newHomeDetail address")
					So(taskDetailHomeMoving["stepInProgress"].(map[string]interface{})["step"], ShouldEqual, 3)

					taskHomeMoving := map[string]map[string]map[string][]map[string]interface{}{}
					json.Unmarshal(bytes, &taskHomeMoving)
					So(len(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"]), ShouldEqual, 5)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][0]["step"], ShouldEqual, 1)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][0]["status"], ShouldEqual, "COMPLETED")
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][1]["step"], ShouldEqual, 2)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][1]["status"], ShouldEqual, "COMPLETED")
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][2]["step"], ShouldEqual, 3)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][2]["status"], ShouldEqual, "IN_PROGRESS")
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][3]["step"], ShouldEqual, 4)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][3]["status"], ShouldBeNil)
				})
			})
		})
	})

	t.Run("38", func(t *testing.T) {
		log.Println("==================================== GetChatHistoryByTask")
		apiUrl := "/api/v3/api-asker-vn/get-chat-history-by-task"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0834567890",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_DONE,
				"askerId":        "0834567890",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567710",
						"name":      "Tasker 01",
						"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating": 4.96999979019165,
						"taskDone":  938,
					},
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"detailHomeMoving": map[string]interface{}{
					"oldHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "from Address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  "VN",
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"newHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "newHomeDetail address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  "VN",
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"furniture": []map[string]interface{}{
						{
							"name": "removableFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ dùng có thể tháo rời",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "solidFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ nội thất nguyên khối",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "electronic",
							"text": map[string]interface{}{
								"vi": "Thiết bị điện tử",
							},
							"quantity": 10,
							"images":   []string{"xxx"},
							"options": []map[string]interface{}{
								{
									"name": "airConditioner",
									"text": map[string]interface{}{
										"vi": "Tháo lắp máy lạnh",
									},
									"quantity": 1,
								},
								{
									"name": "waterHeater",
									"text": map[string]interface{}{
										"vi": "Máy nước nóng",
									},
									"options": []map[string]interface{}{
										{
											"name": "tanklessWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng trực tiếp",
											},
											"quantity": 1,
										},
										{
											"name": "traditionalWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng gián tiếp",
											},
											"quantity": 1,
										},
									},
									"quantity": 2,
								},
							},
						},
					},
					"completedStep": 2.0,
				},
			}})

		//CreateChatMessage
		CreateChatConversation(
			local.ISO_CODE,
			[]map[string]interface{}{
				{
					"askerPhone":  "0834567890",
					"taskerPhone": "0834567710",
					"taskId":      ids[0],
					"askerId":     "0834567890",
					"messages": []map[string]interface{}{
						{
							"from":           "TASKER",
							"message":        "Test ABC",
							"userId":         "0834567710",
							"isRead":         true,
							"translatedText": "kiểm tra 123",
						},
					},
				},
			})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get Chat List", func() {
					respResultR := make(map[string]interface{})
					respResult := make(map[string]map[string]interface{})
					respResultM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultR)
					So(resp.Code, ShouldEqual, 200)
					So(respResultR["_id"], ShouldNotBeBlank)
					So(respResultM["messages"][0]["from"], ShouldEqual, "TASKER")
					So(respResultM["messages"][0]["message"], ShouldEqual, "Test ABC")
					So(respResultM["messages"][0]["isRead"], ShouldBeTrue)
					So(respResultM["messages"][0]["userId"], ShouldEqual, "0834567710")
					So(respResultM["messages"][0]["translatedText"], ShouldEqual, "kiểm tra 123")
					So(respResult["task"]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["task"]["_id"], ShouldEqual, ids[0])
					So(respResult["taskerInfo"]["_id"], ShouldEqual, "0834567710")
					So(respResult["taskerInfo"]["name"], ShouldEqual, "Tasker 01")
					So(respResult["taskerInfo"]["language"], ShouldEqual, globalConstant.LANG_VI)

					taskDetailHomeMoving := cast.ToStringMap(respResult["task"]["homeMovingDetail"])
					So(taskDetailHomeMoving["fromAddress"], ShouldEqual, "from Address")
					So(taskDetailHomeMoving["toAddress"], ShouldEqual, "newHomeDetail address")
					So(taskDetailHomeMoving["stepInProgress"].(map[string]interface{})["step"], ShouldEqual, 3)

					taskHomeMoving := map[string]map[string]map[string][]map[string]interface{}{}
					json.Unmarshal(bytes, &taskHomeMoving)
					So(len(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"]), ShouldEqual, 5)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][0]["step"], ShouldEqual, 1)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][0]["status"], ShouldEqual, "COMPLETED")
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][1]["step"], ShouldEqual, 2)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][1]["status"], ShouldEqual, "COMPLETED")
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][2]["step"], ShouldEqual, 3)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][2]["status"], ShouldEqual, "IN_PROGRESS")
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][3]["step"], ShouldEqual, 4)
					So(taskHomeMoving["task"]["homeMovingDetail"]["homeMovingProcess"][3]["status"], ShouldBeNil)
				})
			})
		})
	})
}
