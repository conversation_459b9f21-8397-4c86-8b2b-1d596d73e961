package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func Test_Task_1(t *testing.T) {
	// ================= Api track asker click bank icon action if using qrCode
	t.Run("1", func(t *testing.T) {
		// CheckTaskSameTime
		apiURL := "/api/v3/api-asker-vn/track-asker-try-to-process-payment"
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{
				"userId": 1123,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)

			So(resp.Code, ShouldEqual, 400)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId": "",
				"taskId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when taskId blank", t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
		})

		Convey("Check request when paymentMethod nil", t, func() {
			body := map[string]interface{}{
				"userId":        "0834567890",
				"taskId":        "123",
				"paymentMethod": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PAYMENT_METHOD_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_PAYMENT_METHOD_REQUIRED.Message)
		})
	})

	t.Run("2", func(t *testing.T) {
		ResetData()
		// CheckTaskSameTime
		apiURL := "/api/v3/api-asker-vn/track-asker-try-to-process-payment"
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isPrepayTask": true,
				"payment": map[string]interface{}{
					"status": globalConstant.TASK_PAYMENT_STATUS_NEW,
					"method": globalConstant.PAYMENT_METHOD_VIET_QR,
				},
			},
		})

		body := map[string]interface{}{
			"userId":        "0834567890",
			"taskId":        taskIds[0],
			"paymentMethod": globalConstant.PAYMENT_METHOD_VIET_QR,
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for api", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Check the response", func() {
				So(resp.Code, ShouldEqual, 200)

				// Check database
				var task *modelTask.Task
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{"_id": 1, "payment": 1}, &task)
				So(task.Payment.Status, ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_NEW)
				So(task.Payment.Method, ShouldEqual, globalConstant.PAYMENT_METHOD_VIET_QR)
				So(task.Payment.IsPaymentAttempted, ShouldBeTrue)
			})
		})
	})

	// ================= Api cancel order when task is posted
	t.Run("3", func(t *testing.T) {
		// CheckTaskSameTime
		apiURL := "/api/v3/api-asker-vn/cancel-task-payment-process"
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{
				"userId": 1123,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)

			So(resp.Code, ShouldEqual, 400)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId": "",
				"taskId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when taskId blank", t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
		})

		Convey("Check request when paymentMethod nil", t, func() {
			body := map[string]interface{}{
				"userId":        "0834567890",
				"taskId":        "123",
				"paymentMethod": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := ioutil.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PAYMENT_METHOD_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_PAYMENT_METHOD_REQUIRED.Message)
		})
	})

	t.Run("4", func(t *testing.T) {
		ResetData()
		// CheckTaskSameTime
		apiURL := "/api/v3/api-asker-vn/cancel-task-payment-process"
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isPrepayTask": true,
				"payment": map[string]interface{}{
					"status": globalConstant.TASK_PAYMENT_STATUS_NEW,
					"method": globalConstant.PAYMENT_METHOD_VIET_QR,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":     globalConstant.CHANGES_HISTORY_KEY_UPDATE_PAYMENT_METHOD,
						"from":    globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
						"content": map[string]interface{}{},
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId":        "0834567890",
			"taskId":        taskIds[0],
			"paymentMethod": globalConstant.PAYMENT_METHOD_VIET_QR,
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for api", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Check the response", func() {
				So(resp.Code, ShouldEqual, 200)

				// Check database
				var task *modelTask.Task
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{"_id": 1, "payment.status": 1}, &task)
				So(task.Payment.Status, ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_CANCELED)
			})
		})
	})

	// ================= Get tasker todo list
	t.Run("5", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-task-todo-list"
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"userId": 1123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})
			Convey("When service handle request if request userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request taskerId is empty", func() {
				body := map[string]interface{}{
					"userId": "xxx",
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-task-todo-list"
		ResetData()

		body := map[string]interface{}{
			"userId": "xxx",
			"taskId": "xxx",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 404)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-task-todo-list"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567880",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
				"askerPhone":     "0834567890",
				"description":    "Task 01",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"userId": "xxx",
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_STATUS_NOT_ALLOW_VIEW_TASK_LIST.ErrorCode)
				})
			})
		})
	})

	// The task is not belong to user
	t.Run("8", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-task-todo-list"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567880",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
				"askerPhone":     "0834567890",
				"description":    "Task 01",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"userId": "xxx",
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_NOT_HAVE_PERMISSION_GET_CHECKLIST.ErrorCode)
				})
			})
		})
	})

	// Success
	t.Run("9", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-task-todo-list"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567880",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
				"askerPhone":     "0834567890",
				"description":    "Task 01",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"todoList": []map[string]interface{}{
					{
						"_id": "xxx",
						"text": map[string]interface{}{
							"vi": "Task 01",
						},
						"status":                   globalConstant.TODO_LIST_STATUS_DONE,
						"isDefaultServiceTodoList": true,
					},
					{
						"_id": "yyy",
						"text": map[string]interface{}{
							"vi": "Task 02",
						},
						"isDefaultServiceTodoList": true,
					},
					{
						"_id":     "zzz",
						"content": "Task 03",
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId": "0834567890",
			"taskId": taskIds[0],
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result["todoList"]), ShouldEqual, 3)
					So(result["todoList"][0]["_id"], ShouldEqual, "xxx")
					So(result["todoList"][0]["text"].(map[string]interface{})["vi"], ShouldEqual, "Task 01")
					So(result["todoList"][0]["status"], ShouldEqual, globalConstant.TODO_LIST_STATUS_DONE)
					So(result["todoList"][0]["isDefaultServiceTodoList"], ShouldBeTrue)
					So(result["todoList"][1]["_id"], ShouldEqual, "yyy")
					So(result["todoList"][1]["text"].(map[string]interface{})["vi"], ShouldEqual, "Task 02")
					So(result["todoList"][1]["isDefaultServiceTodoList"], ShouldBeTrue)
					So(result["todoList"][2]["_id"], ShouldEqual, "zzz")
					So(result["todoList"][2]["content"], ShouldEqual, "Task 03")
				})
			})
		})
	})
	// test API v5/get-beauty-accessories
	t.Run("10", func(t *testing.T) {
		apiURL := "/api/v5/api-asker-vn/get-beauty-accessories"
		ResetData()
		// update service by name beauty care
		accessories := []map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Các sản phẩm làm sạch da",
					"en": "Skin cleansing products",
				},
				"image": "https://i.upanh.org/2025/04/21/sua-rua-mat-removebg-preview5228aaff11a9a1e3.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Các sản phẩm dưỡng & bảo vệ da",
					"en": "Skincare & protection products",
				},
				"image": "https://i.upanh.org/2025/04/21/kem-duong-am-removebg-previewc357fd8476ec045a.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 2,
			},
			{
				"title": map[string]interface{}{
					"vi": "Các loại kem nền & che phủ",
					"en": "Foundation & coverage products",
				},
				"image": "https://i.upanh.org/2025/04/21/61b-mXtEE-L-removebg-preview2daacc196b78a20a.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 3,
			},
			{
				"title": map[string]interface{}{
					"vi": "Bộ cọ & mút trang điểm",
					"en": "Makeup brushes & sponges",
				},
				"image": "https://i.upanh.org/2025/04/21/bo-co-mut-trang-diem-removebg-previewf81e263238fa1a8b.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 4,
			},
			{
				"title": map[string]interface{}{
					"vi": "Trang điểm mắt & chân mày",
					"en": "Eye & brow makeup products",
				},
				"image": "https://i.upanh.org/2025/04/21/but-ke-long-may-removebg-preview18e9e51f327b3c48.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 5,
			},
			{
				"title": map[string]interface{}{
					"vi": "Trang điểm môi & má",
					"en": "Lip & cheek makeup products",
				},
				"image": "https://i.upanh.org/2025/04/18/trang_diem0944fd4354287cf6.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 6,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy duỗi tóc",
					"en": "Hair straightener",
				},
				"image": "https://i.upanh.org/2025/04/21/may-duoi-toc-removebg-preview7885df5dba7e8861.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"inOptionName": []string{
					"naturalMakeupHairStyling",
					"neutralMakeupHairStyling",
					"dramaticMakeupHairStyling",
				},
				"weight": 7,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy uốn lọn",
					"en": "Curling iron",
				},
				"image": "https://i.upanh.org/2025/04/18/tao_mau_toc12bbc18e59bd2cf8.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"inOptionName": []string{
					"naturalMakeupHairStyling",
					"neutralMakeupHairStyling",
					"dramaticMakeupHairStyling",
				},
				"weight": 8,
			}, {
				"title": map[string]interface{}{
					"vi": "Bộ dầu gội & dầu xả",
					"en": "Shampoo & conditioner set",
				},
				"image": "https://i.upanh.org/2025/04/18/goi_dau_thu_gian7266a2ec08f6976f.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"weight": 9,
			},
			{
				"title": map[string]interface{}{
					"vi": "Mặt nạ ủ tóc",
					"en": "Hair treatment mask",
				},
				"image": "https://i.upanh.org/2025/04/21/mat-na-u-toc-removebg-preview3b426ac7c29adf41.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"weight": 10,
			},
			{
				"title": map[string]interface{}{
					"vi": "Sữa rửa mặt",
					"en": "Facial cleanser",
				},
				"image": "https://i.upanh.org/2025/04/21/sua-rua-mat-removebg-preview5228aaff11a9a1e3.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"weight": 11,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy duỗi tóc",
					"en": "Hair straightener",
				},
				"image": "https://i.upanh.org/2025/04/21/may-duoi-toc-removebg-preview7885df5dba7e8861.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"inOptionName": []string{
					"hairDryingStyling",
				},
				"weight": 12,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy uốn lọn",
					"en": "Curling iron",
				},
				"image": "https://i.upanh.org/2025/04/18/tao_mau_toc12bbc18e59bd2cf8.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"inOptionName": []string{
					"hairDryingStyling",
				},
				"weight": 13,
			},
		}
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			bson.M{"name": globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE},
			bson.M{
				"$set": bson.M{
					"detailService.beautyCare.accessories": accessories,
				},
			},
		)
		defer func() {
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
				bson.M{"name": globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE},
				bson.M{"$unset": bson.M{"detailService.beautyCare.accessories": 1}},
			)
		}()
		body := map[string]any{
			"detailBeautyCare": map[string]any{
				"numberOfTaskers": 1,
				"name":            "makeup",
				"packages": []map[string]any{
					{
						"_id": "x11512543a3e28fad55c645d86353b7fe",
						"mainServices": []map[string]any{
							{
								"name": "graduationMakeup",
								"style": map[string]any{
									"name": "naturalMakeup",
									"options": []map[string]any{
										{
											"name": "naturalMakeupHairStyling",
										},
									},
								},
							},
						},
					},
				},
			},
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := []map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 8)
					for i, accessory := range result {
						So(accessory["title"].(map[string]interface{})["vi"], ShouldEqual, accessories[i]["title"].(map[string]interface{})["vi"])
						So(accessory["image"], ShouldEqual, accessories[i]["image"])
						So(accessory["isDisabled"], ShouldBeFalse)
						So(accessory["weight"], ShouldEqual, accessories[i]["weight"])
					}
				})
			})
		})
		// test case 2
		body = map[string]any{
			"detailBeautyCare": map[string]any{
				"numberOfTaskers": 1,
				"name":            "makeup",
				"packages": []map[string]any{
					{
						"_id": "x11512543a3e28fad55c645d86353b7fe",
						"mainServices": []map[string]any{
							{
								"name": "graduationMakeup",
								"style": map[string]any{
									"name": "naturalMakeup",
								},
							},
						},
					},
				},
			},
		}
		reqBody, _ = json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s 2", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := []map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 8)
					oldWeight := 0.0
					newWeight := 0.0
					for i, accessory := range result {
						newWeight = accessory["weight"].(float64)
						So(newWeight, ShouldBeGreaterThan, oldWeight)
						So(accessory["title"].(map[string]interface{})["vi"], ShouldEqual, accessories[i]["title"].(map[string]interface{})["vi"])
						So(accessory["image"], ShouldEqual, accessories[i]["image"])
						So(accessory["weight"], ShouldEqual, accessories[i]["weight"])
						if i <= 5 {
							So(accessory["isDisabled"], ShouldBeFalse)
						} else {
							So(accessory["isDisabled"], ShouldBeTrue)
						}
						oldWeight = newWeight
					}
				})
			})
		})
	})

	// test API v5/get-beauty-accessories - Makeup and Hair Styling
	t.Run("11", func(t *testing.T) {
		apiURL := "/api/v5/api-asker-vn/get-beauty-accessories"
		ResetData()
		// update service by name beauty care
		accessories := []map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Các sản phẩm làm sạch da",
					"en": "Skin cleansing products",
				},
				"image": "https://i.upanh.org/2025/04/21/sua-rua-mat-removebg-preview5228aaff11a9a1e3.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Các sản phẩm dưỡng & bảo vệ da",
					"en": "Skincare & protection products",
				},
				"image": "https://i.upanh.org/2025/04/21/kem-duong-am-removebg-previewc357fd8476ec045a.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 2,
			},
			{
				"title": map[string]interface{}{
					"vi": "Các loại kem nền & che phủ",
					"en": "Foundation & coverage products",
				},
				"image": "https://i.upanh.org/2025/04/21/61b-mXtEE-L-removebg-preview2daacc196b78a20a.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 3,
			},
			{
				"title": map[string]interface{}{
					"vi": "Bộ cọ & mút trang điểm",
					"en": "Makeup brushes & sponges",
				},
				"image": "https://i.upanh.org/2025/04/21/bo-co-mut-trang-diem-removebg-previewf81e263238fa1a8b.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 4,
			},
			{
				"title": map[string]interface{}{
					"vi": "Trang điểm mắt & chân mày",
					"en": "Eye & brow makeup products",
				},
				"image": "https://i.upanh.org/2025/04/21/but-ke-long-may-removebg-preview18e9e51f327b3c48.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 5,
			},
			{
				"title": map[string]interface{}{
					"vi": "Trang điểm môi & má",
					"en": "Lip & cheek makeup products",
				},
				"image": "https://i.upanh.org/2025/04/18/trang_diem0944fd4354287cf6.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"weight": 6,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy duỗi tóc",
					"en": "Hair straightener",
				},
				"image": "https://i.upanh.org/2025/04/21/may-duoi-toc-removebg-preview7885df5dba7e8861.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"inOptionName": []string{
					"naturalMakeupHairStyling",
					"neutralMakeupHairStyling",
					"dramaticMakeupHairStyling",
				},
				"weight": 7,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy uốn lọn",
					"en": "Curling iron",
				},
				"image": "https://i.upanh.org/2025/04/18/tao_mau_toc12bbc18e59bd2cf8.png",
				"inServiceName": []string{
					"graduationMakeup",
					"eventMakeup",
					"bridalMakeup",
				},
				"inOptionName": []string{
					"naturalMakeupHairStyling",
					"neutralMakeupHairStyling",
					"dramaticMakeupHairStyling",
				},
				"weight": 8,
			},
		}
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			bson.M{"name": globalConstant.SERVICE_KEY_NAME_MAKEUP},
			bson.M{
				"$set": bson.M{
					"detailService.makeup.accessories": accessories,
				},
			},
		)
		defer func() {
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
				bson.M{"name": globalConstant.SERVICE_KEY_NAME_MAKEUP},
				bson.M{"$unset": bson.M{"detailService.makeup.accessories": 1}},
			)
		}()
		body := map[string]any{
			"detailMakeup": map[string]any{
				"numberOfTaskers": 1,
				"name":            "makeup",
				"packages": []map[string]any{
					{
						"_id": "x11512543a3e28fad55c645d86353b7fe",
						"mainServices": []map[string]any{
							{
								"name": "graduationMakeup",
								"style": map[string]any{
									"name": "naturalMakeup",
									"options": []map[string]any{
										{
											"name": "naturalMakeupHairStyling",
										},
									},
								},
							},
						},
					},
				},
			},
			"serviceName": globalConstant.SERVICE_KEY_NAME_MAKEUP,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := []map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 8)
					for i, accessory := range result {
						So(accessory["title"].(map[string]interface{})["vi"], ShouldEqual, accessories[i]["title"].(map[string]interface{})["vi"])
						So(accessory["image"], ShouldEqual, accessories[i]["image"])
						So(accessory["isDisabled"], ShouldBeFalse)
						So(accessory["weight"], ShouldEqual, accessories[i]["weight"])
					}
				})
			})
		})
		// test case 2
		body = map[string]any{
			"detailMakeup": map[string]any{
				"numberOfTaskers": 1,
				"name":            "makeup",
				"packages": []map[string]any{
					{
						"_id": "x11512543a3e28fad55c645d86353b7fe",
						"mainServices": []map[string]any{
							{
								"name": "graduationMakeup",
								"style": map[string]any{
									"name": "naturalMakeup",
								},
							},
						},
					},
				},
			},
			"serviceName": globalConstant.SERVICE_KEY_NAME_MAKEUP,
		}
		reqBody, _ = json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s 2", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := []map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 8)
					oldWeight := 0.0
					newWeight := 0.0
					for i, accessory := range result {
						newWeight = accessory["weight"].(float64)
						So(newWeight, ShouldBeGreaterThan, oldWeight)
						So(accessory["title"].(map[string]interface{})["vi"], ShouldEqual, accessories[i]["title"].(map[string]interface{})["vi"])
						So(accessory["image"], ShouldEqual, accessories[i]["image"])
						So(accessory["weight"], ShouldEqual, accessories[i]["weight"])
						if i <= 5 {
							So(accessory["isDisabled"], ShouldBeFalse)
						} else {
							So(accessory["isDisabled"], ShouldBeTrue)
						}
						oldWeight = newWeight
					}
				})
			})
		})
	})
	t.Run("11.1", func(t *testing.T) {
		apiURL := "/api/v5/api-asker-vn/get-beauty-accessories"
		ResetData()
		// update service by name beauty care
		accessories := []map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Bộ dầu gội & dầu xả",
					"en": "Shampoo & conditioner set",
				},
				"image": "https://i.upanh.org/2025/04/18/goi_dau_thu_gian7266a2ec08f6976f.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"weight": 9,
			},
			{
				"title": map[string]interface{}{
					"vi": "Mặt nạ ủ tóc",
					"en": "Hair treatment mask",
				},
				"image": "https://i.upanh.org/2025/04/21/mat-na-u-toc-removebg-preview3b426ac7c29adf41.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"weight": 10,
			},
			{
				"title": map[string]interface{}{
					"vi": "Sữa rửa mặt",
					"en": "Facial cleanser",
				},
				"image": "https://i.upanh.org/2025/04/21/sua-rua-mat-removebg-preview5228aaff11a9a1e3.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"weight": 11,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy duỗi tóc",
					"en": "Hair straightener",
				},
				"image": "https://i.upanh.org/2025/04/21/may-duoi-toc-removebg-preview7885df5dba7e8861.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"inOptionName": []string{
					"hairDryingStyling",
				},
				"weight": 12,
			},
			{
				"title": map[string]interface{}{
					"vi": "Máy uốn lọn",
					"en": "Curling iron",
				},
				"image": "https://i.upanh.org/2025/04/18/tao_mau_toc12bbc18e59bd2cf8.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"inOptionName": []string{
					"hairDryingStyling",
				},
				"weight": 13,
			},
			{ // Duplication => khong lay ve
				"title": map[string]interface{}{
					"vi": "Mặt nạ ủ tóc",
					"en": "Hair treatment mask",
				},
				"image": "https://i.upanh.org/2025/04/21/mat-na-u-toc-removebg-preview3b426ac7c29adf41.png",
				"inServiceName": []string{
					"relaxingShampoo",
				},
				"inOptionName": []string{
					"hairDryingStyling",
				},
				"weight": 14,
			},
		}
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			bson.M{"name": globalConstant.SERVICE_KEY_NAME_HAIR_STYLING},
			bson.M{
				"$set": bson.M{
					"detailService.hairStyling.accessories": accessories,
				},
			},
		)
		defer func() {
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
				bson.M{"name": globalConstant.SERVICE_KEY_NAME_HAIR_STYLING},
				bson.M{"$unset": bson.M{"detailService.hairStyling.accessories": 1}},
			)
		}()
		body := map[string]any{
			"detailHairStyling": map[string]any{
				"numberOfTaskers": 1,
				"packages": []map[string]any{
					{
						"_id": "x11512543a3e28fad55c645d86353b7fe",
						"mainServices": []map[string]any{
							{
								"name": "relaxingShampoo",
								"options": []map[string]any{
									{
										"name": "hairDryingStyling",
									},
								},
							},
						},
					},
				},
			},
			"serviceName": globalConstant.SERVICE_KEY_NAME_HAIR_STYLING,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := []map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 5)
					for i, accessory := range result {
						So(accessory["title"].(map[string]interface{})["vi"], ShouldEqual, accessories[i]["title"].(map[string]interface{})["vi"])
						So(accessory["image"], ShouldEqual, accessories[i]["image"])
						So(accessory["isDisabled"], ShouldBeFalse)
						So(accessory["weight"], ShouldEqual, accessories[i]["weight"])
					}
				})
			})
		})
		// test case 2
		body = map[string]any{
			"detailHairStyling": map[string]any{
				"numberOfTaskers": 1,
				"packages": []map[string]any{
					{
						"_id": "x11512543a3e28fad55c645d86353b7fe",
						"mainServices": []map[string]any{
							{
								"name": "relaxingShampoo",
							},
						},
					},
				},
			},
			"serviceName": globalConstant.SERVICE_KEY_NAME_HAIR_STYLING,
		}
		reqBody, _ = json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s 2", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := []map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(len(result), ShouldEqual, 5)
					oldWeight := 0.0
					newWeight := 0.0
					for i, accessory := range result {
						newWeight = accessory["weight"].(float64)
						So(newWeight, ShouldBeGreaterThan, oldWeight)
						So(accessory["title"].(map[string]interface{})["vi"], ShouldEqual, accessories[i]["title"].(map[string]interface{})["vi"])
						So(accessory["image"], ShouldEqual, accessories[i]["image"])
						So(accessory["weight"], ShouldEqual, accessories[i]["weight"])
						if i <= 2 {
							So(accessory["isDisabled"], ShouldBeFalse)
						} else {
							So(accessory["isDisabled"], ShouldBeTrue)
						}
						oldWeight = newWeight
					}
				})
			})
		})
	})
}
