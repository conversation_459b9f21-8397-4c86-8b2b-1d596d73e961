/*
* @File: 17_serviceChannel_test.go
* @Description: Handler function, case test
* @CreatedAt: 16/11/2020
* @Author: ngoctb
* @UpdatedAt: 17/12/2020
* @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	"go.mongodb.org/mongo-driver/bson"
)

func TestS_erviceChannel(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := subPath + "/get-service-channel"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode empty", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "HA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		apiURL := subPath + "/get-service-channel"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":       "0935115102",
				"name":        "Asker 01",
				"type":        globalConstant.USER_TYPE_TASKER,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			},
		})
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{"tester": []string{"0935115102", "12ZASEAS"}},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":  "0935115102",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := []map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(len(m), ShouldEqual, 1)
				temp := m[0]["text"].(map[string]interface{})
				So(temp["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
			})
		})
		UpdateSettings(bson.M{
			"$unset": bson.M{"tester": 1},
		})
	})
	t.Run("3", func(t *testing.T) {
		apiURL := subPath + "/get-service-channel"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":       "0935115103",
				"name":        "Asker 01",
				"type":        globalConstant.USER_TYPE_TASKER,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			},
		})

		serviceChannel := GetServiceChannel(globalConstant.SERVICE_NAME_AIR_CONDITIONER, bson.M{"_id": 1})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":  "0935115103",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := []map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				for _, v := range m {
					if v["_id"].(string) == serviceChannel.XId {
						res1, _ := json.Marshal(v["text"])
						text := map[string]interface{}{}
						json.Unmarshal(res1, &text)
						So(text["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
					}
				}
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		apiURL := subPath + "/add-user-to-service-channel"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if serviceId empty", func() {
				body := map[string]interface{}{
					"userId":    "132123123",
					"serviceId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		apiURL := subPath + "/add-user-to-service-channel"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if serviceId empty", func() {
				body := map[string]interface{}{
					"userId":    "132123123",
					"serviceId": "123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		apiURL := subPath + "/add-user-to-service-channel"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":       "0935115102",
					"name":        "Asker 01",
					"type":        globalConstant.USER_TYPE_TASKER,
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				},
				{
					"phone":       "0935115103",
					"name":        "Asker 01",
					"type":        globalConstant.USER_TYPE_TASKER,
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				},
			})
			var s *modelService.Service
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1}, &s)
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":    "0935115103",
					"serviceId": s.XId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the http response", func() {
					So(res.Code, ShouldEqual, 200)
				})
				Convey("Check the database", func() {
					var serviceChannel *modelServiceChannel.ServiceChannel
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": s.XId}, bson.M{"taskerList": 1}, &serviceChannel)
					So(serviceChannel.TaskerList[0], ShouldEqual, "0935115102")
					So(serviceChannel.TaskerList[1], ShouldEqual, "0935115103")
				})
			})
		})
	})
}
