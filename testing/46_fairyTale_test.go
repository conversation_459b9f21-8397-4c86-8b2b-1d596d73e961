/*
* @File: 15_reward_test.go
* @Description: Handler function, case test
* @CreatedAt: 20/11/2020
* @Author: vinhnt
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

func TestFairyTale(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate /get-list-fairy-tale")
		apiUrl := "/api/v3/api-asker-vn/get-list-fairy-tale"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
				"blackList":       []string{"0823456717"},
			},
		})

		CreateFairyTale([]map[string]interface{}{
			{
				"_id":   "1",
				"image": "https://i.imgur.com/gsmmy47.png",
				"title": map[string]interface{}{
					"vi": "Sự tích thánh gióng",
					"en": "Sự tích thánh gióng",
					"ko": "Sự tích thánh gióng",
					"th": "Sự tích thánh gióng",
					"id": "Sự tích thánh gióng",
				},
				"audioUrl": map[string]interface{}{
					"vi": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"en": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"ko": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"th": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"id": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
				},
				"description": map[string]interface{}{
					"vi": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"en": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"ko": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"th": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"id": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
				},
			}, {
				"_id":   "2",
				"image": "https://i.imgur.com/gsmmy47.png",
				"title": map[string]interface{}{
					"vi": "Sự tích sọ dừa",
					"en": "Sự tích sọ dừa",
					"ko": "Sự tích sọ dừa",
					"th": "Sự tích sọ dừa",
					"id": "Sự tích sọ dừa",
				},
				"audioUrl": map[string]interface{}{
					"vi": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"en": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"ko": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"th": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"id": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
				},
				"description": map[string]interface{}{
					"vi": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"en": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"ko": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"th": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
					"id": "Ngày xửa ngày xưa có mẹ bán dưa mẹ ngồi mẹ...",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResults := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResults)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResults), ShouldEqual, 2)
					for _, v := range respResults {
						So(v["_id"], ShouldNotBeNil)
						So(v["image"], ShouldNotBeNil)
						So(v["title"], ShouldNotBeNil)
						So(v["description"], ShouldNotBeNil)
						So(v["audioUrl"], ShouldNotBeNil)
					}
				})
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Validate /get-fairy-tale-detail")
		apiUrl := "/api/v3/api-asker-vn/get-fairy-tale-detail"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
				"blackList":       []string{"0823456717"},
			},
		})

		CreateFairyTale([]map[string]interface{}{
			{
				"_id":   "1",
				"image": "https://i.imgur.com/gsmmy47.png",
				"title": map[string]interface{}{
					"vi": "Sự tích thánh gióng",
					"en": "Sự tích thánh gióng",
					"ko": "Sự tích thánh gióng",
					"th": "Sự tích thánh gióng",
					"id": "Sự tích thánh gióng",
				},
				"audioUrl": map[string]interface{}{
					"vi": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"en": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"ko": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"th": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"id": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
				},
				"description": map[string]interface{}{
					"vi": "Ngày xửa ngày xưa có một bà mẹ chửa 3 năm chưa đẻ",
					"en": "Ngày xửa ngày xưa có một bà mẹ chửa 3 năm chưa đẻ",
					"ko": "Ngày xửa ngày xưa có một bà mẹ chửa 3 năm chưa đẻ",
					"th": "Ngày xửa ngày xưa có một bà mẹ chửa 3 năm chưa đẻ",
					"id": "Ngày xửa ngày xưa có một bà mẹ chửa 3 năm chưa đẻ",
				},
			}, {
				"_id":   "2",
				"image": "https://i.imgur.com/gsmmy47.png",
				"title": map[string]interface{}{
					"vi": "Sự tích sọ dừa",
					"en": "Sự tích sọ dừa",
					"ko": "Sự tích sọ dừa",
					"th": "Sự tích sọ dừa",
					"id": "Sự tích sọ dừa",
				},
				"audioUrl": map[string]interface{}{
					"vi": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"en": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"ko": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"th": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"id": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
				},
				"description": map[string]interface{}{
					"vi": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"en": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"ko": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"th": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"id": "Ngày xửa ngày xưa có một cặp vợ chồng già",
				},
			}, {
				"_id":   "3",
				"image": "https://i.imgur.com/gsmmy47.png",
				"title": map[string]interface{}{
					"vi": "Sự tích cây tre trăm đốt",
					"en": "Sự tích cây tre trăm đốt",
					"ko": "Sự tích cây tre trăm đốt",
					"th": "Sự tích cây tre trăm đốt",
					"id": "Sự tích cây tre trăm đốt",
				},
				"audioUrl": map[string]interface{}{
					"vi": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"en": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"ko": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"th": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
					"id": "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3",
				},
				"description": map[string]interface{}{
					"vi": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"en": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"ko": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"th": "Ngày xửa ngày xưa có một cặp vợ chồng già",
					"id": "Ngày xửa ngày xưa có một cặp vợ chồng già",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{"id": "1"}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["_id"], ShouldEqual, "1")
					So(respResult["image"], ShouldEqual, "https://i.imgur.com/gsmmy47.png")
					So(respResult["title"].(map[string]interface{})["vi"], ShouldEqual, "Sự tích thánh gióng")
					So(respResult["audioUrl"].(map[string]interface{})["vi"], ShouldEqual, "https://codeskulptor-demos.commondatastorage.googleapis.com/GalaxyInvaders/theme_01.mp3")
					So(respResult["description"].(map[string]interface{})["vi"], ShouldEqual, "Ngày xửa ngày xưa có một bà mẹ chửa 3 năm chưa đẻ")
					So(len(respResult["relatedFairyTales"].([]any)), ShouldEqual, 2)
					for _, v := range respResult["relatedFairyTales"].([]any) {
						So(v.(map[string]interface{})["_id"], ShouldBeIn, []string{"2", "3"})
						So(v.(map[string]interface{})["image"], ShouldEqual, "https://i.imgur.com/gsmmy47.png")
						So(v.(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldBeIn, []string{"Sự tích sọ dừa", "Sự tích cây tre trăm đốt"})
					}
				})
			})
		})
	})
}
