/*
* @File: 15_reward_test.go
* @Description: Handler function, case test
* @CreatedAt: 20/11/2020
* @Author: vinhnt
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelVoteFavTasker "gitlab.com/btaskee/go-services-model-v2/grpcmodel/voteFavTasker"
	"go.mongodb.org/mongo-driver/bson"
)

func TestVoteFavTasker(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate /get-taskers-for-vote")
		// get-taskers-for-vote
		apiUrl := "/api/v3/api-asker-vn/get-taskers-for-vote"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 132213,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	// t.Run("2", func(t *testing.T) {
	// 	log.Println("==================================== GetTaskersForVote")
	// 	apiUrl := "/api/v3/api-asker-vn/get-taskers-for-vote"
	// 	ResetData()

	// 	//Create User
	// 	CreateUser([]map[string]interface{}{
	// 		{
	// 			"phone":           "0834567890",
	// 			"name":            "Asker 01",
	// 			"type":            globalConstant.USER_TYPE_ASKER,
	// 			"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
	// 		}, {
	// 			"phone":           "0823456789",
	// 			"name":            "Tasker 01",
	// 			"type":            globalConstant.USER_TYPE_TASKER,
	// 			"taskDone":        10,
	// 			"isPremiumTasker": true,
	// 			"avgRating":       5.0,
	// 		}, {
	// 			"phone":           "0823456710",
	// 			"name":            "Tasker 012",
	// 			"type":            globalConstant.USER_TYPE_TASKER,
	// 			"isPremiumTasker": true,
	// 			"taskDone":        10,
	// 			"avgRating":       5.0,
	// 		}, {
	// 			"phone":     "0823456713",
	// 			"name":      "Tasker 013",
	// 			"type":      globalConstant.USER_TYPE_TASKER,
	// 			"taskDone":  20,
	// 			"avgRating": 4.2,
	// 		}, {
	// 			"phone":   "0823456714",
	// 			"name":    "Tasker 014",
	// 			"type":    globalConstant.USER_TYPE_TASKER,
	// 			"isoCode": "TH",
	// 		}, {
	// 			"phone":     "0823456715",
	// 			"name":      "Tasker 015",
	// 			"type":      globalConstant.USER_TYPE_TASKER,
	// 			"avgRating": 3.2,
	// 		}, {
	// 			"phone":        "0823456716",
	// 			"name":         "Tasker 016",
	// 			"type":         globalConstant.USER_TYPE_TASKER,
	// 			"avgRating":    3.7,
	// 			"status":       globalConstant.USER_STATUS_BLOCKED,
	// 			"lastLockedAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
	// 		},
	// 		{
	// 			"phone": "0823456717",
	// 			"name":  "Tasker 015",
	// 			"type":  globalConstant.USER_TYPE_TASKER,
	// 		}, {
	// 			"phone":               "0823456718",
	// 			"name":                "Tasker 018",
	// 			"type":                globalConstant.USER_TYPE_TASKER,
	// 			"avgRating":           3.7,
	// 			"status":              globalConstant.USER_STATUS_BLOCKED,
	// 			"isIndefiniteLocking": true,
	// 			"lastLockedAt":        globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
	// 		},
	// 	})

	// 	//Create Task
	// 	CreateTask([]map[string]interface{}{
	// 		{
	// 			"askerPhone":  "0834567890",
	// 			"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
	// 			"description": "Desc Test 01",
	// 			"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
	// 			"status":      globalConstant.TASK_STATUS_DONE,
	// 			"acceptedTasker": []map[string]interface{}{
	// 				{
	// 					"taskerId": "0823456713",
	// 				},
	// 			},
	// 			"date": "2020,11,02,15,20",
	// 		}, {
	// 			"askerPhone":  "0834567890",
	// 			"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
	// 			"description": "Desc Test 02",
	// 			"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
	// 			"status":      globalConstant.TASK_STATUS_DONE,
	// 			"acceptedTasker": []map[string]interface{}{
	// 				{
	// 					"taskerId": "0823456789",
	// 				},
	// 			},
	// 		},
	// 	})

	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
	// 		body := map[string]interface{}{
	// 			"userId": "0834567890",
	// 		}
	// 		reqBody, _ := json.Marshal(body)
	// 		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 		resp := httptest.NewRecorder()

	// 		Convey("When the request is handled by the Router", func() {
	// 			service.NewRouter().ServeHTTP(resp, req)

	// 			Convey("Then check the response to test Get Favorite Tasker", func() {
	// 				respResultM := map[string][]map[string]interface{}{}
	// 				bytes, _ := io.ReadAll(resp.Body)
	// 				json.Unmarshal(bytes, &respResultM)
	// 				So(resp.Code, ShouldEqual, 200)
	// 				respResult := respResultM["listFavTasker"]
	// 				So(len(respResult), ShouldEqual, 4)
	// 				So(respResult[0]["name"], ShouldEqual, "Tasker 012")
	// 				So(respResult[0]["_id"], ShouldEqual, "0823456710")
	// 				So(respResult[0]["taskDone"], ShouldEqual, 10)
	// 				So(respResult[1]["name"], ShouldEqual, "Tasker 013")
	// 				So(respResult[1]["_id"], ShouldEqual, "0823456713")
	// 				So(respResult[1]["taskDone"], ShouldEqual, 20)
	// 				So(respResult[2]["name"], ShouldEqual, "Tasker 016")
	// 				So(respResult[2]["_id"], ShouldEqual, "0823456716")
	// 				So(respResult[3]["name"], ShouldEqual, "Tasker 015")
	// 				So(respResult[3]["_id"], ShouldEqual, "0823456715")
	// 			})
	// 		})
	// 	})
	// })

	t.Run("2.1", func(t *testing.T) {
		log.Println("==================================== GetTaskersForVote")
		apiUrl := "/api/v3/api-asker-vn/get-taskers-for-vote"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
				"blackList":       []string{"0823456717"},
			}, {
				"phone":           "0823456789",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
				"avgRating":       5.0,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
				"taskDone":        10,
				"avgRating":       5.0,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"taskDone":  20,
				"avgRating": 4.2,
			}, {
				"phone":   "0823456714",
				"name":    "Tasker 014",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": "TH",
			}, {
				"phone":     "0823456715",
				"name":      "Tasker 015",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.2,
			}, {
				"phone":        "0823456716",
				"name":         "Tasker 016",
				"type":         globalConstant.USER_TYPE_TASKER,
				"avgRating":    3.7,
				"status":       globalConstant.USER_STATUS_BLOCKED,
				"lastLockedAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			},
			{
				"phone": "0823456717",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone":               "0823456718",
				"name":                "Tasker 018",
				"type":                globalConstant.USER_TYPE_TASKER,
				"avgRating":           3.7,
				"status":              globalConstant.USER_STATUS_BLOCKED,
				"isIndefiniteLocking": true,
				"lastLockedAt":        globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			}, {
				"phone":     "0823456719",
				"name":      "Tasker 019",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.7,
				"gender":    "MALE",
			}, {
				"phone":     "0823456720",
				"name":      "Tasker 020",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.7,
				"gender":    "FEMALE",
				"company": map[string]interface{}{
					"companyId": "123",
				},
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456710",
					},
				},
				"date": "2020,11,02,15,20",
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456713",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456714",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456715",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456716",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456717",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456718",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456719",
					},
				},
			}, {
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456720",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResultM := map[string][]map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					respResult := respResultM["listFavTasker"]
					So(len(respResult), ShouldEqual, 4)
					So(respResult[0]["name"], ShouldEqual, "Tasker 012")
					So(respResult[0]["_id"], ShouldEqual, "0823456710")
					So(respResult[0]["taskDone"], ShouldEqual, 10)
					So(respResult[1]["name"], ShouldEqual, "Tasker 013")
					So(respResult[1]["_id"], ShouldEqual, "0823456713")
					So(respResult[1]["taskDone"], ShouldEqual, 20)
					So(respResult[2]["name"], ShouldEqual, "Tasker 016")
					So(respResult[2]["_id"], ShouldEqual, "0823456716")
					So(respResult[3]["name"], ShouldEqual, "Tasker 015")
					So(respResult[3]["_id"], ShouldEqual, "0823456715")
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetTaskersForVote")
		apiUrl := "/api/v3/api-asker-vn/get-taskers-for-vote"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
			}, {
				"phone":           "0823456789",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
				"avgRating":       5.0,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
				"taskDone":        10,
				"avgRating":       5.0,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"taskDone":  20,
				"avgRating": 4.2,
			}, {
				"phone":   "0823456714",
				"name":    "Tasker 014",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": "TH",
			}, {
				"phone":     "0823456715",
				"name":      "Tasker 015",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.2,
			}, {
				"phone":        "0823456716",
				"name":         "Tasker 016",
				"type":         globalConstant.USER_TYPE_TASKER,
				"avgRating":    3.7,
				"status":       globalConstant.USER_STATUS_BLOCKED,
				"lastLockedAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			},
			{
				"phone": "0823456717",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone":               "0823456718",
				"name":                "Tasker 018",
				"type":                globalConstant.USER_TYPE_TASKER,
				"avgRating":           3.7,
				"status":              globalConstant.USER_STATUS_BLOCKED,
				"isIndefiniteLocking": true,
				"lastLockedAt":        globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			},
		})
		CreateVoteFavTasker([]map[string]interface{}{
			{
				"taskerId": "0823456789",
				"askerId":  "0834567890",
			},
		},
		)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResultM := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					respResult := respResultM["taskerVoted"].(map[string]interface{})
					So(respResult["name"], ShouldEqual, "Tasker 01")
					So(respResult["_id"], ShouldEqual, "0823456789")
					So(respResult["taskDone"], ShouldEqual, 10)
					So(respResult["avgRating"], ShouldEqual, 5.0)
					So(respResultM["listFavTasker"], ShouldBeNil)
				})
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Validate Vote tasker")
		// vote-tasker
		apiUrl := "/api/v3/api-asker-vn/vote-tasker"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 132213,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== GetTaskersForVote")
		apiUrl := "/api/v3/api-asker-vn/vote-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
			}, {
				"phone":           "0823456789",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
				"avgRating":       5.0,
			},
		})
		CreateVoteFavTasker([]map[string]interface{}{
			{
				"taskerId": "0823456789",
				"askerId":  "0834567890",
			},
		},
		)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567890",
				"taskerId": "0823456781",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					bytes, _ := io.ReadAll(resp.Body)
					var respResult map[string]map[string]interface{}
					json.Unmarshal(bytes, &respResult)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ASKER_VOTED_FAV_TASKER.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ASKER_VOTED_FAV_TASKER.Message)
				})
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== GetTaskersForVote")
		apiUrl := "/api/v3/api-asker-vn/vote-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716", "0823456718"},
			}, {
				"phone":           "0823456789",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
				"avgRating":       5.0,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567890",
				"taskerId": "0823456789",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["reward"]["vi"], ShouldEqual, "25 bPoints")

					var voteFavTasker *modelVoteFavTasker.VoteFavTasker
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_VOTE_FAV_TASKER[local.ISO_CODE], bson.M{"askerId": "0834567890"}, bson.M{}, &voteFavTasker)
					So(voteFavTasker.TaskerId, ShouldEqual, "0823456789")

					asker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{})
					So(asker.Point, ShouldEqual, 25)
					So(asker.RankInfo.Point, ShouldEqual, 25)

					var pointTransaction *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{}, &pointTransaction)
					So(pointTransaction.Point, ShouldEqual, 25)
					So(pointTransaction.Type, ShouldEqual, "D")
					So(pointTransaction.NewPoint, ShouldEqual, 25)
					So(pointTransaction.Source.Name, ShouldEqual, "VOTE_FAV_TASKER")

					var notification *modelNotification.Notification
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{}, &notification)
					So(notification.Description, ShouldEqual, "Truy cập kho ưu đãi bRewards để đổi và sử dụng những phần quà hấp dẫn")
					So(notification.Title, ShouldEqual, "Bình chọn thành công! Bạn được cộng 25 bPoints")
					So(notification.Type, ShouldEqual, 32)
					So(notification.NavigateTo, ShouldEqual, "bPointsHistory")
				})
			})
		})
	})
}
