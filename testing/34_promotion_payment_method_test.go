package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
)

func TestPromotionPaymentMethod(t *testing.T) {
	apiURL := "/api/v3/api-asker-vn/get-promotion-payment-method"

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== User id required")

		body := map[string]interface{}{}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Check request when userId blank", func() {
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Iso code required")

		body := map[string]interface{}{
			"userId": "dump",
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Check request when userId blank", func() {
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
		})
	})
}
