/*
 * @File: 11_paymentCard_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 30/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelPaymentCard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentCard"
	"go.mongodb.org/mongo-driver/bson"
)

func TestPaymentCard(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate SetPaymentCardDefault")
		// SetPaymentCardDefault
		apiSetPaymentCardDefault := "/api/v3/api-asker-vn/set-payment-card-default"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSetPaymentCardDefault), t, func() {
			Convey("Check request when cardId blank", func() {
				body := map[string]interface{}{
					"cardId":    "",
					"isDefault": true,
					"userId":    "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSetPaymentCardDefault, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CARD_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CARD_ID_REQUIRED.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"cardId":    "123",
					"isDefault": true,
					"userId":    "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSetPaymentCardDefault, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"cardId":    12132,
					"isDefault": true,
					"userId":    123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSetPaymentCardDefault, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		log.Println("==================================== SetPaymentCardDefault")
		// SetPaymentCardDefault
		apiSetPaymentCardDefault := "/api/v3/api-asker-vn/set-payment-card-default"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create PaymentCard
		ids := CreatePaymentCard([]map[string]interface{}{
			{
				"userId":        "0834567890",
				"number":        "5373",
				"expiryMonth":   "8",
				"expiryYear":    "2025",
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
			}, {
				"userId":        "0834567890",
				"number":        "2467",
				"expiryMonth":   "8",
				"expiryYear":    "2027",
				"holderName":    "Dr. DisRespect",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSetPaymentCardDefault), t, func() {
			body := map[string]interface{}{
				"cardId":    ids[0],
				"isDefault": true,
				"userId":    "0834567890",
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiSetPaymentCardDefault, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Set Payment Card Default Case Set Default = True", func() {
					So(resp.Code, ShouldEqual, 200)
				})
			})

			Convey("Then check database Set Payment Card Default Case Set Default = True", func() {
				var result []*modelPaymentCard.PaymentCard
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], bson.M{"_id": bson.M{"$in": []string{ids[0], ids[1]}}}, bson.M{"_id": 1, "isDefault": 1}, &result)
				for _, v := range result {
					if v.XId == ids[0] {
						So(v.IsDefault, ShouldBeTrue)
					} else {
						So(v.IsDefault, ShouldBeFalse)
					}
				}
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSetPaymentCardDefault), t, func() {
			body := map[string]interface{}{
				"cardId":    ids[0],
				"isDefault": false,
				"userId":    "0834567890",
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiSetPaymentCardDefault, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Set Payment Card Default Case Set Default = False", func() {
					So(resp.Code, ShouldEqual, 200)
				})
			})

			Convey("Then check database Set Payment Card Default Case Set Default = False", func() {
				var result *modelPaymentCard.PaymentCard
				globalDataAccess.GetOneById(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], ids[0], bson.M{"isDefault": 1}, &result)
				So(result.IsDefault, ShouldBeFalse)
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Validate GetListCard")
		// GetListCard
		apiGetListCard := "/api/v3/api-asker-vn/get-list-cards"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListCard), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "HA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetListCard")
		// GetListCard
		apiGetListCard := "/api/v3/api-asker-vn/get-list-cards"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create PaymentCard
		CreatePaymentCard([]map[string]interface{}{
			{
				"userId":        "0834567890",
				"number":        "5373",
				"expiryMonth":   "8",
				"expiryYear":    "2025",
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
				"isoCode":       local.ISO_CODE,
			}, {
				"userId":        "0834567890",
				"number":        "2467",
				"expiryMonth":   "8",
				"expiryYear":    "2027",
				"holderName":    "Dr. DisRespect",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
				"isoCode":       local.ISO_CODE,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListCard), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Card", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult[0]["number"], ShouldEqual, "5373")
					So(respResult[0]["expiryMonth"], ShouldEqual, "8")
					So(respResult[0]["expiryYear"], ShouldEqual, "2025")
					So(respResult[0]["holderName"], ShouldEqual, "Dr. Nickolas Mills")
					So(respResult[0]["type"], ShouldEqual, "visa")

					So(respResult[1]["number"], ShouldEqual, "2467")
					So(respResult[1]["expiryMonth"], ShouldEqual, "8")
					So(respResult[1]["expiryYear"], ShouldEqual, "2027")
					So(respResult[1]["holderName"], ShouldEqual, "Dr. DisRespect")
					So(respResult[1]["type"], ShouldEqual, "visa")
				})
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== GetListCard if cardPaymentConfig nil")
		// GetListCard
		apiGetListCard := "/api/v3/api-asker-vn/get-list-cards"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create PaymentCard
		CreatePaymentCard([]map[string]interface{}{
			{
				"userId":        "0834567890",
				"number":        "5373",
				"expiryMonth":   "8",
				"expiryYear":    "2025",
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
				"isoCode":       local.ISO_CODE,
			}, {
				"userId":        "0834567890",
				"number":        "2467",
				"expiryMonth":   "8",
				"expiryYear":    "2027",
				"holderName":    "Dr. DisRespect",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
				"isoCode":       local.ISO_CODE,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListCard), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Card", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
				})
			})
		})
	})
	// Check trường hợp enableCardPayment:false nhưng vẫn để khách hàng lấy danh sách thẻ đã tích hợp.
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== GetListCard if cardPaymentConfig nil")
		// GetListCard
		apiGetListCard := "/api/v3/api-asker-vn/get-list-cards"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create PaymentCard
		CreatePaymentCard([]map[string]interface{}{
			{
				"userId":        "0834567890",
				"number":        "2467",
				"expiryMonth":   "8",
				"expiryYear":    "2027",
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
			},
		})

		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_CARD_PAYMENT_CONFIG[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"$set": bson.M{"enableCardPayment": false}})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListCard), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Card", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)

					So(respResult, ShouldNotBeNil)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["number"], ShouldEqual, "2467")
					So(respResult[0]["expiryMonth"], ShouldEqual, "8")
					So(respResult[0]["expiryYear"], ShouldEqual, "2027")
					So(respResult[0]["holderName"], ShouldEqual, "Dr. Nickolas Mills")
					So(respResult[0]["type"], ShouldEqual, "visa")
				})
			})
		})
	})

}
