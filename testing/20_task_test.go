/*
* @File: 20_task_test.go
* @Description: Handler function, case test
* @CreatedAt: 29/10/2020
* @Author: vinhnt
* @UpdatedAt: 31/12/2020
* @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelPricingResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComplaint"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestT_ask(t *testing.T) {
	CHECK_TASKER_CONFLICT := "/api/v3/api-asker-vn/check-tasker-conflict-time"
	GET_TASKER_FREE_TIME := "/api/v3/api-asker-vn/get-tasker-free-time"
	t.Run("1", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")
		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId": "",
				"taskId": "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when taskId blank", t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId": 1323,
				"taskId": 12323132,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})

	})
	t.Run("2", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{
					"*********",
				},
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"fromPartner": globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			},
		})
		//Create FATransaction
		CreateFATransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"value": ids[0],
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_CANCEL_TASK,
				},
				"amount": 123,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["cancellationFee"], ShouldEqual, 123)
					So(respResult["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_VN)
					So(respResult["acceptedTasker"], ShouldNotBeNil)
					So(respResult["fromPartner"], ShouldEqual, globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP)

					var resultAcceptedTasker map[string][]map[string]interface{}
					json.Unmarshal(bytes, &resultAcceptedTasker)
					So(len(resultAcceptedTasker["acceptedTasker"]), ShouldEqual, 1)
					So(resultAcceptedTasker["acceptedTasker"][0]["taskDone"], ShouldEqual, 0)
					So(resultAcceptedTasker["acceptedTasker"][0]["isFavouriteTasker"], ShouldBeTrue)
				})
			})
		})
	})

	t.Run("2.2", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail with refund")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"isPremium":       true,
				"cancellationFee": 100,
			},
		})

		// CreateRefundRequest
		CreateRefundRequest([]map[string]interface{}{
			{
				"type":          "PREPAY_TASK",
				"amount":        200.0,
				"paymentMethod": "TRUE_MONEY",
				"currency":      "THB",
				"status":        "NEW",
				"userId":        "xxx",
				"taskId":        ids[0],
				"cancelTaskFee": 10,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["cancellationFee"], ShouldEqual, 10)
					So(respResult["isPremium"], ShouldBeTrue)
					refundResult := make(map[string]interface{})
					refundResultData, _ := json.Marshal(respResult["refund"])
					json.Unmarshal(refundResultData, &refundResult)
					So(refundResult["status"], ShouldEqual, globalConstant.REFUND_REQUEST_STATUS_NEW)
					So(refundResult["amount"], ShouldEqual, 200)
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{
				"userId":    "",
				"serviceId": "1223",
				"taskDate":  "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId":    "",
				"serviceId": "123123",
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1),
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when serviceId blank", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": "",
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1),
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.Message)
		})

		Convey("Check request when taskDate nil", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": "123",
				"taskDate":  nil,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATE_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATE_REQUIRED.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId":    123213,
				"serviceId": 123231,
				"taskDate":  nil,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})

	})
	t.Run("4", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Format(time.RFC3339),
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "true")
				})
			})
		})
	})

	t.Run("4.1", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": "abcxyzxxx",
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1),
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "false")
				})
			})
		})
	})

	t.Run("4.2", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime Serivce PatientCare")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_PATIENT_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_PATIENT_CARE}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Format(time.RFC3339),
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "true")
				})
			})
		})
	})

	t.Run("4.3", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime Serivce ElderlyCare")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_ELDERLY_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_ELDERLY_CARE}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Format(time.RFC3339),
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "true")
				})
			})
		})
	})

	t.Run("4.4", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime Serivce ElderlyCare with isoCode case true")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_ELDERLY_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"isoCode":     local.ISO_CODE,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_ELDERLY_CARE}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Format(time.RFC3339),
				"isoCode":   local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "true")
				})
			})
		})
	})

	t.Run("4.5", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime Serivce ElderlyCare with isoCode case false")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_ELDERLY_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"isoCode":     local.ISO_CODE,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_ELDERLY_CARE}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Format(time.RFC3339),
				"isoCode":   globalConstant.ISO_CODE_TH,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "false")
				})
			})
		})
	})
	t.Run("4.7", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime Serivce UPHOLSTERY with isoCode case false")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_UPHOLSTERY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"isoCode":     local.ISO_CODE,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_UPHOLSTERY}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Format(time.RFC3339),
				"isoCode":   local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "true")
				})
			})
		})
	})

	t.Run("4.8", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime Serivce GROCERY_ASSISTANT with isoCode case false")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"isoCode":     local.ISO_CODE,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1).Add(1 * time.Hour).Format(time.RFC3339),
				"isoCode":   local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case TRUE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "true")
				})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		// CheckTaskSameTime
		apiCheckTaskSameTime := "/api/v3/api-asker-vn/check-task-sametime"
		log.Println("==================================== CheckTaskSameTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckTaskSameTime), t, func() {
			// Params request
			bodySe := map[string]interface{}{
				"userId":    "**********",
				"serviceId": result["_id"],
				"taskDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 0),
			}
			reqBody, _ := json.Marshal(bodySe)
			req := httptest.NewRequest("POST", apiCheckTaskSameTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Check Task Same Time Case FALSE", func() {
					bytes, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(bytes), ShouldEqual, "false")
				})
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		// GetLastPostTask
		apiGetLastPostTask := "/api/v3/api-asker-vn/get-last-post-task"
		log.Println("==================================== Validate GetLastPostTask")
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId":    "",
				"serviceId": "123",
				"isoCode":   local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when serviceId blank", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": "",
				"isoCode":   local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.Message)
		})

		Convey("Check request when isoCode blank", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": "123",
				"isoCode":   "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
		})
		Convey("Check request when isoCode incorrect", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"serviceId": "123",
				"isoCode":   "HH",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId":    1231232312,
				"serviceId": "123",
				"isoCode":   "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})

	})
	t.Run("7", func(t *testing.T) {
		// GetLastPostTask
		apiGetLastPostTask := "/api/v3/api-asker-vn/get-last-post-task"
		log.Println("==================================== GetLastPostTask")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		body := map[string]interface{}{
			"userId":    "**********",
			"serviceId": result["_id"],
			"isoCode":   local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Last Post Task", func() {
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["description"], ShouldEqual, "Desc Test")
				So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResult["contactName"], ShouldEqual, "Asker 01")
				So(respResult["phone"], ShouldEqual, "**********")
				So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
				So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["serviceId"], ShouldEqual, result["_id"])
				So(respResult["serviceText"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
				So(respResult["payment"].(map[string]interface{})["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_CREDIT)
			})
		})
	})
	t.Run("7.2", func(t *testing.T) {
		// GetLastPostTask
		apiGetLastPostTask := "/api/v3/api-asker-vn/get-last-post-task"
		log.Println("==================================== GetLastPostTask with blacklist payment")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
				},
				"isTetBooking": true,
			},
		})

		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER},
			map[string]interface{}{
				"$set": map[string]interface{}{
					"tetBookingDates": map[string]interface{}{
						"fromDate":   globalLib.ParseDateFromString("2022-11-09T00:00:00.000+07:00", local.TimeZone),
						"toDate":     globalLib.ParseDateFromString("2023-01-31T23:59:59.999+07:00", local.TimeZone),
						"minVersion": "3.2.1",
					},
				},
			},
		)
		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		body := map[string]interface{}{
			"userId":    "**********",
			"serviceId": result["_id"],
			"isoCode":   local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Last Post Task", func() {
				UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"$unset": bson.M{"tetBookingDates": 1}})
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["description"], ShouldEqual, "Desc Test")
				So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResult["contactName"], ShouldEqual, "Asker 01")
				So(respResult["phone"], ShouldEqual, "**********")
				So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
				So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["serviceId"], ShouldEqual, result["_id"])
				So(respResult["serviceText"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
				So(respResult["payment"].(map[string]interface{})["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_CASH)
				So(respResult["isTetBooking"], ShouldBeNil)
			})
		})
	})

	t.Run("7.3", func(t *testing.T) {
		// GetLastPostTask
		apiGetLastPostTask := "/api/v3/api-asker-vn/get-last-post-task"
		log.Println("==================================== GetLastPostTask check requirement")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CASH,
				},
				"requirements": []map[string]interface{}{
					{
						"type": 1,
					},
				},
				"isTetBooking": true,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING},
			bson.M{
				"$set": bson.M{
					"tip.requirements": []map[string]interface{}{
						{
							"type":     1.0,
							"duration": 1.0,
							"text": map[string]interface{}{
								"vi": "vi text",
								"en": "en text",
							},
						},
					},
					"tetBookingDates": map[string]interface{}{
						"fromDate": currentTime.AddDate(0, 0, -10),
						"toDate":   currentTime.AddDate(0, 0, 10),
					},
					"durationByArea": []map[string]interface{}{
						{
							"duration":      2,
							"area":          55,
							"numberOfRooms": 2,
						},
						{
							"duration":      3,
							"area":          65,
							"numberOfRooms": 2,
						},
					},
				},
			},
		)

		//Get Service
		s := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		body := map[string]interface{}{
			"userId":    "**********",
			"serviceId": s.XId,
			"isoCode":   local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$unset": bson.M{"tip.requirements": 1, "tetBookingDates": 1}})
			Convey("Then check the response to test Get Last Post Task", func() {
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["requirements"], ShouldNotBeNil)
				So(respResult["isTetBooking"], ShouldBeTrue)
				// Update to task requirements
				requirementData, _ := json.Marshal(respResult["requirements"])
				var requirements []map[string]interface{}
				json.Unmarshal(requirementData, &requirements)

				So(len(requirements), ShouldEqual, 1)
				So(requirements[0]["type"], ShouldEqual, 1)
				So(requirements[0]["duration"], ShouldEqual, 1)

				// Update to task requirements
				var requirementsText []map[string]map[string]interface{}
				json.Unmarshal(requirementData, &requirementsText)
				So(len(requirementsText), ShouldEqual, 1)
				So(requirementsText[0]["text"]["en"], ShouldEqual, "en text")
				So(requirementsText[0]["text"]["vi"], ShouldEqual, "vi text")

				serviceData, _ := json.Marshal(respResult["service"])
				service := &modelService.Service{}
				json.Unmarshal(serviceData, &service)
				So(len(service.DurationByArea), ShouldEqual, 2)
				So(service.DurationByArea[0].Duration, ShouldEqual, 2)
				So(service.DurationByArea[0].Area, ShouldEqual, 55)
				So(service.DurationByArea[0].NumberOfRooms, ShouldEqual, 2)
				So(service.DurationByArea[1].Duration, ShouldEqual, 3)
				So(service.DurationByArea[1].Area, ShouldEqual, 65)
				So(service.DurationByArea[1].NumberOfRooms, ShouldEqual, 2)
			})
		})
	})

	t.Run("7.3", func(t *testing.T) {
		// GetLastPostTask
		apiGetLastPostTask := "/api/v3/api-asker-vn/get-last-post-task"
		log.Println("==================================== GetLastPostTask check requirement")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CASH,
				},
				"requirements": []map[string]interface{}{
					{
						"type": 1,
					},
				},
			},
		})

		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING},
			bson.M{
				"$set": bson.M{
					"tip.requirements": []map[string]interface{}{
						{
							"type":     1.0,
							"duration": 1.0,
							"text": map[string]interface{}{
								"vi": "vi text",
								"en": "en text",
							},
						},
					},
				},
			},
		)

		//Get Service
		s := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		body := map[string]interface{}{
			"userId":    "**********",
			"serviceId": s.XId,
			"isoCode":   local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetLastPostTask, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$unset": bson.M{"tip.requirements": 1}})
			Convey("Then check the response to test Get Last Post Task", func() {
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["requirements"], ShouldNotBeNil)

				// Update to task requirements
				requirementData, _ := json.Marshal(respResult["requirements"])
				var requirements []map[string]interface{}
				json.Unmarshal(requirementData, &requirements)

				So(len(requirements), ShouldEqual, 1)
				So(requirements[0]["type"], ShouldEqual, 1)
				So(requirements[0]["duration"], ShouldEqual, 1)

				// Update to task requirements
				var requirementsText []map[string]map[string]interface{}
				json.Unmarshal(requirementData, &requirementsText)
				So(len(requirementsText), ShouldEqual, 1)
				So(requirementsText[0]["text"]["en"], ShouldEqual, "en text")
				So(requirementsText[0]["text"]["vi"], ShouldEqual, "vi text")
			})
		})
	})

	t.Run("8", func(t *testing.T) {
		// GetUpCommingTasks
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== Validate GetUpCommingTasks")
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId":  "",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when isoCode blank", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
		})

		Convey("Check request when isoCode incorrect", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "HH",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId":  *********,
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})
	})
	t.Run("9", func(t *testing.T) {
		// GetUpCommingTasks
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,10,00", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailSofa": map[string]interface{}{
					"curtain": map[string]interface{}{
						"dryclean": []map[string]interface{}{
							{
								"text": map[string]interface{}{
									"vi": "Phòng khách",
								},
								"quantity": 1.0,
								"name":     "livingRoom",
							},
						},
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CASH,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  "UPDATE_PAYMENT_METHOD",
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
						"content": map[string]interface{}{
							"oldPaymentMethod": "QRIS",
							"newPaymentMethod": "CASH",
						},
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_WAITING,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,20", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,12,20", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailSofa": map[string]interface{}{
					"curtain": map[string]interface{}{
						"dryclean": []map[string]interface{}{
							{
								"text": map[string]interface{}{
									"vi": "Phòng khách",
								},
								"quantity": 1.0,
								"name":     "livingRoom",
							},
						},
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,25", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,11,25", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailSofa": map[string]interface{}{
					"curtain": map[string]interface{}{
						"dryclean": []map[string]interface{}{
							{
								"text": map[string]interface{}{
									"vi": "Phòng khách",
								},
								"quantity": 1.0,
								"name":     "livingRoom",
							},
						},
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_CHILD_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,13,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailChildCare": map[string]interface{}{
					"numberOfChildren": 1,
					"detailChildren": []map[string]interface{}{
						{
							"weight": 1,
							"age": map[string]interface{}{
								"type": 1,
								"text": map[string]interface{}{
									"ko": "24 tháng - 6 tuổi",
									"vi": "24 tháng - 6 tuổi",
									"en": "24 tháng - 6 tuổi",
								},
							},
						},
					},
				},
				"shortAddress": "HCM",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_OFFICE_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,14,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailOfficeCleaning": map[string]interface{}{
					"numberOfTaskers": 3,
				},
				"costDetail": map[string]interface{}{
					"baseCost":  180000.0,
					"cost":      200000.0,
					"finalCost": 150000.0,
					"vat":       15000,
					"totalCost": 165000,
					"currency":  "VND",
				},
				"shortAddress": "HCM",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WASHING_MACHINE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,15,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailWashingMachine": []map[string]interface{}{
					{
						"name": "TOP_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV1",
							"text": map[string]interface{}{
								"vi": "Dưới 9kg",
							},
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								},
							},
						},
					}, {
						"name": "TOP_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV2",
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								}, {
									"name": "DRYER",
									"text": map[string]interface{}{
										"vi": "Máy có sấy",
									},
								},
							},
						},
					}, {
						"name": "FRONT_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV2",
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								}, {
									"name": "DRYER",
									"text": map[string]interface{}{
										"vi": "Máy có sấy",
									},
								}, {
									"name": "JAPAN_WM",
									"text": map[string]interface{}{
										"vi": "Máy Nhật bãi",
									},
								},
							},
						},
					},
				},
				"costDetail": map[string]interface{}{
					"cost":      200000.0,
					"finalCost": 200000.0,
					"currency":  "VND",
				},
				"shortAddress": "HCM",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WATER_HEATER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,16,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailWaterHeater": []map[string]interface{}{
					{
						"name": "LV1",
						"text": map[string]interface{}{
							"vi": "Dưới 20 lít",
						},
						"quantity": 2,
						"options": []map[string]interface{}{
							{
								"name": "PLASTER_CEILING",
								"text": map[string]interface{}{
									"vi": "Có tháo lồng giặt",
								},
								"quantity": 1,
							},
						},
					}, {
						"name": "LV2",
						"text": map[string]interface{}{
							"vi": "Trên 20 dưới 30 lít",
						},
						"quantity": 1,
					},
				},
				"costDetail": map[string]interface{}{
					"cost":      200000.0,
					"finalCost": 200000.0,
					"currency":  "VND",
				},
				"shortAddress": "HCM",
			}, {
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"askerId":        "**********",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":          "0834567710",
						"name":              "Tasker 01",
						"avatar":            "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating":         4.96999979019165,
						"taskDone":          938,
						"isFavouriteTasker": true,
					},
				},
				"detailHomeMoving": map[string]interface{}{
					"oldHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "from Address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  local.ISO_CODE,
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"newHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "newHomeDetail address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  local.ISO_CODE,
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"furniture": []map[string]interface{}{
						{
							"name": "removableFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ dùng có thể tháo rời",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "solidFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ nội thất nguyên khối",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "electronic",
							"text": map[string]interface{}{
								"vi": "Thiết bị điện tử",
							},
							"quantity": 10,
							"images":   []string{"xxx"},
							"options": []map[string]interface{}{
								{
									"name": "airConditioner",
									"text": map[string]interface{}{
										"vi": "Tháo lắp máy lạnh",
									},
									"quantity": 1,
								},
								{
									"name": "waterHeater",
									"text": map[string]interface{}{
										"vi": "Máy nước nóng",
									},
									"options": []map[string]interface{}{
										{
											"name": "tanklessWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng trực tiếp",
											},
											"quantity": 1,
										},
										{
											"name": "traditionalWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng gián tiếp",
											},
											"quantity": 1,
										},
									},
									"quantity": 2,
								},
							},
						},
					},
					"completedStep": 2.0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,14,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":      fmt.Sprintf("%d,%d,%d,18,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,17,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailAirConditioner": []map[string]interface{}{
					{
						"type": map[string]interface{}{
							"name": "Split",
						},
						"hp": map[string]interface{}{
							"from": 0,
							"to":   2,
						},
						"quantity": 2,
						"options": []map[string]interface{}{
							{
								"name": "Refill",
								"text": map[string]interface{}{
									"vi": "Bơm Gas",
								},
								"quantity": 1,
							},
						},
					}, {
						"type": map[string]interface{}{
							"name": "Split",
						},
						"hp": map[string]interface{}{
							"from": 2,
							"to":   0,
						},
						"quantity": 2,
						"options": []map[string]interface{}{
							{
								"name": "Refill",
								"text": map[string]interface{}{
									"vi": "Bơm Gas",
								},
								"quantity": 2,
							},
						},
					},
				},
				"costDetail": map[string]interface{}{
					"cost":      200000.0,
					"finalCost": 200000.0,
					"currency":  "VND",
				},
				"shortAddress": "HCM",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOUSEKEEPING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,0", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailHousekeeping": &modelTask.TaskDetailHousekeeping{
					Name: "HOTEL",
					RoomTypes: []*modelTask.TaskDetailHousekeepingRoomTypes{
						{
							Name:     "SINGLE",
							Quantity: 3, // 3*20
						}, {
							Name:     "COUPLE",
							Quantity: 2, // 2*30
						}, {
							Name:     "DORM_1",
							Quantity: 2, // 2*45
						},
					},
				},
				"costDetail": map[string]interface{}{
					"cost":      200000.0,
					"finalCost": 200000.0,
					"currency":  "VND",
				},
				"shortAddress": "HCM",
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -lib.MAX_RATING_DATE)
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(len(respResult), ShouldEqual, 10)
				{
					So(resp.Code, ShouldEqual, 200)
					So(respResult[0]["isChangedToCashBecauseOfPaymentFailed"], ShouldBeTrue)
					So(respResult[0]["description"], ShouldEqual, "Desc Test")
					So(respResult[0]["detailSofa"], ShouldNotBeNil)
					So(respResult[0]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult[0]["contactName"], ShouldEqual, "Asker 01")
					So(respResult[0]["phone"], ShouldEqual, "**********")
					So(respResult[0]["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult[0]["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(respResult[0]["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult[0]["cost"], ShouldEqual, 200000)
					So(respResultM[0]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
					So(respResult[0]["createdAt"], ShouldBeGreaterThanOrEqualTo, date.String())
					So(respResult[0]["date"], ShouldBeLessThan, respResult[1]["date"])
					So(respResult[1]["date"], ShouldBeLessThan, respResult[2]["date"])
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResult[1]["_id"], ShouldEqual, ids[2])
					So(respResult[1]["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(respResult[2]["_id"], ShouldEqual, ids[1])
					So(respResult[2]["status"], ShouldEqual, globalConstant.TASK_STATUS_WAITING)
					So(respResult[3]["detailChildCare"], ShouldNotBeNil)
					So(respResultM[3]["detailChildCare"]["numberOfChildren"], ShouldEqual, 1)
					So(respResultM[3]["detailChildCare"]["detailChildren"], ShouldNotBeNil)
					So(respResultM[4]["detailOfficeCleaning"]["numberOfTaskers"], ShouldEqual, 3)
					So(respResultM[4]["costDetail"]["cost"], ShouldEqual, 215000)
					So(respResultM[4]["costDetail"]["finalCost"], ShouldEqual, 165000)
					So(respResultM[4]["costDetail"]["vat"], ShouldEqual, 15000)
					So(respResult[5]["detailWashingMachine"], ShouldNotBeNil)
					So(respResultM[5]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_WASHING_MACHINE)
					So(respResultM[5]["costDetail"]["cost"], ShouldEqual, 200000)
					So(respResultM[5]["costDetail"]["finalCost"], ShouldEqual, 200000)
					var detailWashingMachine []*modelTask.TaskDetailWashingMachine
					detailWashingMachineData, _ := json.Marshal(respResult[5]["detailWashingMachine"])
					json.Unmarshal(detailWashingMachineData, &detailWashingMachine)
					So(len(detailWashingMachine), ShouldEqual, 3)
					So(detailWashingMachine[0].Name, ShouldEqual, "TOP_LOADING")
					So(detailWashingMachine[0].Type.Name, ShouldEqual, "LV1")
					So(len(detailWashingMachine[0].Type.Options), ShouldEqual, 1)
					So(detailWashingMachine[1].Name, ShouldEqual, "TOP_LOADING")
					So(detailWashingMachine[1].Type.Name, ShouldEqual, "LV2")
					So(len(detailWashingMachine[1].Type.Options), ShouldEqual, 2)
					So(detailWashingMachine[2].Name, ShouldEqual, "FRONT_LOADING")
					So(detailWashingMachine[2].Type.Name, ShouldEqual, "LV2")
					So(len(detailWashingMachine[2].Type.Options), ShouldEqual, 3)

					So(respResult[6]["detailWaterHeater"], ShouldNotBeNil)
					So(respResultM[6]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_WATER_HEATER)
					So(respResultM[6]["costDetail"]["cost"], ShouldEqual, 200000)
					So(respResultM[6]["costDetail"]["finalCost"], ShouldEqual, 200000)
					var detailWaterHeater []*modelTask.TaskDetailWaterHeater
					detailWaterHeaterData, _ := json.Marshal(respResult[6]["detailWaterHeater"])
					json.Unmarshal(detailWaterHeaterData, &detailWaterHeater)
					So(len(detailWaterHeater), ShouldEqual, 2)
					So(detailWaterHeater[0].Name, ShouldEqual, "LV1")
					So(detailWaterHeater[0].Text.Vi, ShouldEqual, "Dưới 20 lít")
					So(detailWaterHeater[0].Quantity, ShouldEqual, 2)
					So(len(detailWaterHeater[0].Options), ShouldEqual, 1)
					So(detailWaterHeater[0].Options[0].Name, ShouldEqual, "PLASTER_CEILING")
					So(detailWaterHeater[0].Options[0].Quantity, ShouldEqual, 1)
					So(detailWaterHeater[1].Name, ShouldEqual, "LV2")
					So(detailWaterHeater[1].Text.Vi, ShouldEqual, "Trên 20 dưới 30 lít")
					So(detailWaterHeater[1].Quantity, ShouldEqual, 1)
					So(len(detailWaterHeater[1].Options), ShouldEqual, 0)
					for _, task := range respResult {
						if task["_id"].(string) == ids[0] {
							var acceptedTasker []map[string]interface{}
							acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
							json.Unmarshal(acceptedTaskerData, &acceptedTasker)

							So(len(acceptedTasker), ShouldEqual, 1)
							So(acceptedTasker[0]["taskDone"], ShouldEqual, 0)
						}
					}

					So(respResultM[7]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
					So(respResultM[7]["costDetail"]["cost"], ShouldEqual, 200000)
					So(respResultM[7]["costDetail"]["finalCost"], ShouldEqual, 200000)
					So(respResult[7]["detailAirConditioner"], ShouldNotBeNil)
					var detailAirConditioner []*modelTask.TaskDetailAirConditionerV2
					detailAirConditionerData, _ := json.Marshal(respResult[7]["detailAirConditioner"])
					json.Unmarshal(detailAirConditionerData, &detailAirConditioner)
					So(len(detailAirConditioner), ShouldEqual, 2)
					So(detailAirConditioner[0].Type.Name, ShouldEqual, "Split")
					So(detailAirConditioner[0].Hp.From, ShouldEqual, 0)
					So(detailAirConditioner[0].Hp.To, ShouldEqual, 2)
					So(detailAirConditioner[0].Quantity, ShouldEqual, 2)
					So(len(detailAirConditioner[0].Options), ShouldEqual, 1)
					So(detailAirConditioner[0].Options[0].Name, ShouldEqual, "Refill")
					So(detailAirConditioner[0].Options[0].Quantity, ShouldEqual, 1)
					So(detailAirConditioner[1].Type.Name, ShouldEqual, "Split")
					So(detailAirConditioner[1].Hp.From, ShouldEqual, 2)
					So(detailAirConditioner[1].Hp.To, ShouldEqual, 0)
					So(detailAirConditioner[1].Quantity, ShouldEqual, 2)
					So(len(detailAirConditioner[1].Options), ShouldEqual, 1)
					So(detailAirConditioner[1].Options[0].Name, ShouldEqual, "Refill")
					So(detailAirConditioner[1].Options[0].Quantity, ShouldEqual, 2)
					So(len(detailAirConditioner[1].Options), ShouldEqual, 1)

					So(respResultM[9]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_HOUSEKEEPING)
					So(respResult[9]["detailHousekeeping"], ShouldNotBeNil)
				}

			})
		})
	})
	// Home moving
	t.Run("9.6", func(t *testing.T) {
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks - Only get task has no field 'isPostedBySystem'")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		relatedTaskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"sourceTaskId": "xxx",
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"sourceTaskId": "xxx",
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test 03",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"relatedTasks": map[string]interface{}{
					"oldHomeMovingSubTasks": []map[string]interface{}{
						{
							"_id": relatedTaskIds[0],
						},
					},
					"newHomeMovingSubTasks": []map[string]interface{}{
						{
							"_id": relatedTaskIds[1],
						},
					},
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"detailHomeMoving": map[string]interface{}{
					"completedStep": 1,
				},
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, taskIds[0])
				So(respResult[0]["description"], ShouldEqual, "Desc Test 03")
				So(respResult[0]["detailHomeMoving"], ShouldNotBeNil)
				detailHomeMoving := cast.ToStringMap(respResult[0]["detailHomeMoving"])
				So(detailHomeMoving["completedStep"], ShouldEqual, 1)
				detailHomeMovingStepInProgress := cast.ToStringMap(detailHomeMoving["stepInProgress"])
				So(detailHomeMovingStepInProgress["step"], ShouldEqual, 2)
				So(cast.ToStringMap(detailHomeMovingStepInProgress["title"])["vi"], ShouldEqual, "Bước 2 - Bốc lên xe")
				So(cast.ToStringMap(detailHomeMovingStepInProgress["description"])["vi"], ShouldEqual, "Tất cả thùng đang trong quá trình vận chuyển ra xe")

				So(respResult[0]["relatedTasks"], ShouldNotBeNil)
				relatedTasks := cast.ToStringMap(respResult[0]["relatedTasks"])

				So(relatedTasks["oldHomeMovingSubTasks"], ShouldNotBeNil)
				oldHomeMovingSubTasks := cast.ToSlice(relatedTasks["oldHomeMovingSubTasks"])
				So(len(oldHomeMovingSubTasks), ShouldEqual, 1)
				oldHomeMovingSubTasks0 := cast.ToStringMap(oldHomeMovingSubTasks[0])
				So(oldHomeMovingSubTasks0["_id"], ShouldEqual, relatedTaskIds[0])
				So(oldHomeMovingSubTasks0["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)

				So(relatedTasks["newHomeMovingSubTasks"], ShouldNotBeNil)
				newHomeMovingSubTasks := cast.ToSlice(relatedTasks["newHomeMovingSubTasks"])
				So(len(newHomeMovingSubTasks), ShouldEqual, 1)
				newHomeMovingSubTasks0 := cast.ToStringMap(newHomeMovingSubTasks[0])
				So(newHomeMovingSubTasks0["_id"], ShouldEqual, relatedTaskIds[1])
				So(newHomeMovingSubTasks0["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)
			})
		})
	})

	// Get payment created time and sort failed payment task to top
	t.Run("9.7", func(t *testing.T) {
		// GetUpCommingTasks
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		//Create Task
		now := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method":           globalConstant.PAYMENT_METHOD_MOMO,
					"status":           globalConstant.TASK_PAYMENT_STATUS_PAID,
					"paymentCreatedAt": now.Add(-2 * time.Hour),
				},
				"isPrepayTask": true,
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,15,50", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CASH,
				},
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,16,00", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method":           globalConstant.PAYMENT_METHOD_MOMO,
					"status":           globalConstant.TASK_PAYMENT_STATUS_NEW,
					"paymentCreatedAt": now.Add(-2 * time.Hour),
				},
				"isPrepayTask": true,
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,16,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method":           globalConstant.PAYMENT_METHOD_MOMO,
					"status":           globalConstant.TASK_PAYMENT_STATUS_ERROR,
					"paymentCreatedAt": now.Add(-2 * time.Hour),
				},
				"isPrepayTask": true,
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,16,20", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method":             globalConstant.PAYMENT_METHOD_VIET_QR,
					"status":             globalConstant.TASK_PAYMENT_STATUS_NEW,
					"paymentCreatedAt":   now.Add(-2 * time.Hour),
					"isPaymentAttempted": true,
				},
				"isPrepayTask": true,
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,16,30", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"payment": map[string]interface{}{
					"method":             globalConstant.PAYMENT_METHOD_VIET_QR,
					"status":             globalConstant.TASK_PAYMENT_STATUS_ERROR,
					"paymentCreatedAt":   now.Add(-2 * time.Hour),
					"isPaymentAttempted": true,
				},
				"isPrepayTask": true,
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": globalConstant.ISO_CODE_VN,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := ioutil.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 6)

				So(respResult[0]["_id"], ShouldEqual, ids[2])
				So(respResult[0]["payment"], ShouldNotBeNil)
				payment0 := cast.ToStringMap(respResult[0]["payment"])
				So(payment0, ShouldNotBeNil)
				So(payment0["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_MOMO)
				So(payment0["status"], ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_ERROR)
				payment0CreatedAt := cast.ToTime(payment0["paymentCreatedAt"])
				So(payment0CreatedAt.In(local.TimeZone).Format(time.RFC3339), ShouldEqual, now.Add(-2*time.Hour).Format(time.RFC3339))

				So(respResult[1]["_id"], ShouldEqual, ids[3])
				So(respResult[1]["payment"], ShouldNotBeNil)
				payment1 := cast.ToStringMap(respResult[1]["payment"])
				So(payment1, ShouldNotBeNil)
				So(payment1["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_MOMO)
				So(payment1["status"], ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_ERROR)
				payment1CreatedAt := cast.ToTime(payment1["paymentCreatedAt"])
				So(payment1CreatedAt.In(local.TimeZone).Format(time.RFC3339), ShouldEqual, now.Add(-2*time.Hour).Format(time.RFC3339))

				So(respResult[2]["_id"], ShouldEqual, ids[0])
				So(respResult[3]["_id"], ShouldEqual, ids[1])

				So(respResult[4]["_id"], ShouldEqual, ids[4])
				So(respResult[4]["payment"], ShouldNotBeNil)
				payment4 := cast.ToStringMap(respResult[4]["payment"])
				So(payment4, ShouldNotBeNil)
				So(payment4["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_VIET_QR)
				So(payment4["status"], ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_CHARGING)

				So(respResult[5]["_id"], ShouldEqual, ids[5])
				So(respResult[5]["payment"], ShouldNotBeNil)
				payment5 := cast.ToStringMap(respResult[5]["payment"])
				So(payment5, ShouldNotBeNil)
				So(payment5["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_VIET_QR)
				So(payment5["status"], ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_CHARGING)
			})
		})
	})
	// IndustrialCleaning
	t.Run("9.8", func(t *testing.T) {
		// GetUpCommingTasks
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailIndustrialCleaning": map[string]interface{}{
					"hometype": map[string]interface{}{
						"name": "HOME",
						"text": map[string]interface{}{
							"vi": "Nhà ở",
						},
						"type": map[string]interface{}{
							"name": "oldHouse",
							"text": map[string]interface{}{
								"vi": "Nhà lâu năm",
							},
							"area": map[string]interface{}{
								"name": "area1",
								"text": map[string]interface{}{
									"vi": "<60 m2",
								},
								"customArea": 123123,
							},
							"optional": []map[string]interface{}{
								{
									"name": "hasFurniture",
									"text": map[string]interface{}{
										"vi": "Nhà có nội thất",
									},
								},
							},
						},
					},
					"services": []map[string]interface{}{
						{
							"name": "glassesCleaning",
							"text": map[string]interface{}{
								"vi": "Lau kính bên ngoài",
							},

							"customArea": 123123,
						},
						{
							"name": "floorBurnishing",
							"text": map[string]interface{}{
								"vi": "Đánh bóng sàn",
							},
							"customArea": 123123,
						},
						{
							"name": "sofaCleaning",
							"sofaType": []map[string]interface{}{
								{
									"name": "sofa",
									"options": []map[string]interface{}{
										{
											"name": "sofa1",
											"text": map[string]interface{}{
												"vi": "Sofa 1 ghế",
											},
											"quantity": 1,
										},
									},
								},
								{
									"name": "mattress",
									"options": []map[string]interface{}{
										{
											"name": "mattress1",
											"text": map[string]interface{}{
												"vi": "Nệm đơn",
											},
											"quantity": 1,
										},
									},
								},
							},
						},
					},
				},
				"costDetail": map[string]interface{}{
					"cost":      200000.0,
					"finalCost": 200000.0,
					"currency":  "VND",
				},
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(len(respResult), ShouldEqual, 1)
				So(resp.Code, ShouldEqual, 200)
				So(cast.ToStringMap(respResult[0]["service"])["name"], ShouldEqual, globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING)
				So(respResult[0]["_id"], ShouldEqual, ids[0])
				So(cast.ToStringMap(respResult[0]["costDetail"])["cost"], ShouldEqual, 200000)

				detailIndustrialCleaning := cast.ToStringMap(respResult[0]["detailIndustrialCleaning"])
				So(cast.ToStringMap(detailIndustrialCleaning["hometype"])["name"], ShouldEqual, "HOME")
				homeType := cast.ToStringMap(detailIndustrialCleaning["hometype"])
				So(cast.ToStringMap(homeType["type"])["name"], ShouldEqual, "oldHouse")
			})
		})
	})
	// BeautyCare
	t.Run("9.9", func(t *testing.T) {
		// GetUpCommingTasks
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"detailBeautyCare": map[string]interface{}{
					"type":            globalConstant.TASK_BEAUTY_CARE_TYPE_SIMULTANEOUSLY,
					"numberOfTaskers": 2,
					"title": map[string]interface{}{
						"vi": "Nail",
						"en": "Nail",
					},
					"name": "nail",
					"packages": []map[string]interface{}{
						{
							"mainServices": []map[string]interface{}{
								{
									"name": "regular",
								},
							},
							"extraServices": []map[string]interface{}{
								{
									"name": "toe",
									"options": []map[string]interface{}{
										{
											"name": "regular",
										},
									},
								},
							},
							"costDetail": map[string]interface{}{
								"cost":      100000.0,
								"finalCost": 100000.0,
								"duration":  1.25,
							},
						},
						{
							"mainServices": []map[string]interface{}{
								{
									"name": "gel",
								},
							},
							"extraServices": []map[string]interface{}{
								{
									"name": "handPowder",
									"options": []map[string]interface{}{
										{
											"name": "natural",
										},
									},
								},
							},
							"costDetail": map[string]interface{}{
								"cost":      100000.0,
								"finalCost": 100000.0,
								"duration":  2,
							},
						},
					},
				},
				"costDetail": map[string]interface{}{
					"cost":      200000.0,
					"finalCost": 200000.0,
					"currency":  "VND",
				},
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(len(respResult), ShouldEqual, 1)
				So(resp.Code, ShouldEqual, 200)
				So(cast.ToStringMap(respResult[0]["service"])["name"], ShouldEqual, globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE)
				So(respResult[0]["_id"], ShouldEqual, ids[0])
				So(cast.ToStringMap(respResult[0]["costDetail"])["cost"], ShouldEqual, 200000)

				var detailBeautyCare *modelTask.TaskDetailBeautyCare
				b, _ := json.Marshal(respResult[0]["detailBeautyCare"])
				json.Unmarshal(b, &detailBeautyCare)
				So(detailBeautyCare.Type, ShouldEqual, globalConstant.TASK_BEAUTY_CARE_TYPE_SIMULTANEOUSLY)
				So(detailBeautyCare.NumberOfTaskers, ShouldEqual, 2)

				So(detailBeautyCare.Title.Vi, ShouldEqual, "Nail")
				So(detailBeautyCare.Name, ShouldEqual, "nail")
				So(len(detailBeautyCare.Packages), ShouldEqual, 2)
				// Check first package
				firstPackage := detailBeautyCare.Packages[0]
				So(len(firstPackage.MainServices), ShouldEqual, 1)
				So(firstPackage.MainServices[0].Name, ShouldEqual, "regular")
				So(len(firstPackage.ExtraServices), ShouldEqual, 1)
				So(firstPackage.ExtraServices[0].Name, ShouldEqual, "toe")
				So(len(firstPackage.ExtraServices[0].Options), ShouldEqual, 1)
				So(firstPackage.ExtraServices[0].Options[0].Name, ShouldEqual, "regular")
				So(firstPackage.CostDetail.Cost, ShouldEqual, 100000.0)
				So(firstPackage.CostDetail.FinalCost, ShouldEqual, 100000.0)
				So(firstPackage.CostDetail.Duration, ShouldEqual, 1.25)

				// Check second package
				secondPackage := detailBeautyCare.Packages[1]
				So(len(secondPackage.MainServices), ShouldEqual, 1)
				So(secondPackage.MainServices[0].Name, ShouldEqual, "gel")
				So(len(secondPackage.ExtraServices), ShouldEqual, 1)
				So(secondPackage.ExtraServices[0].Name, ShouldEqual, "handPowder")
				So(len(secondPackage.ExtraServices[0].Options), ShouldEqual, 1)
				So(secondPackage.ExtraServices[0].Options[0].Name, ShouldEqual, "natural")
				So(secondPackage.CostDetail.Cost, ShouldEqual, 100000.0)
				So(secondPackage.CostDetail.FinalCost, ShouldEqual, 100000.0)
				So(secondPackage.CostDetail.Duration, ShouldEqual, 2.0)
			})
		})
	})

	t.Run("10", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== Validate GetListHistoryTasks")
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId":  "",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when isoCode blank", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
		})

		Convey("Check request when isoCode incorrect", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "HH",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId":  *********,
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})
	})
	t.Run("11", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"rated":       true,
				"date":        "2020,11,02,16,15",
				"voiceCallHistory": []map[string]interface{}{
					{
						"to":     "W86An6yApbA8WQYQ7",
						"status": "Incoming",
						"type":   "audio",
						"createdAt": map[string]interface{}{
							"seconds": 1619580713.0,
							"nanos":   764000000.0,
						},
						"callId": "call-vn-1-QPUZ5PW3MB-1618421911543",
						"from":   "h5xvuZZanyPkBzgdh",
					},
					{
						"callId": "call-vn-1-QPUZ5PW3MB-1618421912269",
						"from":   "h5xvuZZanyPkBzgdh",
						"to":     "W86An6yApbA8WQYQ7",
						"status": "Incoming",
						"type":   "audio",
					},
				},
				"costDetail": map[string]interface{}{
					"baseCost":  240000.0,
					"cost":      258000.0,
					"finalCost": 258000.0,
					"duration":  4.0,
					"currency":  "VND",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"status": "PAID",
				},
				"isTetBooking": true,
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,15",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 3, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,20",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 4, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,25",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 05",
				"address":     "Quận 5, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,30",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 06",
				"address":     "Quận 6, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,35",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"rated":       true,
				"date":        "2020,11,02,14,10",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"status": "PAID",
				},
			},
		})

		// CreateRefundRequest
		CreateRefundRequest([]map[string]interface{}{
			{
				"type":          "PREPAY_TASK",
				"amount":        200.0,
				"paymentMethod": "TRUE_MONEY",
				"currency":      "THB",
				"status":        "PAID",
				"userId":        "0834567810",
				"taskId":        ids[0],
				"isoCode":       local.ISO_CODE,
				"cancelTaskFee": 10,
			},
		})

		//Create FATransaction
		CreateFATransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"value":         ids[6],
					"name":          "REFUND_CANCEL_TASK_PROMPT_PAY",
					"cancelTaskFee": 10000,
				},
				"amount": 190000,
			},
		})
		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})

		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER},
			map[string]interface{}{
				"$set": map[string]interface{}{
					"tetBookingDates": map[string]interface{}{
						"fromDate": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -7),
						"toDate":   globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 7),
					},
				},
			},
		)
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get List History Task", func() {
				UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, map[string]interface{}{"$unset": map[string]interface{}{"tetBookingDates": true}})
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 7)
				So(respResult[0]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResult[0]["isTetBooking"], ShouldBeTrue)
				So(respResult[0]["voiceCallHistory"], ShouldBeNil)
				So(respResult[0]["costDetail"], ShouldNotBeNil)
				So(respResultM[0]["costDetail"]["currency"], ShouldBeNil)
				So(respResult[0]["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
				So(respResult[0]["rated"], ShouldBeTrue)
				So(respResult[0]["cancellationFee"], ShouldEqual, 10)
				So(respResultM[0]["rate"]["askerId"], ShouldEqual, "0834567810")
				So(respResultM[0]["rate"]["taskerId"], ShouldEqual, "**********")
				So(respResultM[0]["rate"]["review"], ShouldEqual, "Chị làm tốt, kỹ, nhanh nhẹn")
				So(respResultM[0]["refund"]["status"], ShouldEqual, "PAID")
				So(respResultM[0]["refund"]["amount"], ShouldEqual, 200)
				So(respResult[0]["date"], ShouldBeGreaterThan, respResult[1]["date"])
				So(respResult[2]["date"], ShouldBeGreaterThan, respResult[3]["date"])
				So(respResultM[6]["refund"]["status"], ShouldEqual, "PAID")
				So(respResultM[6]["refund"]["amount"], ShouldEqual, 190000)
				So(respResult[6]["cancellationFee"], ShouldEqual, 10000)

				for _, task := range respResult {
					if task["_id"].(string) == ids[0] {
						var acceptedTasker []map[string]interface{}
						acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
						json.Unmarshal(acceptedTaskerData, &acceptedTasker)

						So(len(acceptedTasker), ShouldEqual, 1)
						So(acceptedTasker[0]["taskDone"], ShouldEqual, 0)
					}
				}
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,16,15",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,15",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 3, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,20",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 4, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,25",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 05",
				"address":     "Quận 5, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,30",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 06",
				"address":     "Quận 6, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,35",
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		Convey("Check request with limit is 4", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
				"limit":   4,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(respResult[0]["date"], ShouldBeGreaterThan, respResult[1]["date"])
			So(respResult[2]["date"], ShouldBeGreaterThan, respResult[3]["date"])
			So(len(respResult), ShouldEqual, 4)
		})
	})

	t.Run("12.1", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Day := now.AddDate(0, 0, -2)
		last6Day := now.AddDate(0, 0, -6)
		last9Day := now.AddDate(0, 0, -9)
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,16,15", last2Day.Year(), int(last2Day.Month()), last2Day.Day()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,16,15", last6Day.Year(), int(last6Day.Month()), last6Day.Day()),
				"payment": map[string]interface{}{
					"method":    globalConstant.PAYMENT_METHOD_CARD,
					"isPaidOff": true,
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 3, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,16,15", last9Day.Year(), int(last9Day.Month()), last9Day.Day()),
				"payment": map[string]interface{}{
					"method":    globalConstant.PAYMENT_METHOD_CARD,
					"isPaidOff": true,
				},
			},
		})

		CreateOutstandingPayment([]map[string]interface{}{
			{
				"charged":       false,
				"cost":          401000,
				"currency":      "VND",
				"cardId":        "o0cj4fi9ihtktxt1e",
				"pspReference":  "2612694315845147",
				"refusalReason": "Refused",
				"askerId":       "**********",
				"data": map[string]interface{}{
					"taskId": taskIds[1],
				},
				"cardInfo": map[string]interface{}{
					"number": "4111",
					"type":   "DEBIT",
				},
				"status": globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW,
			}, {
				"charged":       false,
				"cost":          401000,
				"currency":      "VND",
				"cardId":        "o0cj4fi9ihtktxt1e",
				"pspReference":  "2612694315845147",
				"refusalReason": "Refused",
				"askerId":       "**********",
				"data": map[string]interface{}{
					"taskId": taskIds[2],
				},
				"cardInfo": map[string]interface{}{
					"number": "4222",
					"type":   "VISA",
				},
				"status": globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW,
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      taskIds[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		Convey("Check request with limit is 4", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
				"limit":   4,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)

			So(resp.Code, ShouldEqual, 200)
			So(len(respResult), ShouldEqual, 3)
			So(respResult[0]["askerCanNotRate"], ShouldBeEmpty)
			So(respResult[1]["askerCanNotRate"], ShouldBeEmpty)
			So(respResult[2]["askerCanNotRate"], ShouldEqual, true)
			for _, r := range respResult {
				outStandingPaymentData, _ := json.Marshal(r["outStandingPayment"])
				outStandingPayment := map[string]interface{}{}
				json.Unmarshal(outStandingPaymentData, &outStandingPayment)

				if r["_id"].(string) == taskIds[1] {
					So(outStandingPayment, ShouldNotBeNil)
					So(outStandingPayment["date"], ShouldNotBeNil)
					So(outStandingPayment["cardNumber"], ShouldEqual, "4111")
					So(outStandingPayment["cardType"], ShouldEqual, "DEBIT")
				}
				if r["_id"].(string) == taskIds[2] {
					So(outStandingPayment, ShouldNotBeNil)
					So(outStandingPayment["date"], ShouldNotBeNil)
					So(outStandingPayment["cardNumber"], ShouldEqual, "4222")
					So(outStandingPayment["cardType"], ShouldEqual, "VISA")
				}
			}
		})
	})

	t.Run("13", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,16,15",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,15",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 3, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,20",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 4, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,25",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 05",
				"address":     "Quận 5, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,30",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 06",
				"address":     "Quận 6, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,15,35",
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		Convey("Check request with limit is 2", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
				"limit":   2,
				"page":    1,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult []map[string]interface{}
			bytesResult, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytesResult, &respResult)

			bodySecs := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
				"limit":   2,
				"page":    2,
			}
			reqBodySecond, _ := json.Marshal(bodySecs)
			reqSecond := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBodySecond))
			respSecond := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(respSecond, reqSecond)
			var respResultSecond []map[string]interface{}
			bytesSecond, _ := io.ReadAll(respSecond.Body)
			json.Unmarshal(bytesSecond, &respResultSecond)

			So(resp.Code, ShouldEqual, 200)
			So(respResult[0]["date"], ShouldBeGreaterThan, respResult[1]["date"])
			So(len(respResult), ShouldEqual, 2)

			So(respSecond.Code, ShouldEqual, 200)
			So(respResultSecond[0]["date"], ShouldBeGreaterThan, respResultSecond[1]["date"])
			So(len(respResultSecond), ShouldEqual, 2)

			So(respResult[0]["date"], ShouldBeGreaterThan, respResultSecond[0]["date"])
		})
	})
	// Check tetBookingDates is working => return isTetBooking in task
	t.Run("13.2", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":   "**********",
				"serviceName":  globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":  "Desc Test",
				"address":      "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":       globalConstant.TASK_STATUS_DONE,
				"rated":        true,
				"date":         "2020,11,02,16,15",
				"isTetBooking": true,
			},
		})

		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_HOME_CLEANING},
			map[string]interface{}{
				"$set": map[string]interface{}{
					"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
					"tetBookingDates": map[string]interface{}{
						"fromDate": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -7),
						"toDate":   globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 7),
					},
				},
			},
		)

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		Convey("Check request with limit is 4", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_HOME_CLEANING}, map[string]interface{}{"$unset": map[string]interface{}{"tetBookingDates": true}})

			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(respResult[0]["isTetBooking"], ShouldBeTrue)
		})
	})

	t.Run("13.3", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":   "**********",
				"serviceName":  globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":  "Desc Test",
				"address":      "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":       globalConstant.TASK_STATUS_DONE,
				"rated":        true,
				"date":         "2020,11,02,16,15",
				"isTetBooking": true,
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		Convey("Check request with limit is 4", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(respResult[0]["isTetBooking"], ShouldBeNil)
		})
	})
	t.Run("14", func(t *testing.T) {
		// GetTasksNeedRating
		apiGetTasksNeedRating := "/api/v3/api-asker-vn/get-tasks-need-rating"
		log.Println("==================================== Validate GetTasksNeedRating")
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTasksNeedRating, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId":  "",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTasksNeedRating, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when isoCode blank", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTasksNeedRating, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
		})

		Convey("Check request when isoCode incorrect", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "HH",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTasksNeedRating, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId":  1222222222,
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTasksNeedRating, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})
	})
	t.Run("15", func(t *testing.T) {
		// GetTasksNeedRating
		apiGetTasksNeedRating := "/api/v3/api-asker-vn/get-tasks-need-rating"
		log.Println("==================================== GetTasksNeedRating")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		before1Day := time.Now().AddDate(0, 0, -1)
		before2Days := time.Now().AddDate(0, 0, -2)
		before3Day := time.Now().AddDate(0, 0, -3)
		before1M := time.Now().AddDate(0, -1, 0)
		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", before2Days.Year(), before2Days.Month(), before2Days.Day()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", before3Day.Year(), before3Day.Month(), before3Day.Day()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", before1M.Year(), before1M.Month(), before1M.Day()),
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetTasksNeedRating, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Tasks Need Rating", func() {
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				for _, v := range respResult {
					So(v["_id"], ShouldBeIn, []string{ids[0], ids[1], ids[2]})
				}
			})
		})
	})

	t.Run("16", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-list-employees"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userIds empty", func() {
				body := map[string]interface{}{
					"userIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if taskId empty", func() {
				body := map[string]interface{}{
					"userIds": []string{"123123", "!2312312"},
					"taskId":  "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userIds": []string{"123123", "!2312312"},
					"taskId":  213123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("17", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-list-employees"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567891",
					},
				},
				"collectionDate": timestamppb.Timestamp{
					Seconds: time.Now().Add(10 * time.Hour).Unix(),
					Nanos:   int32(time.Now().Add(10 * time.Hour).Nanosecond()),
				},
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567892",
					},
				},
				"collectionDate": timestamppb.Timestamp{
					Seconds: time.Now().Add(20 * time.Hour).Unix(),
					Nanos:   int32(time.Now().Add(20 * time.Hour).Nanosecond()),
				},
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"collectionDate": timestamppb.Timestamp{
					Seconds: time.Now().Add(10 * time.Hour).Unix(),
					Nanos:   int32(time.Now().Add(10 * time.Hour).Nanosecond()),
				},
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
		})
		Convey("Check the response", t, func() {
			body := map[string]interface{}{
				"userIds": []string{"0834567891", "0834567892"},
				"taskId":  ids[2],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			b, _ := io.ReadAll(res.Body)
			m := map[string]interface{}{}
			json.Unmarshal(b, &m)
			users := []map[string]interface{}{}
			b2, _ := json.Marshal(m["users"])
			json.Unmarshal(b2, &users)
			So(res.Code, ShouldEqual, 200)
			for _, v := range users {
				if v["_id"] == "0834567891" {
					So(v["numberTaskConfict"], ShouldEqual, 1)
				} else {
					So(v["numberTaskConfict"], ShouldEqual, 0)
				}
			}
		})
	})

	t.Run("18", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/done-task-by-company"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if ISOCode empty", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "HA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
		})
	})
	t.Run("19", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/done-task-by-company"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567891",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"employeeIds": []string{
					"0834567891",
					"0834567892",
				},
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567891",
						"companyId": "0834567893",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567892",
						"companyId": "0834567893",
					},
				},
				"collectionDate": time.Now().Add(20 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":     "0834567893",
					"isoCode":    local.ISO_CODE,
					"employeeId": "0834567891",
					"limit":      10,
					"page":       1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]interface{}{}
				result := map[string][]map[string]interface{}{}
				json.Unmarshal(b, &m)
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 200)
				So(len(result["employees"]), ShouldEqual, 2)
				So(m["data"], ShouldNotBeNil)
			})
		})
	})
	t.Run("19.2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/done-task-by-company"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567891",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"employeeIds": []string{
					"0834567891",
					"0834567892",
				},
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567891",
						"companyId": "0834567893",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567892",
						"companyId": "0834567893",
					},
				},
				"collectionDate": time.Now().Add(20 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
		})
		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0834567892",
				"askerPhone":  "**********",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
				"taskId":      ids[0],
			}, {
				"taskerPhone": "0834567891",
				"askerPhone":  "**********",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
				"taskId":      ids[1],
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":  "0834567893",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string][]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 200)
				So(len(result["employees"]), ShouldEqual, 2)
				So(len(result["data"]), ShouldEqual, 2)
			})
		})
	})

	t.Run("20", func(t *testing.T) {
		// DoneTaskByTasker
		apiUrl := "/api/v3/api-asker-vn/done-task-by-tasker"
		log.Println("==================================== Validate DoneTaskByTasker")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": "HW",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})

			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":  123123,
					"isoCode": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("21", func(t *testing.T) {
		// DoneTaskByTasker
		apiUrl := "/api/v3/api-asker-vn/done-task-by-tasker"
		log.Println("==================================== DoneTaskByTasker")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		before1Day := time.Now().AddDate(0, 0, -1)
		before2Days := time.Now().AddDate(0, 0, -2)
		before3Day := time.Now().AddDate(0, 0, -3)
		before1M := time.Now().AddDate(0, -1, 0)
		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", before2Days.Year(), before2Days.Month(), before2Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", before3Day.Year(), before3Day.Month(), before3Day.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", before1M.Year(), before1M.Month(), before1M.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskId":      ids[1],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskId":      ids[2],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskId":      ids[3],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        4,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
				"limit":   10,
				"page":    1,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasks Need Rating", func() {
					var respResult map[string][]map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					for i, v := range respResult["data"] {
						//Check sort createdAt
						if i == 3 {
							So(v["rating"]["rate"], ShouldEqual, 4)
						} else {
							So(v["rating"]["rate"], ShouldEqual, 5)
						}
						So(v["rating"]["review"], ShouldEqual, "Chị làm tốt, kỹ, nhanh nhẹn")
						So(v["rating"]["taskId"], ShouldBeIn, ids)
						So(v["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
					}
				})
			})
		})
	})
	t.Run("22", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/tasker-refuse-task"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"taskId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if taskId empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"taskId": "1111",
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if task not found", func() {
				body := map[string]interface{}{
					"taskId": "1111",
					"userId": "xxxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
				So(m["error"]["message"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.Message)
			})
		})
	})

	t.Run("22.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/tasker-refuse-task"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if taskId empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"taskId": "1111",
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("23", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/tasker-refuse-task"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the http response", func() {
					So(res.Code, ShouldEqual, 200)
				})
				Convey("Check the database", func() {
					task, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": ids[0]}, bson.M{})
					t, _ := json.Marshal(task["taskerRefuse"])
					m := map[string]interface{}{}
					json.Unmarshal(t, &m)
					So(m["status"], ShouldBeTrue)
				})
			})
		})
	})

	t.Run("23.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/tasker-refuse-task"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"isTaskerRefuse": true,
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the http response", func() {
					b, _ := io.ReadAll(res.Body)
					respResult := map[string]map[string]interface{}{}
					json.Unmarshal(b, &respResult)
					So(res.Code, ShouldEqual, 500)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_REFUSED.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_REFUSED.Message)
				})
			})
		})
	})

	t.Run("23.2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/tasker-refuse-task"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "xxx",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the http response", func() {
					b, _ := io.ReadAll(res.Body)
					respResult := map[string]map[string]interface{}{}
					json.Unmarshal(b, &respResult)
					So(res.Code, ShouldEqual, 404)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
					So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
				})
			})
		})
	})

	t.Run("24", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if taskId is empty", func() {
				body := map[string]interface{}{
					"taskId": "xxxxx",
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("25", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
				"shortAddress": "HCM",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.ShortAddress, ShouldEqual, "HCM")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
			})
		})
	})
	// Get task detail childCare service
	t.Run("25.3", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_CHILD_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailChildCare": map[string]interface{}{
					"numberOfChildren": 1,
					"detailChildren": []map[string]interface{}{
						{
							"weight": 1,
							"age": map[string]interface{}{
								"type": 1,
								"text": map[string]interface{}{
									"ko": "24 tháng - 6 tuổi",
									"vi": "24 tháng - 6 tuổi",
									"en": "24 tháng - 6 tuổi",
								},
							},
						},
					},
				},
				"shortAddress": "HCM",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.ShortAddress, ShouldEqual, "HCM")
				So(m.DetailChildCare, ShouldNotBeNil)
				So(m.DetailChildCare.NumberOfChildren, ShouldEqual, 1)
				So(len(m.DetailChildCare.DetailChildren), ShouldEqual, 1)
				So(m.DetailChildCare.DetailChildren[0].Weight, ShouldEqual, 1)
				So(m.DetailChildCare.DetailChildren[0].Age.Type, ShouldEqual, 1)
				So(m.DetailChildCare.DetailChildren[0].Age.Text.Vi, ShouldEqual, "24 tháng - 6 tuổi")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
			})
		})
	})
	// Get task detail homeCleaning service. Subscription
	t.Run("25.4", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"subscriptionId": "xxx",
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
				So(m.Payment.Method, ShouldBeEmpty)
				So(m.Payment.Status, ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_PAID)

				resultMap := map[string]interface{}{}
				json.Unmarshal(b, &resultMap)

				serviceData, _ := json.Marshal(resultMap["service"])
				service := &modelService.Service{}
				json.Unmarshal(serviceData, &service)
				So(len(service.DurationByArea), ShouldEqual, 2)
				So(service.DurationByArea[0].Duration, ShouldEqual, 2)
				So(service.DurationByArea[0].Area, ShouldEqual, 55)
				So(service.DurationByArea[0].NumberOfRooms, ShouldEqual, 2)
				So(service.DurationByArea[1].Duration, ShouldEqual, 3)
				So(service.DurationByArea[1].Area, ShouldEqual, 65)
				So(service.DurationByArea[1].NumberOfRooms, ShouldEqual, 2)
			})
		})
	})
	// Get task detail homeCleaning service. task card
	t.Run("25.5", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"payment": map[string]interface{}{
					"method":   globalConstant.PAYMENT_METHOD_CARD,
					"isPayOff": true,
					"cardId":   "xxx",
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
				So(m.Payment.Status, ShouldEqual, globalConstant.TASK_PAYMENT_STATUS_PAID)
				So(m.Payment.Method, ShouldEqual, globalConstant.PAYMENT_METHOD_CARD)
				So(m.Payment.CardId, ShouldEqual, "xxx")
				So(m.Payment.IsPayOff, ShouldBeTrue)

				resultMap := map[string]interface{}{}
				json.Unmarshal(b, &resultMap)

				serviceData, _ := json.Marshal(resultMap["service"])
				service := &modelService.Service{}
				json.Unmarshal(serviceData, &service)
				So(len(service.DurationByArea), ShouldEqual, 2)
				So(service.DurationByArea[0].Duration, ShouldEqual, 2)
				So(service.DurationByArea[0].Area, ShouldEqual, 55)
				So(service.DurationByArea[0].NumberOfRooms, ShouldEqual, 2)
				So(service.DurationByArea[1].Duration, ShouldEqual, 3)
				So(service.DurationByArea[1].Area, ShouldEqual, 65)
				So(service.DurationByArea[1].NumberOfRooms, ShouldEqual, 2)
			})
		})
	})
	// Get task detail INDUSTRIAL_CLEANING
	t.Run("25.6", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailIndustrialCleaning": map[string]interface{}{
					"hometype": map[string]interface{}{
						"name": "HOME",
						"text": map[string]interface{}{
							"vi": "Nhà ở",
						},
						"type": map[string]interface{}{
							"name": "oldHouse",
							"text": map[string]interface{}{
								"vi": "Nhà lâu năm",
							},
							"area": map[string]interface{}{
								"name": "area1",
								"text": map[string]interface{}{
									"vi": "<60 m2",
								},
								"customArea": 123123,
							},
							"optional": []map[string]interface{}{
								{
									"name": "hasFurniture",
									"text": map[string]interface{}{
										"vi": "Nhà có nội thất",
									},
								},
							},
						},
					},
					"services": []map[string]interface{}{
						{
							"name": "glassesCleaning",
							"text": map[string]interface{}{
								"vi": "Lau kính bên ngoài",
							},
							"customArea": 123123,
						},
						{
							"name": "floorBurnishing",
							"text": map[string]interface{}{
								"vi": "Lau kính bên ngoài",
							},
							"customArea": 123123,
						},
						{
							"name": "sofaCleaning",
							"sofaType": []map[string]interface{}{
								{
									"name": "sofa",
									"options": []map[string]interface{}{
										{
											"name": "sofa1",
											"text": map[string]interface{}{
												"vi": "Sofa 1 ghế",
											},
											"quantity": 1,
										},
									},
								},
								{
									"name": "mattress",
									"options": []map[string]interface{}{
										{
											"name": "mattress1",
											"text": map[string]interface{}{
												"vi": "Nệm đơn",
											},
											"quantity": 1,
										},
									},
								},
							},
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.DetailIndustrialCleaning, ShouldNotBeNil)
				So(m.DetailIndustrialCleaning.HomeType, ShouldNotBeNil)
				So(m.DetailIndustrialCleaning.HomeType.Name, ShouldEqual, "HOME")
				So(m.DetailIndustrialCleaning.HomeType.Text.Vi, ShouldEqual, "Nhà ở")
				So(m.DetailIndustrialCleaning.HomeType.Type.Name, ShouldEqual, "oldHouse")
				So(m.DetailIndustrialCleaning.HomeType.Type.Text.Vi, ShouldEqual, "Nhà lâu năm")
				So(m.DetailIndustrialCleaning.HomeType.Type.Area.Name, ShouldEqual, "area1")
				So(m.DetailIndustrialCleaning.HomeType.Type.Area.Text.Vi, ShouldEqual, "<60 m2")
				So(len(m.DetailIndustrialCleaning.Services), ShouldEqual, 3)
				So(m.DetailIndustrialCleaning.Services[0].Name, ShouldEqual, "glassesCleaning")
				So(m.DetailIndustrialCleaning.Services[1].Name, ShouldEqual, "floorBurnishing")
				So(m.DetailIndustrialCleaning.Services[2].Name, ShouldEqual, "sofaCleaning")
				So(len(m.DetailIndustrialCleaning.Services[2].SofaType), ShouldEqual, 2)
				So(m.DetailIndustrialCleaning.Services[2].SofaType[0].Name, ShouldEqual, "sofa")
				So(len(m.DetailIndustrialCleaning.Services[2].SofaType[0].Options), ShouldEqual, 1)
				So(m.DetailIndustrialCleaning.Services[2].SofaType[0].Options[0].Name, ShouldEqual, "sofa1")
				So(m.DetailIndustrialCleaning.Services[2].SofaType[0].Options[0].Quantity, ShouldEqual, 1)
				So(m.DetailIndustrialCleaning.Services[2].SofaType[1].Name, ShouldEqual, "mattress")
				So(m.DetailIndustrialCleaning.Services[2].SofaType[1].Options[0].Name, ShouldEqual, "mattress1")
				So(m.DetailIndustrialCleaning.Services[2].SofaType[1].Options[0].Quantity, ShouldEqual, 1)

				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
			})
		})
	})

	// Get task detail BEAUTY_CARE
	t.Run("25.7", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailBeautyCare": map[string]interface{}{
					"type":            globalConstant.TASK_BEAUTY_CARE_TYPE_SIMULTANEOUSLY,
					"numberOfTaskers": 2,
					"title": map[string]interface{}{
						"vi": "Nail",
						"en": "Nail",
					},
					"name": "nail",
					"packages": []map[string]interface{}{
						{
							"mainServices": []map[string]interface{}{
								{
									"name": "regular",
								},
							},
							"extraServices": []map[string]interface{}{
								{
									"name": "toe",
									"options": []map[string]interface{}{
										{
											"name": "regular",
										},
									},
								},
							},
							"costDetail": map[string]interface{}{
								"cost":      100000.0,
								"finalCost": 100000.0,
								"duration":  1.25,
							},
						},
						{
							"mainServices": []map[string]interface{}{
								{
									"name": "gel",
								},
							},
							"extraServices": []map[string]interface{}{
								{
									"name": "handPowder",
									"options": []map[string]interface{}{
										{
											"name": "natural",
										},
									},
								},
							},
							"costDetail": map[string]interface{}{
								"cost":      100000.0,
								"finalCost": 100000.0,
								"duration":  2,
							},
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				detailBeautyCare := m.DetailBeautyCare
				So(detailBeautyCare.Type, ShouldEqual, globalConstant.TASK_BEAUTY_CARE_TYPE_SIMULTANEOUSLY)
				So(detailBeautyCare.NumberOfTaskers, ShouldEqual, 2)

				So(detailBeautyCare.Title.Vi, ShouldEqual, "Nail")
				So(detailBeautyCare.Name, ShouldEqual, "nail")
				So(len(detailBeautyCare.Packages), ShouldEqual, 2)
				// Check first package
				firstPackage := detailBeautyCare.Packages[0]
				So(len(firstPackage.MainServices), ShouldEqual, 1)
				So(firstPackage.MainServices[0].Name, ShouldEqual, "regular")
				So(len(firstPackage.ExtraServices), ShouldEqual, 1)
				So(firstPackage.ExtraServices[0].Name, ShouldEqual, "toe")
				So(len(firstPackage.ExtraServices[0].Options), ShouldEqual, 1)
				So(firstPackage.ExtraServices[0].Options[0].Name, ShouldEqual, "regular")
				So(firstPackage.CostDetail.Cost, ShouldEqual, 100000.0)
				So(firstPackage.CostDetail.FinalCost, ShouldEqual, 100000.0)
				So(firstPackage.CostDetail.Duration, ShouldEqual, 1.25)

				// Check second package
				secondPackage := detailBeautyCare.Packages[1]
				So(len(secondPackage.MainServices), ShouldEqual, 1)
				So(secondPackage.MainServices[0].Name, ShouldEqual, "gel")
				So(len(secondPackage.ExtraServices), ShouldEqual, 1)
				So(secondPackage.ExtraServices[0].Name, ShouldEqual, "handPowder")
				So(len(secondPackage.ExtraServices[0].Options), ShouldEqual, 1)
				So(secondPackage.ExtraServices[0].Options[0].Name, ShouldEqual, "natural")
				So(secondPackage.CostDetail.Cost, ShouldEqual, 100000.0)
				So(secondPackage.CostDetail.FinalCost, ShouldEqual, 100000.0)
				So(secondPackage.CostDetail.Duration, ShouldEqual, 2.0)

				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		apiURL := subPath + "/get-tasks-confirmed"
		Convey(fmt.Sprintf("Given HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is empty", func() {
				body := map[string]interface{}{
					"userId":  "xxxxx",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is incorrect", func() {
				body := map[string]interface{}{
					"userId":  "xxxxx",
					"isoCode": "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userId":  123213,
					"isoCode": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("27", func(t *testing.T) {
		apiURL := subPath + "/get-tasks-confirmed"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
				"fromPartner": globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(20 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": true,
				},
				"fromPartner": globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := []map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m[0]["_id"], ShouldEqual, ids[0])
				So(m[0]["status"], ShouldEqual, globalConstant.TASK_STATUS_CONFIRMED)
				So(m[0]["fromPartner"], ShouldEqual, globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP)
				So(m[1]["_id"], ShouldEqual, ids[1])
				So(m[1]["status"], ShouldEqual, globalConstant.TASK_STATUS_CONFIRMED)
				So(m[1]["fromPartner"], ShouldEqual, globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP)
			})
		})
	})

	t.Run("28", func(t *testing.T) {
		apiURL := subPath + "/collect-clothes"
		Convey(fmt.Sprintf("Given HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if taskId is empty", func() {
				body := map[string]interface{}{
					"userId": "xxxxx",
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userId": 112312,
					"taskId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if task not found", func() {
				body := map[string]interface{}{
					"userId": "112312",
					"taskId": "123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("29", func(t *testing.T) {
		apiURL := subPath + "/collect-clothes"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
				"detailLaundry": map[string]interface{}{
					"washing": map[string]interface{}{
						"type": "washing",
						"text": map[string]interface{}{
							"vi": "Giặt nước (theo kg)",
							"en": "Washing (kg)",
							"ko": "일반 세탁 (kg)",
						},
						"data": map[string]interface{}{
							"quantity": 4,
							"price":    15000,
						},
					},
					"dryClean":   nil,
					"others":     nil,
					"isReceived": false,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the reponse", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var task *modelTask.Task
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], body["taskId"].(string), bson.M{"detailLaundry": 1}, &task)
				So(task.DetailLaundry, ShouldNotBeNil)
				So(task.DetailLaundry.IsReceived, ShouldEqual, true)
			})
		})
	})

	t.Run("30", func(t *testing.T) {
		apiURL := subPath + "/tasker-withdraw-task"
		Convey(fmt.Sprintf("Given HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if taskId is empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"taskId": "xxxx",
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"taskId": 111111,
					"userId": 1231231,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("31", func(t *testing.T) {
		apiURL := subPath + "/tasker-withdraw-task"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var task *modelTask.Task
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], body["taskId"].(string), bson.M{"status": 1, "acceptedTasker": 1, "intervalForAccept": 1}, &task)
				So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_POSTED)
				So(task.AcceptedTasker, ShouldBeNil)
				So(task.IntervalForAccept, ShouldEqual, 0)
			})
		})
	})
	t.Run("31.1", func(t *testing.T) {
		apiURL := subPath + "/tasker-withdraw-task"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "0834567891",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var task *modelTask.Task
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], body["taskId"].(string), bson.M{"status": 1, "acceptedTasker": 1, "intervalForAccept": 1}, &task)
				So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_CONFIRMED)
				So(task.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(task.IntervalForAccept, ShouldEqual, 0)
			})
		})
	})

	t.Run("32", func(t *testing.T) {
		apiURL := subPath + "/suggest-next-tasks"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if taskId is empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"taskId": "xxxxx",
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
					"userId": 123213,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("33", func(t *testing.T) {
		apiURL := subPath + "/suggest-next-tasks"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(40 * time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(2 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
			{
				"askerPhone":     "**********",
				"serviceName":    globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"collectionDate": now.Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		serviceChannel := GetServiceChannel(globalConstant.SERVICE_NAME_HOME_CLEANING, bson.M{"serviceId": 1})
		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				So(res.Code, ShouldEqual, 200)

				var task *modelTask.Task
				var task1 *modelTask.Task
				json.Unmarshal(b, &task1)
				currentTime := globalLib.GetCurrentTime(local.TimeZone)
				if currentTime.Add(4*time.Hour).Day() == currentTime.Day() {
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task1.XId, bson.M{"status": 1, "serviceId": 1, "viewedTaskers": 1}, &task)
					So(task.XId, ShouldNotBeNil)
					So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(task.ServiceId, ShouldEqual, serviceChannel.ServiceId)
					So(globalLib.FindStringInSlice(task.ViewedTaskers, body["userId"].(string)), ShouldEqual, 0)
				} else {
					So(task, ShouldBeNil)
				}
			})
		})
	})

	t.Run("33.1", func(t *testing.T) {
		apiURL := subPath + "/suggest-next-tasks"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(40 * time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceName":    globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"collectionDate": now.Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				var result *modelTask.Task
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 200)
				So(result, ShouldBeNil)
			})
		})
	})

	t.Run("33.2", func(t *testing.T) {
		apiURL := subPath + "/suggest-next-tasks"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(40 * time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(2 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				var task *modelTask.Task
				var task1 *modelTask.Task
				json.Unmarshal(b, &task1)
				So(res.Code, ShouldEqual, 200)

				currentTime := globalLib.GetCurrentTime(local.TimeZone)
				if currentTime.Add(2*time.Hour).Day() == currentTime.Day() {
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task1.XId, bson.M{"status": 1, "serviceId": 1, "viewedTaskers": 1}, &task)
					So(task.XId, ShouldNotBeNil)
					So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(globalLib.FindStringInSlice(task.ViewedTaskers, body["userId"].(string)), ShouldEqual, 0)
				} else {
					So(task, ShouldBeNil)
				}
			})
		})
	})

	t.Run("33.3", func(t *testing.T) {
		apiURL := subPath + "/suggest-next-tasks"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(40 * time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(2 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{"timeInBetweenTask": 2},
		})
		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				var task *modelTask.Task
				var task1 *modelTask.Task
				json.Unmarshal(b, &task1)
				So(res.Code, ShouldEqual, 200)
				currentTime := globalLib.GetCurrentTime(local.TimeZone)
				if currentTime.Add(2*time.Hour).Day() == currentTime.Day() {
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task1.XId, bson.M{"status": 1, "serviceId": 1, "viewedTaskers": 1}, &task)
					So(task.XId, ShouldNotBeNil)
					So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(globalLib.FindStringInSlice(task.ViewedTaskers, body["userId"].(string)), ShouldEqual, 0)
				} else {
					So(task, ShouldBeNil)
				}
			})
		})
		UpdateSettings(bson.M{
			"$unset": bson.M{"timeInBetweenTask": 1},
		})
	})

	t.Run("33.4", func(t *testing.T) {
		apiURL := subPath + "/suggest-next-tasks"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567910",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone).Add(40 * time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(2 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"viewedTaskers": []interface{}{
					"**********",
				},
				"acceptedTasker": []map[string]interface{}{
					{"taskerId": "**********"},
				},
				"collectionDate": time.Now().Add(2 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for api %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				var task *modelTask.Task
				var task1 *modelTask.Task
				json.Unmarshal(b, &task1)
				So(res.Code, ShouldEqual, 200)
				currentTime := globalLib.GetCurrentTime(local.TimeZone)
				if currentTime.Add(2*time.Hour).Day() == currentTime.Day() {
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task1.XId, bson.M{"status": 1, "serviceId": 1, "viewedTaskers": 1}, &task)
					So(task.XId, ShouldNotBeNil)
					So(task.Status, ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(globalLib.FindStringInSlice(task.ViewedTaskers, body["userId"].(string)), ShouldEqual, 0)
				} else {
					So(task, ShouldBeNil)
				}
			})
		})
	})
	t.Run("34", func(t *testing.T) {
		apiURL := subPath + "/begin-work"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if taskId is empty", func() {
				body := map[string]interface{}{
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if task not found", func() {
				body := map[string]interface{}{
					"taskId": "123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("35", func(t *testing.T) {
		apiURL := subPath + "/begin-work"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"taskId": ids[0],
					"lat":    106.0001,
					"lng":    10.6000,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var task *modelTask.Task
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], body["taskId"].(string), bson.M{"startWorking": 1}, &task)
				So(task.StartWorking, ShouldNotBeNil)
				So(task.StartWorking.IsStart, ShouldBeTrue)
				So(task.StartWorking.Lng, ShouldEqual, body["lng"])
				So(task.StartWorking.Lat, ShouldEqual, body["lat"])
			})
		})
	})

	t.Run("35.1", func(t *testing.T) {
		apiURL := subPath + "/begin-work"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"taskId": ids[0],
					"lat":    106.0001,
					"lng":    10.6000,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_CONFIRMED.ErrorCode)
				So(m["error"]["message"], ShouldEqual, lib.ERROR_TASK_NOT_CONFIRMED.Message)
			})
		})
	})

	t.Run("35.2", func(t *testing.T) {
		apiURL := subPath + "/begin-work"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":   "**********",
				"serviceName":  globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":  "Desc Test",
				"address":      "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":       globalConstant.TASK_STATUS_CONFIRMED,
				"startWorking": map[string]interface{}{"isStart": true},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"taskId": ids[0],
					"lat":    106.0001,
					"lng":    10.6000,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TASK_STARTED.ErrorCode)
				So(m["error"]["message"], ShouldEqual, lib.ERROR_TASK_STARTED.Message)
			})
		})
	})
	t.Run("41", func(t *testing.T) {
		apiURL := subPath + "/get-done-task"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userId": *********,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("42", func(t *testing.T) {
		apiURL := subPath + "/get-done-task"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		callId := globalLib.GenerateObjectId()
		now := globalLib.GetCurrentTime(local.TimeZone).Add(-30 * time.Minute)
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"viewedTaskers": []interface{}{
					"**********",
				},
				"collectionDate": time.Now().Add(-4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"voiceCallHistory": map[string]interface{}{
					"callId": callId,
					"from":   "**********",
					"to":     "**********",
					"status": "ACTIVE",
				},
				"taskerRated": false,
				"fromPartner": globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				b, _ := io.ReadAll(res.Body)
				var result *modelTask.Task
				json.Unmarshal(b, &result)
				So(result.AcceptedTasker[0].TaskerId, ShouldEqual, body["userId"])
				So(result.FromPartner, ShouldEqual, globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP)
			})
		})
	})

	t.Run("43", func(t *testing.T) {
		apiURL := subPath + "/update-verification-status"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate UpdateVerificationStatus")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if taskId is blank", func() {
				body := map[string]interface{}{
					"taskId": "",
					"verificationData": map[string]interface{}{
						"vTaskId":   "9oEqchzTiRmTR5uTX",
						"vTaskerId": "TCyp9o4cyMngYTkuh",
						"status":    true,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if verificationData is nil", func() {
				body := map[string]interface{}{
					"taskId": "9oEqchzTiRmTR5uTX",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_VERIFICATION_DATA_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_VERIFICATION_DATA_REQUIRED.ErrorCode)
			})

			Convey("Check the response if Task not found", func() {
				body := map[string]interface{}{
					"taskId": "123",
					"verificationData": map[string]interface{}{
						"vTaskId":   "9oEqchzTiRmTR5uTX",
						"vTaskerId": "TCyp9o4cyMngYTkuh",
						"status":    true,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("44", func(t *testing.T) {
		// UpdateVerificationStatus
		apiUrl := "/api/v3/api-asker-vn/update-verification-status"
		log.Println("==================================== UpdateVerificationStatus")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().Month(), time.Now().AddDate(0, 0, -3).Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().AddDate(0, -1, 0).Month(), time.Now().Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId": ids[0],
				"verificationData": map[string]interface{}{
					"vTaskId":   ids[0],
					"vTaskerId": "**********",
					"status":    true,
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test UpdateVerificationStatus", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["name"], ShouldEqual, "Tasker 01")
					So(respResult["avatar"], ShouldEqual, "Tasker Avatar 01")
				})
				Convey("Then check database UpdateVerificationStatus", func() {
					var task *modelTask.Task
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], ids[0], bson.M{"_id": 1, "verification": 1}, &task)
					So(task, ShouldNotBeNil)
					So(task.Verification, ShouldNotBeNil)
					So(task.Verification.VTaskId, ShouldEqual, ids[0])
				})
			})
		})
	})

	t.Run("45", func(t *testing.T) {
		// GetLastPostTaskCleaning
		apiUrl := "/api/v3/api-asker-vn/get-last-post-task-cleaning"
		log.Println("==================================== Validate GetLastPostTaskCleaning")
		Convey("Check request when params nil", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when userId blank", t, func() {
			body := map[string]interface{}{
				"userId":  "",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
		})

		Convey("Check request when isoCode blank", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
		})

		Convey("Check request when isoCode incorrect", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": "HH",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
		})

		Convey("Check request when params invalid", t, func() {
			body := map[string]interface{}{
				"userId":  1231232312,
				"isoCode": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 400)

			var respResult map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
		})
	})

	t.Run("46", func(t *testing.T) {
		// GetLastPostTaskCleaning
		apiUrl := "/api/v3/api-asker-vn/get-last-post-task-cleaning"
		log.Println("==================================== GetLastPostTaskCleaning")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING},
			bson.M{
				"$push": bson.M{"tip.requirements": bson.M{
					"type":     1,
					"duration": 1,
				}},
			})

		defer UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$unset": bson.M{"tip.requirements": 1}})
		//Get Service
		repoService := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"serviceId":   repoService.XId,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"isTetBooking": true,
				"requirements": []map[string]interface{}{
					{
						"type": 1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Last Post Task", func() {
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["description"], ShouldEqual, "Desc Test")
				So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResult["contactName"], ShouldEqual, "Asker 01")
				So(respResult["phone"], ShouldEqual, "**********")
				So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
				So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["serviceId"], ShouldEqual, result["_id"])
				So(respResult["serviceText"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_HOME_CLEANING)
				So(respResult["date"], ShouldNotBeNil)
				So(respResult["payment"].(map[string]interface{})["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_CREDIT)
				So(respResult["isTetBooking"], ShouldBeNil)
				So(len(respResult["requirements"].([]interface{})), ShouldEqual, 1)
				So(respResult["requirements"].([]interface{})[0].(map[string]interface{})["type"], ShouldEqual, 1)
				So(respResult["requirements"].([]interface{})[0].(map[string]interface{})["duration"], ShouldEqual, 1)
			})
		})
	})

	t.Run("46.1", func(t *testing.T) {
		// GetLastPostTaskCleaning
		apiUrl := "/api/v3/api-asker-vn/get-last-post-task-cleaning"
		log.Println("==================================== GetLastPostTaskCleaning with blacklist payment")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_HOME_CLEANING},
			map[string]interface{}{
				"$set": map[string]interface{}{
					"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
					"tetBookingDates": map[string]interface{}{
						"fromDate":   globalLib.ParseDateFromString("2022-11-09T00:00:00.000+07:00", local.TimeZone),
						"toDate":     globalLib.ParseDateFromString("2023-01-31T23:59:59.999+07:00", local.TimeZone),
						"minVersion": "3.2.1",
					},
				},
			},
		)

		//Get Service
		repoService := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"serviceId":   repoService.XId,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
				},
				"isTetBooking": true,
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Last Post Task", func() {
				UpdateService(bson.M{"text.en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$unset": bson.M{"tetBookingDates": 1}})
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["description"], ShouldEqual, "Desc Test")
				So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResult["contactName"], ShouldEqual, "Asker 01")
				So(respResult["phone"], ShouldEqual, "**********")
				So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
				So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				So(respResult["serviceId"], ShouldEqual, result["_id"])
				So(respResult["serviceText"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_HOME_CLEANING)
				So(respResult["date"], ShouldNotBeNil)
				So(respResult["payment"].(map[string]interface{})["method"], ShouldEqual, globalConstant.PAYMENT_METHOD_CASH)
				So(respResult["isTetBooking"], ShouldBeNil)
			})
		})
	})

	t.Run("47", func(t *testing.T) {
		apiURL := subPath + "/update-task-report"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate UpdateTaskReport")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if taskId is blank", func() {
				body := map[string]interface{}{
					"taskId": "",
					"reportData": map[string]interface{}{
						"reason": map[string]interface{}{
							"vi": "Báo cáo mất cắp",
							"en": "Report theft",
							"ko": "도난 신고",
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if reportData is nil", func() {
				body := map[string]interface{}{
					"taskId": "9oEqchzTiRmTR5uTX",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_REPORT_DATA_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_REPORT_DATA_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("48", func(t *testing.T) {
		// UpdateVerificationStatus
		apiUrl := "/api/v3/api-asker-vn/update-task-report"
		log.Println("==================================== UpdateTaskReport")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().Month(), time.Now().AddDate(0, 0, -3).Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().AddDate(0, -1, 0).Month(), time.Now().Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"taskId": ids[0],
				"reportData": map[string]interface{}{
					"reason": map[string]interface{}{
						"vi": "Báo cáo mất cắp",
						"en": "Report theft",
						"ko": "도난 신고",
					},
				},
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database UpdateTaskReport", func() {
				var task *modelTask.Task
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
					bson.M{
						"_id":              ids[0],
						"report.reason.en": "Report theft",
					},
					bson.M{"_id": 1},
					&task,
				)
				So(task, ShouldNotBeNil)
			})
		})
	})

	t.Run("49", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-tasks-done"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate GetTasksDone")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId is blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is incorrect", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
		})
	})

	t.Run("50", func(t *testing.T) {
		// GetTasksDone
		apiUrl := "/api/v3/api-asker-vn/get-tasks-done"
		log.Println("==================================== GetTasksDone")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Day := now.AddDate(0, 0, -2)
		last3Day := now.AddDate(0, 0, -3)
		last4Day := now.AddDate(0, 0, -4)
		last5Day := now.AddDate(0, 0, -5)
		last6Day := now.AddDate(0, 0, -6)

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,15,15", last2Day.Year(), int(last2Day.Month()), last2Day.Day()),
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 02",
				"address":       "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_DONE,
				"date":          fmt.Sprintf("%d,%d,%d,15,15", last3Day.Year(), int(last3Day.Month()), last3Day.Day()),
				"isCloseRating": false,
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 03",
				"address":       "Quận 3, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_DONE,
				"rated":         false,
				"date":          fmt.Sprintf("%d,%d,%d,15,15", last4Day.Year(), int(last4Day.Month()), last4Day.Day()),
				"isCloseRating": false,
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 4, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       false,
				"date":        fmt.Sprintf("%d,%d,%d,15,15", last5Day.Year(), int(last5Day.Month()), last5Day.Day()),
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 05",
				"address":     "Quận 5, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       false,
				"date":        fmt.Sprintf("%d,%d,%d,15,15", last6Day.Year(), int(last6Day.Month()), last6Day.Day()),
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 06",
				"address":       "Quận 6, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_DONE,
				"date":          "2020,11,02,15,35",
				"isCloseRating": false,
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(len(respResult), ShouldEqual, 5)
		})
	})

	t.Run("51", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-tasks-confirmed-asker"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate GetTasksConfirmedAsker")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId is blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode is incorrect", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
		})
	})

	t.Run("52", func(t *testing.T) {
		// GetTasksConfirmedAsker
		apiUrl := "/api/v3/api-asker-vn/get-tasks-confirmed-asker"
		log.Println("==================================== GetTasksConfirmedAsker")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		yesterday := now.AddDate(0, 0, -1)
		twoDayAgo := now.AddDate(0, 0, -2)
		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"duration":    2,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", yesterday.Year(), yesterday.Month(), yesterday.Day(), yesterday.Hour(), yesterday.Minute()),
				"fromPartner": globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 02",
				"address":       "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_CONFIRMED,
				"date":          fmt.Sprintf("%d,%d,%d,%d,%d", twoDayAgo.Year(), twoDayAgo.Month(), twoDayAgo.Day(), twoDayAgo.Hour(), twoDayAgo.Minute()),
				"isCloseRating": false,
				"duration":      3,
				"detailLaundry": map[string]interface{}{"isReceived": true},
				"fromPartner":   globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 03",
				"address":       "Quận 3, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_CONFIRMED,
				"rated":         false,
				"duration":      2,
				"date":          fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"isCloseRating": false,
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 04",
				"address":     "Quận 4, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"rated":       false,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 05",
				"address":       "Quận 5, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_DONE,
				"rated":         false,
				"date":          fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"detailLaundry": map[string]interface{}{"mapisReceived": false},
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 06",
				"address":       "Quận 6, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_CONFIRMED,
				"date":          "2020,11,02,15,35",
				"isCloseRating": false,
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(respResult[0]["_id"], ShouldEqual, ids[1])
			So(respResult[0]["fromPartner"], ShouldEqual, globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP)
			So(respResult[1]["date"], ShouldBeGreaterThan, respResult[0]["date"])
			So(respResult[1]["fromPartner"], ShouldEqual, globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP)
			So(len(respResult), ShouldEqual, 2)
		})
	})

	t.Run("53", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/close-rating-task"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate CloseRatingTask")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId is blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"taskId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if taskId is blank", func() {
				body := map[string]interface{}{
					"userId": "123",
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("54", func(t *testing.T) {
		// GetTasksConfirmedAsker
		apiUrl := "/api/v3/api-asker-vn/close-rating-task"
		log.Println("==================================== CloseRatingTask")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"duration":    2,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.AddDate(0, 0, -1).Day(), now.Hour(), now.Minute()),
			}, {
				"askerPhone":    "**********",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description":   "Desc Test 02",
				"address":       "Quận 2, Hồ Chí Minh, Việt Nam",
				"status":        globalConstant.TASK_STATUS_CONFIRMED,
				"date":          fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.AddDate(0, 0, -2).Day(), now.Hour(), now.Minute()),
				"isCloseRating": false,
				"duration":      3,
				"detailLaundry": map[string]interface{}{"isReceived": true},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)
		})
		Convey("Check database", t, func() {
			var task *modelTask.Task
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": ids[0], "isCloseRating": true}, bson.M{"isCloseRating": 1}, &task)
			So(task, ShouldNotBeNil)
		})
	})
	t.Run("55", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/translate-listbuy"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate TranslateListBuy")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if taskId is blank", func() {
				body := map[string]interface{}{
					"Language": globalConstant.LANG_VI,
					"taskId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("56", func(t *testing.T) {
		// TranslateListBuy
		apiUrl := "/api/v3/api-asker-vn/translate-listbuy"
		log.Println("==================================== TranslateListBuy")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"duration":    2,
				"goMarketDetailTranslate": map[string]interface{}{
					"text": "Cash",
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"taskId":   ids[0],
				"language": globalConstant.LANG_VI,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(respResult["translated"], ShouldBeTrue)
			So(respResult["translatedText"], ShouldEqual, "Cash")
		})
	})
	t.Run("57", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/translate-task-note"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			log.Println("==================================== Validate TranslateTaskNote")
			Convey("Check the response if params is not valid", func() {
				body := map[string]interface{}{
					"taskId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if taskId is blank", func() {
				body := map[string]interface{}{
					"Language": globalConstant.LANG_VI,
					"taskId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("58", func(t *testing.T) {
		// TranslateTaskNote
		apiUrl := "/api/v3/api-asker-vn/translate-task-note"
		log.Println("==================================== TranslateTaskNote")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"duration":    2,
				"taskNoteTranslated": map[string]interface{}{
					"text": "Cash",
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"taskId":   ids[0],
				"language": globalConstant.LANG_VI,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var respResult map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(resp.Code, ShouldEqual, 200)
			So(respResult["translated"], ShouldBeTrue)
			So(respResult["translatedText"], ShouldEqual, "Cash")
		})
	})

	// t.Run("59", func(t *testing.T) {
	// 	apiURL := "/api/v3/api-asker-vn/tracking-done-task-aiqua"
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
	// 		log.Println("==================================== Validate TrackingDoneTaskAiqua")
	// 		Convey("Check the response if params is not valid", func() {
	// 			body := map[string]interface{}{
	// 				"taskId": 123123,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
	// 		})
	// 		Convey("Check the response if taskId is blank", func() {
	// 			body := map[string]interface{}{
	// 				"taskId": "",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
	// 		})
	// 		Convey("Check the response if cfg is blank", func() {
	// 			body := map[string]interface{}{
	// 				"taskId": "123",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 500)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_AIQUA_CONFIG_NOT_SET.Message)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_AIQUA_CONFIG_NOT_SET.ErrorCode)
	// 		})
	// 	})
	// })

	// ================= create-asker-complaint
	// Validate
	t.Run("60", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/create-user-complaint"
		log.Println("==================================== Create asker complaint")

		Convey("Check response if taskId is empty", t, func() {
			body := map[string]interface{}{
				"taskId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var result map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &result)
			So(resp.Code, ShouldEqual, 400)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
		})

		Convey("Check response if reason is nil", t, func() {
			body := map[string]interface{}{
				"taskId": "1",
				"reason": nil,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var result map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &result)
			So(resp.Code, ShouldEqual, 400)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_REASON_REQUIRED.ErrorCode)
		})

		Convey("Check response if reportData is nil", t, func() {
			body := map[string]interface{}{
				"taskId": "1",
				"reason": map[string]interface{}{
					"vi": "11",
				},
				"reportData": map[string]interface{}{},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			var result map[string]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &result)
			So(resp.Code, ShouldEqual, 400)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_REPORT_DATA_REQUIRED.ErrorCode)
		})
	})

	t.Run("61", func(t *testing.T) {
		// Create asker complaint
		apiUrl := "/api/v3/api-asker-vn/create-user-complaint"
		log.Println("==================================== Create asker complaint")

		ResetData()

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"reason": map[string]interface{}{
					"vi": "12",
				},
				"taskId": "123",
				"reportData": map[string]interface{}{
					"test": "test",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 404)
			result := map[string]map[string]interface{}{}
			b, _ := io.ReadAll(resp.Body)
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_FOUND.ErrorCode)
		})
	})

	t.Run("62", func(t *testing.T) {
		// Create asker complaint
		apiUrl := "/api/v3/api-asker-vn/create-user-complaint"
		log.Println("==================================== Create asker complaint")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().Month(), time.Now().AddDate(0, 0, -3).Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"reason": map[string]interface{}{
					"vi": "12",
				},
				"taskId": ids[0],
				"reportData": map[string]interface{}{
					"test": "test",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)
			result := map[string]map[string]interface{}{}
			b, _ := io.ReadAll(resp.Body)
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_NOT_DONE.ErrorCode)
		})
	})

	t.Run("63", func(t *testing.T) {
		// Create asker complaint
		apiUrl := "/api/v3/api-asker-vn/create-user-complaint"
		log.Println("==================================== Create asker complaint")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().Month(), time.Now().AddDate(0, 0, -3).Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
				"reported": true,
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"reason": map[string]interface{}{
					"vi": "12",
				},
				"taskId": ids[0],
				"reportData": map[string]interface{}{
					"test": "test",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 500)
			result := map[string]map[string]interface{}{}
			b, _ := io.ReadAll(resp.Body)
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_HAS_BEEN_REPORTED.ErrorCode)
		})
	})

	t.Run("64", func(t *testing.T) {
		// Create asker complaint
		apiUrl := "/api/v3/api-asker-vn/create-user-complaint"
		log.Println("==================================== Create asker complaint")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0777777772",
				"name":  "Dev Test 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"emails": []map[string]interface{}{
					{"address": "<EMAIL>"},
				},
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0777777772",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", time.Now().Year(), time.Now().Month(), time.Now().AddDate(0, 0, -3).Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Tasker 01",
						"avatar":   "Tasker Avatar 01",
					},
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"reason": map[string]interface{}{
					"vi": "Báo cáo mất cắp",
					"en": "Report theft",
					"ko": "도난 신고",
					"th": "รายงานสิ่งของสูญหาย",
				},
				"taskId": ids[0],
				"reportData": map[string]interface{}{
					"description":       "Tôi đã bị mất ...",
					"valueOfItem":       "*************đ",
					"lastSeenItem":      "Là trước khi tui mất",
					"peoplesEnter":      "Tasker 1, 2, 3",
					"timeMissItem":      "Ngày hôm nay",
					"timeToReport":      "Bây giờ",
					"detailDescription": "Tôi đã bị đánh cắp mất trái tim ...",
					"attitudes":         []string{"Thái độ tốt"},
					"skills":            []string{"Skill tốt"},
					"laundryQuality":    []string{"Chất lượng tốt"},
				},
				"type": "Mất đồ",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)

			var complaint *userComplaint.UserComplaint
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_COMPLAINT[local.ISO_CODE], bson.M{}, bson.M{}, &complaint)
			So(complaint, ShouldNotBeNil)
			So(complaint.UserId, ShouldEqual, "0777777772")
			So(complaint.Reason.En, ShouldEqual, "Report theft")
			So(complaint.TaskId, ShouldEqual, ids[0])
			So(complaint.Type, ShouldEqual, "Mất đồ")
			So(complaint.ReportData, ShouldNotBeNil)
			So(complaint.ReportData.Description, ShouldEqual, "Tôi đã bị mất ...")
			So(complaint.ReportData.ValueOfItem, ShouldEqual, "*************đ")
			So(complaint.ReportData.LastSeenItem, ShouldEqual, "Là trước khi tui mất")
			So(complaint.ReportData.PeoplesEnter, ShouldEqual, "Tasker 1, 2, 3")
			So(complaint.ReportData.TimeMissItem, ShouldEqual, "Ngày hôm nay")
			So(complaint.ReportData.TimeToReport, ShouldEqual, "Bây giờ")
			So(complaint.ReportData.DetailDescription, ShouldEqual, "Tôi đã bị đánh cắp mất trái tim ...")
			So(len(complaint.ReportData.Attitudes), ShouldEqual, 1)
			So(complaint.ReportData.Attitudes[0], ShouldEqual, "Thái độ tốt")
			So(len(complaint.ReportData.Skills), ShouldEqual, 1)
			So(complaint.ReportData.Skills[0], ShouldEqual, "Skill tốt")
			So(len(complaint.ReportData.LaundryQuality), ShouldEqual, 1)
			So(complaint.ReportData.LaundryQuality[0], ShouldEqual, "Chất lượng tốt")

			var task *modelTask.Task
			globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], ids[0], bson.M{"reported": 1}, &task)
			So(task.Reported, ShouldBeTrue)
		})
	})
	// GET TASK DETAIL (With payment time)
	// ShopeePay
	t.Run("66", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		updatedAt := globalLib.GetCurrentTime(local.TimeZone)
		transactionIds := CreateVNShopeePayTransaction([]map[string]interface{}{
			{
				"id":        globalLib.GenerateObjectId(),
				"status":    globalConstant.PAYMENT_TRANSACTION_STATUS_RESPONSED,
				"createdAt": updatedAt,
				"updatedAt": updatedAt,
				"charged":   true,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method":        globalConstant.PAYMENT_METHOD_SHOPEE_PAY,
					"status":        globalConstant.TASK_PAYMENT_STATUS_PAID,
					"transactionId": transactionIds[0],
				},
				"isPrepayTask": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)
					So(respResult["payment"], ShouldNotBeNil)
					So(respResult["payment"]["date"], ShouldNotBeNil)
					So(globalLib.ParseDateFromString(respResult["payment"]["date"].(string), local.TimeZone).String()[:16], ShouldEqual, updatedAt.String()[:16])
				})
			})
		})
	})

	// VnPay
	t.Run("67", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		updatedAt := globalLib.GetCurrentTime(local.TimeZone)
		transactionIds := CreateVNPayTransaction([]map[string]interface{}{
			{
				"id":        globalLib.GenerateObjectId(),
				"status":    globalConstant.PAYMENT_TRANSACTION_STATUS_RESPONSED,
				"createdAt": updatedAt,
				"updatedAt": updatedAt,
				"charged":   true,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method":        globalConstant.PAYMENT_METHOD_VN_PAY,
					"status":        globalConstant.TASK_PAYMENT_STATUS_PAID,
					"transactionId": transactionIds[0],
				},
				"isPrepayTask": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)
					So(respResult["payment"], ShouldNotBeNil)
					So(respResult["payment"]["date"], ShouldNotBeNil)
					So(globalLib.ParseDateFromString(respResult["payment"]["date"].(string), local.TimeZone).String()[:16], ShouldEqual, updatedAt.String()[:16])
				})
			})
		})
	})

	// ZaloPay
	t.Run("68", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		updatedAt := globalLib.GetCurrentTime(local.TimeZone)
		transactionIds := CreateZaloPayTransaction([]map[string]interface{}{
			{
				"id":        globalLib.GenerateObjectId(),
				"status":    globalConstant.PAYMENT_TRANSACTION_STATUS_RESPONSED,
				"createdAt": updatedAt,
				"updatedAt": updatedAt,
				"charged":   true,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method":        globalConstant.PAYMENT_METHOD_ZALO_PAY,
					"status":        globalConstant.TASK_PAYMENT_STATUS_PAID,
					"transactionId": transactionIds[0],
				},
				"isPrepayTask": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)
					So(respResult["payment"], ShouldNotBeNil)
					So(respResult["payment"]["date"], ShouldNotBeNil)
					So(globalLib.ParseDateFromString(respResult["payment"]["date"].(string), local.TimeZone).String()[:16], ShouldEqual, updatedAt.String()[:16])
				})
			})
		})
	})

	// Momo
	t.Run("69", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		updatedAt := globalLib.GetCurrentTime(local.TimeZone)
		transactionIds := CreateMomoTransaction([]map[string]interface{}{
			{
				"id":        globalLib.GenerateObjectId(),
				"status":    globalConstant.PAYMENT_TRANSACTION_STATUS_RESPONSED,
				"createdAt": updatedAt,
				"updatedAt": updatedAt,
				"charged":   true,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method":        globalConstant.PAYMENT_METHOD_MOMO,
					"status":        globalConstant.TASK_PAYMENT_STATUS_PAID,
					"transactionId": transactionIds[0],
				},
				"isPrepayTask": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)
					So(respResult["payment"], ShouldNotBeNil)
					So(respResult["payment"]["date"], ShouldNotBeNil)
					So(globalLib.ParseDateFromString(respResult["payment"]["date"].(string), local.TimeZone).String()[:16], ShouldEqual, updatedAt.String()[:16])
				})
			})
		})
	})

	// Tiki mini app
	t.Run("70", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		updatedAt := globalLib.GetCurrentTime(local.TimeZone)
		transactionIds := CreateTikiMiniAppTransaction([]map[string]interface{}{
			{
				"id":        globalLib.GenerateObjectId(),
				"status":    globalConstant.PAYMENT_TRANSACTION_STATUS_RESPONSED,
				"createdAt": updatedAt,
				"updatedAt": updatedAt,
				"charged":   true,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method":        globalConstant.PAYMENT_METHOD_TIKI_MINI_APP,
					"status":        globalConstant.TASK_PAYMENT_STATUS_PAID,
					"transactionId": transactionIds[0],
				},
				"isPrepayTask": true,
				"fromPartner":  globalConstant.TASK_FROM_PARTNER_TIKI_MINI_APP,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)
					So(respResult["payment"], ShouldNotBeNil)
					So(respResult["payment"]["date"], ShouldNotBeNil)
					So(globalLib.ParseDateFromString(respResult["payment"]["date"].(string), local.TimeZone).String()[:16], ShouldEqual, updatedAt.String()[:16])
				})
			})
		})
	})
	// GET TASK DETAIL (with task outstandingPayment)
	// CARD
	t.Run("74", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method":    globalConstant.PAYMENT_METHOD_CARD,
					"isPaidOff": true,
				},
				"isoCode": local.ISO_CODE,
			},
		})

		CreateOutstandingPayment([]map[string]interface{}{
			{
				"charged":       false,
				"cost":          401000,
				"currency":      "VND",
				"cardId":        "o0cj4fi9ihtktxt1e",
				"pspReference":  "2612694315845147",
				"refusalReason": "Refused",
				"askerId":       "**********",
				"data": map[string]interface{}{
					"taskId": taskIds[0],
				},
				"cardInfo": map[string]interface{}{
					"number": "4111",
					"type":   "DEBIT",
				},
				"status": globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": taskIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)
					So(respResult["outStandingPayment"], ShouldNotBeNil)
					So(respResult["outStandingPayment"]["date"], ShouldNotBeNil)
					So(respResult["outStandingPayment"]["cardNumber"], ShouldEqual, "4111")
					So(respResult["outStandingPayment"]["cardType"], ShouldEqual, "DEBIT")
				})
			})
		})
	})

	// Get task detail not have permission view chat & send chat
	t.Run("75", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get task detail not have permission view chat & send chat")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 2
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-7 * 24 * time.Hour).Add(-time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeFalse)
					So(respResult["canSendChat"], ShouldBeFalse)
				})
			})
		})
	})

	// Get task detail can view chat but cannot send chat
	t.Run("76", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get task detail can view chat but cannot send chat")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 2
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-7 * 24 * time.Hour).Add(time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "xxx",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeTrue)
					So(respResult["canSendChat"], ShouldBeFalse)
				})
			})
		})
	})

	// Get task detail can view chat but can not send chat (status CANCELED)
	t.Run("77", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get task detail can view chat but can not send chat (status CANCELED)")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 2
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-30 * time.Minute).Add(time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "xxx",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeTrue)
					So(respResult["canSendChat"], ShouldBeFalse)
				})
			})
		})
	})

	// Get task detail can view chat but can not send chat (status DONE)
	t.Run("78", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get task detail can view chat but can not send chat (status DONE)")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 2
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-30 * time.Minute).Add(time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeFalse)
					So(respResult["canSendChat"], ShouldBeTrue)
				})
			})
		})
	})

	// MOVING TASK. Get task detail can view chat and send chat because task not done && task not pass 24h from done task time
	t.Run("78.1", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== MOVING TASK. Get task detail can view chat and send chat because task not done && task not pass 24h from done task time")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 1
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-30 * time.Minute).Add(time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)

					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeFalse)
					So(respResult["canSendChat"], ShouldBeTrue)
				})
			})
		})
	})

	// MOVING TASK. Get task detail can view chat but can not send chat because task DONE although task not pass 24h from done task time
	t.Run("78.2", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== MOVING TASK. Get task detail can view chat but can not send chat because task DONE although task not pass 24h from done task time")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 1
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-30 * time.Minute).Add(time.Minute)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "xxx",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)

					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeTrue)
					So(respResult["canSendChat"], ShouldBeFalse)
				})
			})
		})
	})

	t.Run("79", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail with VAT")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"costDetail": map[string]interface{}{
					"baseCost":  180000.0,
					"cost":      200000.0,
					"finalCost": 150000.0,
					"vat":       15000,
					"totalCost": 165000,
					"currency":  "VND",
				},
				"requestVatInfo": map[string]interface{}{
					"companyEmail":   "<EMAIL>",
					"companyName":    "cong ty bTaskee",
					"taxCode":        "*********",
					"companyAddress": "09 Tranaf nAox",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
					requestVatInfo := respResult["requestVatInfo"].(map[string]interface{})
					So(requestVatInfo["companyEmail"], ShouldEqual, "<EMAIL>")
					So(requestVatInfo["companyName"], ShouldEqual, "cong ty bTaskee")
					So(requestVatInfo["taxCode"], ShouldEqual, "*********")
					So(requestVatInfo["companyAddress"], ShouldEqual, "09 Tranaf nAox")
					var taskCostDetail *modelPricingResponse.CostResult
					taskCostDetailData, _ := json.Marshal(respResult["costDetail"])
					json.Unmarshal(taskCostDetailData, &taskCostDetail)
					So(taskCostDetail.Cost, ShouldEqual, 215000)
					So(taskCostDetail.FinalCost, ShouldEqual, 165000)
					So(taskCostDetail.Vat, ShouldEqual, 15000)
				})
			})
		})
	})
	// GetListHistoryTasks task has VAT
	t.Run("80", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,16,15",
				"costDetail": map[string]interface{}{
					"baseCost":  180000.0,
					"cost":      200000.0,
					"finalCost": 150000.0,
					"vat":       15000,
					"totalCost": 165000,
					"currency":  "VND",
				},
				"requestVatInfo": map[string]interface{}{
					"companyEmail":   "<EMAIL>",
					"companyName":    "cong ty bTaskee",
					"taxCode":        "*********",
					"companyAddress": "09 Tranaf nAox",
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			var respResults []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResults)
			So(resp.Code, ShouldEqual, 200)
			respResult := respResults[0]
			var taskCostDetail *modelPricingResponse.CostResult
			taskCostDetailData, _ := json.Marshal(respResult["costDetail"])
			json.Unmarshal(taskCostDetailData, &taskCostDetail)
			So(taskCostDetail.Cost, ShouldEqual, 215000)
			So(taskCostDetail.FinalCost, ShouldEqual, 165000)
			So(taskCostDetail.Vat, ShouldEqual, 15000)
		})
	})

	// GetListHistoryTasks task has VAT
	t.Run("80.1", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_OFFICE_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,16,15",
				"costDetail": map[string]interface{}{
					"baseCost":     200000.0,
					"cost":         200000.0,
					"finalCost":    200000.0,
					"vat":          20000,
					"totalCost":    220000,
					"newFinalCost": 300000,
					"currency":     "VND",
				},
				"newCostDetail": map[string]interface{}{
					"baseCost":  300000.0,
					"cost":      300000.0,
					"finalCost": 300000.0,
					"vat":       20000,
					"totalCost": 320000,
					"currency":  "VND",
				},
				"requestVatInfo": map[string]interface{}{
					"companyEmail":   "<EMAIL>",
					"companyName":    "cong ty bTaskee",
					"taxCode":        "*********",
					"companyAddress": "09 Tranaf nAox",
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			var respResults []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResults)
			So(resp.Code, ShouldEqual, 200)
			respResult := respResults[0]
			var taskCostDetail *modelPricingResponse.CostResult
			taskCostDetailData, _ := json.Marshal(respResult["costDetail"])
			json.Unmarshal(taskCostDetailData, &taskCostDetail)
			So(taskCostDetail.Cost, ShouldEqual, 220000)
			So(taskCostDetail.FinalCost, ShouldEqual, 220000)
			So(taskCostDetail.NewFinalCost, ShouldEqual, 320000)
			So(taskCostDetail.Vat, ShouldEqual, 20000)
		})
	})

	// GetListHistoryTasks task has VAT
	t.Run("80.2", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_OFFICE_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,16,15",
				"costDetail": map[string]interface{}{
					"baseCost":     200000.0,
					"cost":         200000.0,
					"finalCost":    150000.0,
					"vat":          20000,
					"totalCost":    170000,
					"newFinalCost": 250000,
					"currency":     "VND",
				},
				"newCostDetail": map[string]interface{}{
					"baseCost":  300000.0,
					"cost":      300000.0,
					"finalCost": 250000.0,
					"vat":       20000,
					"totalCost": 270000,
					"currency":  "VND",
				},
				"requestVatInfo": map[string]interface{}{
					"companyEmail":   "<EMAIL>",
					"companyName":    "cong ty bTaskee",
					"taxCode":        "*********",
					"companyAddress": "09 Tranaf nAox",
				},
			},
		})

		Convey("Check request", t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)

			var respResults []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResults)
			So(resp.Code, ShouldEqual, 200)
			respResult := respResults[0]
			var taskCostDetail *modelPricingResponse.CostResult
			taskCostDetailData, _ := json.Marshal(respResult["costDetail"])
			json.Unmarshal(taskCostDetailData, &taskCostDetail)
			So(taskCostDetail.Cost, ShouldEqual, 220000)
			So(taskCostDetail.FinalCost, ShouldEqual, 170000)
			So(taskCostDetail.NewFinalCost, ShouldEqual, 270000)
			So(taskCostDetail.Vat, ShouldEqual, 20000)
		})
	})
	// get task detail task washing machine
	t.Run("81", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WASHING_MACHINE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailWashingMachine": []map[string]interface{}{
					{
						"name": "TOP_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV1",
							"text": map[string]interface{}{
								"vi": "Dưới 9kg",
							},
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								},
							},
						},
					}, {
						"name": "TOP_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV2",
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								}, {
									"name": "DRYER",
									"text": map[string]interface{}{
										"vi": "Máy có sấy",
									},
								},
							},
						},
					}, {
						"name": "FRONT_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV2",
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								}, {
									"name": "DRYER",
									"text": map[string]interface{}{
										"vi": "Máy có sấy",
									},
								}, {
									"name": "JAPAN_WM",
									"text": map[string]interface{}{
										"vi": "Máy Nhật bãi",
									},
								},
							},
						},
					},
				},
				"collectionDate": time.Now().Add(10 * time.Hour),

				"shortAddress": "HCM",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.ShortAddress, ShouldEqual, "HCM")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
				detailWashingMachine := m.DetailWashingMachine
				So(len(detailWashingMachine), ShouldEqual, 3)
				So(detailWashingMachine[0].Name, ShouldEqual, "TOP_LOADING")
				So(detailWashingMachine[0].Type.Name, ShouldEqual, "LV1")
				So(len(detailWashingMachine[0].Type.Options), ShouldEqual, 1)
				So(detailWashingMachine[1].Name, ShouldEqual, "TOP_LOADING")
				So(detailWashingMachine[1].Type.Name, ShouldEqual, "LV2")
				So(len(detailWashingMachine[1].Type.Options), ShouldEqual, 2)
				So(detailWashingMachine[2].Name, ShouldEqual, "FRONT_LOADING")
				So(detailWashingMachine[2].Type.Name, ShouldEqual, "LV2")
				So(len(detailWashingMachine[2].Type.Options), ShouldEqual, 3)
			})
		})
	})
	t.Run("82", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": local.ISO_CODE,
			}, {
				"phone":   "0834567810",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WASHING_MACHINE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        "2020,11,02,16,15",
				"detailWashingMachine": []map[string]interface{}{
					{
						"name": "TOP_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV1",
							"text": map[string]interface{}{
								"vi": "Dưới 9kg",
							},
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								},
							},
						},
					}, {
						"name": "TOP_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV2",
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								}, {
									"name": "DRYER",
									"text": map[string]interface{}{
										"vi": "Máy có sấy",
									},
								},
							},
						},
					}, {
						"name": "FRONT_LOADING",
						"text": map[string]interface{}{
							"vi": "Máy cửa trên",
						},
						"type": map[string]interface{}{
							"name": "LV2",
							"options": []map[string]interface{}{
								{
									"name": "REMOVE_TUB",
									"text": map[string]interface{}{
										"vi": "Có tháo lồng giặt",
									},
								}, {
									"name": "DRYER",
									"text": map[string]interface{}{
										"vi": "Máy có sấy",
									},
								}, {
									"name": "JAPAN_WM",
									"text": map[string]interface{}{
										"vi": "Máy Nhật bãi",
									},
								},
							},
						},
					},
				},
				"isoCode": local.ISO_CODE,
				"costDetail": map[string]interface{}{
					"baseCost":  240000.0,
					"cost":      258000.0,
					"finalCost": 258000.0,
					"duration":  4.0,
					"currency":  "VND",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"status": "PAID",
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WATER_HEATER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "*********",
						"isPremiumTasker": true,
					},
				},
				"date": "2020,10,02,16,15",
				"detailWaterHeater": []map[string]interface{}{
					{
						"name": "LV1",
						"text": map[string]interface{}{
							"vi": "Dưới 20 lít",
						},
						"quantity": 2,
						"options": []map[string]interface{}{
							{
								"name": "PLASTER_CEILING",
								"text": map[string]interface{}{
									"vi": "Có tháo lồng giặt",
								},
								"quantity": 1,
							},
						},
					}, {
						"name": "LV2",
						"text": map[string]interface{}{
							"vi": "Trên 20 dưới 30 lít",
						},
						"quantity": 1,
					},
				},
				"costDetail": map[string]interface{}{
					"baseCost":  240000.0,
					"cost":      258000.0,
					"finalCost": 258000.0,
					"duration":  4.0,
					"currency":  "VND",
				},
				"shortAddress": "HCM",
			},
		})
		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      ids[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get List History Task", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)
				So(respResult[0]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
				So(respResult[0]["costDetail"], ShouldNotBeNil)
				So(respResultM[0]["costDetail"]["currency"], ShouldBeNil)
				So(respResult[0]["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(respResult[0]["rated"], ShouldBeTrue)
				So(respResultM[0]["rate"]["askerId"], ShouldEqual, "0834567810")
				So(respResultM[0]["rate"]["taskerId"], ShouldEqual, "**********")
				So(respResultM[0]["rate"]["review"], ShouldEqual, "Chị làm tốt, kỹ, nhanh nhẹn")
				var detailWashingMachine []*modelTask.TaskDetailWashingMachine
				detailWashingMachineData, _ := json.Marshal(respResult[0]["detailWashingMachine"])
				json.Unmarshal(detailWashingMachineData, &detailWashingMachine)
				So(len(detailWashingMachine), ShouldEqual, 3)
				So(detailWashingMachine[0].Name, ShouldEqual, "TOP_LOADING")
				So(detailWashingMachine[0].Type.Name, ShouldEqual, "LV1")
				So(len(detailWashingMachine[0].Type.Options), ShouldEqual, 1)
				So(detailWashingMachine[1].Name, ShouldEqual, "TOP_LOADING")
				So(detailWashingMachine[1].Type.Name, ShouldEqual, "LV2")
				So(len(detailWashingMachine[1].Type.Options), ShouldEqual, 2)
				So(detailWashingMachine[2].Name, ShouldEqual, "FRONT_LOADING")
				So(detailWashingMachine[2].Type.Name, ShouldEqual, "LV2")
				So(len(detailWashingMachine[2].Type.Options), ShouldEqual, 3)

				So(respResult[1]["detailWaterHeater"], ShouldNotBeNil)
				So(respResultM[1]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_WATER_HEATER)
				So(respResultM[1]["costDetail"]["cost"], ShouldNotBeNil)
				So(respResultM[1]["costDetail"]["finalCost"], ShouldNotBeNil)
				var detailWaterHeater []*modelTask.TaskDetailWaterHeater
				detailWaterHeaterData, _ := json.Marshal(respResult[1]["detailWaterHeater"])
				json.Unmarshal(detailWaterHeaterData, &detailWaterHeater)
				So(len(detailWaterHeater), ShouldEqual, 2)
				So(detailWaterHeater[0].Name, ShouldEqual, "LV1")
				So(detailWaterHeater[0].Text.Vi, ShouldEqual, "Dưới 20 lít")
				So(detailWaterHeater[0].Quantity, ShouldEqual, 2)
				So(len(detailWaterHeater[0].Options), ShouldEqual, 1)
				So(detailWaterHeater[0].Options[0].Name, ShouldEqual, "PLASTER_CEILING")
				So(detailWaterHeater[0].Options[0].Quantity, ShouldEqual, 1)
				So(detailWaterHeater[1].Name, ShouldEqual, "LV2")
				So(detailWaterHeater[1].Text.Vi, ShouldEqual, "Trên 20 dưới 30 lít")
				So(detailWaterHeater[1].Quantity, ShouldEqual, 1)
				So(len(detailWaterHeater[1].Options), ShouldEqual, 0)

				for _, task := range respResult {
					if task["_id"].(string) == ids[0] {
						var acceptedTasker []map[string]interface{}
						acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
						json.Unmarshal(acceptedTaskerData, &acceptedTasker)

						So(len(acceptedTasker), ShouldEqual, 1)
						So(acceptedTasker[0]["taskDone"], ShouldEqual, 0)
					}
				}
			})
		})
	})

	t.Run("83.1", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks has task moving")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": local.ISO_CODE,
			}, {
				"phone":   "0834567810",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		dateHomeCleaning := now.AddDate(0, 0, 1)
		date := now.AddDate(0, 0, -1)
		relatedTaskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 01",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"date":        fmt.Sprintf("%d,%d,%d,14,40", dateHomeCleaning.Year(), dateHomeCleaning.Month(), dateHomeCleaning.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"sourceTaskId": "xxx",
			},
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"date":        fmt.Sprintf("%d,%d,%d,20,40", dateHomeCleaning.Year(), dateHomeCleaning.Month(), dateHomeCleaning.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"sourceTaskId": "xxx",
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test 03",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"relatedTasks": map[string]interface{}{
					"oldHomeMovingSubTasks": []map[string]interface{}{
						{
							"_id": relatedTaskIds[0],
						},
					},
					"newHomeMovingSubTasks": []map[string]interface{}{
						{
							"_id": relatedTaskIds[1],
						},
					},
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"detailHomeMoving": map[string]interface{}{
					"completedStep": 1,
				},
				"date": fmt.Sprintf("%d,%d,%d,15,40", date.Year(), date.Month(), date.Day()),
			},
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 03",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"date": fmt.Sprintf("%d,%d,%d,14,40", date.Year(), date.Month(), date.Day()),
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      taskIds[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		body := map[string]interface{}{
			"userId":  "0834567810",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get List History Task", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2)

				So(respResult[0]["_id"], ShouldEqual, taskIds[0])
				So(respResult[0]["description"], ShouldEqual, "Desc Test 03")
				So(respResult[0]["detailHomeMoving"], ShouldNotBeNil)

				So(respResult[0]["relatedTasks"], ShouldNotBeNil)
				relatedTasks := cast.ToStringMap(respResult[0]["relatedTasks"])

				So(relatedTasks["oldHomeMovingSubTasks"], ShouldNotBeNil)
				oldHomeMovingSubTasks := cast.ToSlice(relatedTasks["oldHomeMovingSubTasks"])
				So(len(oldHomeMovingSubTasks), ShouldEqual, 1)
				oldHomeMovingSubTasks0 := cast.ToStringMap(oldHomeMovingSubTasks[0])
				So(oldHomeMovingSubTasks0["_id"], ShouldEqual, relatedTaskIds[0])
				So(oldHomeMovingSubTasks0["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)

				So(relatedTasks["newHomeMovingSubTasks"], ShouldNotBeNil)
				newHomeMovingSubTasks := cast.ToSlice(relatedTasks["newHomeMovingSubTasks"])
				So(len(newHomeMovingSubTasks), ShouldEqual, 1)
				newHomeMovingSubTasks0 := cast.ToStringMap(newHomeMovingSubTasks[0])
				So(newHomeMovingSubTasks0["_id"], ShouldEqual, relatedTaskIds[1])
				So(newHomeMovingSubTasks0["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)

				So(respResult[1]["_id"], ShouldEqual, taskIds[1])
				So(respResult[1]["description"], ShouldEqual, "Desc Test 03")
				So(respResult[1]["detailHomeMoving"], ShouldBeNil)
				So(respResult[1]["serviceName"], ShouldEqual, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING)
			})
		})
	})

	t.Run("83.2", func(t *testing.T) {
		// GetListHistoryTasks
		apiGetListHistoryTasks := "/api/v3/api-asker-vn/get-list-history-tasks"
		log.Println("==================================== GetListHistoryTasks has task moving")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": local.ISO_CODE,
			}, {
				"phone":   "0834567810",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, -1)
		for range [12]int{} {
			CreateTask([]map[string]interface{}{
				{
					"askerPhone":  "0834567810",
					"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
					"description": "Desc Test 03",
					"isoCode":     local.ISO_CODE,
					"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
					"status":      globalConstant.TASK_STATUS_DONE,
					"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
					"payment": map[string]interface{}{
						"method": globalConstant.PAYMENT_METHOD_CREDIT,
					},
					"date": fmt.Sprintf("%d,%d,%d,15,40", date.Year(), date.Month(), date.Day()),
				},
			})
		}

		dateHomeCleaning := now.AddDate(0, 0, 1)
		relatedTaskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 01",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"date":        fmt.Sprintf("%d,%d,%d,14,40", dateHomeCleaning.Year(), dateHomeCleaning.Month(), dateHomeCleaning.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"sourceTaskId": "xxx",
			},
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test 02",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"date":        fmt.Sprintf("%d,%d,%d,20,40", dateHomeCleaning.Year(), dateHomeCleaning.Month(), dateHomeCleaning.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"sourceTaskId": "xxx",
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_MOVING,
				"description": "Desc Test 03",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", now.Year(), now.Month(), now.Day()),
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"relatedTasks": map[string]interface{}{
					"oldHomeMovingSubTasks": []map[string]interface{}{
						{
							"_id": relatedTaskIds[0],
						},
					},
					"newHomeMovingSubTasks": []map[string]interface{}{
						{
							"_id": relatedTaskIds[1],
						},
					},
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"detailHomeMoving": map[string]interface{}{
					"completedStep": 1,
				},
				"date": fmt.Sprintf("%d,%d,%d,16,40", date.Year(), date.Month(), date.Day()),
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskId":      taskIds[0],
				"taskerPhone": "**********",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		body := map[string]interface{}{
			"userId":  "0834567810",
			"isoCode": local.ISO_CODE,
			"limit":   10,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetListHistoryTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get List History Task", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)

				So(len(respResult), ShouldEqual, 10)

				So(respResult[0]["_id"], ShouldEqual, taskIds[0])
				So(respResult[0]["description"], ShouldEqual, "Desc Test 03")
				So(respResult[0]["detailHomeMoving"], ShouldNotBeNil)

				So(respResult[0]["relatedTasks"], ShouldNotBeNil)
				relatedTasks := cast.ToStringMap(respResult[0]["relatedTasks"])

				So(relatedTasks["oldHomeMovingSubTasks"], ShouldNotBeNil)
				oldHomeMovingSubTasks := cast.ToSlice(relatedTasks["oldHomeMovingSubTasks"])
				So(len(oldHomeMovingSubTasks), ShouldEqual, 1)
				oldHomeMovingSubTasks0 := cast.ToStringMap(oldHomeMovingSubTasks[0])
				So(oldHomeMovingSubTasks0["_id"], ShouldEqual, relatedTaskIds[0])
				So(oldHomeMovingSubTasks0["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)

				So(relatedTasks["newHomeMovingSubTasks"], ShouldNotBeNil)
				newHomeMovingSubTasks := cast.ToSlice(relatedTasks["newHomeMovingSubTasks"])
				So(len(newHomeMovingSubTasks), ShouldEqual, 1)
				newHomeMovingSubTasks0 := cast.ToStringMap(newHomeMovingSubTasks[0])
				So(newHomeMovingSubTasks0["_id"], ShouldEqual, relatedTaskIds[1])
				So(newHomeMovingSubTasks0["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)
			})
		})
	})

	// Fix case error task laundry not have duration (duration = 0.0) -> will return canViewChatHistory base on task.date
	t.Run("83", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get task detail not have permission view chat & send chat")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 0.0
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-6 * 24 * time.Hour)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"isoCode":     globalConstant.ISO_CODE_TH,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "xxx",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			// return
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["canViewChatHistory"], ShouldBeTrue)
					So(respResult["canSendChat"], ShouldBeFalse)
				})
			})
		})
	})

	// Get task detail has report content
	t.Run("84", func(t *testing.T) {
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get task detail has report content")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567899",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Task
		duration := 0.0
		taskDate := globalLib.GetCurrentTime(local.TimeZone).Add(-time.Duration(duration) * time.Hour).Add(-6 * 24 * time.Hour)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"payment": map[string]interface{}{
					"status":        globalConstant.PAYMENT_STATUS_PAID,
					"transactionId": "123",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", taskDate.Year(), taskDate.Month(), taskDate.Day(), taskDate.Hour(), taskDate.Minute()),
				"duration": duration,
				"reported": true,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		userComplaintIds := CreateUserComplaint([]map[string]interface{}{
			{
				"userId": "**********",
				"taskId": ids[0],
				"reason": map[string]interface{}{
					"vi": "Khác",
					"en": "Other",
					"ko": "다른 사람",
					"th": "อื่น ๆ",
				},
				"reportData": map[string]interface{}{
					"description": "khoong co go dau bro report choi thoi",
					"images": []string{
						"https://toanphambucket.s3.amazonaws.com/cs%2Fcomplain%2Fthailandcomplaint-o2fvzc6y784qpkq",
						"https://toanphambucket.s3.amazonaws.com/cs%2Fcomplain%2Fthailandcomplaint-1ih2iymxp3xm1bn",
						"https://toanphambucket.s3.amazonaws.com/cs%2Fcomplain%2Fthailandcomplaint-orpe26x934uz3br",
					},
				},
				"createdAt": now,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["askerComplaint"], ShouldNotBeNil)

					askerComplaint := make(map[string]interface{})
					askerComplaintData, _ := json.Marshal(respResult["askerComplaint"])
					json.Unmarshal(askerComplaintData, &askerComplaint)
					So(askerComplaint["_id"], ShouldEqual, userComplaintIds[0])
					So(askerComplaint["userId"], ShouldEqual, "**********")
					So(askerComplaint["taskId"], ShouldEqual, ids[0])
					So(askerComplaint["reason"], ShouldResemble, map[string]interface{}{
						"vi": "Khác",
						"en": "Other",
						"ko": "다른 사람",
						"th": "อื่น ๆ",
					})
					So(askerComplaint["reportData"], ShouldResemble, map[string]interface{}{
						"description": "khoong co go dau bro report choi thoi",
						"images": []interface{}{
							"https://toanphambucket.s3.amazonaws.com/cs%2Fcomplain%2Fthailandcomplaint-o2fvzc6y784qpkq",
							"https://toanphambucket.s3.amazonaws.com/cs%2Fcomplain%2Fthailandcomplaint-1ih2iymxp3xm1bn",
							"https://toanphambucket.s3.amazonaws.com/cs%2Fcomplain%2Fthailandcomplaint-orpe26x934uz3br",
						},
					})
				})
			})
		})
	})
	// get task detail task water heater
	t.Run("85", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WATER_HEATER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailWaterHeater": []map[string]interface{}{
					{
						"name": "LV1",
						"text": map[string]interface{}{
							"vi": "Dưới 20 lít",
						},
						"quantity": 2,
						"options": []map[string]interface{}{
							{
								"name": "REMOVE_TUB",
								"text": map[string]interface{}{
									"vi": "Có tháo lồng giặt",
								},
								"quantity": 1,
							},
						},
					}, {
						"name": "LV2",
						"text": map[string]interface{}{
							"vi": "Trên 20 dưới 30 lít",
						},
						"quantity": 1,
					},
				},
				"shortAddress": "HCM",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.ShortAddress, ShouldEqual, "HCM")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
				detailWaterHeater := m.DetailWaterHeater
				So(len(detailWaterHeater), ShouldEqual, 2)
				So(detailWaterHeater[0].Name, ShouldEqual, "LV1")
				So(detailWaterHeater[0].Text.Vi, ShouldEqual, "Dưới 20 lít")
				So(detailWaterHeater[0].Quantity, ShouldEqual, 2)
				So(len(detailWaterHeater[0].Options), ShouldEqual, 1)
				So(detailWaterHeater[1].Name, ShouldEqual, "LV2")
				So(detailWaterHeater[1].Text.Vi, ShouldEqual, "Trên 20 dưới 30 lít")
				So(detailWaterHeater[1].Quantity, ShouldEqual, 1)
				So(len(detailWaterHeater[1].Options), ShouldEqual, 0)

				respResult := make(map[string]map[string]interface{})
				json.Unmarshal(b, &respResult)
				So(respResult["service"]["name"], ShouldEqual, globalConstant.SERVICE_KEY_NAME_WATER_HEATER)
			})
		})
	})

	// Get cancellationFee for isoCode VN
	t.Run("86", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CARD,
				},
			},
		})

		CreatePaymentTransaction([]map[string]interface{}{
			{
				"cost":      100000,
				"reference": "CT_" + ids[0],
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["cancellationFee"], ShouldEqual, 100000)
					So(respResult["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_VN)
					So(respResult["acceptedTasker"], ShouldNotBeNil)

					var resultAcceptedTasker map[string][]map[string]interface{}
					json.Unmarshal(bytes, &resultAcceptedTasker)
					So(len(resultAcceptedTasker["acceptedTasker"]), ShouldEqual, 1)
					So(resultAcceptedTasker["acceptedTasker"][0]["taskDone"], ShouldEqual, 0)
				})
			})
		})
	})

	// Get task detail with field to check if it changed to cash because of payment failed
	t.Run("86.1", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CASH,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  "UPDATE_PAYMENT_METHOD",
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
						"content": map[string]interface{}{
							"oldPaymentMethod": "QRIS",
							"newPaymentMethod": "CASH",
						},
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_VN)
					So(respResult["acceptedTasker"], ShouldNotBeNil)
					So(respResult["isChangedToCashBecauseOfPaymentFailed"], ShouldBeTrue)
				})
			})
		})
	})

	// Get cancellationFee for if cancellationFee is saved in task
	t.Run("87", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== Get cancellationFee for if cancellationFee is saved in task")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CANCELED,
				"countryCode": globalConstant.COUNTRY_CODE_TH,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "*********",
					},
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CARD,
				},
				"isoCode":         globalConstant.ISO_CODE_TH,
				"cancellationFee": 2000,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["cancellationFee"], ShouldEqual, 2000)
					So(respResult["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_TH)
					So(respResult["acceptedTasker"], ShouldNotBeNil)

					var resultAcceptedTasker map[string][]map[string]interface{}
					json.Unmarshal(bytes, &resultAcceptedTasker)
					So(len(resultAcceptedTasker["acceptedTasker"]), ShouldEqual, 1)
					So(resultAcceptedTasker["acceptedTasker"][0]["taskDone"], ShouldEqual, 0)
				})
			})
		})
	})
	t.Run("88", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail Home Moving started")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{
					"*********",
				},
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"askerId":        "**********",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":          "0834567710",
						"name":              "Tasker 01",
						"avatar":            "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating":         4.96999979019165,
						"taskDone":          938,
						"isFavouriteTasker": true,
					},
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"detailHomeMoving": map[string]interface{}{
					"oldHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "from Address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  local.ISO_CODE,
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"newHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "newHomeDetail address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  local.ISO_CODE,
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"furniture": []map[string]interface{}{
						{
							"name": "removableFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ dùng có thể tháo rời",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "solidFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ nội thất nguyên khối",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "electronic",
							"text": map[string]interface{}{
								"vi": "Thiết bị điện tử",
							},
							"quantity": 10,
							"images":   []string{"xxx"},
							"options": []map[string]interface{}{
								{
									"name": "airConditioner",
									"text": map[string]interface{}{
										"vi": "Tháo lắp máy lạnh",
									},
									"quantity": 1,
								},
								{
									"name": "waterHeater",
									"text": map[string]interface{}{
										"vi": "Máy nước nóng",
									},
									"options": []map[string]interface{}{
										{
											"name": "tanklessWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng trực tiếp",
											},
											"quantity": 1,
										},
										{
											"name": "traditionalWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng gián tiếp",
											},
											"quantity": 1,
										},
									},
									"quantity": 2,
								},
							},
						},
					},
					"completedStep": 2.0,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CONFIRMED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_VN)
					So(respResult["acceptedTasker"], ShouldNotBeNil)
					var resultAcceptedTasker map[string][]map[string]interface{}
					json.Unmarshal(bytes, &resultAcceptedTasker)
					So(len(resultAcceptedTasker["acceptedTasker"]), ShouldEqual, 1)
					So(resultAcceptedTasker["acceptedTasker"][0]["taskDone"], ShouldEqual, 938)
					So(resultAcceptedTasker["acceptedTasker"][0]["isFavouriteTasker"], ShouldBeTrue)

					So(respResult["detailHomeMoving"], ShouldNotBeNil)
					detailHomeMoving := cast.ToStringMap(respResult["detailHomeMoving"])
					So(detailHomeMoving["homeMovingProcess"], ShouldNotBeNil)
					So(detailHomeMoving["stepInProgress"], ShouldNotBeNil)
					So(detailHomeMoving["furniture"], ShouldNotBeNil)
					So(detailHomeMoving["newHomeDetail"], ShouldNotBeNil)
					So(detailHomeMoving["oldHomeDetail"], ShouldNotBeNil)
					So(detailHomeMoving["completedStep"], ShouldEqual, 2)
					So(detailHomeMoving["stepInProgress"].(map[string]interface{})["step"], ShouldEqual, 3)

					homeMovingProcess := detailHomeMoving["homeMovingProcess"].([]interface{})
					So(len(homeMovingProcess), ShouldEqual, 5)
					So(homeMovingProcess[0].(map[string]interface{})["step"], ShouldEqual, 1)
					So(homeMovingProcess[0].(map[string]interface{})["status"], ShouldEqual, "COMPLETED")
					So(homeMovingProcess[1].(map[string]interface{})["step"], ShouldEqual, 2)
					So(homeMovingProcess[1].(map[string]interface{})["status"], ShouldEqual, "COMPLETED")
					So(homeMovingProcess[2].(map[string]interface{})["step"], ShouldEqual, 3)
					So(homeMovingProcess[2].(map[string]interface{})["status"], ShouldEqual, "IN_PROGRESS")
					So(homeMovingProcess[3].(map[string]interface{})["step"], ShouldEqual, 4)
					So(homeMovingProcess[3].(map[string]interface{})["status"], ShouldBeNil)

					So(len(detailHomeMoving["furniture"].([]interface{})), ShouldEqual, 3)

					oldHomeDetail := &modelTask.TaskDetailHomeMovingHouseDetail{}
					b, _ := json.Marshal(detailHomeMoving["oldHomeDetail"])
					protojson.Unmarshal(b, oldHomeDetail)
					So(oldHomeDetail, ShouldNotBeNil)
					So(oldHomeDetail.AddressDetail.Address, ShouldEqual, "from Address")
					So(oldHomeDetail.AddressDetail.Description, ShouldEqual, "Toa nha bTaskee")
					So(oldHomeDetail.AddressDetail.HomeType, ShouldEqual, "HOME")
					So(oldHomeDetail.HomeType.Name, ShouldEqual, "apartment")
					So(oldHomeDetail.HomeType.Type.Name, ShouldEqual, "1bedroom")
					So(oldHomeDetail.IsCleaningRequired, ShouldBeTrue)
					So(len(oldHomeDetail.Options), ShouldEqual, 5)

					newHomeDetail := &modelTask.TaskDetailHomeMovingHouseDetail{}
					b, _ = json.Marshal(detailHomeMoving["newHomeDetail"])
					protojson.Unmarshal(b, newHomeDetail)
					So(newHomeDetail, ShouldNotBeNil)
					So(newHomeDetail.AddressDetail.Address, ShouldEqual, "newHomeDetail address")
					So(newHomeDetail.AddressDetail.Description, ShouldEqual, "Toa nha bTaskee")
					So(newHomeDetail.AddressDetail.HomeType, ShouldEqual, "HOME")
					So(newHomeDetail.HomeType.Name, ShouldEqual, "apartment")
					So(newHomeDetail.HomeType.Type.Name, ShouldEqual, "1bedroom")
					So(newHomeDetail.IsCleaningRequired, ShouldBeTrue)
					So(len(newHomeDetail.Options), ShouldEqual, 5)

				})
			})
		})
	})

	t.Run("89", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := "/api/v3/api-asker-vn/get-task-detail"
		log.Println("==================================== GetTaskDetail Home Moving not start yet")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{
					"*********",
				},
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"askerId":        "**********",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":          "0834567710",
						"name":              "Tasker 01",
						"avatar":            "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
						"avgRating":         4.96999979019165,
						"taskDone":          938,
						"isFavouriteTasker": true,
					},
				},
				"detailHomeMoving": map[string]interface{}{
					"oldHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "from Address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  local.ISO_CODE,
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"newHomeDetail": map[string]interface{}{
						"homeType": map[string]interface{}{
							"name": "apartment",
							"text": map[string]interface{}{
								"vi": "Chung cư",
							},
							"type": map[string]interface{}{
								"name": "1bedroom",
								"text": map[string]interface{}{
									"vi": "1 phòng ngủ",
								},
							},
						},
						"options": []map[string]interface{}{
							{
								"name": "stairsTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển thang bộ",
								},
								"option": map[string]interface{}{
									"name": "2floor",
									"text": map[string]interface{}{
										"vi": "2 tầng",
									},
								},
							},
							{
								"name": "byroad",
								"text": map[string]interface{}{
									"vi": "Nhà trong hẻm",
								},
								"option": map[string]interface{}{
									"name": "50m",
									"text": map[string]interface{}{
										"vi": "50m",
									},
								},
							},
							{
								"name": "garage",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
									"en": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "elevatorTransport",
								"text": map[string]interface{}{
									"vi": "Vận chuyển từ hầm xe",
								},
							},
							{
								"name": "cleaning",
							},
						},
						"addressDetail": map[string]interface{}{
							"address": "newHomeDetail address",
							"lat":     100.001,
							"lng":     10.007,
							"taskPlace": map[string]interface{}{
								"country":  local.ISO_CODE,
								"city":     "Hồ Chí Minh",
								"district": "Quận 7",
							},
							"homeType":    "HOME",
							"description": "Toa nha bTaskee",
							"houseNumber": "Toa nha bTaskee",
						},
						"isCleaningRequired": true,
					},
					"furniture": []map[string]interface{}{
						{
							"name": "removableFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ dùng có thể tháo rời",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "solidFurniture",
							"text": map[string]interface{}{
								"vi": "Đồ nội thất nguyên khối",
							},
							"quantity": 1,
							"images":   []string{"xxx"},
						},
						{
							"name": "electronic",
							"text": map[string]interface{}{
								"vi": "Thiết bị điện tử",
							},
							"quantity": 10,
							"images":   []string{"xxx"},
							"options": []map[string]interface{}{
								{
									"name": "airConditioner",
									"text": map[string]interface{}{
										"vi": "Tháo lắp máy lạnh",
									},
									"quantity": 1,
								},
								{
									"name": "waterHeater",
									"text": map[string]interface{}{
										"vi": "Máy nước nóng",
									},
									"options": []map[string]interface{}{
										{
											"name": "tanklessWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng trực tiếp",
											},
											"quantity": 1,
										},
										{
											"name": "traditionalWaterHeater",
											"text": map[string]interface{}{
												"vi": "Máy nước nóng gián tiếp",
											},
											"quantity": 1,
										},
									},
									"quantity": 2,
								},
							},
						},
					},
					"completedStep": 2.0,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_CONFIRMED)
					So(respResult["cost"], ShouldEqual, 200000)
					So(respResult["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_VN)
					So(respResult["acceptedTasker"], ShouldNotBeNil)
					var resultAcceptedTasker map[string][]map[string]interface{}
					json.Unmarshal(bytes, &resultAcceptedTasker)
					So(len(resultAcceptedTasker["acceptedTasker"]), ShouldEqual, 1)
					So(resultAcceptedTasker["acceptedTasker"][0]["taskDone"], ShouldEqual, 938)
					So(resultAcceptedTasker["acceptedTasker"][0]["isFavouriteTasker"], ShouldBeTrue)

					So(respResult["detailHomeMoving"], ShouldNotBeNil)
					detailHomeMoving := cast.ToStringMap(respResult["detailHomeMoving"])
					So(detailHomeMoving["homeMovingProcess"], ShouldNotBeNil)
					So(detailHomeMoving["stepInProgress"], ShouldBeNil)
					So(detailHomeMoving["furniture"], ShouldNotBeNil)
					So(detailHomeMoving["newHomeDetail"], ShouldNotBeNil)
					So(detailHomeMoving["oldHomeDetail"], ShouldNotBeNil)
					So(detailHomeMoving["completedStep"], ShouldEqual, 2)

					homeMovingProcess := detailHomeMoving["homeMovingProcess"].([]interface{})
					So(len(homeMovingProcess), ShouldEqual, 5)
					So(homeMovingProcess[0].(map[string]interface{})["step"], ShouldEqual, 1)
					So(homeMovingProcess[0].(map[string]interface{})["status"], ShouldBeNil)
					So(homeMovingProcess[1].(map[string]interface{})["step"], ShouldEqual, 2)
					So(homeMovingProcess[1].(map[string]interface{})["status"], ShouldBeNil)
					So(homeMovingProcess[2].(map[string]interface{})["step"], ShouldEqual, 3)
					So(homeMovingProcess[2].(map[string]interface{})["status"], ShouldBeNil)
					So(homeMovingProcess[3].(map[string]interface{})["step"], ShouldEqual, 4)
					So(homeMovingProcess[3].(map[string]interface{})["status"], ShouldBeNil)

					So(len(detailHomeMoving["furniture"].([]interface{})), ShouldEqual, 3)
					So(len(detailHomeMoving["removableElectronic"].([]interface{})), ShouldEqual, 3)

					So(cast.ToStringMap(cast.ToSlice(detailHomeMoving["removableElectronic"])[0])["name"], ShouldEqual, "airConditioner")
					So(cast.ToStringMap(cast.ToSlice(detailHomeMoving["removableElectronic"])[0])["quantity"], ShouldEqual, 1)
					So(cast.ToStringMap(cast.ToSlice(detailHomeMoving["removableElectronic"])[1])["name"], ShouldEqual, "tanklessWaterHeater")
					So(cast.ToStringMap(cast.ToSlice(detailHomeMoving["removableElectronic"])[1])["quantity"], ShouldEqual, 1)
					So(cast.ToStringMap(cast.ToSlice(detailHomeMoving["removableElectronic"])[2])["name"], ShouldEqual, "traditionalWaterHeater")
					So(cast.ToStringMap(cast.ToSlice(detailHomeMoving["removableElectronic"])[2])["quantity"], ShouldEqual, 1)

					oldHomeDetail := &modelTask.TaskDetailHomeMovingHouseDetail{}
					b, _ := json.Marshal(detailHomeMoving["oldHomeDetail"])
					protojson.Unmarshal(b, oldHomeDetail)
					So(oldHomeDetail, ShouldNotBeNil)
					So(oldHomeDetail.AddressDetail.Address, ShouldEqual, "from Address")
					So(oldHomeDetail.AddressDetail.Description, ShouldEqual, "Toa nha bTaskee")
					So(oldHomeDetail.AddressDetail.HomeType, ShouldEqual, "HOME")
					So(oldHomeDetail.HomeType.Name, ShouldEqual, "apartment")
					So(oldHomeDetail.HomeType.Type.Name, ShouldEqual, "1bedroom")
					So(oldHomeDetail.IsCleaningRequired, ShouldBeTrue)
					So(len(oldHomeDetail.Options), ShouldEqual, 5)

					newHomeDetail := &modelTask.TaskDetailHomeMovingHouseDetail{}
					b, _ = json.Marshal(detailHomeMoving["newHomeDetail"])
					protojson.Unmarshal(b, newHomeDetail)
					So(newHomeDetail, ShouldNotBeNil)
					So(newHomeDetail.AddressDetail.Address, ShouldEqual, "newHomeDetail address")
					So(newHomeDetail.AddressDetail.Description, ShouldEqual, "Toa nha bTaskee")
					So(newHomeDetail.AddressDetail.HomeType, ShouldEqual, "HOME")
					So(newHomeDetail.HomeType.Name, ShouldEqual, "apartment")
					So(newHomeDetail.HomeType.Type.Name, ShouldEqual, "1bedroom")
					So(newHomeDetail.IsCleaningRequired, ShouldBeTrue)
					So(len(newHomeDetail.Options), ShouldEqual, 5)
				})
			})
		})
	})

	// get task detail task office cleaning has VAT and newCostDetail
	t.Run("90", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_OFFICE_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"costDetail": map[string]interface{}{
					"baseCost":     200000.0,
					"cost":         200000.0,
					"finalCost":    150000.0,
					"vat":          20000,
					"totalCost":    170000,
					"newFinalCost": 250000,
					"currency":     "VND",
				},
				"newCostDetail": map[string]interface{}{
					"baseCost":  300000.0,
					"cost":      300000.0,
					"finalCost": 250000.0,
					"vat":       20000,
					"totalCost": 270000,
					"currency":  "VND",
				},
				"requestVatInfo": map[string]interface{}{
					"companyEmail":   "<EMAIL>",
					"companyName":    "cong ty bTaskee",
					"taxCode":        "*********",
					"companyAddress": "09 Tranaf nAox",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				task := &modelTask.Task{}
				json.Unmarshal(b, &task)
				So(res.Code, ShouldEqual, 200)
				So(task.CostDetail.Cost, ShouldEqual, 220000)
				So(task.CostDetail.FinalCost, ShouldEqual, 170000)
				So(task.CostDetail.NewFinalCost, ShouldEqual, 270000)
				So(task.CostDetail.Vat, ShouldEqual, 20000)
			})
		})
	})
	t.Run("93", func(t *testing.T) {
		// GetTaskDetail
		apiGetTaskDetail := subPath + "/get-task-detail"
		log.Println("==================================== GetTaskDetail remove isLeader when show in app")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567710",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567711",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{
					"*********",
				},
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"askerId":        "**********",
				"acceptedTasker": []map[string]interface{}{{
					"taskerId":  "0834567711",
					"name":      "Tasker 02",
					"avatar":    "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/cEmWtDWEpekcnvbb4",
					"avgRating": 4.5,
					"taskDone":  3,
					"isLeader":  true,
				}},
				"cost": 270000,
				"cstDetail": map[string]interface{}{
					"baseCost":    270000,
					"cost":        270000,
					"finalCost":   270000,
					"promotionBy": "BTASKEE",
				},
				"detailOfficeCleaning": map[string]interface{}{
					"numberOfTaskers": 1,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskDetail), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"taskId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Task Detail", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["description"], ShouldEqual, "Desc Test")
					So(respResult["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult["contactName"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["status"], ShouldEqual, globalConstant.TASK_STATUS_POSTED)
					So(respResult["cost"], ShouldEqual, 270000)
					So(respResult["acceptedTasker"], ShouldNotBeNil)
					var resultAcceptedTasker map[string][]map[string]interface{}
					json.Unmarshal(bytes, &resultAcceptedTasker)
					So(len(resultAcceptedTasker["acceptedTasker"]), ShouldEqual, 1)
					for _, v := range resultAcceptedTasker["acceptedTasker"] {
						So(v["taskerId"], ShouldEqual, "0834567711")
						So(v["avgRating"], ShouldEqual, 4.5)
						So(v["taskDone"], ShouldEqual, 3)
						So(v["isLeader"], ShouldBeEmpty)
					}
				})
			})
		})
	})

	t.Run("94", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", CHECK_TASKER_CONFLICT), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"isoCode": 1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})
			Convey("When service handle request if isoCode is required", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if taskDate is required", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DATE_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if duration is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DURATION_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if taskerId is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
					"duration": 2,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if lat,lng is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
					"duration": 2,
					"taskerId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_LAT_LNG_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if serviceId is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
					"duration": 2,
					"taskerId": "123",
					"lat":      1,
					"lng":      1.5,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// check tasker conflict task HOME_CLEANING
	t.Run("95", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		var serviceData *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1, "name": 1}, &serviceData)
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		date = time.Date(date.Year(), date.Month(), date.Day(), 10, 0, 0, 0, date.Location())
		newDate := date.Add(-1 * time.Hour)
		CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"duration":    3,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", newDate.Year(), newDate.Month(), newDate.Day(), newDate.Hour(), newDate.Minute()),
				"cost":        200000,
				"costDetail": map[string]interface{}{
					"baseCost":  200000,
					"cost":      200000,
					"finalCost": 200000,
					"duration":  2,
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"status": globalConstant.TASK_STATUS_CONFIRMED,
			},
		})

		Convey(fmt.Sprintf("Give a http request for %s", CHECK_TASKER_CONFLICT), t, func() {
			body := map[string]interface{}{
				"isoCode":   local.ISO_CODE,
				"taskDate":  date,
				"duration":  2,
				"taskerId":  "0834567810",
				"lat":       1,
				"lng":       1.5,
				"serviceId": serviceData.XId,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, lib.ERROR_TASKER_CONFLICT_TIME.StatusCode)
			})
		})
	})
	// check tasker conflict task DEEP_CLEANING
	t.Run("96", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		var serviceData *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1, "name": 1}, &serviceData)
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		date = time.Date(date.Year(), date.Month(), date.Day(), 10, 0, 0, 0, date.Location())
		y, m, _ := date.Date()
		newDate := date.Add(-1 * time.Hour)
		CreateTask([]map[string]interface{}{{
			"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
			"askerPhone":  "**********",
			"description": "Task 01",
			"duration":    3,
			"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, newDate.Day(), newDate.Hour(), newDate.Minute()),
			"cost":        200000,
			"costDetail": map[string]interface{}{
				"baseCost":  200000,
				"cost":      200000,
				"finalCost": 200000,
				"duration":  2,
			},
			"acceptedTasker": []map[string]interface{}{
				{
					"taskerId": "0834567810",
				},
			},
			"status": globalConstant.TASK_STATUS_CONFIRMED,
		},
		})

		Convey(fmt.Sprintf("Give a http request for %s", CHECK_TASKER_CONFLICT), t, func() {
			body := map[string]interface{}{
				"isoCode":   local.ISO_CODE,
				"taskDate":  date,
				"duration":  2,
				"taskerId":  "0834567810",
				"lat":       1,
				"lng":       1.5,
				"serviceId": serviceData.XId,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, lib.ERROR_TASKER_CONFLICT_TIME.StatusCode)
			})
		})
	})
	// check tasker conflict task LAUDRY => no check conflict
	t.Run("97", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		var serviceData *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_LAUNDRY}, bson.M{"_id": 1, "name": 1}, &serviceData)
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		y, m, _ := date.Date()
		newDate := date.Add(-1 * time.Hour)
		CreateTask([]map[string]interface{}{{
			"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
			"askerPhone":  "**********",
			"description": "Task 01",
			"duration":    3,
			"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, newDate.Day(), newDate.Hour(), newDate.Minute()),
			"cost":        200000,
			"costDetail": map[string]interface{}{
				"baseCost":  200000,
				"cost":      200000,
				"finalCost": 200000,
				"duration":  2,
			},
			"acceptedTasker": []map[string]interface{}{
				{
					"taskerId": "0834567810",
				},
			},
			"status": globalConstant.TASK_STATUS_CONFIRMED,
		},
		})

		Convey(fmt.Sprintf("Give a http request for %s", CHECK_TASKER_CONFLICT), t, func() {
			body := map[string]interface{}{
				"isoCode":   local.ISO_CODE,
				"taskDate":  date,
				"duration":  2,
				"taskerId":  "0834567810",
				"lat":       1,
				"lng":       1.5,
				"serviceId": serviceData.XId,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", CHECK_TASKER_CONFLICT, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
			})
		})
	})
	// Get tasker free time
	t.Run("98", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", GET_TASKER_FREE_TIME), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"isoCode": 1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})
			Convey("When service handle request if isoCode is required", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if taskDate is required", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DATE_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if duration is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DURATION_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if taskerId is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
					"duration": 2,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if lat,lng is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
					"duration": 2,
					"taskerId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_LAT_LNG_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if serviceId is required", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"taskDate": globalLib.GetCurrentTime(local.TimeZone),
					"duration": 2,
					"taskerId": "123",
					"lat":      1,
					"lng":      1.5,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// get tasker free time
	t.Run("99", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		var serviceData *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1, "name": 1}, &serviceData)

		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			serviceData.XId,
			bson.M{
				"$set": bson.M{
					"postingLimits": map[string]interface{}{
						"from": "6:00:00",
						"to":   "23:00:00",
					},
				},
			},
		)
		defer globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceData.XId, bson.M{"$unset": bson.M{"postingLimits": 1}})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		y, m, d := taskDate.Date()
		CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"duration":    3,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d, 10, 0),
				"cost":        200000,
				"costDetail": map[string]interface{}{
					"baseCost":  200000,
					"cost":      200000,
					"finalCost": 200000,
					"duration":  2,
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"status": globalConstant.TASK_STATUS_CONFIRMED,
			},
		})

		Convey(fmt.Sprintf("Give a http request for %s", GET_TASKER_FREE_TIME), t, func() {
			body := map[string]interface{}{
				"isoCode":   local.ISO_CODE,
				"taskDate":  taskDate,
				"duration":  2,
				"taskerId":  "0834567810",
				"lat":       1,
				"lng":       1.5,
				"serviceId": serviceData.XId,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				respResult := map[string][]time.Time{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult["morning"]), ShouldEqual, 4)
				So(len(respResult["afternoon"]), ShouldEqual, 10)
				So(len(respResult["evening"]), ShouldEqual, 6)

				for k, v := range respResult {
					So(k, ShouldBeIn, []string{"morning", "afternoon", "evening"})
					for _, t := range v {
						time1 := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 10-2, 0, 0, 0, local.TimeZone) // task date conflict - duration
						time2 := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 10+3, 0, 0, 0, local.TimeZone) // task date conflict + task conflict duration
						So(t, ShouldNotHappenOnOrBetween, time1, time2)
						So(t, ShouldHappenOnOrAfter, time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 6, 0, 0, 0, local.TimeZone))
						So(t, ShouldHappenOnOrBefore, time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 23-2, 0, 0, 0, local.TimeZone))
					}
				}
			})
		})
	})
	// get tasker free time LAUDRY dont need check tasker conflict time
	t.Run("99.1", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		var serviceData *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_LAUNDRY}, bson.M{"_id": 1, "name": 1}, &serviceData)

		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			serviceData.XId,
			bson.M{
				"$set": bson.M{
					"postingLimits": map[string]interface{}{
						"from": "6:00:00",
						"to":   "23:00:00",
					},
				},
			},
		)
		defer globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceData.XId, bson.M{"$unset": bson.M{"postingLimits": 1}})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		y, m, d := taskDate.Date()
		CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"duration":    3,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d, 10, 0),
				"cost":        200000,
				"costDetail": map[string]interface{}{
					"baseCost":  200000,
					"cost":      200000,
					"finalCost": 200000,
					"duration":  2,
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"status": globalConstant.TASK_STATUS_CONFIRMED,
			},
		})

		Convey(fmt.Sprintf("Give a http request for %s", GET_TASKER_FREE_TIME), t, func() {
			body := map[string]interface{}{
				"isoCode":   local.ISO_CODE,
				"taskDate":  taskDate,
				"duration":  2,
				"taskerId":  "0834567810",
				"lat":       1,
				"lng":       1.5,
				"serviceId": serviceData.XId,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				respResult := map[string][]time.Time{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult["morning"]), ShouldEqual, 13)
				So(len(respResult["afternoon"]), ShouldEqual, 12)
				So(len(respResult["evening"]), ShouldEqual, 6)

				for k, v := range respResult {
					So(k, ShouldBeIn, []string{"morning", "afternoon", "evening"})
					for _, t := range v {
						// time1 := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 10-2, 0, 0, 0, local.TimeZone) // task date conflict - duration
						// time2 := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 10+3, 0, 0, 0, local.TimeZone) // task date conflict + task conflict duration
						// So(t, ShouldNotHappenOnOrBetween, time1, time2)
						So(t, ShouldHappenOnOrAfter, time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 6, 0, 0, 0, local.TimeZone))
						So(t, ShouldHappenOnOrBefore, time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 23-2, 0, 0, 0, local.TimeZone))
					}
				}
			})
		})
	})
	// get tasker free time HOME COOKING, check posting limit as eating time
	t.Run("99.2", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		var serviceData *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_COOKING}, bson.M{"_id": 1, "name": 1}, &serviceData)

		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
			serviceData.XId,
			bson.M{
				"$set": bson.M{
					"postingLimits": map[string]interface{}{
						"from": "9:00:00",  // actual 7:00
						"to":   "19:00:00", // actual 17:00
					},
				},
			},
		)
		defer globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceData.XId, bson.M{"$unset": bson.M{"postingLimits": 1}})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		y, m, d := taskDate.Date()
		CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"duration":    3,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d, 11, 0),
				"cost":        200000,
				"costDetail": map[string]interface{}{
					"baseCost":  200000,
					"cost":      200000,
					"finalCost": 200000,
					"duration":  2,
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"status": globalConstant.TASK_STATUS_CONFIRMED,
			},
		})

		Convey(fmt.Sprintf("Give a http request for %s", GET_TASKER_FREE_TIME), t, func() {
			body := map[string]interface{}{
				"isoCode":   local.ISO_CODE,
				"taskDate":  taskDate,
				"duration":  2,
				"taskerId":  "0834567810",
				"lat":       1,
				"lng":       1.5,
				"serviceId": serviceData.XId,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", GET_TASKER_FREE_TIME, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				respResult := map[string][]time.Time{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult["morning"]), ShouldEqual, 4)
				So(len(respResult["afternoon"]), ShouldEqual, 2)
				So(len(respResult["evening"]), ShouldEqual, 0)

				for k, v := range respResult {
					So(k, ShouldBeIn, []string{"morning", "afternoon", "evening"})
					for _, t := range v {
						time1 := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 11-2, 0, 0, 0, local.TimeZone) // task date conflict - duration
						time2 := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 11+3, 0, 0, 0, local.TimeZone) // task date conflict + task conflict duration
						So(t, ShouldNotHappenOnOrBetween, time1, time2)
						So(t, ShouldHappenOnOrAfter, time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 7, 0, 0, 0, local.TimeZone))
						So(t, ShouldHappenOnOrBefore, time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 17-2, 0, 0, 0, local.TimeZone))
					}
				}
			})
		})
	})
	t.Run("100", func(t *testing.T) {
		// GetUpCommingTasks - Task book with favorite tasker 3 options
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks - Task book with favorite tasker 3 options")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone)
		taskDate := time.Date(date.Year(), date.Month(), date.Day(), 10, 0, 0, 0, local.TimeZone).AddDate(0, 0, 1)
		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"isoCode":     local.ISO_CODE,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"forceTasker": map[string]interface{}{
					"taskerId": "0981234567",
					"name":     "tasker01",
					"avatar":   "avatar01",
				},
				"dateOptions": []map[string]interface{}{
					{
						"_id": "123",
						"costDetail": map[string]interface{}{
							"baseCost":  350000,
							"cost":      350000,
							"finalCost": 350000,
						},
						"isDefault": true,
						"date":      taskDate.Add(time.Hour * 6),
					}, {
						"_id": "456",
						"costDetail": map[string]interface{}{
							"baseCost":  200000,
							"cost":      200000,
							"finalCost": 200000,
						},
						"date": taskDate.Add(time.Hour * 2),
					}, {
						"_id": "678",
						"costDetail": map[string]interface{}{
							"baseCost":  300000,
							"cost":      300000,
							"finalCost": 300000,
						},
						"date": taskDate.Add(time.Hour * 4),
					},
				},
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				var respResultArrM []map[string][]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				json.Unmarshal(bytes, &respResultArrM)
				So(resp.Code, ShouldEqual, 200)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, ids[0])
				So(respResultM[0]["forceTasker"]["taskerId"], ShouldEqual, "0981234567")
				So(respResultM[0]["forceTasker"]["name"], ShouldEqual, "tasker01")
				So(respResultM[0]["forceTasker"]["avatar"], ShouldEqual, "avatar01")

				//Check if date is in range
				So(respResultArrM[0]["dateOptions"][0]["_id"], ShouldEqual, "123")
				So(respResultArrM[0]["dateOptions"][0]["costDetail"].(map[string]interface{})["baseCost"], ShouldEqual, 350000)
				So(respResultArrM[0]["dateOptions"][0]["costDetail"].(map[string]interface{})["cost"], ShouldEqual, 350000)
				So(respResultArrM[0]["dateOptions"][0]["costDetail"].(map[string]interface{})["finalCost"], ShouldEqual, 350000)
				So(respResultArrM[0]["dateOptions"][0]["isDefault"], ShouldEqual, true)
				date1 := cast.ToTime(respResultArrM[0]["dateOptions"][0]["date"]).In(local.TimeZone).Format(time.RFC3339Nano)
				So(date1, ShouldEqual, taskDate.Add(time.Hour*6).Format(time.RFC3339Nano))

				So(respResultArrM[0]["dateOptions"][1]["_id"], ShouldEqual, "456")
				So(respResultArrM[0]["dateOptions"][1]["costDetail"].(map[string]interface{})["baseCost"], ShouldEqual, 200000)
				So(respResultArrM[0]["dateOptions"][1]["costDetail"].(map[string]interface{})["cost"], ShouldEqual, 200000)
				So(respResultArrM[0]["dateOptions"][1]["costDetail"].(map[string]interface{})["finalCost"], ShouldEqual, 200000)
				So(respResultArrM[0]["dateOptions"][1]["isDefault"], ShouldBeNil)
				date2 := cast.ToTime(respResultArrM[0]["dateOptions"][1]["date"]).In(local.TimeZone).Format(time.RFC3339Nano)
				So(date2, ShouldEqual, taskDate.Add(time.Hour*2).Format(time.RFC3339Nano))

				So(respResultArrM[0]["dateOptions"][2]["_id"], ShouldEqual, "678")
				So(respResultArrM[0]["dateOptions"][2]["costDetail"].(map[string]interface{})["baseCost"], ShouldEqual, 300000)
				So(respResultArrM[0]["dateOptions"][2]["costDetail"].(map[string]interface{})["cost"], ShouldEqual, 300000)
				So(respResultArrM[0]["dateOptions"][2]["costDetail"].(map[string]interface{})["finalCost"], ShouldEqual, 300000)
				So(respResultArrM[0]["dateOptions"][2]["isDefault"], ShouldBeNil)
				date3 := cast.ToTime(respResultArrM[0]["dateOptions"][2]["date"]).In(local.TimeZone).Format(time.RFC3339Nano)
				So(date3, ShouldEqual, taskDate.Add(time.Hour*4).Format(time.RFC3339Nano))

			})
		})
	})
	// Get task detail homeCleaning service - Task book with favorite tasker 3 options
	t.Run("101", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone)
		taskDate := time.Date(date.Year(), date.Month(), date.Day(), 10, 0, 0, 0, local.TimeZone).AddDate(0, 0, 1)
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "**********",
					"name":     "tasker01",
					"avatar":   "avatar01",
				},
				"dateOptions": []map[string]interface{}{
					{
						"_id": "123",
						"costDetail": map[string]interface{}{
							"baseCost":  350000,
							"cost":      350000,
							"finalCost": 350000,
						},
						"isDefault": true,
						"date":      taskDate.Add(time.Hour * 6),
					}, {
						"_id": "456",
						"costDetail": map[string]interface{}{
							"baseCost":  200000,
							"cost":      200000,
							"finalCost": 200000,
						},
						"date": taskDate.Add(time.Hour * 2),
					}, {
						"_id": "678",
						"costDetail": map[string]interface{}{
							"baseCost":  300000,
							"cost":      300000,
							"finalCost": 300000,
						},
						"date": taskDate.Add(time.Hour * 4),
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)

				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)

				resultMap := map[string]interface{}{}
				var respResultArrM map[string][]map[string]interface{}
				json.Unmarshal(b, &resultMap)
				json.Unmarshal(b, &respResultArrM)

				So(m.ForceTasker.TaskerId, ShouldEqual, "**********")
				So(m.ForceTasker.Name, ShouldEqual, "tasker01")
				So(m.ForceTasker.Avatar, ShouldEqual, "avatar01")
				//Check if date is in range
				So(m.DateOptions[0].XId, ShouldEqual, "123")
				So(m.DateOptions[0].CostDetail.BaseCost, ShouldEqual, 350000)
				So(m.DateOptions[0].CostDetail.Cost, ShouldEqual, 350000)
				So(m.DateOptions[0].CostDetail.FinalCost, ShouldEqual, 350000)
				So(m.DateOptions[0].IsDefault, ShouldEqual, true)
				date1 := cast.ToTime(respResultArrM["dateOptions"][0]["date"]).In(local.TimeZone).Format(time.RFC3339Nano)
				So(date1, ShouldEqual, taskDate.Add(time.Hour*6).Format(time.RFC3339Nano))

				So(m.DateOptions[1].XId, ShouldEqual, "456")
				So(m.DateOptions[1].CostDetail.BaseCost, ShouldEqual, 200000)
				So(m.DateOptions[1].CostDetail.Cost, ShouldEqual, 200000)
				So(m.DateOptions[1].CostDetail.FinalCost, ShouldEqual, 200000)
				So(m.DateOptions[1].IsDefault, ShouldEqual, false)
				date2 := cast.ToTime(respResultArrM["dateOptions"][1]["date"]).In(local.TimeZone).Format(time.RFC3339Nano)
				So(date2, ShouldEqual, taskDate.Add(time.Hour*2).Format(time.RFC3339Nano))

				So(m.DateOptions[2].XId, ShouldEqual, "678")
				So(m.DateOptions[2].CostDetail.BaseCost, ShouldEqual, 300000)
				So(m.DateOptions[2].CostDetail.Cost, ShouldEqual, 300000)
				So(m.DateOptions[2].CostDetail.FinalCost, ShouldEqual, 300000)
				So(m.DateOptions[2].IsDefault, ShouldEqual, false)
				date3 := cast.ToTime(respResultArrM["dateOptions"][2]["date"]).In(local.TimeZone).Format(time.RFC3339Nano)
				So(date3, ShouldEqual, taskDate.Add(time.Hour*4).Format(time.RFC3339Nano))
			})
		})
	})

	// get task detail task air conditioner V2
	t.Run("102", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_WASHING_MACHINE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailAirConditioner": []map[string]interface{}{
					{
						"type": map[string]interface{}{
							"name": "Split",
						},
						"hp": map[string]interface{}{
							"from": 0,
							"to":   2,
						},
						"quantity": 2,
						"options": []map[string]interface{}{
							{
								"name": "Refill",
								"text": map[string]interface{}{
									"vi": "Bơm ga",
								},
								"quantity": 1,
							},
						},
					}, {
						"type": map[string]interface{}{
							"name": "Ceilling",
						},
						"hp": map[string]interface{}{
							"from": 3,
							"to":   0,
						},
						"quantity": 3,
						"options": []map[string]interface{}{
							{
								"name": "Refill",
								"text": map[string]interface{}{
									"vi": "Bơm ga",
								},
								"quantity": 2,
							},
						},
					},
				},
				"shortAddress": "HCM",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.ShortAddress, ShouldEqual, "HCM")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
				detailAirConditioner := m.DetailAirConditioner
				So(len(detailAirConditioner), ShouldEqual, 2)
				So(detailAirConditioner[0].Type.Name, ShouldEqual, "Split")
				So(detailAirConditioner[0].Hp.From, ShouldEqual, 0)
				So(detailAirConditioner[0].Hp.To, ShouldEqual, 2)
				So(detailAirConditioner[0].Quantity, ShouldEqual, 2)
				So(len(detailAirConditioner[0].Options), ShouldEqual, 1)
				So(detailAirConditioner[0].Options[0].Quantity, ShouldEqual, 1)
				So(detailAirConditioner[0].Options[0].Name, ShouldEqual, "Refill")
				So(detailAirConditioner[1].Type.Name, ShouldEqual, "Ceilling")
				So(detailAirConditioner[1].Hp.From, ShouldEqual, 3)
				So(detailAirConditioner[1].Hp.To, ShouldEqual, 0)
				So(detailAirConditioner[1].Quantity, ShouldEqual, 3)
				So(len(detailAirConditioner[1].Options), ShouldEqual, 1)
				So(detailAirConditioner[1].Options[0].Quantity, ShouldEqual, 2)
				So(detailAirConditioner[1].Options[0].Name, ShouldEqual, "Refill")
			})
		})
	})

	// Get task schedule and task subcription
	t.Run("103", func(t *testing.T) {
		// GetUpCommingTasks
		apiGetUpCommingTasks := "/api/v3/api-asker-vn/get-up-coming-tasks"
		log.Println("==================================== GetUpCommingTasks")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		//Create Task
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,11,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"scheduleId":  "scheduleId123",
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,30", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,12,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"scheduleId":  "scheduleId123",
			},
			{
				"askerPhone":     "**********",
				"serviceName":    globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"isoCode":        globalConstant.ISO_CODE_VN,
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"createdAt":      fmt.Sprintf("%d,%d,%d,15,20", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":           fmt.Sprintf("%d,%d,%d,13,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"subscriptionId": "subscriptionId123",
			},
			{
				"askerPhone":     "**********",
				"serviceName":    globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"isoCode":        globalConstant.ISO_CODE_VN,
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"createdAt":      fmt.Sprintf("%d,%d,%d,15,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":           fmt.Sprintf("%d,%d,%d,14,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"subscriptionId": "subscriptionId123",
			},
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"createdAt":   fmt.Sprintf("%d,%d,%d,15,30", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":        fmt.Sprintf("%d,%d,%d,7,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"scheduleId":  "scheduleId123",
			},
			{
				"askerPhone":     "**********",
				"serviceName":    globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"isoCode":        globalConstant.ISO_CODE_VN,
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"createdAt":      fmt.Sprintf("%d,%d,%d,15,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":           fmt.Sprintf("%d,%d,%d,16,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"subscriptionId": "subscriptionId345",
				"isEco":          true,
			},
			{
				"askerPhone":     "**********",
				"serviceName":    globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"isoCode":        globalConstant.ISO_CODE_VN,
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"createdAt":      fmt.Sprintf("%d,%d,%d,15,10", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"date":           fmt.Sprintf("%d,%d,%d,17,40", time.Now().Year(), time.Now().Month(), time.Now().Day()),
				"subscriptionId": "subscriptionId345",
				"isEco":          true,
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": globalConstant.ISO_CODE_VN,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiGetUpCommingTasks, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := ioutil.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				So(respResult[0]["_id"], ShouldEqual, ids[4])
				So(respResult[0]["scheduleId"], ShouldEqual, "scheduleId123")
				relatedTasks := cast.ToSlice(respResultM[0]["relatedTasks"]["tasks"])
				So(len(relatedTasks), ShouldEqual, 2)
				So(relatedTasks[0].(map[string]interface{})["_id"], ShouldEqual, ids[0])
				So(relatedTasks[1].(map[string]interface{})["_id"], ShouldEqual, ids[1])
				So(respResult[1]["_id"], ShouldEqual, ids[2])
				So(respResult[1]["subscriptionId"], ShouldEqual, "subscriptionId123")
				relatedTasks = cast.ToSlice(respResultM[1]["relatedTasks"]["tasks"])
				So(len(relatedTasks), ShouldEqual, 1)

				// Task create by eco Subs
				So(respResult[2]["_id"], ShouldEqual, ids[5])
				So(respResult[2]["subscriptionId"], ShouldEqual, "subscriptionId345")
				So(respResult[2]["isEco"], ShouldEqual, true)
				relatedTasks = cast.ToSlice(respResultM[1]["relatedTasks"]["tasks"])
				So(len(relatedTasks), ShouldEqual, 1)
			})
		})
	})
	// get task detail task house keeping
	t.Run("104", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_HOUSEKEEPING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailHousekeeping": &modelTask.TaskDetailHousekeeping{
					Name: "HOTEL",
					RoomTypes: []*modelTask.TaskDetailHousekeepingRoomTypes{
						{
							Name:     "SINGLE",
							Quantity: 3, // 3*20
						}, {
							Name:     "COUPLE",
							Quantity: 2, // 2*30
						}, {
							Name:     "DORM_1",
							Quantity: 2, // 2*45
						},
					},
				},
				"shortAddress": "HCM",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := ioutil.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m.AskerId, ShouldEqual, body["userId"])
				So(m.Duration, ShouldEqual, 2)
				So(m.Phone, ShouldEqual, "**********")
				So(m.ShortAddress, ShouldEqual, "HCM")
				So(m.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
				So(m.AcceptedTasker[0].TaskerId, ShouldEqual, "**********")
				So(m.AcceptedTasker[0].IsPremiumTasker, ShouldBeTrue)
				detailHousekeeping := m.DetailHousekeeping
				So(detailHousekeeping, ShouldNotBeNil)
				So(detailHousekeeping.Name, ShouldEqual, "HOTEL")

				respResult := make(map[string]map[string]interface{})
				json.Unmarshal(b, &respResult)
				So(respResult["service"]["name"], ShouldEqual, globalConstant.SERVICE_KEY_NAME_HOUSEKEEPING)
			})
		})
	})

	// get task detail book from service VSCN
	t.Run("105", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		idVSCN := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailIndustrialCleaning": &modelTask.TaskDetailIndustrialCleaning{
					HomeType: &modelTask.TaskDetailIndustrialCleaningHomeType{
						Name: "HOME",
						Text: &modelService.ServiceText{
							Vi: "Nhà ở",
						},
						Type: &modelTask.TaskDetailIndustrialCleaningHomeTypeType{
							Name: "oldHouse",
							Text: &modelService.ServiceText{
								Vi: "Nhà lâu năm",
							},
							Area: &modelTask.TaskDetailIndustrialCleaningHomeTypeTypeArea{
								Name: "area1",
							},
						},
					},
				},
			},
		})

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOUSEKEEPING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":        "**********",
						"isPremiumTasker": true,
					},
				},
				"detailHousekeeping": &modelTask.TaskDetailHousekeeping{
					Name: "HOTEL",
					RoomTypes: []*modelTask.TaskDetailHousekeepingRoomTypes{
						{
							Name:     "SINGLE",
							Quantity: 3, // 3*20
						}, {
							Name:     "COUPLE",
							Quantity: 2, // 2*30
						}, {
							Name:     "DORM_1",
							Quantity: 2, // 2*45
						},
					},
				},
				"shortAddress": "HCM",
				"source": map[string]interface{}{
					"from":   globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
					"taskId": idVSCN[0],
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := ioutil.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)

				respResult := make(map[string]map[string]interface{})
				json.Unmarshal(b, &respResult)
				So(respResult["source"]["from"], ShouldEqual, globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING)
				So(respResult["source"], ShouldNotBeNil)
				So(respResult["source"]["taskInfo"].(map[string]interface{})["taskId"], ShouldEqual, idVSCN[0])
			})
		})
	})

	// get task detail has lixi info
	t.Run("106", func(t *testing.T) {
		apiURL := subPath + "/get-task-detail"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567881",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567882",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "**********",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567881",
					},
					{
						"taskerId": "0834567882",
					},
				},
			},
		})

		CreateFATransaction([]map[string]interface{}{
			{
				"type":   "D",
				"userId": "0834567881",
				"source": map[string]interface{}{
					"name":   globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER,
					"value":  "**********",
					"taskId": ids[0],
				},
				"amount": 50000,
			},
			{
				"type":   "D",
				"userId": "0834567882",
				"source": map[string]interface{}{
					"name":   globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER,
					"value":  "**********",
					"taskId": ids[0],
				},
				"amount": 50000,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"taskId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := ioutil.ReadAll(res.Body)
				m := &modelTask.Task{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)

				respResult := make(map[string]map[string]interface{})
				json.Unmarshal(b, &respResult)
				So(respResult["lixiInfo"], ShouldNotBeNil)
				So(respResult["lixiInfo"]["totalAmount"], ShouldEqual, 100000)
			})
		})
	})

}
