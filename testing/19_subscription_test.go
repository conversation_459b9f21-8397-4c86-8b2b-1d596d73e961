/*
* @File: 19_subscription_test.go
* @Description: Handler function, case test
* @CreatedAt: 30/10/2020
* @Author: vinhnt
* @UpdatedAt: 21/12/2020
* @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPurchaseOrder "gitlab.com/btaskee/go-services-model-v2/grpcmodel/purchaseorder"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionRequest"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSubscription(t *testing.T) {
	// GetSupscription
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== GetSupscription")
		apiGetSubscription := "/api/v3/api-asker-vn/get-subscription-by-userId"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone":           "0834567880",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
			},
		})

		//Create Subscription
		ids := CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":          "0834567890",
				"price":               1803600,
				"discount":            0.1,
				"serviceName":         globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":         "5B03-15",
				"contactName":         "Osvaldo Raynor",
				"phone":               "0761781715",
				"status":              globalConstant.SUBSCRIPTION_STATUS_NEW,
				"forceAcceptTaskerId": "0834567880",
				"costDetail": map[string]interface{}{
					"baseCost":  1803600,
					"cost":      1803600,
					"finalCost": 1803600,
					"session":   9,
					"currency": map[string]interface{}{
						"sign": "₫",
						"code": "VND",
					},
					"pricing": []map[string]interface{}{
						{
							"duration": 2.0,
							"costDetail": map[string]interface{}{
								"baseCost":     1803600,
								"cost":         1803600,
								"finalCost":    1803600,
								"duration":     2.0,
								"transportFee": 0.0,
								"depositMoney": 0.0,
							},
						},
					},
					"decreasedReasons": []map[string]interface{}{
						{
							"key":         "PROMOTION_CODE",
							"value":       90000.0,
							"promotionBy": "BTASKEE",
						},
					},
				},
			}, {
				"address": "15 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       9999,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "5B03-20",
				"contactName": "Osvaldo Raynor",
				"phone":       "0761781799",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetSubscription), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSubscription, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123123213,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSubscription, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetSubscription), t, func() {
			var cleaningSubscriptionSv *modelService.Service
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}, bson.M{"_id": 1}, &cleaningSubscriptionSv)
			if cleaningSubscriptionSv == nil {
				cleaningSubscriptionSv = &modelService.Service{
					XId: globalLib.GenerateObjectId(),
				}
				globalDataAccess.InsertOne(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], map[string]interface{}{
					"_id":  cleaningSubscriptionSv.XId,
					"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION,
					"icon": "123",
					"text": map[string]interface{}{
						"vi": "Don dep nha",
						"en": "Cleaning",
					},
					"status": "ACTIVE",
				})
			}
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Subscription by User", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)

					So(len(respResult), ShouldEqual, 2)
					for _, v := range respResult {
						if v["_id"].(string) == ids[0] {
							So(v["address"], ShouldEqual, "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam")
							So(v["contactName"], ShouldEqual, "Osvaldo Raynor")
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["status"], ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_NEW)
							So(v["price"], ShouldEqual, 1803600)
							So(v["costDetail"], ShouldNotBeNil)
							So(v["forceAcceptTasker"], ShouldNotBeNil)
							So(v["month"], ShouldEqual, 1)
						}
						if v["_id"].(string) == ids[1] {
							So(v["address"], ShouldEqual, "15 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam")
							So(v["contactName"], ShouldEqual, "Osvaldo Raynor")
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["status"], ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_NEW)
							So(v["price"], ShouldEqual, 9999)
							So(v["month"], ShouldEqual, 1)
						}
						So(v["serviceId"], ShouldEqual, cleaningSubscriptionSv.XId)
					}
					for i, v := range respResultM {
						if respResult[i]["_id"].(string) == ids[0] {
							So(v["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
							So(v["taskPlace"]["city"], ShouldEqual, "Hồ Chí Minh")
							So(v["taskPlace"]["district"], ShouldEqual, "Quận 7")
							So(v["costDetail"]["baseCost"], ShouldEqual, 1803600)
							So(v["costDetail"]["cost"], ShouldEqual, 1803600)
							So(v["costDetail"]["finalCost"], ShouldEqual, 1803600)
							So(v["forceAcceptTasker"]["_id"], ShouldEqual, "0834567880")
							So(v["forceAcceptTasker"]["name"], ShouldEqual, "Tasker 01")
							So(v["forceAcceptTasker"]["isPremiumTasker"], ShouldEqual, true)
						}
						if respResult[i]["_id"].(string) == ids[1] {
							So(v["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
							So(v["taskPlace"]["city"], ShouldEqual, "Hồ Chí Minh")
							So(v["taskPlace"]["district"], ShouldEqual, "Quận 7")
						}
						So(v["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_HOME_CLEANING)
					}
					So(respResultM[0]["taskPlace"]["country"], ShouldEqual, local.ISO_CODE)
					So(respResultM[0]["taskPlace"]["city"], ShouldEqual, "Hồ Chí Minh")
					So(respResultM[0]["taskPlace"]["district"], ShouldEqual, "Quận 7")
				})
			})
		})
	})

	// CancelSubscription
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== CancelSubscription")
		apiCancelSubscription := "/api/v3/api-asker-vn/cancel-subscription"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Subscription
		ids := CreateSubscription([]map[string]interface{}{
			{
				"address":     "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "0761781715",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
			}, {
				"address":     "3/2, Quận 10, Hồ Chí Minh, Việt Nam",
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-16",
				"contactName": "Niko",
				"phone":       "0764433261",
				"status":      globalConstant.SUBSCRIPTION_STATUS_EXPIRED,
			}, {
				"address":     "Nguyễn Đình Chiểu, Quận 1, Hồ Chí Minh, Việt Nam",
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-17",
				"contactName": "Shroud",
				"phone":       "0768844265",
				"status":      globalConstant.SUBSCRIPTION_STATUS_ACTIVE,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCancelSubscription), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"subscriptionId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SUBSCRIPTION_ID_REQUIRED.Message)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case Status NEW", apiCancelSubscription), t, func() {
			body := map[string]interface{}{
				"subscriptionId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Cancal Subscription Case Status NEW", func() {
					So(resp.Code, ShouldEqual, 200)
				})
			})

			Convey("Then check database Cancal Subscription Case Status NEW", func() {
				var result *modelSubscription.Subscription
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], ids[0], bson.M{"status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_CANCELED)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case Status EXPIRED", apiCancelSubscription), t, func() {
			body := map[string]interface{}{
				"subscriptionId": ids[1],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Cancal Subscription Case Status EXPIRED", func() {
					So(resp.Code, ShouldEqual, 200)
				})
			})

			Convey("Then check database Cancal Subscription Case Status EXPIRED", func() {
				var result *modelSubscription.Subscription
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], ids[1], bson.M{"status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_CANCELED)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case Status ACTIVE", apiCancelSubscription), t, func() {
			body := map[string]interface{}{
				"subscriptionId": ids[2],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiCancelSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Cancal Subscription Case Status ACTIVE", func() {
					So(resp.Code, ShouldEqual, 200)
				})
			})

			Convey("Then check database Cancal Subscription Case Status ACTIVE", func() {
				var result *modelSubscription.Subscription
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], ids[2], bson.M{"status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_ACTIVE)
			})
		})
	})
	t.Run("2.1", func(t *testing.T) {
		log.Println("==================================== validate SubscriptionCheckRequest")
		apiUrl := "/api/v3/api-asker-vn/cancel-subscription"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"subscriptionId": 834567890,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	// SubscriptionCheckRequest
	t.Run("3.1", func(t *testing.T) {
		log.Println("==================================== validate SubscriptionCheckRequest")
		apiUrl := "/api/v3/api-asker-vn/suggest-subscription-check-request"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"askerId":  834567890,
					"taskerId": 834567810,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== SubscriptionCheckRequest")
		apiUrl := "/api/v3/api-asker-vn/suggest-subscription-check-request"

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create SubscriptionRequest
		CreateSubscriptionRequest([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567810",
				"status":      globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey(fmt.Sprintf("Check request when %s", lib.ERROR_SUBSCRIPTION_REQUEST_EXIST.Message), func() {
				body := map[string]interface{}{
					"askerId":  "0834567890",
					"taskerId": "0834567810",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				// So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_REQUEST_EXIST.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SUBSCRIPTION_REQUEST_EXIST.Message)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== SubscriptionCheckRequest")
		apiUrl := "/api/v3/api-asker-vn/suggest-subscription-check-request"

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0834567810",
				"askerPhone":  "0834567890",
				"rate":        2,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey(fmt.Sprintf("Check request when %s", lib.ERROR_BAD_RATING.Message), func() {
				body := map[string]interface{}{
					"askerId":  "0834567890",
					"taskerId": "0834567810",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				// So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BAD_RATING.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BAD_RATING.Message)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== SubscriptionCheckRequest")
		apiUrl := "/api/v3/api-asker-vn/suggest-subscription-check-request"

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request okie", func() {
				body := map[string]interface{}{
					"askerId":  "0834567890",
					"taskerId": "0834567810",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)
			})
		})
	})

	// SuggestSubscriptionGetTask
	t.Run("6", func(t *testing.T) {
		log.Println("=================== validate SuggestSubscriptionGetTask")
		apiURL := subPath + "/suggest-subscription-get-task"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"beginAt": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("=================== SuggestSubscriptionGetTask")
		apiURL := subPath + "/suggest-subscription-get-task"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		tasks := []map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"date": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"voiceCallHistory": []map[string]interface{}{
					{
						"to":     "W86An6yApbA8WQYQ7",
						"status": "Incoming",
						"type":   "audio",
						"createdAt": map[string]interface{}{
							"seconds": 1619580713.0,
							"nanos":   764000000.0,
						},
						"callId": "call-vn-1-QPUZ5PW3MB-1618421911543",
						"from":   "h5xvuZZanyPkBzgdh",
					},
					{
						"callId": "call-vn-1-QPUZ5PW3MB-1618421912269",
						"from":   "h5xvuZZanyPkBzgdh",
						"to":     "W86An6yApbA8WQYQ7",
						"status": "Incoming",
						"type":   "audio",
					},
				},
				"costDetail": map[string]interface{}{
					"baseCost":  240000.0,
					"cost":      258000.0,
					"finalCost": 258000.0,
					"duration":  4.0,
					"currency":  "VND",
				},
			},
		}
		CreateTask(tasks)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"beginAt":     globalLib.GetCurrentTime(local.TimeZone),
					"phone":       tasks[0]["askerPhone"],
					"name":        "Asker 01",
					"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
					"cost":        200000,
					"address":     tasks[0]["address"],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				var task *modelTask.Task
				json.Unmarshal(b, &task)
				So(res.Code, ShouldEqual, 200)
				So(task.Duration, ShouldEqual, 2)
				So(task.HomeType, ShouldEqual, globalConstant.HOME_TYPE_HOME)
				So(task.ServiceText.En, ShouldEqual, globalConstant.SERVICE_NAME_HOME_CLEANING)
				So(task.ContactName, ShouldEqual, "Asker 01")
				So(task.Cost, ShouldEqual, 200000)
				So(task.Address, ShouldEqual, tasks[0]["address"])
				So(task.Phone, ShouldEqual, tasks[0]["askerPhone"])
			})
		})
	})

	// SuggestSubscriptionNewSubscription
	t.Run("8", func(t *testing.T) {
		log.Println("=================== validate SuggestSubscriptionNewSubscription")
		apiURL := subPath + "/suggest-subscription-new-subscription"
		Convey(fmt.Sprintf("Give	n a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"askerId":  123,
					"taskerId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("=================== SuggestSubscriptionNewSubscriptionSuggestSubscriptionNewSubscription")
		apiURL := subPath + "/suggest-subscription-new-subscription"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Subscription
		CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "0761781715",
				"status":      globalConstant.SUBSCRIPTION_STATUS_ACTIVE,
				"schedule": []string{
					"2020,11,20,0,0",
					"2020,11,21,0,0",
					"2020,11,21,10,0",
					"2020,11,22,0,0",
				},
				"forceAcceptTaskerId": "08345678910",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if conflict schedule", func() {
				body := map[string]interface{}{
					"askerId":  "0834567890",
					"taskerId": "08345678910",
					"schedule": []interface{}{
						time.Date(2020, 11, 20, 0, 0, 0, 0, local.TimeZone).Format(time.RFC3339),
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_CONFLICTED.ErrorCode)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("=================== SuggestSubscriptionNewSubscription")
		apiURL := subPath + "/suggest-subscription-new-subscription"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Subscription
		CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "0761781715",
				"status":      globalConstant.SUBSCRIPTION_STATUS_ACTIVE,
				"schedule": []string{
					"2020,11,20,0,0",
					"2020,11,21,0,0",
					"2020,11,21,10,0",
					"2020,11,22,0,0",
				},
				"forceAcceptTaskerId": "08345678910",
			},
		})

		//CreateTask
		now := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 2)
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "08345678910",
					},
				},
				"viewedTaskers": []interface{}{
					"08345678910",
				},
				"collectionDate": time.Now().Add(-4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"taskerRated": false,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if not conflict schedule, task not conflict", func() {
				body := map[string]interface{}{
					"askerId":  "0834567890",
					"taskerId": "08345678910",
					"schedule": []interface{}{
						time.Date(2020, 11, 17, 0, 0, 0, 0, local.TimeZone).Format(time.RFC3339),
					},
					"address":     "Quan 1",
					"phone":       "0834567890",
					"contactName": "Asker1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)

				var subscriptionRequest *modelSubscriptionRequest.SubscriptionRequest
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], bson.M{"askerId": "0834567890", "taskerId": "08345678910"}, bson.M{}, &subscriptionRequest)
				So(subscriptionRequest, ShouldNotBeNil)
				So(subscriptionRequest.Status, ShouldEqual, globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW)
				So(subscriptionRequest.Address, ShouldEqual, body["address"])
				So(subscriptionRequest.Phone, ShouldEqual, body["phone"])
				So(subscriptionRequest.ContactName, ShouldEqual, body["contactName"])
			})
		})
	})

	// SuggestSubscriptionGetSubscription
	t.Run("13", func(t *testing.T) {
		log.Println("=================== validate SuggestSubscriptionGetSubscription")
		apiURL := subPath + "/suggest-subscription-get"
		Convey(fmt.Sprintf("Give	n a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("14", func(t *testing.T) {
		log.Println("=================== SuggestSubscriptionGetSubscription")
		apiURL := subPath + "/suggest-subscription-get"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Subscription
		CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "08345678910",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
			}, {
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "08345678910",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
			},
		})
		Convey(fmt.Sprintf("Give	n a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId": "08345678910",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				var results []*modelSubscriptionRequest.SubscriptionRequest
				json.Unmarshal(b, &results)
				So(res.Code, ShouldEqual, 200)
				//TODO: "taskerId" subscription/base.go:312
			})
		})
	})

	// GetInfoSubscription
	t.Run("15", func(t *testing.T) {
		log.Println("=================== validate GetSubscriptionByUserId")
		apiURL := subPath + "/get-info-subscription"
		Convey(fmt.Sprintf("Give	n a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"subscriptionId": "2112",
					"userId":         "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("16", func(t *testing.T) {
		log.Println("=================== GetInfoSubscription")
		apiURL := subPath + "/get-info-subscription"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Subscription
		ids := CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "08345678910",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
				"schedule": []string{
					"2020,11,20,0,0",
					"2020,11,21,0,0",
					"2020,11,21,10,0",
					"2020,11,22,0,0",
				},
				"forceAcceptTaskerId": "08345678910",
			},
		})
		Convey(fmt.Sprintf("Give	n a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"subscriptionId": ids[0],
					"userId":         "08345678910",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				So(res.Code, ShouldEqual, 200)
				result := map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(globalLib.ParseDateFromString(result["startDate"].(string), local.TimeZone), ShouldEqual, time.Date(2020, 11, 20, 0, 0, 0, 0, local.TimeZone))
				So(globalLib.ParseDateFromString(result["endDate"].(string), local.TimeZone), ShouldEqual, time.Date(2020, 11, 22, 0, 0, 0, 0, local.TimeZone))
			})
		})
	})

	// CancelSubsciption
	t.Run("19", func(t *testing.T) {
		log.Println("========================= validate CancelSubsciption")
		apiURL := subPath + "/cancel-subscription"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"subscriptionId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("20", func(t *testing.T) {
		log.Println("========================= CancelSubsciption")
		apiURL := subPath + "/cancel-subscription"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Subscription
		ids := CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "08345678910",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
				"schedule": []string{
					"2020,11,20,0,0",
					"2020,11,21,0,0",
					"2020,11,21,10,0",
					"2020,11,22,0,0",
				},
				"forceAcceptTaskerId": "08345678910",
			},
		})
		//Create Subscription
		CreatePurchaseOrder([]map[string]interface{}{
			{
				"subscriptionId": ids[0],
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)

				var subscription *modelSubscription.Subscription
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], ids[0], bson.M{"status": 1}, &subscription)
				So(subscription.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_CANCELED)

				var purchaseOrder *modelPurchaseOrder.PurchaseOrder
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE], bson.M{"subscriptionId": subscription.XId}, bson.M{"status": 1}, &purchaseOrder)
				So(purchaseOrder.Status, ShouldEqual, globalConstant.PURCHASE_ORDER_STATUS_CANCEL)
			})
		})
	})

	// GetSubscriptionSuggestion
	t.Run("21", func(t *testing.T) {
		log.Println("========================= Validate GetSubscriptionSuggestion")
		apiURL := subPath + "/get-subscription-suggestion"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if subscriptionRequestId is empty", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("22", func(t *testing.T) {
		log.Println("========================= GetSubscriptionSuggestion")
		apiURL := subPath + "/get-subscription-suggestion"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "08345678990",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		before23h := now.Add(23 * time.Hour)

		//Create SubscriptionRequest
		ids := CreateSubscriptionRequest([]map[string]interface{}{
			{
				"askerPhone":  "08345678990",
				"taskerPhone": "08345678910",
				"status":      globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
				"schedule": []interface{}{
					before23h,
				},
			},
		})
		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "08345678990",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
				"data": map[string]interface{}{
					"subscriptionRequestId": ids[0],
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(res.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)
			})
			Convey("Check the database", func() {
				var subscriptionRequest *modelSubscriptionRequest.SubscriptionRequest
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], ids[0], bson.M{"status": 1}, &subscriptionRequest)
				So(subscriptionRequest.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_EXPIRED)

				var notification *modelNotification.Notification
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "08345678910"}, bson.M{"status": 1}, &notification)
				So(notification, ShouldBeNil)
			})
		})
	})

	t.Run("23", func(t *testing.T) {
		log.Println("========================= GetSubscriptionSuggestion")
		apiURL := subPath + "/get-subscription-suggestion"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "08345678990",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		beforetime := now.Add(25 * time.Hour)

		//Create SubscriptionRequest
		ids := CreateSubscriptionRequest([]map[string]interface{}{
			{
				"askerPhone":  "08345678990",
				"taskerPhone": "08345678910",
				"taskerId":    "xxx",
				"status":      globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
				"schedule": []interface{}{
					beforetime,
				},
			},
		})
		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "08345678990",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
				"data": map[string]interface{}{
					"subscriptionRequestId": ids[0],
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(res.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)
			})
			Convey("Check the database", func() {
				var subscriptionRequest *modelSubscriptionRequest.SubscriptionRequest
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], ids[0], bson.M{"status": 1}, &subscriptionRequest)
				So(subscriptionRequest.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_CANCELED)

				var notification *modelNotification.Notification
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "08345678910"}, bson.M{"status": 1}, &notification)
				So(notification, ShouldBeNil)
			})
		})
	})

	t.Run("24", func(t *testing.T) {
		log.Println("========================= GetSubscriptionSuggestion")
		apiURL := subPath + "/get-subscription-suggestion"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "08345678990",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		beforetime := now.Add(25 * time.Hour)

		//Create SubscriptionRequest
		ids := CreateSubscriptionRequest([]map[string]interface{}{
			{
				"askerPhone":  "08345678990",
				"taskerPhone": "08345678910",
				"status":      globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
				"schedule": []interface{}{
					beforetime,
				},
				"countryCode": "+84",
			},
		})
		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "08345678990",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
				"data": map[string]interface{}{
					"subscriptionRequestId": ids[0],
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(respResult["_id"], ShouldEqual, ids[0])
				So(respResult["status"], ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_NEW)
				So(respResult["taskerId"], ShouldEqual, "08345678910")
				So(respResult["countryCode"], ShouldEqual, "+84")
				So(respResultM["taskerInfo"]["_id"], ShouldEqual, "08345678910")
			})
			Convey("Check the database", func() {
				var subscriptionRequest *modelSubscriptionRequest.SubscriptionRequest
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], ids[0], bson.M{"status": 1}, &subscriptionRequest)
				So(subscriptionRequest.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_NEW)
			})
		})
	})

	// CancelSubscriptionSuggestion
	t.Run("25", func(t *testing.T) {
		log.Println("========================= Validate CancelSubscriptionSuggestion")
		apiURL := subPath + "/cancel-subscription-suggestion"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if subscriptionRequestId is empty", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		log.Println("========================= CancelSubscriptionSuggestion")
		apiURL := subPath + "/cancel-subscription-suggestion"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "08345678990",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": "xxxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(res.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)
			})
			Convey("Check the database", func() {
				var subscriptionRequest *modelSubscriptionRequest.SubscriptionRequest
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], "xxxx", bson.M{"status": 1}, &subscriptionRequest)
				So(subscriptionRequest, ShouldBeNil)
			})
		})
	})

	t.Run("27", func(t *testing.T) {
		log.Println("========================= CancelSubscriptionSuggestion")
		apiURL := subPath + "/cancel-subscription-suggestion"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "08345678990",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "08345678910",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create SubscriptionRequest
		ids := CreateSubscriptionRequest([]map[string]interface{}{
			{
				"askerPhone":  "08345678990",
				"taskerPhone": "08345678910",
				"status":      globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
			},
		})
		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "08345678990",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
				"data": map[string]interface{}{
					"subscriptionRequestId": ids[0],
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"subscriptionRequestId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(res.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)
			})
			Convey("Check the database", func() {
				var subscriptionRequest *modelSubscriptionRequest.SubscriptionRequest
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], ids[0], bson.M{"status": 1}, &subscriptionRequest)
				So(subscriptionRequest.Status, ShouldEqual, globalConstant.SUBSCRIPTION_STATUS_CANCELED)
				lang := globalConstant.LANG_VI
				var notification *modelNotification.Notification
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "08345678910"}, bson.M{"description": 1, "type": 1}, &notification)
				So(notification.Description, ShouldStartWith, localization.T(lang, "SUBSCRIPTION_REQUEST_CANCEL", ""))
				So(notification.Type, ShouldEqual, 25)
			})
		})
	})
	// Cancel subscription has promotion
	t.Run("28", func(t *testing.T) {
		log.Println("========================= Cancel subscription has promotion")
		apiURL := subPath + "/cancel-subscription"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		ids := CreateSubscription([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"contactName": "Osvaldo Raynor",
				"phone":       "0834567890",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
				"promotion": map[string]interface{}{
					"code": "abc123",
				},
			},
		})
		CreatePromotionHistory([]map[string]interface{}{
			{
				"promotionCode": "abc123",
				"userId":        "0834567890",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if subscriptionId is empty", func() {
				// Check promotion history is exist in db
				isExistsPromotionHistory, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], bson.M{"promotionCode": "abc123", "userId": "0834567890"})
				So(isExistsPromotionHistory, ShouldBeTrue)

				body := map[string]interface{}{
					"subscriptionId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				So(res.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(res.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult, ShouldBeEmpty)

				// After cancel subscription has promotion, this promotion code must be removed from db
				isExistsPromotionHistory, _ = globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], bson.M{"promotionCode": "abc123", "userId": "0834567890"})
				So(isExistsPromotionHistory, ShouldBeFalse)
			})
		})
	})

	// GetDetailSubscriptionSchedule
	t.Run("29", func(t *testing.T) {
		log.Println("========================= Validate GetDetailSubscriptionSchedule")
		apiURL := subPath + "/get-detail-subscription-schedule"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"subscriptionId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check the response if subscriptionId is empty", func() {
				body := map[string]interface{}{
					"userId":         "123",
					"subscriptionId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_ID_REQUIRED.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_SUBSCRIPTION_ID_REQUIRED.Message)
			})
			Convey("Check the response if subscription not found", func() {
				body := map[string]interface{}{
					"userId":         "123",
					"subscriptionId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 404)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_SUBSCRIPTION_NOT_FOUND.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_SUBSCRIPTION_NOT_FOUND.Message)
			})
		})
	})
	t.Run("30", func(t *testing.T) {
		log.Println("==================================== GetDetailSubscriptionSchedule")
		apiGetSubscription := "/api/v3/api-asker-vn/get-detail-subscription-schedule"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone":           "0834567880",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"duration":    3,
				"dateTime":    globalLib.ParseDateFromString("2024-05-11T08:00:00.000+07:00", local.TimeZone),
				"cost":        200000,
				"status":      globalConstant.TASK_STATUS_DONE,
			}, {
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"duration":    3,
				"dateTime":    globalLib.ParseDateFromString("2024-05-18T08:00:00.000+07:00", local.TimeZone),
				"cost":        200000,
				"status":      globalConstant.TASK_STATUS_CANCELED,
			}, {
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"duration":    3,
				"dateTime":    globalLib.ParseDateFromString("2024-05-25T15:00:00.000+07:00", local.TimeZone),
				"cost":        200000,
				"status":      globalConstant.TASK_STATUS_DONE,
			}, {
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"duration":    3,
				"dateTime":    globalLib.ParseDateFromString("2024-06-01T08:00:00.000+07:00", local.TimeZone),
				"cost":        200000,
				"status":      globalConstant.TASK_STATUS_EXPIRED,
			}, {
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"duration":    3,
				"dateTime":    globalLib.ParseDateFromString("2024-06-08T08:00:00.000+07:00", local.TimeZone),
				"cost":        200000,
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
			}, {
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"duration":    3,
				"dateTime":    globalLib.ParseDateFromString("2024-06-15T08:00:00.000+07:00", local.TimeZone),
				"cost":        200000,
				"status":      globalConstant.TASK_STATUS_POSTED,
			},
		})

		//Create Subscription
		subIds := CreateSubscription([]map[string]interface{}{
			{
				"address": "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"location": map[string]interface{}{
					"lat": 10.7311904132054,
					"lng": 106.706043547317,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 7",
				},
				"askerPhone":          "0834567890",
				"price":               1803600,
				"discount":            0.1,
				"serviceName":         globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description":         "5B03-15",
				"contactName":         "Osvaldo Raynor",
				"phone":               "0761781715",
				"status":              globalConstant.SUBSCRIPTION_STATUS_NEW,
				"forceAcceptTaskerId": "0834567880",
				"scheduleTime": []time.Time{
					globalLib.ParseDateFromString("2024-05-11T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-05-18T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-05-25T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-06-01T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-06-08T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-06-15T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-06-22T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-06-29T08:00:00.000+07:00", local.TimeZone),
					globalLib.ParseDateFromString("2024-07-06T08:00:00.000+07:00", local.TimeZone),
				},
				"taskIds": []string{taskIds[0], taskIds[1], taskIds[2], taskIds[3], taskIds[4], taskIds[5]},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetSubscription), t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"isoCode":        local.ISO_CODE,
				"subscriptionId": subIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetSubscription, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Subscription by User", func() {
					var respResult map[string]interface{}
					var respResultM map[string][]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)

					So(resp.Code, ShouldEqual, 200)
					So(respResult["totalTask"], ShouldEqual, 9)
					So(respResult["remainingTask"], ShouldEqual, 3)
					So(respResult["totalTaskDone"], ShouldEqual, 2)
					So(respResult["totalTaskCancel"], ShouldEqual, 1)
					So(respResult["totalTaskExpired"], ShouldEqual, 1)
					So(respResult["totalTaskProcess"], ShouldEqual, 2)

					So(len(respResultM["scheduleInfos"]), ShouldEqual, 9)
					So(respResultM["scheduleInfos"][0]["date"], ShouldEqual, "2024-05-11T08:00:00+07:00")
					So(respResultM["scheduleInfos"][0]["status"], ShouldEqual, "DONE")
					So(respResultM["scheduleInfos"][1]["date"], ShouldEqual, "2024-05-18T08:00:00+07:00")
					So(respResultM["scheduleInfos"][1]["status"], ShouldEqual, "CANCELED")
					So(respResultM["scheduleInfos"][2]["date"], ShouldEqual, "2024-05-25T15:00:00+07:00")
					So(respResultM["scheduleInfos"][2]["status"], ShouldEqual, "DONE")
					So(respResultM["scheduleInfos"][3]["date"], ShouldEqual, "2024-06-01T08:00:00+07:00")
					So(respResultM["scheduleInfos"][3]["status"], ShouldEqual, "EXPIRED")
					So(respResultM["scheduleInfos"][4]["date"], ShouldEqual, "2024-06-08T08:00:00+07:00")
					So(respResultM["scheduleInfos"][4]["status"], ShouldEqual, "PROCESSING")
					So(respResultM["scheduleInfos"][5]["date"], ShouldEqual, "2024-06-15T08:00:00+07:00")
					So(respResultM["scheduleInfos"][5]["status"], ShouldEqual, "PROCESSING")
					So(respResultM["scheduleInfos"][6]["date"], ShouldEqual, "2024-06-22T08:00:00+07:00")
					So(respResultM["scheduleInfos"][6]["status"], ShouldEqual, "WAITING_POSTED")
					So(respResultM["scheduleInfos"][7]["date"], ShouldEqual, "2024-06-29T08:00:00+07:00")
					So(respResultM["scheduleInfos"][7]["status"], ShouldEqual, "WAITING_POSTED")
					So(respResultM["scheduleInfos"][8]["date"], ShouldEqual, "2024-07-06T08:00:00+07:00")
					So(respResultM["scheduleInfos"][8]["status"], ShouldEqual, "WAITING_POSTED")
				})
			})
		})
	})

}
