/*
 * @File: 3_FATransaction_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 20/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func TestFATransaction(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		apiGetFATransactions := "/api/v3/api-asker-vn/get-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		time := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		timeSe := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   time,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			},
		})
		log.Println("==================================== Validate GetFATransactions")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFATransactions), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get FATransaction", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 4)
					So(respResult[0]["userId"], ShouldEqual, "**********")
					So(respResult[0]["accountType"], ShouldEqual, "M")
					So(respResult[0]["type"], ShouldEqual, "COD")
					So(respResultM[0]["source"]["name"], ShouldEqual, "NAME TEST")
					So(respResultM[0]["source"]["value"], ShouldEqual, "VALUE TEST")
					So(respResult[0]["amount"], ShouldBeGreaterThan, 0)

					So(respResult[1]["userId"], ShouldEqual, "**********")
					So(respResult[1]["accountType"], ShouldEqual, "M")
					So(respResult[1]["type"], ShouldEqual, "COD")
					So(respResultM[1]["source"]["name"], ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK)
					So(respResultM[1]["source"]["value"], ShouldEqual, ids[0])
					So(respResult[1]["amount"], ShouldBeGreaterThan, 0)
					So(respResultM[1]["dataTask"]["_id"], ShouldEqual, ids[0])
					So(respResultM[1]["dataTask"]["cost"], ShouldEqual, 200000)
					So(respResultM[1]["dataTask"]["address"], ShouldNotBeBlank)
				})
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFATransactions), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		apiGetFATransactions := "/api/v3/api-asker-vn/get-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		timeSe := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"isoCode": "",
				"amount":  193000,
				"date":    timeSe,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			},
		})
		log.Println("==================================== GetFATransactions")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFATransactions), t, func() {
			Convey("Check request with page, limit", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": local.ISO_CODE,
					"page":    1,
					"limit":   5,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				for _, v := range respResultM {
					So(v["currency"]["code"], ShouldEqual, "VND")
				}
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		apiGetFATransactions := "/api/v3/api-asker-vn/get-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		time := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		timeSe := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   time,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   timeSe,
			},
		})
		log.Println("==================================== GetFATransactions")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFATransactions), t, func() {
			Convey("Check request with page, limit (page 2)", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": local.ISO_CODE,
					"page":    2,
					"limit":   3,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 1)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		// GetDetailFATransaction
		log.Println("==================================== Validate GetDetailFATransaction")
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"id": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   now,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   now,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})

					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["isReport"], ShouldBeFalse)
					So(respResultM["data"]["_id"], ShouldEqual, idFATs[0])
					So(respResultM["data"]["accountType"], ShouldEqual, "M")
					So(respResultM["data"]["source"].(map[string]interface{})["name"], ShouldEqual, "NAME TEST")
					So(respResultM["data"]["source"].(map[string]interface{})["value"], ShouldEqual, "VALUE TEST")

					date, _ := time.Parse(time.RFC3339, respResult["date"].(string))
					timeNow := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
					isDateEqual := date.UTC().Equal(timeNow.UTC())
					So(isDateEqual, ShouldBeTrue)
				})
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   now,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": ids[0],
				},
				"amount": 193000,
				"date":   now,
			},
		})
		CreateReportTransaction([]map[string]interface{}{
			{
				"userId":        "**********",
				"transactionId": idFATs[1],
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[1],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["data"]["_id"], ShouldEqual, ids[0])
					So(respResultM["data"]["phone"], ShouldEqual, "**********")

					date, _ := time.Parse(time.RFC3339, respResult["date"].(string))
					timeNow := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
					isDateEqual := date.UTC().Equal(timeNow.UTC())
					So(isDateEqual, ShouldBeTrue)
					So(respResultM["data"]["address"], ShouldNotBeBlank)
					So(respResultM["data"]["contactName"], ShouldEqual, "Asker 01")
					So(respResultM["data"]["description"], ShouldEqual, "Desc Test")
					So(respResultM["data"]["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
					So(respResult["type"], ShouldEqual, "task")
					So(respResult["isReport"], ShouldBeTrue)
				})
			})
		})
	})
	// t.Run("7", func(t *testing.T) {
	// 	log.Println("==================================== Validate GetAdsCarTransactionHistory")
	// 	// GetAdsCarTransactionHistory
	// 	apiUrl := "/api/v3/api-asker-vn/get-ads-car-transaction-history"
	// 	ResetData()
	// 	fromDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 0)
	// 	toDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 7)

	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
	// 		Convey("Check request when PARSE_API_PARAMS ERROR", func() {
	// 			body := map[string]interface{}{
	// 				"userId": 123,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)
	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
	// 		})

	// 		Convey("Check request when userId blank", func() {
	// 			body := map[string]interface{}{
	// 				"userId":   "",
	// 				"fromDate": fromDate,
	// 				"toDate":   toDate,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
	// 		})

	// 		Convey("Check request when fromDate nil", func() {
	// 			body := map[string]interface{}{
	// 				"userId": "**********",
	// 				"toDate": toDate,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TIME_REQUIRED.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TIME_REQUIRED.Message)
	// 		})

	// 		Convey("Check request when toDate nil", func() {
	// 			body := map[string]interface{}{
	// 				"userId":   "**********",
	// 				"fromDate": fromDate,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TIME_REQUIRED.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TIME_REQUIRED.Message)
	// 		})
	// 	})
	// })
	// t.Run("8", func(t *testing.T) {
	// 	log.Println("==================================== GetAdsCarTransactionHistory")
	// 	// GetAdsCarTransactionHistory
	// 	apiUrl := "/api/v3/api-asker-vn/get-ads-car-transaction-history"
	// 	ResetData()
	// 	time := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
	// 	fromDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 0)
	// 	toDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 7)
	// 	//Create User
	// 	CreateUser([]map[string]interface{}{
	// 		map[string]interface{}{
	// 			"phone": "**********",
	// 			"name":  "Asker 01",
	// 			"type":  globalConstant.USER_TYPE_ASKER,
	// 		},
	// 	})
	// 	//Create TASK
	// 	ids := CreateTask([]map[string]interface{}{
	// 		map[string]interface{}{
	// 			"askerPhone":  "**********",
	// 			"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
	// 			"description": "Desc Test",
	// 			"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
	// 			"status":      globalConstant.TASK_STATUS_DONE,
	// 		},
	// 	})
	// 	//Create FATransaction
	// 	idFATs := CreateFATransaction([]map[string]interface{}{
	// 		map[string]interface{}{
	// 			"userId":      "**********",
	// 			"accountType": "P",
	// 			"type":        "COD",
	// 			"source": map[string]interface{}{
	// 				"name":  "NAME TEST",
	// 				"value": "VALUE TEST",
	// 			},
	// 			"amount":         162000,
	// 			"date": time,
	// 		}, {
	// 			"userId":      "**********",
	// 			"accountType": "P",
	// 			"type":        "COD",
	// 			"source": map[string]interface{}{
	// 				"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
	// 				"value": ids[0],
	// 			},
	// 			"amount":         193000,
	// 			"date": time,
	// 		},
	// 	})
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
	// 		body := map[string]interface{}{
	// 			"userId":   "**********",
	// 			"fromDate": fromDate,
	// 			"toDate":   toDate,
	// 		}
	// 		reqBody, _ := json.Marshal(body)
	// 		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 		resp := httptest.NewRecorder()

	// 		Convey("When the request is handled by the Router", func() {
	// 			service.NewRouter().ServeHTTP(resp, req)

	// 			Convey("Then check the response to test Get Detail FATransaction", func() {
	// 				respResult := []map[string]interface{}{}
	// 				bytes, _ := io.ReadAll(resp.Body)

	// 				json.Unmarshal(bytes, &respResult)
	// 				So(resp.Code, ShouldEqual, 200)
	// 				So(respResult[0]["_id"], ShouldEqual, idFATs[0])
	// 				So(respResult[1]["_id"], ShouldEqual, idFATs[1])
	// 				for _, v := range respResult {
	// 					So(v["accountType"], ShouldEqual, "P")
	// 					So(v["type"], ShouldEqual, "COD")
	// 					So(v["userId"], ShouldEqual, "**********")
	// 					fromDateTimestamp := globalLib.ParseTimestampFromDate(fromDate)
	// 					toDateTimestamp := globalLib.ParseTimestampFromDate(toDate)
	// 					So(v["date"].(map[string]interface{})["seconds"].(float64), ShouldBeGreaterThanOrEqualTo, float64(fromDateTimestamp.Seconds))
	// 					So(v["date"].(map[string]interface{})["seconds"].(float64), ShouldBeLessThanOrEqualTo, float64(toDateTimestamp.Seconds))
	// 				}
	// 			})
	// 		})
	// 	})
	// })

	t.Run("9", func(t *testing.T) {
		apiGetFATransactions := "/api/v3/api-asker-vn/get-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})
		//Create Reward
		rewardIds := CreateReward([]map[string]interface{}{
			{
				"userPhone": "**********",
				"type":      "Type Test 01",
			},
		})

		timeSe := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":     globalConstant.FA_TRANSACTION_SOURCE_NAME_TASKER_MONTHLY_REWARD,
					"value":    ids[0],
					"rewardId": rewardIds[0],
				},
				"amount": 193000,
				"date":   timeSe,
			},
		})
		log.Println("==================================== GetFATransactions")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFATransactions), t, func() {
			Convey("Check request with page, limit", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFATransactions, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult []map[string]interface{}
				var respResultM []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)

				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResultM[0]["dataReward"], ShouldNotBeNil)
			})
		})
	})

	t.Run("10", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Subscription
		subIds := CreateSubscription([]map[string]interface{}{
			{
				"address":     "12 Phạm Văn Nghị - Bắc, Tân Phong, Quận 7, Hồ Chí Minh, Việt Nam",
				"askerPhone":  "**********",
				"price":       1803600,
				"discount":    0.1,
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "5B03-15",
				"contactName": "Osvaldo Raynor",
				"phone":       "**********",
				"status":      globalConstant.SUBSCRIPTION_STATUS_NEW,
			},
		})

		//Create Subscription
		purIds := CreatePurchaseOrder([]map[string]interface{}{
			{
				"subscriptionId": subIds[0],
			},
		})

		time := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "taskId:",
				},
				"amount": 162000,
				"date":   time,
			}, {
				"userId":      "**********",
				"accountType": "T",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "PAY_SUBSCRIPTION",
					"value": purIds[0],
				},
				"amount": 193000,
				"date":   time,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[1],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["data"]["_id"], ShouldEqual, subIds[0])
					So(respResultM["data"]["address"], ShouldNotBeBlank)
					So(respResultM["data"]["contactName"], ShouldEqual, "Osvaldo Raynor")
				})
			})
		})
	})
	t.Run("11", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create TASK
		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Kaliser",
						"avatar":   "/avatars/avatarDefault.png",
						"taskDone": 3,
					},
				},
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   now,
			}, {
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK,
					"value": fmt.Sprintf("taskId: %s", ids[0]),
				},
				"amount": 193000,
				"date":   now,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[1],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					respResultSM := make(map[string]map[string][]map[string]interface{})
					respResultMM := make(map[string]map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultMM)
					So(resp.Code, ShouldEqual, 200)
					for _, v := range respResultSM["data"]["acceptedTasker"] {
						So(v["name"], ShouldEqual, "Kaliser")
						So(v["taskerId"], ShouldEqual, "**********")
					}
					So(respResultMM["data"]["infoTasker"]["name"], ShouldEqual, "Tasker 01")
					So(respResultMM["data"]["infoTasker"]["_id"], ShouldEqual, "**********")
					So(respResultM["data"]["_id"], ShouldEqual, ids[0])
					So(respResultM["data"]["phone"], ShouldEqual, "**********")

					date, _ := time.Parse(time.RFC3339, respResult["date"].(string))
					timeNow := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
					isDateEqual := date.UTC().Equal(timeNow.UTC())
					So(isDateEqual, ShouldBeTrue)
					So(respResultM["data"]["address"], ShouldNotBeBlank)
					So(respResultM["data"]["contactName"], ShouldEqual, "Asker 01")
					So(respResultM["data"]["description"], ShouldEqual, "Desc Test")
					So(respResultM["data"]["status"], ShouldEqual, globalConstant.TASK_STATUS_DONE)
					So(respResult["type"], ShouldEqual, "task")
				})
			})
		})
	})

	t.Run("12", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create FATransaction
		timeSe := globalLib.GetCurrentTime(local.TimeZone)
		//Create Reward
		rewardIds := CreateReward([]map[string]interface{}{
			{
				"userPhone": "**********",
				"type":      "Type Test 01",
			},
		})
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":     "MONTHLY REWARD FOR ASKER",
					"value":    "test Reward",
					"rewardId": rewardIds[0],
				},
				"amount": 193000,
				"date":   timeSe,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["data"]["_id"], ShouldEqual, rewardIds[0])
					So(respResult["type"], ShouldEqual, "reward")
				})
			})
		})
	})

	t.Run("13", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create FATransaction
		timeSe := globalLib.GetCurrentTime(local.TimeZone)
		trIds := CreateTransaction([]map[string]interface{}{
			{
				"payment": map[string]interface{}{
					"method":     globalConstant.PAYMENT_METHOD_CARD,
					"cardNumber": "123",
					"cardType":   "test",
				},
			},
		})

		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "TOP_UP_CREDIT",
					"value": trIds[0],
				},
				"amount": 193000,
				"date":   timeSe,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["data"]["payment"]["cardNumber"], ShouldEqual, "123")
					So(respResultM["data"]["payment"]["cardType"], ShouldEqual, "test")
					So(respResultM["data"]["payment"]["method"], ShouldEqual, "CARD")
					So(respResultM["data"]["payment"]["cardInfo"], ShouldNotBeNil)
					So(respResult["type"], ShouldEqual, "topUp")
				})
			})
		})
	})

	t.Run("14", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "REFERRAL",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   now,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["data"]["_id"], ShouldEqual, idFATs[0])
					So(respResultM["data"]["accountType"], ShouldEqual, "M")
					So(respResultM["data"]["source"].(map[string]interface{})["name"], ShouldEqual, "REFERRAL")
					So(respResultM["data"]["source"].(map[string]interface{})["value"], ShouldEqual, "VALUE TEST")

					date, _ := time.Parse(time.RFC3339, respResult["date"].(string))
					timeNow := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
					isDateEqual := date.UTC().Equal(timeNow.UTC())
					So(isDateEqual, ShouldBeTrue)
					So(respResult["type"], ShouldEqual, "referral")
				})
			})
		})
	})

	t.Run("15", func(t *testing.T) {
		log.Println("==================================== GetDetailFATransaction")
		// GetDetailFATransaction
		apiGetDetailFATransaction := "/api/v3/api-asker-vn/get-detail-financial-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		//Create FATransaction
		idFATs := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "PROMOTION",
					"value": "VALUE TEST",
				},
				"amount": 162000,
				"date":   now,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetDetailFATransaction), t, func() {
			body := map[string]interface{}{
				"id": idFATs[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetDetailFATransaction, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Detail FATransaction", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["data"]["_id"], ShouldEqual, idFATs[0])
					So(respResultM["data"]["accountType"], ShouldEqual, "M")
					So(respResultM["data"]["source"].(map[string]interface{})["name"], ShouldEqual, "PROMOTION")
					So(respResultM["data"]["source"].(map[string]interface{})["value"], ShouldEqual, "VALUE TEST")

					date, _ := time.Parse(time.RFC3339, respResult["date"].(string))
					timeNow := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), 0, now.Location())
					isDateEqual := date.UTC().Equal(timeNow.UTC())
					So(isDateEqual, ShouldBeTrue)
					So(respResult["type"], ShouldEqual, "promotion")
				})
			})
		})
	})
}
