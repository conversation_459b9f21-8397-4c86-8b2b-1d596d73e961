package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelRedeemBundleVoucherHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemBundleVoucherHistory"
	modelRedeemBundleVoucherTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemBundleVoucherTransaction"

	"go.mongodb.org/mongo-driver/bson"
)

func TestRedeemBundleVoucher(t *testing.T) {
	apiRedeemBundleVoucher := "/api/v3/api-asker-vn/redeem-bundle-voucher"
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate ")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if bundleVoucherId empty", func() {
				body := map[string]interface{}{
					"userId":          "1234",
					"bundleVoucherId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_BUNDLE_VOUCHER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId":          "123",
					"bundleVoucherId": "12234",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher not found")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 1,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "1234",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 404)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_BUNDLE_VOUCHER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		ResetData()
		log.Println("==================================== rank user is not allowed")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"rankRequire": 4,
				"createdAt":   currentTime.AddDate(0, 0, -2),
			},
		})
		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 500)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_YOUR_RANK_CAN_NOT_REDEEM_GIFT.ErrorCode)
				})
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		ResetData()
		log.Println("==================================== asker turn not allowed")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"createdAt": currentTime.AddDate(0, 0, -2),
				"askerTurn": 1,
			},
		})
		CreateBundleVoucherHistory([]map[string]interface{}{
			{
				"bundleVoucherId": "bundleVoucherId",
				"userId":          "0823456720",
				"totalRedeem":     1,
				"createdAt":       currentTime.AddDate(0, 0, -2),
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 500)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_YOUR_TURN_CAN_NOT_REDEEM_GIFT.ErrorCode)
				})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher has stopped")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "INACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": true,
			},
		})
		CreateBundleVoucherHistory([]map[string]interface{}{
			{
				"bundleVoucherId": "bundleVoucherId",
				"userId":          "0823456720",
				"totalRedeem":     1,
				"createdAt":       currentTime.AddDate(0, 0, -2),
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 500)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_BUNDLE_VOUCHER_HAS_STOPPED.ErrorCode)
				})
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		ResetData()
		log.Println("==================================== asker not enough point")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 0,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": true,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 500)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_ENOUGH_POINTS_REDEEM_BUNDLE.ErrorCode)
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher expired")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 35,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -5),
				"endDate":   currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": true,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 500)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_BUNDLE_VOUCHER_EXPIRED.ErrorCode)
				})
			})
		})
	})

	t.Run("8", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher has not stock")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 35,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -5),
				"endDate":   currentTime.AddDate(0, 0, 5),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
					},
					{
						"incentiveId": "voucher2",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
					},
					{
						"incentiveId": "voucher3",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": false,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 500)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_BUNDLE_VOUCHER_HAS_NO_STOCK.ErrorCode)
				})
			})
		})
	})

	t.Run("9", func(t *testing.T) {
		ResetData()
		log.Println("==================================== incentive not found")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 35,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -5),
				"endDate":   currentTime.AddDate(0, 0, 5),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    1,
					},
					{
						"incentiveId": "voucher2",
						"stock":       2,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    3,
					},
					{
						"incentiveId": "voucher3",
						"stock":       1,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    4,
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": false,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(res.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(res.Code, ShouldEqual, 404)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_INCENTIVE_NOT_FOUND.ErrorCode)
				})
			})
		})
	})

	t.Run("10", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher redeem success with voucher quantity = 1")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 1000,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)

		before1Day := currentTime.AddDate(0, 0, -1)
		after2Days := currentTime.AddDate(0, 0, 2)
		CreateIncentive([]map[string]interface{}{
			{
				"_id":       "voucher1",
				"image":     "voucher1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              10000,
				},
				"content": map[string]interface{}{
					"vi": "voucher1 - 10.000đ",
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
			}, {
				"_id":       "voucher2",
				"image":     "voucher2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"content": map[string]interface{}{
					"vi": "voucher2 - 5.000đ",
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              5000,
				},
				"from":         "SYSTEM",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
		})

		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -5),
				"endDate":   currentTime.AddDate(0, 0, 5),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"stock":       1,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    1,
					},
					{
						"incentiveId": "voucher2",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    2,
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": false,
				"askerTurn":       4,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					// check bundle transaction
					var bundleTransaction *modelRedeemBundleVoucherTransaction.RedeemBundleVoucherTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0823456720", "bundleVoucherInfo.bundleVoucherId": "bundleVoucherId"}, bson.M{"bundleVoucherInfo": 1, "status": 1}, &bundleTransaction)
					So(bundleTransaction, ShouldNotBeNil)
					So(bundleTransaction.Status, ShouldEqual, globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_SUCCESS)
					So(bundleTransaction.BundleVoucherInfo.BundleVoucherId, ShouldEqual, "bundleVoucherId")
					So(bundleTransaction.BundleVoucherInfo.Title.Vi, ShouldEqual, "Gói voucher bí ẩn")
					So(bundleTransaction.BundleVoucherInfo.Exchange.RequiredPoint, ShouldEqual, 30)

					// check bundle history
					var bundleHistorty *modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{"userId": "0823456720", "bundleVoucherId": "bundleVoucherId"}, bson.M{}, &bundleHistorty)
					So(bundleHistorty, ShouldNotBeNil)
					So(bundleHistorty.TotalRedeem, ShouldEqual, 1)
					So(len(bundleHistorty.VoucherHistories), ShouldBeGreaterThan, 0)
					So(bundleHistorty.VoucherHistories[0].IncentiveId, ShouldEqual, "voucher1")
					So(bundleHistorty.VoucherHistories[0].Quantity, ShouldEqual, 1)

					// check promotion
					var promotion *modelPromotionCode.PromotionCode
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"userIds": bson.M{"$in": []string{"0823456720"}}}, bson.M{}, &promotion)
					So(promotion, ShouldNotBeNil)

					// check point transaction
					var pointTransaction *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0823456720", "referenceId": "bundleVoucherId"}, bson.M{}, &pointTransaction)
					So(pointTransaction, ShouldNotBeNil)
					So(pointTransaction.Point, ShouldEqual, 30)
					So(pointTransaction.Type, ShouldEqual, "C")
					So(pointTransaction.Source, ShouldNotBeNil)
					So(pointTransaction.Source.Name, ShouldEqual, "SYSTEM")

					var gift *modelGift.Gift
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"pointTransactionId": pointTransaction.XId, "promotionId": promotion.XId, "userId": "0823456720"}, bson.M{}, &gift)
					So(gift, ShouldNotBeNil)
					So(gift.Content.Vi, ShouldEqual, "voucher1 - 10.000đ")
					So(gift.Image, ShouldEqual, "voucher1")
					So(gift.PointTransactionId, ShouldEqual, pointTransaction.XId)
					So(gift.PromotionCode, ShouldEqual, promotion.Code)
					So(gift.PromotionId, ShouldEqual, promotion.XId)
				})
			})
		})
	})

	t.Run("11", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher redeem success with voucher quantity = 3")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 1000,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)

		before1Day := currentTime.AddDate(0, 0, -1)
		after2Days := currentTime.AddDate(0, 0, 2)
		CreateIncentive([]map[string]interface{}{
			{
				"_id":       "voucher1",
				"image":     "voucher1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              10000,
				},
				"content": map[string]interface{}{
					"vi": "voucher1 - 10.000đ",
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
			}, {
				"_id":       "voucher2",
				"image":     "voucher2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"content": map[string]interface{}{
					"vi": "voucher2 - 5.000đ",
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              5000,
				},
				"from":         "SYSTEM",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
		})

		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -5),
				"endDate":   currentTime.AddDate(0, 0, 5),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 30,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"stock":       1,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    3,
					},
					{
						"incentiveId": "voucher2",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    2,
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": false,
				"askerTurn":       4,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					// check bundle transaction
					var bundleTransaction *modelRedeemBundleVoucherTransaction.RedeemBundleVoucherTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0823456720", "bundleVoucherInfo.bundleVoucherId": "bundleVoucherId"}, bson.M{"bundleVoucherInfo": 1, "status": 1}, &bundleTransaction)
					So(bundleTransaction, ShouldNotBeNil)
					So(bundleTransaction.Status, ShouldEqual, globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_SUCCESS)
					So(bundleTransaction.BundleVoucherInfo.BundleVoucherId, ShouldEqual, "bundleVoucherId")
					So(bundleTransaction.BundleVoucherInfo.Title.Vi, ShouldEqual, "Gói voucher bí ẩn")
					So(bundleTransaction.BundleVoucherInfo.Exchange.RequiredPoint, ShouldEqual, 30)

					// check bundle history
					var bundleHistorty *modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{"userId": "0823456720", "bundleVoucherId": "bundleVoucherId"}, bson.M{}, &bundleHistorty)
					So(bundleHistorty, ShouldNotBeNil)
					So(bundleHistorty.TotalRedeem, ShouldEqual, 1)
					So(len(bundleHistorty.VoucherHistories), ShouldBeGreaterThan, 0)
					So(bundleHistorty.VoucherHistories[0].IncentiveId, ShouldEqual, "voucher1")
					So(bundleHistorty.VoucherHistories[0].Quantity, ShouldEqual, 3)

					// check promotion
					var promotion []*modelPromotionCode.PromotionCode
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"userIds": bson.M{"$in": []string{"0823456720"}}}, bson.M{}, &promotion)
					So(len(promotion), ShouldEqual, 3)
					for _, v := range promotion {
						So(v.Value.Value, ShouldEqual, 10000)
						So(v.Value.Type, ShouldEqual, "MONEY")
					}

					// check point transaction
					var pointTransaction *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0823456720", "referenceId": "bundleVoucherId"}, bson.M{}, &pointTransaction)
					So(pointTransaction, ShouldNotBeNil)
					So(pointTransaction.Point, ShouldEqual, 30)
					So(pointTransaction.Type, ShouldEqual, "C")
					So(pointTransaction.Source, ShouldNotBeNil)
					So(pointTransaction.Source.Name, ShouldEqual, "SYSTEM")

					var gift []*modelGift.Gift
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"pointTransactionId": pointTransaction.XId, "userId": "0823456720"}, bson.M{}, &gift)
					So(len(gift), ShouldEqual, 3)
				})
			})
		})
	})

	t.Run("12", func(t *testing.T) {
		ResetData()
		log.Println("==================================== bundle voucher redeem success with 0 point")
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
				"point": 0,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)

		before1Day := currentTime.AddDate(0, 0, -1)
		after2Days := currentTime.AddDate(0, 0, 2)
		CreateIncentive([]map[string]interface{}{
			{
				"_id":       "voucher1",
				"image":     "voucher1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              10000,
				},
				"content": map[string]interface{}{
					"vi": "voucher1 - 10.000đ",
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
			}, {
				"_id":       "voucher2",
				"image":     "voucher2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"content": map[string]interface{}{
					"vi": "voucher2 - 5.000đ",
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
					"type":               "MONEY",
					"value":              5000,
				},
				"from":         "SYSTEM",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
		})

		CreateBundleVoucher([]map[string]interface{}{
			{
				"_id":       "bundleVoucherId",
				"image":     "abcxyz",
				"type":      globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX,
				"startDate": currentTime.AddDate(0, 0, -5),
				"endDate":   currentTime.AddDate(0, 0, 5),
				"status":    "ACTIVE",
				"title": map[string]interface{}{
					"vi": "Gói voucher bí ẩn",
				},
				"content": map[string]interface{}{
					"vi": "Gói voucher bí ẩn chứa ít nhất 1 trong 5 phần quà",
				},
				"termsAndCondition": map[string]interface{}{
					"vi": "- Mã ưu đãi chỉ áp dụng cho công việc Dọn dẹp nhà ca lẻ.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất\n- Không có giá trị quy đổi thành tiền mặt, không hoàn trả\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác\n- Mã chỉ có hạn sử dụng 30 ngày kể từ lúc Khách hàng thao tác đổi điểm.",
				},
				"exchange": map[string]interface{}{
					"requiredPoint": 0,
				},
				"vouchers": []map[string]interface{}{
					{
						"incentiveId": "voucher1",
						"stock":       1,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    3,
					},
					{
						"incentiveId": "voucher2",
						"stock":       0,
						"type":        globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT,
						"quantity":    2,
					},
				},
				"createdAt":       currentTime.AddDate(0, 0, -2),
				"isUnlimitRedeem": false,
				"askerTurn":       4,
			},
		})

		body := map[string]interface{}{
			"userId":          "0823456720",
			"bundleVoucherId": "bundleVoucherId",
		}
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRedeemBundleVoucher), t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRedeemBundleVoucher, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					// check bundle transaction
					var bundleTransaction *modelRedeemBundleVoucherTransaction.RedeemBundleVoucherTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0823456720", "bundleVoucherInfo.bundleVoucherId": "bundleVoucherId"}, bson.M{"bundleVoucherInfo": 1, "status": 1}, &bundleTransaction)
					So(bundleTransaction, ShouldNotBeNil)
					So(bundleTransaction.Status, ShouldEqual, globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_SUCCESS)
					So(bundleTransaction.BundleVoucherInfo.BundleVoucherId, ShouldEqual, "bundleVoucherId")
					So(bundleTransaction.BundleVoucherInfo.Title.Vi, ShouldEqual, "Gói voucher bí ẩn")
					So(bundleTransaction.BundleVoucherInfo.Exchange.RequiredPoint, ShouldEqual, 0)

					// check bundle history
					var bundleHistorty *modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{"userId": "0823456720", "bundleVoucherId": "bundleVoucherId"}, bson.M{}, &bundleHistorty)
					So(bundleHistorty, ShouldNotBeNil)
					So(bundleHistorty.TotalRedeem, ShouldEqual, 1)
					So(len(bundleHistorty.VoucherHistories), ShouldBeGreaterThan, 0)
					So(bundleHistorty.VoucherHistories[0].IncentiveId, ShouldEqual, "voucher1")
					So(bundleHistorty.VoucherHistories[0].Quantity, ShouldEqual, 3)

					// check promotion
					var promotion []*modelPromotionCode.PromotionCode
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"userIds": bson.M{"$in": []string{"0823456720"}}}, bson.M{}, &promotion)
					So(len(promotion), ShouldEqual, 3)
					for _, v := range promotion {
						So(v.Value.Value, ShouldEqual, 10000)
						So(v.Value.Type, ShouldEqual, "MONEY")
					}

					// check point transaction
					var pointTransaction *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "0823456720", "referenceId": "bundleVoucherId"}, bson.M{}, &pointTransaction)
					So(pointTransaction, ShouldBeNil)

					var gift []*modelGift.Gift
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"incentiveId": "voucher1", "userId": "0823456720"}, bson.M{}, &gift)
					So(len(gift), ShouldEqual, 3)
				})
			})
		})
	})

}
