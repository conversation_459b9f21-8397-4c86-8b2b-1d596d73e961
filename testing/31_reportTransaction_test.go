package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelReportTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reportTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

func TestReportTransaction(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/create-report-transaction"
		ResetData()

		log.Println("==================================== Validate CreateReportTransaction")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"UserId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"isoCode":       local.ISO_CODE,
					"userId":        "",
					"transactionId": "abc",
					"reasonReport": []map[string]interface{}{
						{
							"vi": "xyz",
							"ko": "aksd",
						},
						{
							"vi": "a-z",
							"ko": "12",
						},
					},
					"otherReport": "abcxyz",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"isoCode":       "",
					"userId":        "**********",
					"transactionId": "abc",
					"reasonReport": []map[string]interface{}{
						{
							"vi": "xyz",
							"ko": "aksd",
						},
						{
							"vi": "a-z",
							"ko": "12",
						},
					},
					"otherReport": "abcxyz",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"isoCode":       "HA",
					"userId":        "**********",
					"transactionId": "abc",
					"reasonReport": []map[string]interface{}{
						{
							"vi": "xyz",
							"ko": "aksd",
						},
						{
							"vi": "a-z",
							"ko": "12",
						},
					},
					"otherReport": "abcxyz",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when transactionId blank", func() {
				body := map[string]interface{}{
					"isoCode":       local.ISO_CODE,
					"userId":        "**********",
					"transactionId": "",
					"reasonReport": []map[string]interface{}{
						{
							"vi": "xyz",
							"ko": "aksd",
						},
						{
							"vi": "a-z",
							"ko": "12",
						},
					},
					"otherReport": "abcxyz",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TRANSACTION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TRANSACTION_ID_REQUIRED.Message)
			})
			Convey("Check request when reason nil and other blank", func() {
				body := map[string]interface{}{
					"isoCode":       local.ISO_CODE,
					"userId":        "**********",
					"transactionId": "xx",
					"otherReport":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_REASON_REPORT_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_REASON_REPORT_REQUIRED.Message)
			})
			Convey("Check request when user not found", func() {
				body := map[string]interface{}{
					"isoCode":       local.ISO_CODE,
					"userId":        "**********",
					"transactionId": "xxx",
					"reasonReport": []map[string]interface{}{
						{
							"vi": "xyz",
							"ko": "aksd",
						},
						{
							"vi": "123",
							"ko": "1234",
						},
					},
					"otherReport": "test orther",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetUser")
		apiUrl := "/api/v3/api-asker-vn/create-report-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"locations": []map[string]interface{}{
					{
						"_id": "1",
					},
				},
				"point": 1,
				"rankInfo": map[string]interface{}{
					"point": 1,
				},
				"lastPostedTask": globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		//Create Transaction
		trIds := CreateTransaction([]map[string]interface{}{
			{
				"payment": map[string]interface{}{
					"method":     globalConstant.PAYMENT_METHOD_CARD,
					"cardNumber": "123",
					"cardType":   "test",
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"isoCode":       local.ISO_CODE,
				"userId":        "**********",
				"transactionId": trIds[0],
				"reasonReport": []map[string]interface{}{
					{
						"vi": "xyz",
						"ko": "aksd",
					},
					{
						"ko": "1234",
						"en": "456",
					},
				},
				"otherReport": "test orther",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Create Report Transaction", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check Database to test Create Report Transaction", func() {
				var reportTransaction *modelReportTransaction.ReportTransaction
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE], bson.M{"transactionId": trIds[0]}, bson.M{}, &reportTransaction)
				So(reportTransaction.UserId, ShouldEqual, "**********")
				So(reportTransaction.IsoCode, ShouldEqual, local.ISO_CODE)
				So(reportTransaction.Other, ShouldEqual, "test orther")
				So(reportTransaction.Reason, ShouldNotBeNil)
				So(reportTransaction.Reason[0].Vi, ShouldEqual, "xyz")
				So(reportTransaction.Reason[1].En, ShouldEqual, "456")
			})

		})
	})
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Transaction has been report")
		apiUrl := "/api/v3/api-asker-vn/create-report-transaction"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"locations": []map[string]interface{}{
					{
						"_id": "1",
					},
				},
				"point": 1,
				"rankInfo": map[string]interface{}{
					"point": 1,
				},
				"lastPostedTask": globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		//Create Transaction
		faTransIds := CreateFATransaction([]map[string]interface{}{
			{
				"userId":      "**********",
				"accountType": "M",
				"type":        "COD",
				"source": map[string]interface{}{
					"name":  "NAME TEST",
					"value": "taskId:",
				},
				"amount": 162000,
				"date":   globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		CreateReportTransaction([]map[string]interface{}{
			{
				"userId":        "**********",
				"transactionId": faTransIds[0],
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"isoCode":       local.ISO_CODE,
				"userId":        "**********",
				"transactionId": faTransIds[0],
				"reasonReport": []map[string]interface{}{
					{
						"vi": "xyz",
						"ko": "aksd",
					},
					{
						"ko": "1234",
						"en": "456",
					},
				},
				"otherReport": "test orther",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Create Report Transaction", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TRANSACTION_HAS_BEEN_REPORT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TRANSACTION_HAS_BEEN_REPORT.Message)
			})
		})
	})
}
