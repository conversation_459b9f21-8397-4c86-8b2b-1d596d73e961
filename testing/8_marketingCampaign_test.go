/*
 * @File: 8_marketingCampaign_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 02/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 16/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelMarketingCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/marketingCampaign"
	"go.mongodb.org/mongo-driver/bson"
)

func TestGetMarketingCampaign(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "AA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2.2", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"isTesting": true,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 0)
				})
			})
		})
	})

	t.Run("2.3", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"isOldUser": true,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.ParseDateFromString("2021-10-01T00:00:00Z", local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":       fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"serviceName":     globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":          globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":         local.ISO_CODE,
				"includeNewUsers": true,
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":     globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":          globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":         local.ISO_CODE,
				"includeNewUsers": true,
				"createdAt":       fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":     globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":          globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":         local.ISO_CODE,
				"includeNewUsers": true,
				"createdAt":       fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"isTesting":       true,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 0)
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Validate GetMarketingCampaignDetail")
		// GetMarketingCampaignDetail
		apiUrl := "/api/v3/api-asker-vn/get-marketing-campaign-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when CampaignId blank", func() {
				body := map[string]interface{}{
					"campaignId": "",
					"isoCode":    local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.Message)
			})
			// Convey("Check request when isoCode blank", func() {
			// 	body := map[string]interface{}{
			// 		"campaignId": "0834567890",
			// 		"isoCode":    "",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 400)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			// })
			// Convey("Check request when isoCode incorrect", func() {
			// 	body := map[string]interface{}{
			// 		"campaignId": "0834567890",
			// 		"isoCode":    "HA",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 500)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			// })
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaignDetail")
		// GetMarketingCampaignDetail
		apiUrl := "/api/v3/api-asker-vn/get-marketing-campaign-detail"
		ResetData()

		CreatePromotionCode([]map[string]interface{}{
			{
				"code":           "xxx",
				"paymentMethods": []string{"MOMO"},
			},
		})

		//Create MarketingCampaign
		createdAt := globalLib.ParseDateFromString("2021-10-01T00:00:00Z", local.TimeZone)
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"type":         globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"promotion": map[string]interface{}{
					"code": "xxx",
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"campaignId": ids[0],
				"isoCode":    local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["description"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM["description"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["description"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResult["paymentMethods"], ShouldResemble, []interface{}{"MOMO"})
				})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		log.Println("==================================== Validate GetSpecialCampaign")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			// Convey("Check request when isoCode blank", func() {
			// 	body := map[string]interface{}{
			// 		"userId":  "0834567890",
			// 		"isoCode": "",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 400)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			// })
			// Convey("Check request when isoCode incorrect", func() {
			// 	body := map[string]interface{}{
			// 		"userId":  "0834567890",
			// 		"isoCode": "HA",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 500)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			// })
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("6.2", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"description": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})

	t.Run("6.3", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign oldUser")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.ParseDateFromString("2021-10-01T00:00:00Z", local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
				"isOldUser": true,
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":     globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":          globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"includeNewUsers": true,
				"isoCode":         local.ISO_CODE,
				"createdAt":       fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"isTesting":       true,
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"description": map[string]interface{}{
					"vi": "description test vi 2",
					"en": "description test en 2",
					"ko": "description test ko 2",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":     globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":          globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":         local.ISO_CODE,
				"includeNewUsers": true,
				"createdAt":       fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()-1),
				"isTesting":       true,
			}, {
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"description": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":       globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":            globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":           local.ISO_CODE,
				"includeNewUsers":   true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"isSpecialCampaign": true,
			},
		})

		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{"tester": []string{"0834567890"}},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				UpdateSettings(map[string]interface{}{
					"$unset": bson.M{"tester": 1},
				})
				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when CampaignId blank", func() {
				body := map[string]interface{}{
					"campaignId":     "",
					"userId":         "0834567890",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":         "",
					"campaignId":     "123",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when campaignAction blank", func() {
				body := map[string]interface{}{
					"userId":         "0834567890",
					"campaignId":     "123",
					"campaignAction": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAMPAIGN_ACTION_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAMPAIGN_ACTION_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":         123,
					"campaignId":     "123",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when campaign not found", func() {
				body := map[string]interface{}{
					"userId":         "123",
					"campaignId":     "123",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_MARKETING_CAMPAIGN_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_MARKETING_CAMPAIGN_NOT_FOUND.Message)
			})
		})
	})

	t.Run("8", func(t *testing.T) {
		log.Println("==================================== TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		ResetData()
		//Create MarketingCampaign
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"campaignId":     ids[0],
				"campaignAction": "seen",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database TrackUserCampaign", func() {
				var result *modelMarketingCampaign.MarketingCampaign
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE],
					bson.M{
						"_id":     ids[0],
						"userIds": bson.M{"0834567890": globalConstant.MARKETING_CAMPAIGN_ACTION_SEEN},
					},
					bson.M{},
					&result,
				)
				So(result, ShouldNotBeNil)
			})
		})
	})

	t.Run("9", func(t *testing.T) {
		log.Println("==================================== TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		ResetData()
		//Create MarketingCampaign
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"campaignId":     ids[0],
				"campaignAction": "abc",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_UPDATE_FAILED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_UPDATE_FAILED.Message)
			})
		})
	})

	// New api without login
	// t.Run("10", func(t *testing.T) {
	// 	log.Println("==================================== Validate GetSpecialCampaign Without login")
	// 	// GetSpecialCampaign
	// 	apiUrl := "/api/v3/api-asker-vn/get-special-campaign-without-login"
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
	// 		Convey("Check request when isoCode blank", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": "",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
	// 		})
	// 		Convey("Check request when isoCode incorrect", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": "HA",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 500)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
	// 		})
	// 		Convey("Check request when params is invalid", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": 123,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
	// 		})
	// 	})
	// })

	t.Run("11.1", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign Without login")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign-without-login"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)

		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"includeNewUsers":   true,
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"description": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,23,59", now.Year(), now.Month(), now.Day()),
				"includeNewUsers":   true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})

	t.Run("13.1", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign-without-login"
		ResetData()
		//Create MarketingCampaign
		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+1),
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
				"isTesting": true,
			}, {
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
			}, {
				"img": "Img Test 4",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 1",
					"en": "Happy Vietnam's Women Day 1",
					"ko": "Happy Vietnam's Women Day 1",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 70,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 70,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 70,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":     globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":          globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":         local.ISO_CODE,
				"includeNewUsers": true,
				"createdAt":       fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				UpdateSettings(map[string]interface{}{
					"$unset": map[string]interface{}{"tester": 1},
				})

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM, ShouldBeNil)
				})
			})
		})
	})

	t.Run("14", func(t *testing.T) {
		log.Println("==================================== Validate GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "AA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("15", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()-3),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()-2),
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()-2),
				"isTesting": true,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 2)
					So(respResultM[0]["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM[0]["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM[0]["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResult[0]["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResultM[1]["content"]["vi"], ShouldEqual, "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM[1]["content"]["en"], ShouldEqual, "Save 50% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM[1]["content"]["ko"], ShouldEqual, "Save 50% when you booking on the app today (Maximum 30,000đ)")
					So(respResult[1]["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
				})
			})
		})
	})

	t.Run("15.1", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign isOldUser")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"isOldUser": true,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode":   local.ISO_CODE,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 0)
				})
			})
		})
	})
	t.Run("15.2", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign totalTaskDone > 0")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":         "0834567890",
				"name":          "Asker 01",
				"type":          globalConstant.USER_TYPE_ASKER,
				"totalTaskDone": 2,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"image": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
			}, {
				"image": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_CURRENT,
				},
				"isoCode":           local.ISO_CODE,
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), createdAt.Month(), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
			}, {
				"image": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH,
				},
				"isoCode":           local.ISO_CODE,
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), createdAt.Month(), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					for _, v := range respResult {
						So(v["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
						So(v["image"], ShouldBeIn, []string{"Img Test 2", "Img Test 3"})
					}
				})
			})
		})
	})

	t.Run("15.3", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign isHidingOnHomePage")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()-3),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
				"isHidingOnHomePage": false,
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt":          fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"startDate":          fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()-2),
				"isHidingOnHomePage": true,
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt":          fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"startDate":          fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()-2),
				"isTesting":          true,
				"isHidingOnHomePage": true,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 1)
					So(respResultM[0]["content"]["vi"], ShouldEqual, "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM[0]["content"]["en"], ShouldEqual, "Save 50% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM[0]["content"]["ko"], ShouldEqual, "Save 50% when you booking on the app today (Maximum 30,000đ)")
					So(respResult[0]["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
				})
			})
		})
	})

	t.Run("16", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create MarketingCampaign
		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 10% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 10% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 10% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+1),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567891"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 20% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 20% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 20% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
							"data": map[string]interface{}{
								"incentiveId": "incentiveId",
							},
							"categoryName": "categoryName",
							"title":        "title",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
			},
			// {
			// 	"img": "Img Test 1",
			// 	"title": map[string]interface{}{
			// 		"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
			// 		"en": "Happy Vietnam's Women Day",
			// 		"ko": "Happy Vietnam's Women Day",
			// 	},
			// 	"content": map[string]interface{}{
			// 		"vi": "Giảm 30% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
			// 		"en": "Save 30% when you booking on the app today (Maximum 30,000đ)",
			// 		"ko": "Save 30% when you booking on the app today (Maximum 30,000đ)",
			// 	},
			// 	"action": "now",
			// 	"actionText": map[string]interface{}{
			// 		"vi": " Đặt ngay",
			// 		"en": "Book Now",
			// 		"ko": "Book Now",
			// 	},
			// 	"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			// 	"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
			// 	"applyForUser": map[string]interface{}{
			// 		"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
			// 	},
			// 	"isoCode": local.ISO_CODE,
			// 	"userIds": map[string]interface{}{
			// 		"0834567890": 0,
			// 	},
			// 	"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
			// 	"isTesting": true,
			// 	"primaryButtonConfig": map[string]interface{}{
			// 		"action": map[string]interface{}{
			// 			"isRequireLogin": true,
			// 		},
			// 	},
			// 	"secondaryButtonConfig": map[string]interface{}{
			// 		"action": map[string]interface{}{
			// 			"isRequireLogin": true,
			// 		},
			// 	},
			// },
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"data": map[string]interface{}{
								"incentiveId": "incentiveId",
							},
							"categoryName": "categoryName",
							"title":        "title",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"endDate": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 40,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 40,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 40,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
							"data": map[string]interface{}{
								"incentiveId": "incentiveId",
							},
							"categoryName": "categoryName",
							"title":        "title",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
			},
		})

		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{"tester": []string{"0834567890"}},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				UpdateSettings(map[string]interface{}{
					"$unset": map[string]interface{}{"tester": 1},
				})

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 2)
					for i := range respResultM {
						So(respResultM[i]["content"]["vi"], ShouldBeIn, []string{"Giảm 20% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)", "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 40,000đ)"})
						So(respResultM[i]["content"]["en"], ShouldBeIn, []string{"Save 20% when you booking on the app today (Maximum 30,000đ)", "Save 50% when you booking on the app today (Maximum 40,000đ)"})
						So(respResultM[i]["content"]["ko"], ShouldBeIn, []string{"Save 20% when you booking on the app today (Maximum 30,000đ)", "Save 50% when you booking on the app today (Maximum 40,000đ)"})
						So(respResultM[0]["primaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
							"isRequireLogin": true,
							"params": map[string]interface{}{
								"screen": "TabUpcomming",
								"data": map[string]interface{}{
									"incentiveId": "incentiveId",
								},
								"categoryName": "categoryName",
								"title":        "title",
							},
						})
						So(respResultM[0]["secondaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
							"isRequireLogin": true,
							"params": map[string]interface{}{
								"serviceId": "serviceId",
							},
						})
						So(respResult[i]["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					}
				})
			})
		})
	})

	t.Run("17", func(t *testing.T) {
		log.Println("==================================== Validate GetMarketingCampaignDetail")
		// GetMarketingCampaignDetail
		apiUrl := "/api/v3/api-asker-vn/get-marketing-campaign-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when CampaignId blank", func() {
				body := map[string]interface{}{
					"campaignId": "",
					"isoCode":    local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.Message)
			})
			// Convey("Check request when isoCode blank", func() {
			// 	body := map[string]interface{}{
			// 		"campaignId": "0834567890",
			// 		"isoCode":    "",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 400)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			// })
			// Convey("Check request when isoCode incorrect", func() {
			// 	body := map[string]interface{}{
			// 		"campaignId": "0834567890",
			// 		"isoCode":    "HA",
			// 	}
			// 	reqBody, _ := json.Marshal(body)
			// 	req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			// 	resp := httptest.NewRecorder()
			// 	service.NewRouter().ServeHTTP(resp, req)
			// 	So(resp.Code, ShouldEqual, 500)

			// 	var respResult map[string]map[string]interface{}
			// 	bytes, _ := io.ReadAll(resp.Body)
			// 	json.Unmarshal(bytes, &respResult)
			// 	So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			// 	So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			// })
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})

	t.Run("18", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaignDetail")
		// GetMarketingCampaignDetail
		apiUrl := "/api/v3/api-asker-vn/get-marketing-campaign-detail"
		ResetData()
		//Create MarketingCampaign
		createdAt := globalLib.ParseDateFromString("2021-10-01T00:00:00Z", local.TimeZone)
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
							"data": map[string]interface{}{
								"incentiveId": "incentiveId",
							},
							"categoryName": "categoryName",
							"title":        "title",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"campaignId": ids[0],
				"isoCode":    local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["primaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
							"data": map[string]interface{}{
								"incentiveId": "incentiveId",
							},
							"categoryName": "categoryName",
							"title":        "title",
						},
					})
					So(respResultM["secondaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					})
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
				})
			})
		})
	})

	t.Run("19", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResult["action"], ShouldEqual, "now")
					So(respResult["_id"], ShouldNotBeNil)
				})
			})
		})
	})

	t.Run("19.0", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
			},
		})

		CreatePromotionCode([]map[string]interface{}{
			{
				"code":           "PROMOTION_CODE_1",
				"discount":       10,
				"paymentMethods": []interface{}{"MOMO", "VNPAY"},
			},
		})

		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"promotion": map[string]interface{}{
					"code": "PROMOTION_CODE_1",
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"type":              globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResult["paymentMethods"], ShouldResemble, []interface{}{"MOMO", "VNPAY"})
					So(respResult["action"], ShouldEqual, "now")
					So(respResult["_id"], ShouldNotBeNil)
				})
			})
		})
	})

	t.Run("19.1", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign isOldUser")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
				"isOldUser": true,
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode":           local.ISO_CODE,
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})

	t.Run("19.2", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign totalTaskDone")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":         "0834567890",
				"name":          "Asker 01",
				"type":          globalConstant.USER_TYPE_ASKER,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
				"totalTaskDone": 2,
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode":           local.ISO_CODE,
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})
	t.Run("20", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{ // tester: isoCode TH -> Không lấy
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH,
				},
				"isoCode": globalConstant.ISO_CODE_TH,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
			}, { // tester: Đã hết hạn -> Không lấy
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 60% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 60% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 60% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()-2),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"endDate": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()-1),
			}, { // Lấy
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH,
				},
				"isSpecialCampaign": true,
				"isoCode":           local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()-3),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi 2",
					"en": "description test en 2",
					"ko": "description test ko 2",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 1,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()-1),
				"isTesting": true,
			}, {
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 1,
				},
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"isSpecialCampaign": true,
			},
		})

		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{"tester": []string{"0834567890"}},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				UpdateSettings(map[string]interface{}{
					"$unset": bson.M{"tester": 1},
				})
				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["primaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					})
					So(respResultM["secondaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					})
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResult["action"], ShouldEqual, "now")
					So(respResult["_id"], ShouldNotBeNil)
				})
			})
		})
	})

	t.Run("21", func(t *testing.T) {
		log.Println("==================================== Validate TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when CampaignId blank", func() {
				body := map[string]interface{}{
					"campaignId":     "",
					"userId":         "0834567890",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAMPAIGN_ID_REQUIRED.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":         "",
					"campaignId":     "123",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when campaignAction blank", func() {
				body := map[string]interface{}{
					"userId":         "0834567890",
					"campaignId":     "123",
					"campaignAction": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAMPAIGN_ACTION_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAMPAIGN_ACTION_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId":         123,
					"campaignId":     "123",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when campaign not found", func() {
				body := map[string]interface{}{
					"userId":         "123",
					"campaignId":     "123",
					"campaignAction": "seen",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_MARKETING_CAMPAIGN_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_MARKETING_CAMPAIGN_NOT_FOUND.Message)
			})
		})
	})

	t.Run("22", func(t *testing.T) {
		log.Println("==================================== TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		ResetData()
		//Create MarketingCampaign
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"campaignId":     ids[0],
				"campaignAction": "seen",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database TrackUserCampaign", func() {
				var result *modelMarketingCampaign.MarketingCampaign
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE],
					bson.M{
						"_id":     ids[0],
						"userIds": bson.M{"0834567890": globalConstant.MARKETING_CAMPAIGN_ACTION_SEEN},
					},
					bson.M{},
					&result,
				)
				So(result, ShouldNotBeNil)
			})
		})
	})

	t.Run("23", func(t *testing.T) {
		log.Println("==================================== TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		ResetData()
		//Create MarketingCampaign
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"campaignId":     ids[0],
				"campaignAction": "abc",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_UPDATE_FAILED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_UPDATE_FAILED.Message)
			})
		})
	})

	// TODO: Remove when update query action:40 in api /get-marketing-campaign
	t.Run("23.1", func(t *testing.T) {
		log.Println("==================================== TrackUserCampaign")
		// TrackUserCampaign
		apiUrl := "/api/v3/api-asker-vn/track-user-campaign"
		ResetData()
		//Create MarketingCampaign
		ids := CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"campaignId":     ids[0],
				"campaignAction": "book",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database TrackUserCampaign", func() {
				var result *modelMarketingCampaign.MarketingCampaign
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE],
					bson.M{
						"_id":     ids[0],
						"userIds": bson.M{"0834567890": 32},
					},
					bson.M{},
					&result,
				)
				So(result, ShouldNotBeNil)
			})
		})
	})

	// New api without login
	// t.Run("24", func(t *testing.T) {
	// 	log.Println("==================================== Validate GetSpecialCampaign Without login")
	// 	// GetSpecialCampaign
	// 	apiUrl := "/api/v3/api-asker-vn/get-special-campaign-without-login"
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
	// 		Convey("Check request when isoCode blank", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": "",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
	// 		})
	// 		Convey("Check request when isoCode incorrect", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": "HA",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 500)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
	// 		})
	// 		Convey("Check request when params is invalid", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": 123,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
	// 		})
	// 	})
	// })

	// New api without login
	t.Run("25", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign Without login")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign-without-login"
		ResetData()
		//Create User

		now := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)

		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode":           local.ISO_CODE,
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,23,59", now.Year(), now.Month(), now.Day()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)")
					So(respResultM["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResultM["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 30,000đ)")
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResult["action"], ShouldEqual, "now")
					So(respResult["_id"], ShouldNotBeNil)
				})
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign Without login")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign-without-login"
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)

		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"isTesting": true,
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi 2",
					"en": "description test en 2",
					"ko": "description test ko 2",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()-1),
				"isTesting":         true,
				"isSpecialCampaign": true,
				"includeNewUsers":   true,
			}, {
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"content": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"isSpecialCampaign": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["content"]["vi"], ShouldEqual, "description test vi")
					So(respResultM["content"]["en"], ShouldEqual, "description test en")
					So(respResultM["content"]["ko"], ShouldEqual, "description test ko")
					So(respResultM["primaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					})
					So(respResultM["secondaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					})
					So(respResult["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
					So(respResult["action"], ShouldEqual, "now")
					So(respResult["_id"], ShouldNotBeNil)
				})
			})
		})
	})

	t.Run("27", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign-without-login"
		ResetData()
		//Create MarketingCampaign
		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+1),
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
			}, {
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
			}, {
				"img": "Img Test 4",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 1",
					"en": "Happy Vietnam's Women Day 1",
					"ko": "Happy Vietnam's Women Day 1",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 70,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 70,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 70,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				UpdateSettings(map[string]interface{}{
					"$unset": map[string]interface{}{"tester": 1},
				})

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 1)
					So(respResultM[0]["content"]["vi"], ShouldEqual, "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 70,000đ)")
					So(respResultM[0]["content"]["en"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 70,000đ)")
					So(respResultM[0]["content"]["ko"], ShouldEqual, "Save 40% when you booking on the app today (Maximum 70,000đ)")
					So(respResultM[0]["primaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					})
					So(respResultM[0]["secondaryButtonConfig"]["action"], ShouldResemble, map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					})
					So(respResult[0]["status"], ShouldEqual, globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE)
				})
			})
		})
	})

	t.Run("27.1", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign-without-login"
		ResetData()
		//Create MarketingCampaign
		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":    fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+1),
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
				"isHidingOnHomePage": true,
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
				"isTesting": true,
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
					},
				},
				"isHidingOnHomePage": true,
			}, {
				"img": "Img Test 3",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt":          fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()+2),
				"isHidingOnHomePage": true,
			}, {
				"img": "Img Test 4",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 1",
					"en": "Happy Vietnam's Women Day 1",
					"ko": "Happy Vietnam's Women Day 1",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 70,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 70,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 70,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"isoCode":     local.ISO_CODE,
				"applyForUser": map[string]interface{}{
					"target": globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), int(now.Month()), now.Day(), now.Hour(), now.Minute()-2),
				"primaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"screen": "TabUpcomming",
						},
					},
				},
				"secondaryButtonConfig": map[string]interface{}{
					"action": map[string]interface{}{
						"isRequireLogin": true,
						"params": map[string]interface{}{
							"serviceId": "serviceId",
						},
					},
				},
				"isHidingOnHomePage": true,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				UpdateSettings(map[string]interface{}{
					"$unset": map[string]interface{}{"tester": 1},
				})

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 0)
				})
			})
		})
	})

	t.Run("28", func(t *testing.T) {
		log.Println("==================================== GetMarketingCampaign")
		// GetMarketingCampaign
		apiGetMarketingCampaign := "/api/v3/api-asker-vn/get-marketing-campaign"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create MarketingCampaign
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+1),
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 32,
				},
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"applyForUser": map[string]interface{}{
					"userIds": []string{"0834567890"},
				},
				"isoCode": local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
			}, {
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"content": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"startDate": fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()+2),
				"isTesting": true,
			},
		})

		//Get Service
		repoService := GetService(bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
		result := map[string]interface{}{}
		b, _ := json.Marshal(repoService)
		json.Unmarshal(b, &result)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetMarketingCampaign), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetMarketingCampaign, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultM), ShouldEqual, 0)
				})
			})
		})
	})

	t.Run("29", func(t *testing.T) {
		log.Println("==================================== GetSpecialCampaign")
		// GetSpecialCampaign
		apiUrl := "/api/v3/api-asker-vn/get-special-campaign"
		ResetData()
		//Create User
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		now := globalLib.ParseDateFromString("2021-10-01T00:00:00Z", local.TimeZone)
		last2Days := now.AddDate(0, 0, -2)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", last2Days.Year(), last2Days.Month(), last2Days.Day(), last2Days.Hour(), last2Days.Minute()),
			},
		})
		//Create MarketingCampaign
		CreateMarketingCampaign([]map[string]interface{}{
			{
				"img": "Img Test 1",
				"title": map[string]interface{}{
					"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
					"en": "Happy Vietnam's Women Day",
					"ko": "Happy Vietnam's Women Day",
				},
				"description": map[string]interface{}{
					"vi": "Giảm 40% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
					"en": "Save 40% when you booking on the app today (Maximum 30,000đ)",
					"ko": "Save 40% when you booking on the app today (Maximum 30,000đ)",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
				"startDate":         fmt.Sprintf("%d,%d,%d,%d,%d", startDate.Year(), startDate.Month(), startDate.Day(), now.Hour(), startDate.Minute()),
			}, {
				"img": "Img Test 2",
				"title": map[string]interface{}{
					"vi": "title test vi",
					"en": "title test en",
					"ko": "title test ko",
				},
				"description": map[string]interface{}{
					"vi": "description test vi",
					"en": "description test en",
					"ko": "description test ko",
				},
				"action": "now",
				"actionText": map[string]interface{}{
					"vi": " Đặt ngay",
					"en": "Book Now",
					"ko": "Book Now",
				},
				"serviceName":  globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":       globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
				"appliedUsers": []string{"0834567890"},
				"isoCode":      local.ISO_CODE,
				"userIds": map[string]interface{}{
					"0834567890": 0,
				},
				"isSpecialCampaign": true,
				"createdAt":         fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()),
				"startDate":         fmt.Sprintf("%d,%d,%d,%d,%d", startDate.Year(), startDate.Month(), startDate.Day(), now.Hour(), startDate.Minute()),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get MarketingCampaign", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeNil)
				})
			})
		})
	})
}
