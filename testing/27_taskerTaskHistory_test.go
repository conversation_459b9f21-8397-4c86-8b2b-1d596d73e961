/*
* @File: 27_taskerTaskHistory_test.go
* @Description: Handler function, case test
* @CreatedAt: 25/10/2020
* @Author: ngoctb
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

func TestT_History(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/done-task-history-by-tasker"
		ResetData()

		log.Println("==================================== Validate DoneTaskHistoryByTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when time not allow", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"month":  "2",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TIME_NOT_ALLOW.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TIME_NOT_ALLOW.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/done-task-history-by-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		CreateTaskerTaskHistory([]map[string]interface{}{
			{
				"_id": "0834567890",
				"doneTask": []map[string]interface{}{
					{
						"address":     "500 Thanon Tanao, Wat Bowon Niwet, Phra Nakhon, Vùng đô thị Bangkok, Thái Lan",
						"phonenumber": "0834567890",
						"serviceName": map[string]interface{}{
							"vi": "Dọn dẹp nhà",
							"en": "Cleaning",
							"ko": "가사 도우미",
							"th": "ทำความสะอาดบ้าน",
						},
						"beginAt":   time.Date(2020, 11, 5, 7, 0, 0, 0, local.TimeZone),
						"endAt":     time.Date(2020, 11, 5, 9, 0, 0, 0, local.TimeZone),
						"cost":      150000.0,
						"askerName": "Khot Khac kun",
					},
				},
			},
		})
		log.Println("==================================== DoneTaskHistoryByTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"month":  "2020-11-24T00:00:00Z",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)

			var respResult map[string]interface{}
			var respResultSM map[string][]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			json.Unmarshal(bytes, &respResultSM)
			So(respResultSM["data"][0]["address"], ShouldEqual, "500 Thanon Tanao, Wat Bowon Niwet, Phra Nakhon, Vùng đô thị Bangkok, Thái Lan")
			So(respResultSM["data"][0]["askerName"], ShouldEqual, "Khot Khac kun")
			So(respResult["total"], ShouldEqual, 150000)
			So(respResult["totalByMonth"], ShouldEqual, 150000)
		})
	})

	t.Run("3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/done-task-history-by-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		CreateTaskerTaskHistory([]map[string]interface{}{
			{
				"_id": "0834567890",
				"doneTask": []map[string]interface{}{
					{
						"address":     "500 Thanon Tanao, Wat Bowon Niwet, Phra Nakhon, Vùng đô thị Bangkok, Thái Lan",
						"phonenumber": "0834567810",
						"companyId":   "0834567810",
						"serviceName": map[string]interface{}{
							"vi": "Dọn dẹp nhà",
							"en": "Cleaning",
							"ko": "가사 도우미",
							"th": "ทำความสะอาดบ้าน",
						},
						"beginAt":   time.Date(2020, 11, 5, 7, 0, 0, 0, local.TimeZone),
						"endAt":     time.Date(2020, 11, 5, 10, 0, 0, 0, local.TimeZone),
						"cost":      150000.0,
						"askerName": "Khot Khac kun",
					},
				},
			},
		})
		log.Println("==================================== DoneTaskHistoryByTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":    "0834567810",
				"isCompany": true,
				"month":     "2020-11-24T00:00:00Z",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)

			var respResult map[string]interface{}
			var respResultSM map[string][]map[string]interface{}
			var respResultSMSM map[string][]map[string][]map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			json.Unmarshal(bytes, &respResultSM)
			json.Unmarshal(bytes, &respResultSMSM)
			So(respResultSM["data"][0]["name"], ShouldEqual, "Asker 01")
			So(respResultSM["data"][0]["totalCost"], ShouldEqual, 150000)
			So(respResultSMSM["data"][0]["data"][0]["address"], ShouldEqual, "500 Thanon Tanao, Wat Bowon Niwet, Phra Nakhon, Vùng đô thị Bangkok, Thái Lan")
			So(respResultSMSM["data"][0]["data"][0]["askerName"], ShouldEqual, "Khot Khac kun")
			So(respResultSMSM["data"][0]["data"][0]["companyId"], ShouldEqual, "0834567810")
			So(respResultSMSM["data"][0]["data"][0]["cost"], ShouldEqual, 150000)
			So(respResult["total"], ShouldEqual, 150000)
			So(respResult["totalByMonth"], ShouldEqual, 150000)
		})
	})
}
