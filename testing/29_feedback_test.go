/*
* @File: 29_feedback_test.go
* @Description: Handler function, case test
* @CreatedAt: 16/11/2020
* @Author: ngoctb
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelFeedback "gitlab.com/btaskee/go-services-model-v2/grpcmodel/feedback"
	"go.mongodb.org/mongo-driver/bson"
)

func TestFeedback(t *testing.T) {
	apiURL := subPath + "/create-feedback"
	t.Run("1", func(t *testing.T) {
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})

			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if phone empty", func() {
				body := map[string]interface{}{
					"userId": "123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if name empty", func() {
				body := map[string]interface{}{
					"userId":      "123123",
					"phoneNumber": "123123",
					"name":        "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
			})
			Convey("Check the response if feedback empty", func() {
				body := map[string]interface{}{
					"userId":      "123123",
					"phoneNumber": "123123",
					"name":        "123132",
					"feedback":    "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_FEEDBACK_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			ResetData()

			//Create User
			CreateUser([]map[string]interface{}{
				{
					"phone":   "0777777771",
					"name":    "Dev Test 01",
					"type":    globalConstant.USER_TYPE_ASKER,
					"isoCode": local.ISO_CODE,
					"emails": []map[string]interface{}{
						{"address": "<EMAIL>"},
					},
					"locations": []map[string]interface{}{{"city": "HCMC"}},
				},
			})
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId":      "0777777771",
					"phoneNumber": "0777777771",
					"name":        "Dev Test 01",
					"feedback":    "Feedback from dev Test feedback VN",
					"services": []map[string]interface{}{
						{
							"name": "Vệ sinh máy lạnh",
							"text": map[string]interface{}{
								"vi": "Vệ sinh máy lạnh",
								"en": "Air-conditioner Service",
								"ko": "에어컨 청소",
								"th": "ทำความสะอาดเครื่องปรับอากาศ",
							},
						},
						{
							"name": "Giặt ủi",
							"text": map[string]interface{}{
								"vi": "Giặt ủi",
								"en": globalConstant.SERVICE_NAME_LAUNDRY,
								"ko": "에어컨 청소",
								"th": "ทำความสะอาดเครื่องปรับอากาศ",
							},
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				Convey("Check the database", func() {
					var feedback *modelFeedback.Feedback
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FEEDBACK[local.ISO_CODE], bson.M{"userId": "0777777771", "phoneNumber": "0777777771"}, bson.M{}, &feedback)
					So(feedback, ShouldNotBeNil)
					So(feedback.Feedback, ShouldEqual, "Feedback from dev Test feedback VN")
					So(len(feedback.Services), ShouldEqual, 2)
					So(feedback.IsoCode, ShouldEqual, local.ISO_CODE)
				})
			})
		})
	})
}
