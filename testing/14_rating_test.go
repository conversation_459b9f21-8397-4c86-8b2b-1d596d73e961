/*
 * @File: 14_rating_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 30/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 21/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	"github.com/golang-jwt/jwt/v5"
	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestRating(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetTaskerReview")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v3/api-asker-vn/get-tasker-reviews"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskerReview), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when param is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Validate GetTaskerReview")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v3/api-asker-vn/get-tasker-reviews"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskerReview), t, func() {
			Convey("Check request when user not found", func() {
				body := map[string]interface{}{
					"userId": "123123123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetTaskerReview")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v3/api-asker-vn/get-tasker-reviews"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"taskDone":        30,
				"avgRating":       4.8,
				"covid19Vaccine":  []bool{true, false},
				"isPremiumTasker": true,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 1",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 2",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 3",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 4",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        2,
				"review":      "Làm tốt. Lịch sự 6",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"review":      "Làm tốt. Lịch sự 6",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"review":      "",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetTaskerReview), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Reviews", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["name"], ShouldEqual, "Tasker 01")
					So(respResult["numberOfReview"], ShouldEqual, 9)
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["isPremiumTasker"], ShouldBeTrue)
					So(len(respResult["reviews"].([]interface{})), ShouldEqual, 5)
					So(len(respResult["badges"].([]interface{})), ShouldEqual, 1)
					So(respResult["numberOfTaskDone"], ShouldEqual, 30)
					So(respResult["avgRating"], ShouldEqual, 4.8)
					badgesData, _ := json.Marshal(respResult["badges"])
					badges := []map[string]interface{}{}
					json.Unmarshal(badgesData, &badges)
					So(badges[0]["name"], ShouldEqual, "ON_TIME")
					So(badges[0]["numOfBadges"], ShouldEqual, 22)
					for _, v := range respResult["reviews"].([]interface{}) {
						So(v.(string), ShouldNotEqual, "")
					}
				})
			})
		})
	})

	t.Run("3.1", func(t *testing.T) {
		log.Println("==================================== GetTaskerReview tasker <= 20 task")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v3/api-asker-vn/get-tasker-reviews"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"taskDone":            15,
				"isVaccinatedCovid19": true,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{
					"0834567890",
				},
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 1",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetTaskerReview), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"askerId": "0834567810",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Reviews", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["name"], ShouldEqual, "Tasker 01")
					So(respResult["numberOfReview"], ShouldEqual, 3)
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["numberOfTaskDone"], ShouldEqual, 15)
					So(respResult["avgRating"], ShouldEqual, 5)
					So(respResult["isFavouriteTasker"], ShouldBeTrue)
				})
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		apiURL := subPath + "/get-rating-by-tasker-id"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if param is not valid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		apiURL := subPath + "/get-rating-by-tasker-id"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Rating
		data := []map[string]interface{}{
			{
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        4,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
				"createdAt":   "2020,10,22,11,11",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
				"createdAt":   "2020,10,22,14,11",
			},
		}
		CreateRating(data)
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": user.XId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				b, _ := io.ReadAll(res.Body)
				var rating []*modelRating.Rating
				json.Unmarshal(b, &rating)
				So(rating[0].Review, ShouldEqual, data[1]["review"])
				So(rating[0].Rate, ShouldEqual, data[1]["rate"])
				So(rating[1].Review, ShouldEqual, data[0]["review"])
				So(rating[1].Rate, ShouldEqual, data[0]["rate"])
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		log.Println("==================================== Validate GetTaskerReview")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v5/api-asker-vn/get-tasker-reviews"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskerReview), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"taskerId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.Message)
			})
			Convey("Check request when param is invalid", func() {
				body := map[string]interface{}{
					"taskerId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate GetTaskerReview")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v5/api-asker-vn/get-tasker-reviews"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetTaskerReview), t, func() {
			Convey("Check request when user not found", func() {
				body := map[string]interface{}{
					"taskerId": "123123123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASKER_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("8", func(t *testing.T) {
		log.Println("==================================== GetTaskerReview")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v5/api-asker-vn/get-tasker-reviews"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"taskDone":        30,
				"avgRating":       4.8,
				"covid19Vaccine":  []bool{true, false},
				"isPremiumTasker": true,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 1",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 2",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 3",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 4",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        2,
				"review":      "Làm tốt. Lịch sự 6",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"review":      "Làm tốt. Lịch sự 6",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"review":      "",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetTaskerReview), t, func() {
			body := map[string]interface{}{
				"taskerId": "0834567890",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Reviews", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["name"], ShouldEqual, "Tasker 01")
					So(respResult["numberOfReview"], ShouldEqual, 9)
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["isPremiumTasker"], ShouldBeTrue)
					So(len(respResult["reviews"].([]interface{})), ShouldEqual, 5)
					So(len(respResult["badges"].([]interface{})), ShouldEqual, 1)
					So(respResult["numberOfTaskDone"], ShouldEqual, 30)
					So(respResult["avgRating"], ShouldEqual, 4.8)
					badgesData, _ := json.Marshal(respResult["badges"])
					badges := []map[string]interface{}{}
					json.Unmarshal(badgesData, &badges)
					So(badges[0]["name"], ShouldEqual, "ON_TIME")
					So(badges[0]["numOfBadges"], ShouldEqual, 22)
					for _, v := range respResult["reviews"].([]interface{}) {
						So(v.(string), ShouldNotEqual, "")
					}
				})
			})
		})
	})

	t.Run("9", func(t *testing.T) {
		log.Println("==================================== GetTaskerReview tasker <= 20 task")
		// GetTaskerReview
		apiGetTaskerReview := "/api/v5/api-asker-vn/get-tasker-reviews"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"taskDone":            15,
				"isVaccinatedCovid19": true,
			}, {
				"phone": "0834567810",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{
					"0834567890",
				},
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
			}, {
				"taskerPhone": "0834567890",
				"askerPhone":  "0834567810",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 1",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetTaskerReview), t, func() {
			body := map[string]interface{}{
				"taskerId": "0834567890",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetTaskerReview, bytes.NewBuffer(reqBody))
			token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
				"uid": "0834567810",
			})
			tokenString, _ := token.SignedString([]byte("secret"))
			req.Header.Set("Authorization", "Bearer "+tokenString)
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Reviews", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["name"], ShouldEqual, "Tasker 01")
					So(respResult["numberOfReview"], ShouldEqual, 3)
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["numberOfTaskDone"], ShouldEqual, 15)
					So(respResult["avgRating"], ShouldEqual, 5)
					So(respResult["isFavouriteTasker"], ShouldBeTrue)
				})
			})
		})
	})
}
