/*
* @File: 26_workingPlaces_test.go
* @Description: Handler function, case test
* @CreatedAt: 10/11/2020
* @Author: ngoctb
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"go.mongodb.org/mongo-driver/bson"
)

func TestWorkingPlaces(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiGetSignUpWorkingPlaces := "/api/v3/api-asker-vn/get-sign-up-working-places"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetSignUpWorkingPlaces), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSignUpWorkingPlaces, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				resData, _ := io.ReadAll(resp.Body)
				resMap := map[string]map[string]interface{}{}
				json.Unmarshal(resData, &resMap)
				So(resp.Code, ShouldEqual, 400)
				So(resMap["error"]["code"], ShouldEqual, "COUNTRY_CODE_REQUIRED")
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		apiGetSignUpWorkingPlaces := "/api/v3/api-asker-vn/get-sign-up-working-places"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetSignUpWorkingPlaces), t, func() {
			Convey("Check request when params not valid", func() {
				body := map[string]interface{}{
					"countryCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSignUpWorkingPlaces, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				resData, _ := io.ReadAll(resp.Body)
				resMap := map[string]map[string]interface{}{}
				json.Unmarshal(resData, &resMap)
				So(resp.Code, ShouldEqual, 400)
				So(resMap["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetWorkingPlaces")
		apiGetSignUpWorkingPlaces := "/api/v3/api-asker-vn/get-sign-up-working-places"
		UpdateWorkingPlaces(bson.M{"countryCode": local.ISO_CODE}, bson.M{"$set": bson.M{"countryCode": "+84"}})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetSignUpWorkingPlaces), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"countryCode": "+84",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSignUpWorkingPlaces, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult[0]["countryCode"], ShouldEqual, "+84")
				So(respResult[0]["countryName"], ShouldEqual, "Việt Nam")
				So(respResult[0]["cities"], ShouldNotBeEmpty)
			})
		})
		UpdateWorkingPlaces(bson.M{"countryCode": "+84"}, bson.M{"$set": bson.M{"countryCode": local.ISO_CODE}})
	})
}
