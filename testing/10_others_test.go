/*
 * @File: 10_others_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 13/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 17/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNextExpansion "gitlab.com/btaskee/go-services-model-v2/grpcmodel/nextExpansion"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestOthers(t *testing.T) {
	t.Run("3", func(t *testing.T) {
		log.Println("======================= validate InitialDataV2")
		apiURL := subPath + "/initial-data-v2"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if isoCode is empty", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("======================= InitialDataV2")
		apiURL := subPath + "/initial-data-v2"
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"referralValue":              50000,
				"minFAccountTasker":          40000,
				"limitNumberAcceptTaskInDay": 4,
				"taskerNotCommingFee": map[string]interface{}{
					"earlyCancelFee": 20000,
					"lateCancelFee":  50,
					"notCommingFee":  100,
				},
				"limitNumberTaskerAcceptTask": 10,
				"taskerReferralValue": map[string]interface{}{
					"inviter": 100000,
					"invitee": 50000,
				},
				"referralSetting": map[string]interface{}{
					"type": "voucher",
					"voucher": map[string]interface{}{
						"image": map[string]interface{}{
							"vi": "http://...",
						},
						"value": 30000,
					},
				},
			},
		})

		UpdateTaskerSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"trainingInput": map[string]interface{}{
					"link":         "https://raw.githubusercontent.com/bTaskee/TrainingV3/master/chapter_1/quizz_8.json",
					"minRateScore": 80.0,
					"version":      2.0,
					"numberTest":   2.0,
				},
				"trainingTasker": map[string]interface{}{
					"link":         "https://raw.githubusercontent.com/bTaskee/TrainingV3/master/courses.json",
					"minRateScore": 80.0,
					"version":      2.0,
					"numberTest":   2.0,
				},
				"handbook": map[string]interface{}{
					"vi": "https://docs.google.com/uc?export=download&id=1j4cA339-T15eaEYQHt3evsmK38fccdDB",
					"ko": "https://docs.google.com/uc?export=download&id=1j4cA339-T15eaEYQHt3evsmK38fccdDB",
					"en": "https://docs.google.com/uc?export=download&id=1j4cA339-T15eaEYQHt3evsmK38fccdDB",
				},
				"kitsAndChemicals": map[string]interface{}{
					"kits": []map[string]interface{}{
						{
							"name": map[string]interface{}{
								"vi": "Balo",
								"en": "Backpack",
								"ko": "Backpack",
							},
							"amount":        1,
							"price":         350000,
							"imageUrl":      "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LaH4qhSQ9ZhaJE4jB",
							"buyAddressUrl": "https://docs.google.com/forms/d/e/1FAIpQLSdCtA7ggCpozA9oYqIA95KLQh3hUhRkEa_RPFpB-v0mEHfysQ/viewform",
							"note": map[string]interface{}{
								"vi": "Đăng ký trên trang FB CĐ hoặc mua trực tiếp tại VP gần nhất.",
								"en": "Register in FB community group or contact the nearest office.",
								"ko": "Register in FB community group or contact the nearest office.",
							},
						},
						{
							"name": map[string]interface{}{
								"vi": "Khăn đa năng",
								"en": "Multi-purpose cloth",
								"ko": "Multi-purpose cloth",
							},
						},
					},
				},
			},
		})
		UpdateService(bson.M{}, bson.M{"$set": bson.M{"onlyShowAsker": false}})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				So(res.Code, ShouldEqual, 200)
				json.Unmarshal(b, &m)
				So(m["settingSystem"], ShouldNotBeNil)
				So(m["taskerSettings"], ShouldNotBeNil)
				So(m["settingSystem"]["limitNumberTaskerAcceptTask"], ShouldNotBeNil)
				So(m["settingSystem"]["taskerNotCommingFee"], ShouldNotBeNil)
				So(m["settingSystem"]["referralValue"], ShouldEqual, 30000)
				So(m["settingSystem"]["minFAccountTasker"], ShouldNotBeNil)
				So(m["settingSystem"]["limitNumberAcceptTaskInDay"], ShouldNotBeNil)
				So(m["settingSystem"]["taskerReferralValue"], ShouldNotBeNil)
				So(m["taskerSettings"]["trainingInput"], ShouldNotBeNil)
				So(m["taskerSettings"]["trainingTasker"], ShouldNotBeNil)
				So(m["taskerSettings"]["handbook"], ShouldNotBeNil)
				So(m["taskerSettings"]["kitsAndChemicals"], ShouldNotBeNil)
			})
		})

		UpdateSettings(bson.M{
			"$unset": bson.M{
				"referralValue":               1,
				"minFAccountTasker":           1,
				"limitNumberAcceptTaskInDay":  1,
				"taskerNotCommingFee":         1,
				"limitNumberTaskerAcceptTask": 1,
				"taskerReferralValue":         1,
			},
		})
		UpdateTaskerSettings(bson.M{
			"$unset": bson.M{
				"trainingInput":    1,
				"trainingTasker":   1,
				"handbook":         1,
				"kitsAndChemicals": 1,
			},
		})
		UpdateService(bson.M{}, bson.M{"$unset": bson.M{"onlyShowAsker": 1}})
	})

	t.Run("4.1", func(t *testing.T) {
		log.Println("======================= InitialDataV2")
		apiURL := subPath + "/initial-data-v2"

		UpdateTaskerSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"trainingInput": map[string]interface{}{
					"link":         "https://raw.githubusercontent.com/bTaskee/TrainingV3/master/chapter_1/quizz_8.json",
					"minRateScore": 80.0,
					"version":      2.0,
					"numberTest":   2.0,
				},
				"trainingTasker": map[string]interface{}{
					"link":         "https://raw.githubusercontent.com/bTaskee/TrainingV3/master/courses.json",
					"minRateScore": 80.0,
					"version":      2.0,
					"numberTest":   2.0,
				},
				"handbook": map[string]interface{}{
					"vi": "https://docs.google.com/uc?export=download&id=1j4cA339-T15eaEYQHt3evsmK38fccdDB",
					"ko": "https://docs.google.com/uc?export=download&id=1j4cA339-T15eaEYQHt3evsmK38fccdDB",
					"en": "https://docs.google.com/uc?export=download&id=1j4cA339-T15eaEYQHt3evsmK38fccdDB",
				},
				"kitsAndChemicals": map[string]interface{}{
					"kits": []map[string]interface{}{
						{
							"name": map[string]interface{}{
								"vi": "Balo",
								"en": "Backpack",
								"ko": "Backpack",
							},
							"amount":        1,
							"price":         350000,
							"imageUrl":      "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LaH4qhSQ9ZhaJE4jB",
							"buyAddressUrl": "https://docs.google.com/forms/d/e/1FAIpQLSdCtA7ggCpozA9oYqIA95KLQh3hUhRkEa_RPFpB-v0mEHfysQ/viewform",
							"note": map[string]interface{}{
								"vi": "Đăng ký trên trang FB CĐ hoặc mua trực tiếp tại VP gần nhất.",
								"en": "Register in FB community group or contact the nearest office.",
								"ko": "Register in FB community group or contact the nearest office.",
							},
						},
						{
							"name": map[string]interface{}{
								"vi": "Khăn đa năng",
								"en": "Multi-purpose cloth",
								"ko": "Multi-purpose cloth",
							},
						},
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
			})
		})
		UpdateTaskerSettings(bson.M{
			"$unset": bson.M{
				"trainingInput":    1,
				"trainingTasker":   1,
				"handbook":         1,
				"kitsAndChemicals": 1,
			},
		})
	})

	t.Run("5", func(t *testing.T) {
		log.Println("====================== validate GetAnalysisTask")
		apiURL := subPath + "/get-analysis-task"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("====================== GetAnalysisTask")
		apiURL := subPath + "/get-analysis-task"
		//Create User
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":        "0834567810",
				"name":         "Tasker 01",
				"type":         globalConstant.USER_TYPE_TASKER,
				"referralCode": "h6h5larai",
			}, {
				"phone":      "0834567890",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "h6h5larai",
			},
		})
		//Create Rating
		//now := globalLib.GetCurrentTime(local.TimeZone).AddDate(0,0,-3)
		now := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -3)
		data := []map[string]interface{}{
			{
				"taskerPhone": "0834567810",
				"askerPhone":  "0834567890",
				"rate":        4,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"taskerPhone": "0834567810",
				"askerPhone":  "0834567890",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		}
		CreateRating(data)
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567810"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": user.XId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string][]string{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m["listAskersWorked"][0], ShouldEqual, "0834567890")
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		log.Println("====================== GetAnalysisTask")
		apiURL := subPath + "/get-analysis-task"
		//Create User
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":        "0834567810",
				"name":         "Tasker 01",
				"type":         globalConstant.USER_TYPE_TASKER,
				"referralCode": "h6h5larai",
				"countryCode":  globalConstant.COUNTRY_CODE_TH,
			}, {
				"phone":       "0834567890",
				"name":        "Asker 01",
				"type":        globalConstant.USER_TYPE_ASKER,
				"friendCode":  "h6h5larai",
				"countryCode": globalConstant.COUNTRY_CODE_TH,
			},
		})
		//Create Rating
		//now := globalLib.GetCurrentTime(local.TimeZone).AddDate(0,0,-3)
		now := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -3)
		data := []map[string]interface{}{
			{
				"taskerPhone": "0834567810",
				"askerPhone":  "0834567890",
				"rate":        4,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			}, {
				"taskerPhone": "0834567810",
				"askerPhone":  "0834567890",
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute()),
			},
		}
		CreateRating(data)
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567810"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": user.XId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string][]string{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
				So(m["listAskersWorked"][0], ShouldEqual, "0834567890")
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		apiURL := subPath + "/post-to-slack"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := "123"
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := make(map[string]map[string]interface{})
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if channel empty", func() {
				body := map[string]interface{}{
					"channel": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CHANNEL_REQUIRED.ErrorCode)
			})
			Convey("Check the response if username empty", func() {
				body := map[string]interface{}{
					"channel":  "1",
					"username": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USERNAME_REQUIRED.ErrorCode)
			})
			Convey("Check the response if message empty", func() {
				body := map[string]interface{}{
					"channel":  "1",
					"username": "1",
					"message":  "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_MESSAGE_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("9", func(t *testing.T) {
		apiURL := subPath + "/post-to-slack"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if channel empty", func() {
				body := map[string]interface{}{
					"channel":  "login-error",
					"username": "App asker v3",
					"message":  "Login facebook fail",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 200)
			})
		})
	})

	t.Run("10", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/add-place-not-support"

		log.Println("==================================== AddPlaceNotSupport")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"place": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 10",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				var result *modelNextExpansion.NextExpansion
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NEXT_EXPANSION[local.ISO_CODE], bson.M{}, bson.M{}, &result)
				So(result, ShouldNotBeNil)
				So(result.Place.Country, ShouldEqual, local.ISO_CODE)
				So(result.Place.City, ShouldEqual, "Hồ Chí Minh")
				So(result.Place.District, ShouldEqual, "Quận 10")
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"place": map[string]interface{}{
					"country":  "NA",
					"city":     "CALI",
					"district": "FORT",
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("Then check the response to test Init Conversation", func() {
				service.NewRouter().ServeHTTP(resp, req)
				respResult := make(map[string]interface{})
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Init Conversation", func() {
				var result *modelNextExpansion.NextExpansion
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_WORLD_NEXT_EXPANSION[local.ISO_CODE], bson.M{}, bson.M{}, &result)
				So(result, ShouldNotBeNil)
				So(result.Place.Country, ShouldEqual, "NA")
				So(result.Place.City, ShouldEqual, "CALI")
				So(result.Place.District, ShouldEqual, "FORT")
			})
		})
	})
	t.Run("11", func(t *testing.T) {
		apiURL := subPath + "/give-lixi-tet-2022"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := "123"
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := make(map[string]map[string]interface{})
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if isoCode empty", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
			Convey("Check the response if isoCode not equal VN", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "TH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
			})
		})
	})

	t.Run("12", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/set-review-store"
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"userId": 1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})

			Convey("When service handle request if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if both count and isReviewStore is false", func() {
				body := map[string]interface{}{
					"userId": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				})
			})
		})
	})

	t.Run("13", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/set-review-store"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"userId": "0834567880",
			"count":  1,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					user, _ := modelUser.GetOneById(local.ISO_CODE, "0834567880", bson.M{"reviewStore": 1})
					So(user.ReviewStore, ShouldNotBeNil)
					So(user.ReviewStore.Count, ShouldEqual, 1)
					So(user.ReviewStore.IsReviewStore, ShouldBeFalse)
				})
			})
		})
	})

	t.Run("14", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/set-review-store"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"userId":        "0834567880",
			"isReviewStore": true,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					user, _ := modelUser.GetOneById(local.ISO_CODE, "0834567880", bson.M{"reviewStore": 1})
					So(user.ReviewStore, ShouldNotBeNil)
					So(user.ReviewStore.Count, ShouldEqual, 0)
					So(user.ReviewStore.IsReviewStore, ShouldBeTrue)
				})
			})
		})
	})

	t.Run("15", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/set-review-store"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"userId":        "0834567880",
			"count":         1,
			"isReviewStore": true,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					user, _ := modelUser.GetOneById(local.ISO_CODE, "0834567880", bson.M{"reviewStore": 1})
					So(user.ReviewStore, ShouldNotBeNil)
					So(user.ReviewStore.Count, ShouldEqual, 1)
					So(user.ReviewStore.IsReviewStore, ShouldBeTrue)
				})
			})
		})
	})

	t.Run("16", func(t *testing.T) {
		apiURL := "/api/v5/api-asker-vn/get-services-v2"
		ResetData()
		serviceHomeCleaning := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		serviceHomeCooking := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_COOKING}, bson.M{"_id": 1})

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"serviceIds": []string{serviceHomeCleaning.GetXId(), serviceHomeCooking.GetXId()},
			"appVersion": "4.0.0",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := make([]map[string]interface{}, 0)
					bytes, _ := io.ReadAll(res.Body)
					json.Unmarshal(bytes, &result)

					So(len(result), ShouldEqual, 2)

				})
			})
		})
	})
}
