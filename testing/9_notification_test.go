/*
 * @File: 9_notification_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 27/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 21/12/2020
 * @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelRaixPushAppTokens "gitlab.com/btaskee/go-services-model-v2/grpcmodel/raixPushAppTokens"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestNotification(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetNotifications")
		apiGetNotifications := "/api/v3/api-asker-vn/get-notification"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotifications), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetNotifications")
		// GetNotifications
		apiGetNotifications := "/api/v3/api-asker-vn/get-notification"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 3",
				"title":       "Test Notification Title 3",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotifications), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 4)
					for _, v := range respResult {
						So(v["userId"], ShouldEqual, "0834567890")
						So(v["type"], ShouldEqual, 25)
						So(v["description"], ShouldStartWith, "Test Notification Desc")
					}
				})
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotifications), t, func() {
			Convey("Check request with page/limit (page 1)", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"limit":  2,
					"page":   1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Notifications", func() {
						var respResult []map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 2)
						for i, v := range respResult {
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["type"], ShouldEqual, 25)
							So(v["description"], ShouldStartWith, "Test Notification Desc")
							if i < len(respResult)-1 {
								So(respResult[i]["createdAt"], ShouldBeGreaterThan, respResult[i+1]["createdAt"])
							}
						}
					})
				})
			})

			Convey("Check request with page/limit (page 2)", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"limit":  3,
					"page":   2,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Notifications", func() {
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 1)
						for i, v := range respResult {
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["type"], ShouldEqual, 25)
							So(v["description"], ShouldStartWith, "Test Notification Desc")
							if i < len(respResult)-1 {
								So(respResultM[i]["createdAt"]["seconds"], ShouldBeGreaterThan, respResultM[i+1]["createdAt"]["seconds"])
							}
						}
					})
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Validate GetNotificationDetail")
		apiGetNotification := "/api/v3/api-asker-vn/get-notification-detail"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotification), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"id": 1234124,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetNotificationDetail")
		// GetNotificationDetail
		apiGetNotification := "/api/v3/api-asker-vn/get-notification-detail"

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Notification
		ids := CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        27,
				"description": "Test Notification Detail Desc",
				"title":       "Test Notification Detail Title",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotification), t, func() {
			body := map[string]interface{}{
				"id": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetNotification, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["_id"], ShouldEqual, ids[0])
					So(respResult["userId"], ShouldEqual, "0834567890")
					So(respResult["type"], ShouldEqual, 27)
					So(respResult["description"], ShouldEqual, "Test Notification Detail Desc")
				})
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== Validate RemoveNotifications")
		// RemoveNotifications
		apiRemoveNotifications := "/api/v3/api-asker-vn/remove-notification-user"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotifications), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== RemoveNotifications")
		// RemoveNotifications
		apiRemoveNotifications := "/api/v3/api-asker-vn/remove-notification-user"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567112",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567112",
				"type":        22,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567112",
				"type":        30,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
			}, {
				"userPhone":   "0834567112",
				"type":        27,
				"description": "Test Notification Desc 5",
				"title":       "Test Notification Title 5",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotifications), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						var result []*modelNotification.Notification
						globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{}, &result)
						So(len(result), ShouldEqual, 0)
					})
				})
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotifications), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"userId": "0834567112",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						var result []*modelNotification.Notification
						globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "0834567112"}, bson.M{}, &result)
						So(len(result), ShouldEqual, 0)
					})
				})
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate GetNotificationNotFromBtaskee")
		apiGetNotificationNotFromBtaskee := "/api/v3/api-asker-vn/get-notification-not-from-btaskee"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotificationNotFromBtaskee), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotificationNotFromBtaskee, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotificationNotFromBtaskee, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

		})
	})
	t.Run("8", func(t *testing.T) {
		log.Println("==================================== GetNotificationNotFromBtaskee")
		// GetNotificationNotFromBtaskee
		apiGetNotificationNotFromBtaskee := "/api/v3/api-asker-vn/get-notification-not-from-btaskee"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567612",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification: 19, 22, 24
		insertData := []map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
				"data": map[string]interface{}{
					"subscriptionRequestId": "xxxxx1",
				},
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
				"data": map[string]interface{}{
					"subscriptionRequestId": "xxxxx2",
				},
			}, {
				"userPhone":   "0834567890",
				"type":        6,
				"description": "Test Notification Desc 3",
				"title":       "Test Notification Title 3",
				"data": map[string]interface{}{
					"subscriptionRequestId": "xxxxx3",
				},
			}, {
				"userPhone":   "0834567612",
				"type":        6,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
				"data": map[string]interface{}{
					"subscriptionRequestId": "xxxxx4",
				},
			},
		}
		CreateNotification(insertData)
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotificationNotFromBtaskee), t, func() {
			Convey("Get Notification case type nin LIST_NOTIFY_FROM_BTASKEE", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"page":   1,
					"limit":  20,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotificationNotFromBtaskee, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Notification Not From bTaskee", func() {
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 3)
						for i, v := range respResult {
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["type"], ShouldEqual, insertData[i]["type"])
							So(v["description"], ShouldStartWith, "Test Notification Desc")
							So(respResultM[i]["data"]["subscriptionRequestId"], ShouldStartWith, "xxxxx")
							if i < len(respResult)-1 {
								So(respResult[i]["createdAt"], ShouldBeGreaterThan, respResult[i+1]["createdAt"])
							}
						}
					})
				})
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotificationNotFromBtaskee), t, func() {
			Convey("Get Notification case type in LIST_NOTIFY_FROM_BTASKEE", func() {
				body := map[string]interface{}{
					"userId": "0834567612",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotificationNotFromBtaskee, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Notification Not From bTaskee", func() {
						bytes, _ := io.ReadAll(resp.Body)
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)

						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 1)

						for i, v := range respResult {
							So(v["userId"], ShouldEqual, "0834567612")
							So(v["type"], ShouldEqual, 6)
							So(v["description"], ShouldStartWith, "Test Notification Desc")
							So(respResultM[i]["data"]["subscriptionRequestId"], ShouldStartWith, "xxxxx")
							if i < len(respResult)-1 {
								So(respResultM[i]["createdAt"]["seconds"], ShouldBeGreaterThan, respResultM[i+1]["createdAt"]["seconds"])
							}
						}
					})
				})
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("==================================== Validate GetNotificationsTakser")
		apiGetUrl := "/api/v3/api-asker-vn/get-notification-tasker"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("==================================== GetNotificationsTakser")
		// GetNotificationsTakser
		apiGetUrl := "/api/v3/api-asker-vn/get-notification-tasker"

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 3",
				"title":       "Test Notification Title 3",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUrl), t, func() {
			Convey("Check request with page/limit (page 1)", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"limit":  2,
					"page":   1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Notifications", func() {
						var respResult []map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 2)
						for i, v := range respResult {
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["type"], ShouldEqual, 25)
							So(v["description"], ShouldStartWith, "Test Notification Desc")
							if i < len(respResult)-1 {
								So(respResult[i]["createdAt"], ShouldBeGreaterThan, respResult[i+1]["createdAt"])
							}
						}
					})
				})
			})
		})
	})
	t.Run("11", func(t *testing.T) {
		log.Println("==================================== GetNotificationsTakser")
		// GetNotificationsTakser
		apiGetUrl := "/api/v3/api-asker-vn/get-notification-tasker"

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 3",
				"title":       "Test Notification Title 3",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUrl), t, func() {
			Convey("Check request with page/limit (page 2)", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"limit":  3,
					"page":   2,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Get Notifications", func() {
						var respResult []map[string]interface{}
						var respResultM []map[string]map[string]interface{}
						bytes, _ := io.ReadAll(resp.Body)

						json.Unmarshal(bytes, &respResult)
						json.Unmarshal(bytes, &respResultM)
						So(resp.Code, ShouldEqual, 200)
						So(len(respResult), ShouldEqual, 1)
						for i, v := range respResult {
							So(v["userId"], ShouldEqual, "0834567890")
							So(v["type"], ShouldEqual, 25)
							So(v["description"], ShouldStartWith, "Test Notification Desc")
							if i < len(respResult)-1 {
								So(respResultM[i]["createdAt"]["seconds"], ShouldBeGreaterThan, respResultM[i+1]["createdAt"]["seconds"])
							}
						}
					})
				})
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		log.Println("==================================== Validation RemoveNotification")
		apiRemoveNotification := "/api/v3/api-asker-vn/remove-notification-by-id"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotification), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"id": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("13", func(t *testing.T) {
		log.Println("==================================== RemoveNotification")
		// RemoveNotification
		apiRemoveNotification := "/api/v3/api-asker-vn/remove-notification-by-id"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567112",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		ids := CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567112",
				"type":        22,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotification), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"id": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						var result *modelNotification.Notification
						globalDataAccess.GetOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], ids[0], bson.M{}, &result)
						So(result, ShouldBeNil)
					})
				})
			})
		})
	})
	t.Run("14", func(t *testing.T) {
		log.Println("==================================== Validation UpdateStatusNotification")
		apiUrl := "/api/v3/api-asker-vn/update-status-notification"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"id": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("15", func(t *testing.T) {
		log.Println("==================================== UpdateStatusNotification")
		// UpdateStatusNotification
		apiUrl := "/api/v3/api-asker-vn/update-status-notification"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567112",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		ids := CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567112",
				"type":        22,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Update Status Notification", func() {
				body := map[string]interface{}{
					"id":     ids[0],
					"isRead": true,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Update Status Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Update Status Notifications", func() {
						var result *modelNotification.Notification
						globalDataAccess.GetOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], ids[0], bson.M{"isRead": 1}, &result)
						So(result.IsRead, ShouldBeTrue)
					})
				})
			})
		})
	})
	t.Run("16", func(t *testing.T) {
		log.Println("==================================== Validation RemoveAllNotification")
		apiRemoveAllNotifications := "/api/v3/api-asker-vn/remove-all-notification"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveAllNotifications), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveAllNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveAllNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("17", func(t *testing.T) {
		log.Println("==================================== RemoveAllNotification")
		// RemoveAllNotifications
		apiRemoveAllNotifications := "/api/v3/api-asker-vn/remove-all-notification"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567112",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567112",
				"type":        22,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveAllNotifications), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveAllNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						var result []*modelNotification.Notification
						globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
							bson.M{"userId": bson.M{"$in": []string{"0834567890", "0834567112"}}}, bson.M{}, &result)
						So(len(result), ShouldEqual, 1)
					})
				})
			})
		})
	})
	t.Run("18", func(t *testing.T) {
		log.Println("==================================== Validate LoadMoreNotification")
		// LoadMoreNotification
		apiUrl := "/api/v3/api-asker-vn/load-more-notification"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 132131231,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

		})
	})
	t.Run("19", func(t *testing.T) {
		log.Println("==================================== LoadMoreNotification")
		// LoadMoreNotification
		apiUrl := "/api/v3/api-asker-vn/load-more-notification"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Notification
		CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        30,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        30,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567890",
				"type":        30,
				"description": "Test Notification Desc 3",
				"title":       "Test Notification Title 3",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"page":   1,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test LoadMoreNotification", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 3)
					for i, v := range respResult {
						So(v["userId"], ShouldEqual, "0834567890")
						So(v["type"], ShouldEqual, 30)
						So(v["description"], ShouldStartWith, "Test Notification Desc")
						if i < len(respResult)-1 {
							So(respResult[i]["createdAt"], ShouldBeGreaterThan, respResult[i+1]["createdAt"])
						}
					}
				})
			})
		})
	})
	t.Run("20", func(t *testing.T) {
		log.Println("==================================== Validation UpdateReceiveNotification")

		// UpdateReceiveNotification
		apiUrl := "/api/v3/api-asker-vn/update-receive-notification"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"userId": 21313212,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when user not found", func() {
				body := map[string]interface{}{
					"userId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})

		})
	})
	t.Run("21", func(t *testing.T) {
		log.Println("==================================== UpdateReceiveNotification")
		// UpdateReceiveNotification
		apiUrl := "/api/v3/api-asker-vn/update-receive-notification"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":                 "0834567890",
				"name":                  "Tasker 01",
				"type":                  globalConstant.USER_TYPE_TASKER,
				"noReceiveNotification": false,
			},
		})

		//Create RaixPushAppTokens
		ids := CreateRaixPushAppTokens([]map[string]interface{}{
			{
				"userPhone": "0834567890",
				"token":     "abcxyz",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"userId":                "0834567890",
					"noReceiveNotification": true,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						user, _ := modelUser.GetOneById(local.ISO_CODE, "0834567890", bson.M{"noReceiveNotification": 1})
						So(user.NoReceiveNotification, ShouldBeTrue)
						var raix *modelRaixPushAppTokens.RaixPushAppTokens
						globalDataAccess.GetOneById(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, ids[0], bson.M{}, &raix)
						So(raix.Token, ShouldBeNil)
					})
				})
			})
		})
	})
	t.Run("22", func(t *testing.T) {
		log.Println("==================================== Validation RemoveNotification")
		apiRemoveNotification := "/api/v3/api-asker-vn/remove-notification-not-from-btaskee-by-id"

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotification), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"id": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("23", func(t *testing.T) {
		log.Println("==================================== RemoveNotification")
		// RemoveNotification
		apiRemoveNotification := "/api/v3/api-asker-vn/remove-notification-not-from-btaskee-by-id"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567112",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		ids := CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			}, {
				"userPhone":   "0834567112",
				"type":        22,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotification), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"id": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						var result *modelNotification.Notification
						globalDataAccess.GetOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], ids[0], bson.M{}, &result)
						So(result, ShouldBeNil)
					})
				})
			})
		})
	})
	t.Run("24", func(t *testing.T) {
		log.Println("==================================== RemoveNotification")
		// RemoveNotification
		apiRemoveNotification := "/api/v3/api-asker-vn/remove-notification-not-from-btaskee-by-id"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567112",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		ids := CreateNotification([]map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        24,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveNotification), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"id": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveNotification, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove Notifications", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database Remove Notifications", func() {
						var result *modelNotification.Notification
						globalDataAccess.GetOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], ids[0], bson.M{}, &result)
						So(result, ShouldNotBeNil)
						So(result.IsRead, ShouldBeTrue)
						So(result.IsForce, ShouldBeFalse)
					})
				})
			})
		})
	})

	t.Run("25", func(t *testing.T) {
		log.Println("==================================== GetNotifications")
		// GetNotifications
		apiGetNotifications := "/api/v3/api-asker-vn/get-notification-not-from-btaskee-v2"

		Convey(fmt.Sprintf("Given a HTTP request for %s case page 1 limit 5", apiGetNotifications), t, func() {
			body := map[string]interface{}{
				"userId": "",
				"page":   1,
				"limit":  5,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					var respResult map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)

					So(resp.Code, ShouldEqual, 400)
					So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		log.Println("==================================== GetNotifications")
		// GetNotifications
		apiGetNotifications := "/api/v3/api-asker-vn/get-notification-not-from-btaskee-v2"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Notification
		now := globalLib.GetCurrentTime(local.TimeZone)
		createdAt_1 := now.Add(-1 * time.Hour)
		createdAt_2 := now.Add(-2 * time.Hour)
		createdAt_3 := now.Add(-3 * time.Hour)
		createdAt_4 := now.Add(-4 * time.Hour)
		createdAt_5 := now.Add(-5 * time.Hour)
		createdAt_6 := now.Add(-6 * time.Hour)
		createdAt_7 := now.Add(-7 * time.Hour)
		createdAt_8 := now.Add(-8 * time.Hour)
		createdAt_9 := now.Add(-9 * time.Hour)

		CreateNotificationV2(globalConstant.ISO_CODE_VN, []map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 1",
				"title":       "Test Notification Title 1",
				"createdAt":   createdAt_1,
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 2",
				"title":       "Test Notification Title 2",
				"createdAt":   createdAt_2,
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 3",
				"title":       "Test Notification Title 3",
				"createdAt":   createdAt_3,
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 6",
				"title":       "Test Notification Title 6",
				"createdAt":   createdAt_6,
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 9",
				"title":       "Test Notification Title 9",
				"createdAt":   createdAt_9,
			},
		})

		CreateNotificationV2(globalConstant.ISO_CODE_TH, []map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 5",
				"title":       "Test Notification Title 5",
				"createdAt":   createdAt_5,
			},
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 7",
				"title":       "Test Notification Title 7",
				"createdAt":   createdAt_7,
			},
		})

		CreateNotificationV2(globalConstant.ISO_CODE_INDO, []map[string]interface{}{
			{
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 4",
				"title":       "Test Notification Title 4",
				"createdAt":   createdAt_4,
			}, {
				"userPhone":   "0834567890",
				"type":        25,
				"description": "Test Notification Desc 8",
				"title":       "Test Notification Title 8",
				"createdAt":   createdAt_8,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s case page 1 limit 5", apiGetNotifications), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"page":   1,
				"limit":  5,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 5)
					So(respResult[0]["userId"], ShouldEqual, "0834567890")
					So(respResult[0]["type"], ShouldEqual, 25)
					So(respResult[0]["description"], ShouldStartWith, "Test Notification Desc 1")
					So(respResult[0]["title"], ShouldStartWith, "Test Notification Title 1")
					So(respResult[0]["createdAt"], ShouldNotBeNil)
					So(respResult[0]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_VN)

					So(respResult[1]["userId"], ShouldEqual, "0834567890")
					So(respResult[1]["type"], ShouldEqual, 25)
					So(respResult[1]["description"], ShouldStartWith, "Test Notification Desc 2")
					So(respResult[1]["title"], ShouldStartWith, "Test Notification Title 2")
					So(respResult[1]["createdAt"], ShouldNotBeNil)
					So(respResult[1]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_VN)

					So(respResult[2]["userId"], ShouldEqual, "0834567890")
					So(respResult[2]["type"], ShouldEqual, 25)
					So(respResult[2]["description"], ShouldStartWith, "Test Notification Desc 3")
					So(respResult[2]["title"], ShouldStartWith, "Test Notification Title 3")
					So(respResult[2]["createdAt"], ShouldNotBeNil)
					So(respResult[2]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_VN)

					So(respResult[3]["userId"], ShouldEqual, "0834567890")
					So(respResult[3]["type"], ShouldEqual, 25)
					So(respResult[3]["description"], ShouldStartWith, "Test Notification Desc 4")
					So(respResult[3]["title"], ShouldStartWith, fmt.Sprintf("%s %s", globalConstant.COUNTRY_FLAG_BY_ISO_CODE[globalConstant.ISO_CODE_INDO], "Test Notification Title 4"))
					So(respResult[3]["createdAt"], ShouldNotBeNil)
					So(respResult[3]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_INDO)

					So(respResult[4]["userId"], ShouldEqual, "0834567890")
					So(respResult[4]["type"], ShouldEqual, 25)
					So(respResult[4]["description"], ShouldStartWith, "Test Notification Desc 5")
					So(respResult[4]["title"], ShouldStartWith, fmt.Sprintf("%s %s", globalConstant.COUNTRY_FLAG_BY_ISO_CODE[globalConstant.ISO_CODE_TH], "Test Notification Title 5"))
					So(respResult[4]["createdAt"], ShouldNotBeNil)
					So(respResult[4]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_TH)
				})
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s case page 2 limit 5", apiGetNotifications), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"page":   2,
				"limit":  5,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 4)
					So(respResult[0]["userId"], ShouldEqual, "0834567890")
					So(respResult[0]["type"], ShouldEqual, 25)
					So(respResult[0]["description"], ShouldStartWith, "Test Notification Desc 6")
					So(respResult[0]["title"], ShouldStartWith, "Test Notification Title 6")
					So(respResult[0]["createdAt"], ShouldNotBeNil)
					So(respResult[0]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_VN)

					So(respResult[1]["userId"], ShouldEqual, "0834567890")
					So(respResult[1]["type"], ShouldEqual, 25)
					So(respResult[1]["description"], ShouldStartWith, "Test Notification Desc 7")
					So(respResult[1]["title"], ShouldStartWith, fmt.Sprintf("%s %s", globalConstant.COUNTRY_FLAG_BY_ISO_CODE[globalConstant.ISO_CODE_TH], "Test Notification Title 7"))
					So(respResult[1]["createdAt"], ShouldNotBeNil)
					So(respResult[1]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_TH)

					So(respResult[2]["userId"], ShouldEqual, "0834567890")
					So(respResult[2]["type"], ShouldEqual, 25)
					So(respResult[2]["description"], ShouldStartWith, "Test Notification Desc 8")
					So(respResult[2]["title"], ShouldStartWith, fmt.Sprintf("%s %s", globalConstant.COUNTRY_FLAG_BY_ISO_CODE[globalConstant.ISO_CODE_INDO], "Test Notification Title 8"))
					So(respResult[2]["createdAt"], ShouldNotBeNil)
					So(respResult[2]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_INDO)

					So(respResult[3]["userId"], ShouldEqual, "0834567890")
					So(respResult[3]["type"], ShouldEqual, 25)
					So(respResult[3]["description"], ShouldStartWith, "Test Notification Desc 9")
					So(respResult[3]["title"], ShouldStartWith, "Test Notification Title 9")
					So(respResult[3]["createdAt"], ShouldNotBeNil)
					So(respResult[3]["isoCode"], ShouldEqual, globalConstant.ISO_CODE_VN)
				})
			})
		})
	})

}
