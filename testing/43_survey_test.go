/*
* @File: 29_feedback_test.go
* @Description: Handler function, case test
* @CreatedAt: 16/11/2020
* @Author: ngoctb
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSurvey(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		Convey(fmt.Sprintf("Given a HTTP request for %s", "/api/v3/api-asker-vn/get-survey-by-id"), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/get-survey-by-id", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if isoCode empty", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/get-survey-by-id", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
					"userId":  "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/get-survey-by-id", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if phone empty", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
					"userId":  "123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/get-survey-by-id", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SURVEY_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if asker have sent report", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone":   "0834567890",
						"name":    "Asker 01",
						"type":    globalConstant.USER_TYPE_ASKER,
						"isoCode": local.ISO_CODE,
					},
				})

				CreateVNSurveyReport([]map[string]interface{}{
					{
						"isoCode":  local.ISO_CODE,
						"userId":   "0834567890",
						"surveyId": "123",
					},
				})

				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"userId":   "0834567890",
					"surveyId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/get-survey-by-id", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ASKER_HAVE_SENT_SURVEY.ErrorCode)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		Convey(fmt.Sprintf("Given a HTTP request for %s", "/api/v3/api-asker-vn/get-survey-by-id"), t, func() {
			ResetData()

			//Create User
			CreateUser([]map[string]interface{}{
				{
					"phone":   "0834567890",
					"name":    "Asker 01",
					"type":    globalConstant.USER_TYPE_ASKER,
					"isoCode": local.ISO_CODE,
				},
			})

			//Create Survey
			ids := CreateVNSurveySetting([]map[string]interface{}{
				{
					"_id":    "xxx",
					"name":   "FIRST_DONE_TASK",
					"status": "ACTIVE",
					"title": map[string]interface{}{
						"vi": "Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker",
						"en": "Please rate your satisfaction level with Tasker's skills",
						"ko": "작업자의 솜씨에 대한 만족도를 평가해주세요",
						"th": "กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker",
						"id": "Mohon berikan penilaian tentang kemampuan Tasker's",
					},
					"reward": map[string]interface{}{
						"title": map[string]interface{}{
							"vi": "xxx",
							"en": "xxx",
						},
						"content": map[string]interface{}{
							"vi": "xxx",
							"en": "xxx",
						},
						"note": map[string]interface{}{
							"vi": "xxx",
							"en": "xxx",
						},
						"promotionPrefix": "xxx",
						"promotionValue":  123.0,
						"promotionType":   "MONEY",
						"image":           "xx",
						"period":          30.0,
					},
					"questions": []map[string]interface{}{
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker",
								"en": "Please rate your satisfaction level with Tasker's skills",
								"ko": "작업자의 솜씨에 대한 만족도를 평가해주세요",
								"th": "กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker",
								"id": "Mohon berikan penilaian tentang kemampuan Tasker's",
							},
							"type": "RATING",
						},
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng chia sẻ nhu cầu sử dụng dịch vụ của bạn",
								"en": "Please share your service usage needs",
								"ko": "서비스 사용 필요에 대해 알려주세요",
								"th": "กรุณาบอกความต้องการในการใช้บริการของคุณ",
								"id": "Mohon informasikan seberapa sering Anda menggunakan service kebersihan",
							},
							"type": "SINGLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Trên 3 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tháng ",
									"en": "xxx",
								},
								{
									"vi": "Không cố định, chỉ sử dụng khi cần",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn biết đến bTaskee qua phương tiện nào?",
								"en": "How do you know bTaskee?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "MULTIPLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Google",
									"en": "xxx",
								},
								{
									"vi": "Facebook",
									"en": "xxx",
								},
								{
									"vi": "Tiktok",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn có những góp ý gì thêm cho bTaskee để nâng cao chất lượng dịch vụ không?",
								"en": "Do you have any recommendations for the improvement of bTaskee's services?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "INPUT",
						},
					},
				},
			})

			Convey("Check the response", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"userId":   "0834567890",
					"surveyId": ids[0],
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/get-survey-by-id", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the HTTP response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["name"], ShouldEqual, "FIRST_DONE_TASK")
					So(result["reward"], ShouldNotBeNil)
					So(result["title"], ShouldNotBeNil)
					So(len(result["questions"].([]interface{})), ShouldEqual, 4)
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		Convey(fmt.Sprintf("Given a HTTP request for %s", "/api/v3/api-asker-vn/asker-send-survey"), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if isoCode empty", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
					"userId":  "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if phone empty", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
					"userId":  "123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SURVEY_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if surveyAnswer empty", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"userId":   "123123",
					"surveyId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_SURVEY_ANSWERS_REQUIRED.ErrorCode)
			})
			Convey("Check the response if asker have sent report", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone":   "0834567890",
						"name":    "Asker 01",
						"type":    globalConstant.USER_TYPE_ASKER,
						"isoCode": local.ISO_CODE,
					},
				})

				CreateVNSurveyReport([]map[string]interface{}{
					{
						"isoCode":  local.ISO_CODE,
						"userId":   "0834567890",
						"surveyId": "123",
					},
				})

				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"userId":   "0834567890",
					"surveyId": "123",
					"surveyAnswers": []map[string]interface{}{
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker",
								"en": "Please rate your satisfaction level with Tasker's skills",
								"ko": "작업자의 솜씨에 대한 만족도를 평가해주세요",
								"th": "กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker",
								"id": "Mohon berikan penilaian tentang kemampuan Tasker's",
							},
							"type": "RATING",
						},
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng chia sẻ nhu cầu sử dụng dịch vụ của bạn",
								"en": "Please share your service usage needs",
								"ko": "서비스 사용 필요에 대해 알려주세요",
								"th": "กรุณาบอกความต้องการในการใช้บริการของคุณ",
								"id": "Mohon informasikan seberapa sering Anda menggunakan service kebersihan",
							},
							"type": "SINGLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Trên 3 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tháng ",
									"en": "xxx",
								},
								{
									"vi": "Không cố định, chỉ sử dụng khi cần",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn biết đến bTaskee qua phương tiện nào?",
								"en": "How do you know bTaskee?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "MULTIPLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Google",
									"en": "xxx",
								},
								{
									"vi": "Facebook",
									"en": "xxx",
								},
								{
									"vi": "Tiktok",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn có những góp ý gì thêm cho bTaskee để nâng cao chất lượng dịch vụ không?",
								"en": "Do you have any recommendations for the improvement of bTaskee's services?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "INPUT",
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ASKER_HAVE_SENT_SURVEY.ErrorCode)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		Convey(fmt.Sprintf("Given a HTTP request for %s", "/asker-send-survey"), t, func() {
			ResetData()

			//Create User
			CreateUser([]map[string]interface{}{
				{
					"phone":   "0834567890",
					"name":    "Asker 01",
					"type":    globalConstant.USER_TYPE_ASKER,
					"isoCode": local.ISO_CODE,
				},
			})

			//Create Survey
			ids := CreateVNSurveySetting([]map[string]interface{}{
				{
					"_id":    "xxx",
					"name":   "FIRST_DONE_TASK",
					"status": "ACTIVE",
					"title": map[string]interface{}{
						"vi": "Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker",
						"en": "Please rate your satisfaction level with Tasker's skills",
						"ko": "작업자의 솜씨에 대한 만족도를 평가해주세요",
						"th": "กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker",
						"id": "Mohon berikan penilaian tentang kemampuan Tasker's",
					},
					"reward": map[string]interface{}{
						"title": map[string]interface{}{
							"vi": "xxx",
							"en": "xxx",
						},
						"content": map[string]interface{}{
							"vi": "xxx",
							"en": "xxx",
						},
						"note": map[string]interface{}{
							"vi": "xxx",
							"en": "xxx",
						},
						"promotionPrefix":    "xxx",
						"promotionValue":     123.0,
						"promotionType":      "MONEY",
						"image":              "xx",
						"period":             30.0,
						"promotionServiceId": "123",
					},
					"questions": []map[string]interface{}{
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker",
								"en": "Please rate your satisfaction level with Tasker's skills",
								"ko": "작업자의 솜씨에 대한 만족도를 평가해주세요",
								"th": "กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker",
								"id": "Mohon berikan penilaian tentang kemampuan Tasker's",
							},
							"type": "RATING",
						},
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng chia sẻ nhu cầu sử dụng dịch vụ của bạn",
								"en": "Please share your service usage needs",
								"ko": "서비스 사용 필요에 대해 알려주세요",
								"th": "กรุณาบอกความต้องการในการใช้บริการของคุณ",
								"id": "Mohon informasikan seberapa sering Anda menggunakan service kebersihan",
							},
							"type": "SINGLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Trên 3 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tháng ",
									"en": "xxx",
								},
								{
									"vi": "Không cố định, chỉ sử dụng khi cần",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn biết đến bTaskee qua phương tiện nào?",
								"en": "How do you know bTaskee?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "MULTIPLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Google",
									"en": "xxx",
								},
								{
									"vi": "Facebook",
									"en": "xxx",
								},
								{
									"vi": "Tiktok",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn có những góp ý gì thêm cho bTaskee để nâng cao chất lượng dịch vụ không?",
								"en": "Do you have any recommendations for the improvement of bTaskee's services?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "INPUT",
						},
					},
				},
			})

			Convey("Check the response", func() {
				body := map[string]interface{}{
					"isoCode":  local.ISO_CODE,
					"userId":   "0834567890",
					"surveyId": ids[0],
					"surveyAnswers": []map[string]interface{}{
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker",
								"en": "Please rate your satisfaction level with Tasker's skills",
								"ko": "작업자의 솜씨에 대한 만족도를 평가해주세요",
								"th": "กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker",
								"id": "Mohon berikan penilaian tentang kemampuan Tasker's",
							},
							"type": "RATING",
						},
						{
							"question": map[string]interface{}{
								"vi": "Vui lòng chia sẻ nhu cầu sử dụng dịch vụ của bạn",
								"en": "Please share your service usage needs",
								"ko": "서비스 사용 필요에 대해 알려주세요",
								"th": "กรุณาบอกความต้องการในการใช้บริการของคุณ",
								"id": "Mohon informasikan seberapa sering Anda menggunakan service kebersihan",
							},
							"type": "SINGLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Trên 3 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tuần",
									"en": "xxx",
								},
								{
									"vi": "Khoản 1-2 lần/tháng ",
									"en": "xxx",
								},
								{
									"vi": "Không cố định, chỉ sử dụng khi cần",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn biết đến bTaskee qua phương tiện nào?",
								"en": "How do you know bTaskee?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "MULTIPLE",
							"answer": []map[string]interface{}{
								{
									"vi": "Google",
									"en": "xxx",
								},
								{
									"vi": "Facebook",
									"en": "xxx",
								},
								{
									"vi": "Tiktok",
									"en": "xxx",
								},
							},
						},
						{
							"question": map[string]interface{}{
								"vi": "Bạn có những góp ý gì thêm cho bTaskee để nâng cao chất lượng dịch vụ không?",
								"en": "Do you have any recommendations for the improvement of bTaskee's services?",
								"ko": "xxx",
								"th": "xxx",
								"id": "xxx",
							},
							"type": "INPUT",
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/asker-send-survey", bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the HTTP response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["giftId"], ShouldNotBeNil)
					isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_SURVEY_REPORT[local.ISO_CODE], bson.M{"userId": "0834567890", "surveyId": ids[0]})
					So(isExist, ShouldBeTrue)

					var promotionCode *modelPromotionCode.PromotionCode
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"userIds": "0834567890"}, bson.M{}, &promotionCode)
					So(promotionCode.Value.Type, ShouldEqual, "MONEY")
					So(promotionCode.Value.Value, ShouldEqual, 123.0)
					So(promotionCode.TypeOfPromotion, ShouldEqual, "BOTH")
					So(promotionCode.Target, ShouldEqual, "ASKER")
					So(promotionCode.ServiceId, ShouldEqual, "123")
					So(promotionCode.Source, ShouldEqual, globalConstant.PROMOTION_CODE_SOURCE_SURVEY)

					var gift *modelGift.Gift
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{}, &gift)
					So(gift.PromotionCode, ShouldEqual, promotionCode.Code)
					So(gift.Source, ShouldEqual, "SYSTEM")
					So(gift.PromotionId, ShouldEqual, promotionCode.XId)
				})
			})
		})
	})
}
