/*
 * @File: 35_refund_request_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 10/11/2021
 * @Author: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
)

func Test_LuckyDraw(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate CreateRefundRequest")
		// CreateRefundRequest
		apiURL := "/api/v3/api-asker-vn/get-number-lucky-draw-by-user-id"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Validate CreateRefundRequest")
		// CreateRefundRequest
		apiURL := "/api/v3/api-asker-vn/get-number-lucky-draw-by-user-id"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":              "0834567890",
				"name":               "Asker 01",
				"type":               globalConstant.USER_TYPE_ASKER,
				"numberOfLuckyDraws": 100,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateGameCampaign([]map[string]interface{}{
			{
				"startDate": now,
				"endDate":   now.AddDate(0, 0, 1),
				"list": []map[string]interface{}{
					{
						"rewardKey":  1,
						"percentage": 100,
						"title": map[string]interface{}{
							"vi": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch",
							"en": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch",
							"ko": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch",
							"th": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch",
						},
						"name": "Tivi",
					}, {
						"rewardKey":  13,
						"percentage": 40,
						"title": map[string]interface{}{
							"vi": "Chúc bạn may mắn lần sau",
							"en": "Chúc bạn may mắn lần sau",
							"ko": "Chúc bạn may mắn lần sau",
							"th": "Chúc bạn may mắn lần sau",
						},
						"name": "Good luck",
					},
				},
				"limitedRewardKeyEachUser": []float64{1, 2, 3},
			},
		})

		code, _ := promotioncode.GeneratePromotionCode(local.ISO_CODE, "", 7)
		code1, _ := promotioncode.GeneratePromotionCode(local.ISO_CODE, "", 7)
		CreateLuckyDraw([]map[string]interface{}{
			{
				"code": code,
				"title": map[string]interface{}{
					"vi": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch",
				},
				"note": map[string]interface{}{
					"vi": "HƯỚNG DẪN SỬ DỤNG:\n\t\t\t\t- Lựa chọn dịch vụ bạn muốn sử dụng.\n\t\t\t\t- Nhập thông tin chi tiết công việc.\n\t\t\t\t- Nhập mã khuyến mãi tại bước \"Xác nhận và thanh toán\".\n\t\t\t\t- Hoàn tất đặt lịch.\n\t\t \n\t\t ĐIỀU KHOẢN SỬ DỤNG:\n\t\t\t\t- Ưu đãi áp dụng đến hết ngày 31/01/2022\n\t\t\t\t- Chỉ áp dụng cho dịch vụ trên ứng dụng bTaskee.\n\t\t\t\t- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất.\n\t\t\t\t- Không có giá trị quy đổi thành tiền mặt.\n\t\t\t\t- Không áp dụng đồng thời cùng các chương trình ưu đãi khác.",
				},
				"brandInfo": map[string]interface{}{
					"name": "bTaskee",
					"text": map[string]interface{}{
						"vi": "bTaskee",
						"en": "bTaskee",
						"ko": "bTaskee",
						"th": "bTaskee",
					},
					"image": "https://i.imgur.com/xArAC0e.png",
				},
				"rewardKey": 1.0,
				"expiredAt": "2022-06-12T22:18:11.353+07:00",
				"source":    "SYSTEM_WITH_PARTNER",
				"startDate": now.AddDate(0, 0, -1),
				"image":     "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/bRewards/bTaskee/thailand/DiscountVN30B.png",
				"userId":    "0834567890",
			}, {
				"code": code1,
				"title": map[string]interface{}{
					"vi": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch 2",
				},
				"note": map[string]interface{}{
					"vi": "HƯỚNG DẪN SỬ DỤNG:\n\t\t\t\t- Lựa chọn dịch vụ bạn muốn sử dụng.\n\t\t\t\t- Nhập thông tin chi tiết công việc.\n\t\t\t\t- Nhập mã khuyến mãi tại bước \"Xác nhận và thanh toán\".\n\t\t\t\t- Hoàn tất đặt lịch.\n\t\t \n\t\t ĐIỀU KHOẢN SỬ DỤNG:\n\t\t\t\t- Ưu đãi áp dụng đến hết ngày 31/01/2022\n\t\t\t\t- Chỉ áp dụng cho dịch vụ trên ứng dụng bTaskee.\n\t\t\t\t- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất.\n\t\t\t\t- Không có giá trị quy đổi thành tiền mặt.\n\t\t\t\t- Không áp dụng đồng thời cùng các chương trình ưu đãi khác.",
				},
				"brandInfo": map[string]interface{}{
					"name": "bTaskee",
					"text": map[string]interface{}{
						"vi": "bTaskee",
						"en": "bTaskee",
						"ko": "bTaskee",
						"th": "bTaskee",
					},
					"image": "https://i.imgur.com/xArAC0e.png",
				},
				"rewardKey": 2.0,
				"expiredAt": "2022-06-12T22:18:11.353+07:00",
				"source":    "SYSTEM_WITH_PARTNER",
				"startDate": now.AddDate(0, 0, -1),
				"image":     "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/bRewards/bTaskee/thailand/DiscountVN30B.png",
				"userId":    "0834567890",
			},
			{
				"code": code1,
				"title": map[string]interface{}{
					"vi": "[QUÀ GIÁNG SINH] Tivi Sony 4k 55 inch 2",
				},
				"note": map[string]interface{}{
					"vi": "HƯỚNG DẪN SỬ DỤNG:\n\t\t\t\t- Lựa chọn dịch vụ bạn muốn sử dụng.\n\t\t\t\t- Nhập thông tin chi tiết công việc.\n\t\t\t\t- Nhập mã khuyến mãi tại bước \"Xác nhận và thanh toán\".\n\t\t\t\t- Hoàn tất đặt lịch.\n\t\t \n\t\t ĐIỀU KHOẢN SỬ DỤNG:\n\t\t\t\t- Ưu đãi áp dụng đến hết ngày 31/01/2022\n\t\t\t\t- Chỉ áp dụng cho dịch vụ trên ứng dụng bTaskee.\n\t\t\t\t- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất.\n\t\t\t\t- Không có giá trị quy đổi thành tiền mặt.\n\t\t\t\t- Không áp dụng đồng thời cùng các chương trình ưu đãi khác.",
				},
				"brandInfo": map[string]interface{}{
					"name": "bTaskee",
					"text": map[string]interface{}{
						"vi": "bTaskee",
						"en": "bTaskee",
						"ko": "bTaskee",
						"th": "bTaskee",
					},
					"image": "https://i.imgur.com/xArAC0e.png",
				},
				"rewardKey": 2.0,
				"expiredAt": "2022-06-12T22:18:11.353+07:00",
				"source":    "SYSTEM_WITH_PARTNER",
				"startDate": now.AddDate(0, 0, -1),
				"image":     "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/bRewards/bTaskee/thailand/DiscountVN30B.png",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)

				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["result"], ShouldEqual, 2)
			})
		})
	})
}
