/*
* @File: 30_outstandingPayment_test.go
* @Description: Handler function before Test
* @CreatedAt: 02/03/2020
* @Author: linhnh
* @UpdatedAt: 23/03/2021
* @UpdatedBy: ngoctb3
 */
package testing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	subPath = "/api/v3/api-asker-vn"
)

func ResetData() {
	// Remove User
	modelUser.DeleteAllByQuery(local.ISO_CODE, bson.M{})

	// Remove task
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{})

	// Remove Incantive
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], bson.M{})

	// Remove FAccount
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{})

	// Remove Notification
	for _, collectionName := range globalCollection.COLLECTION_NOTIFICATION {
		globalDataAccess.DeleteAllByQuery(collectionName, bson.M{})
	}

	// Remove FATaskerTaskHistory
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_TASK_HISTORY[local.ISO_CODE], bson.M{})

	// Remove FATransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{})

	// Remove Gift
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{})

	// Remove TaskSchedule
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], bson.M{})

	// Remove PaymentCard
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], bson.M{})

	// Remove Subscription
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], bson.M{})

	// Remove Rating
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{})

	// Remove PointTransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove MarketingCampaign
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], bson.M{})

	// Remove ChatMessage
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE], bson.M{})

	// Remove SubscriptionRequest
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], bson.M{})

	// Remove ChatMessage
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{})

	// Remove RaixPushAppTokens
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, bson.M{})

	// Remove RegisterService
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REGISTER_SERVICE[local.ISO_CODE], bson.M{})

	// Remove Transaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove PurchaseOrder
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE], bson.M{})

	// Remove TaskerTraining
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{})

	// Remove Reward
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REWARD[local.ISO_CODE], bson.M{})

	// Remove OutstandingPayment
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_OUTSTANDING_PAYMENT[local.ISO_CODE], bson.M{})

	// Remove ReportTransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE], bson.M{})

	// Reset Service Channel
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{}, bson.M{"$unset": bson.M{"taskerList": 1}})

	// Remove UserLocationHistory
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE], bson.M{})

	// Remove ActivationCode
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_ACTIVATION[local.ISO_CODE], bson.M{})

	// Remove TaskReportTasker
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK_REPORT_TASKER[local.ISO_CODE], bson.M{})

	// Remove RedeemGiftTransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove Feedback
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FEEDBACK[local.ISO_CODE], bson.M{})

	// Remove shoppingCart
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_REFUND_REQUEST
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE], bson.M{})

	// Remove Promotion Wallet
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PROMOTION_PAYMENT_METHOD[local.ISO_CODE], bson.M{})

	// Remove storeGroceryAssistant
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE], bson.M{"_id": bson.M{"$ne": "s1"}})

	// Remove vn_NextExpansion
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NEXT_EXPANSION[local.ISO_CODE], bson.M{})

	// Remove world_NextExpansion
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_WORLD_NEXT_EXPANSION[local.ISO_CODE], bson.M{})

	// Remove luckyDraw
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_LUCKY_DRAW[local.ISO_CODE], bson.M{})

	lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
	// Remove taskerYearEndReport
	collectionTaskerYearEndReport := fmt.Sprintf("%s%d", globalCollection.COLLECTION_TASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.DeleteAllByQuery(collectionTaskerYearEndReport, bson.M{})

	// Remove askerYearEndReport
	collectionAskerYearEndReport := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.DeleteAllByQuery(collectionAskerYearEndReport, bson.M{})

	// Remove COLLECTION_USER_COMPLAINT
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_COMPLAINT[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_VN_PAY_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_VN_PAY_TRANSACTION, bson.M{})

	// Remove COLLECTION_MOMO_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_MOMO_TRANSACTION, bson.M{})

	// Remove COLLECTION_ZALOPAY_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ZALOPAY_TRANSACTION, bson.M{})

	// Remove COLLECTION_USERS_DELETED
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USERS_DELETED[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_PROMOTION_CODE
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_VN_COMBO_VOUCHER
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_VN_COMBO_VOUCHER_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_VN_ASKER_REFERRAL_SETTING
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_REFERRAL_SETTING[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_VN_ASKER_REFERRAL_CAMPAIGN
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_REFERRAL_CAMPAIGN[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_VN_ASKER_FLASH_SALE_INCENTIVE
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_FLASH_SALE_INCENTIVE[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_GAME_CAMPAIGN_HISTORY[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SERVICE_GROUP[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SURVEY_REPORT[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SURVEY_SETTING[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PAYMENT_MB_TRANSACTION, bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_VOTE_FAV_TASKER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FAIRY_TALE[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PAYMENT_METHOD_CAMPAIGN[local.ISO_CODE], bson.M{})

	// Business
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], bson.M{})

	// CommunitySetting
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_SETTING[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_EVENT_CONFIG[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SERVICE_PACKAGE[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUNDLE_VOUCHER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{})
}

// ============================= TASK

func CreateTask(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		var service *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": v["serviceName"]}, bson.M{"_id": 1, "text": 1, "name": 1}, &service)
		if v["serviceKeyName"] != nil {
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": v["serviceKeyName"]}, bson.M{"_id": 1, "text": 1, "name": 1}, &service)
		}
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["date"] != nil {
			date := strings.Split(v["date"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			taskDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		duration := 2.0
		if v["duration"] != nil {
			value, ok := v["duration"].(int)
			if ok {
				valueStr := fmt.Sprintf("%v", value)
				duration, _ = strconv.ParseFloat(valueStr, 64)
			} else {
				duration, _ = v["duration"].(float64)
			}
		}
		cost := 200000
		if v["cost"] != nil {
			cost = v["cost"].(int)
		}
		address := "69 D1, Tân Hưng, quận 7, TPHCM"
		if v["address"] != nil {
			address = v["address"].(string)
		}
		status := globalConstant.TASK_STATUS_POSTED
		if v["status"] != nil {
			status = v["status"].(string)
		}
		rated := false
		if v["rated"] != nil {
			rated = v["rated"].(bool)
		}
		homeType := globalConstant.HOME_TYPE_HOME
		if v["homeType"] != nil {
			homeType = v["homeType"].(string)
		}
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		countryCode := globalConstant.COUNTRY_CODE_VN
		if v["countryCode"] != nil {
			countryCode = v["countryCode"].(string)
		}
		acceptedTasker := []map[string]interface{}{}
		if v["acceptedTasker"] != nil {
			acceptedTasker = v["acceptedTasker"].([]map[string]interface{})
		}
		taskItem := map[string]interface{}{
			"serviceId":   service.XId,
			"serviceText": service.Text,
			"description": v["description"],
			"date":        taskDate,
			"duration":    duration,
			"cost":        cost,
			"address":     address,
			"lat":         10.741432,
			"lng":         106.702033,
			"phone":       v["askerPhone"],
			"contactName": existAsker.Name,
			"createdAt":   createdAt,
			"askerId":     existAsker.XId,
			"status":      status,
			"rated":       rated,
			"visibility":  1,
			"payment": map[string]interface{}{
				"method": globalConstant.PAYMENT_METHOD_CASH,
			},
			"homeType":       homeType,
			"isoCode":        local.ISO_CODE,
			"acceptedTasker": acceptedTasker,
			"collectionDate": v["collectionDate"],
			"taskPlace":      v["taskPlace"],
			"viewedTaskers":  v["viewedTaskers"],
			"countryCode":    countryCode,
			"serviceName":    service.Name,
		}
		if v["isPostedBySystem"] != nil {
			taskItem["isPostedBySystem"] = v["isPostedBySystem"]
		}
		if existTasker != nil {
			taskItem["taskerId"] = existTasker.XId
		}
		taskItem["_id"] = globalLib.GenerateObjectId()
		if v["detailLaundry"] != nil {
			taskItem["detailLaundry"] = v["detailLaundry"]
		}
		if v["isCloseRating"] != nil {
			taskItem["isCloseRating"] = v["isCloseRating"]
		}
		if v["voiceCallHistory"] != nil {
			taskItem["voiceCallHistory"] = v["voiceCallHistory"]
		}
		if v["taskerRated"] != nil {
			taskItem["taskerRated"] = v["taskerRated"]
		}
		if v["detailSofa"] != nil {
			taskItem["detailSofa"] = v["detailSofa"]
		}
		if v["detailHostel"] != nil {
			taskItem["detailHostel"] = v["detailHostel"]
		}
		if v["serviceId"] != nil {
			taskItem["serviceId"] = v["serviceId"]
		}
		if v["serviceText"] != nil {
			taskItem["serviceText"] = v["serviceText"]
		}
		if v["report"] != nil {
			taskItem["report"] = v["report"]
		}
		if v["goMarketDetailTranslate"] != nil {
			taskItem["goMarketDetailTranslate"] = v["goMarketDetailTranslate"]
		}
		if v["taskNoteTranslated"] != nil {
			taskItem["taskNoteTranslated"] = v["taskNoteTranslated"]
		}
		if v["askerId"] != nil {
			taskItem["askerId"] = v["askerId"]
		}
		if v["isTaskerRefuse"] != nil {
			taskItem["isTaskerRefuse"] = v["isTaskerRefuse"]
		}
		if v["startWorking"] != nil {
			taskItem["startWorking"] = v["startWorking"]
		}
		if v["shortAddress"] != nil {
			taskItem["shortAddress"] = v["shortAddress"]
		}
		if v["payment"] != nil {
			taskItem["payment"] = v["payment"]
		}
		if v["voiceCallHistory"] != nil {
			taskItem["voiceCallHistory"] = v["voiceCallHistory"]
		}
		if v["costDetail"] != nil {
			taskItem["costDetail"] = v["costDetail"]
		}
		if v["disinfectionDetail"] != nil {
			taskItem["disinfectionDetail"] = v["disinfectionDetail"]
		}
		if v["detailSofaTH"] != nil {
			taskItem["detailSofaTH"] = v["detailSofaTH"]
		}
		if v["covidInfos"] != nil {
			taskItem["covidInfos"] = v["covidInfos"]
		}
		if v["isPremium"] != nil {
			taskItem["isPremium"] = v["isPremium"]
		}
		if v["isPrepayTask"] != nil {
			taskItem["isPrepayTask"] = v["isPrepayTask"]
		}
		if v["tip"] != nil {
			taskItem["tip"] = v["tip"]
		}
		if v["requirements"] != nil {
			taskItem["requirements"] = v["requirements"]
		}
		if v["detailDeepCleaning"] != nil {
			taskItem["detailDeepCleaning"] = v["detailDeepCleaning"]
		}
		if v["isTetBooking"] != nil {
			taskItem["isTetBooking"] = v["isTetBooking"]
		}
		if v["fromPartner"] != nil {
			taskItem["fromPartner"] = v["fromPartner"]
		}
		if v["detailChildCare"] != nil {
			taskItem["detailChildCare"] = v["detailChildCare"]
		}
		if v["newCostDetail"] != nil {
			taskItem["newCostDetail"] = v["newCostDetail"]
		}
		if v["reported"] != nil {
			taskItem["reported"] = v["reported"]
		}
		if v["contactName"] != nil {
			taskItem["contactName"] = v["contactName"]
		}
		if v["detailOfficeCleaning"] != nil {
			taskItem["detailOfficeCleaning"] = v["detailOfficeCleaning"]
		}
		if v["requestVatInfo"] != nil {
			taskItem["requestVatInfo"] = v["requestVatInfo"]
		}
		if v["detailWashingMachine"] != nil {
			taskItem["detailWashingMachine"] = v["detailWashingMachine"]
		}
		if v["detailWaterHeater"] != nil {
			taskItem["detailWaterHeater"] = v["detailWaterHeater"]
		}
		if v["cancellationFee"] != nil {
			taskItem["cancellationFee"] = v["cancellationFee"]
		}
		if v["detailHomeMoving"] != nil {
			taskItem["detailHomeMoving"] = v["detailHomeMoving"]
		}
		if v["sourceTaskId"] != nil {
			taskItem["sourceTaskId"] = v["sourceTaskId"]
		}
		if v["relatedTasks"] != nil {
			taskItem["relatedTasks"] = v["relatedTasks"]
		}
		if v["startWorking"] != nil {
			taskItem["startWorking"] = v["startWorking"]
		}
		if v["detailMassage"] != nil {
			taskItem["detailMassage"] = v["detailMassage"]
		}
		if v["subscriptionId"] != nil {
			taskItem["subscriptionId"] = v["subscriptionId"]
		}
		if v["forceTasker"] != nil {
			taskItem["forceTasker"] = v["forceTasker"]
		}
		if v["dateOptions"] != nil {
			taskItem["dateOptions"] = v["dateOptions"]
		}
		if v["detailAirConditioner"] != nil {
			taskItem["detailAirConditioner"] = v["detailAirConditioner"]
		}
		if v["scheduleId"] != nil {
			taskItem["scheduleId"] = v["scheduleId"]
		}
		if v["dateTime"] != nil {
			taskItem["date"] = v["dateTime"]
		}
		if v["changesHistory"] != nil {
			taskItem["changesHistory"] = v["changesHistory"]
		}
		if v["isEco"] != nil {
			taskItem["isEco"] = v["isEco"]
		}
		if v["originCurrency"] != nil {
			taskItem["originCurrency"] = v["originCurrency"]
		}
		if v["detailHousekeeping"] != nil {
			taskItem["detailHousekeeping"] = v["detailHousekeeping"]
		}
		if v["detailIndustrialCleaning"] != nil {
			taskItem["detailIndustrialCleaning"] = v["detailIndustrialCleaning"]
		}
		if v["detailBeautyCare"] != nil {
			taskItem["detailBeautyCare"] = v["detailBeautyCare"]
		}
		if v["todoList"] != nil {
			taskItem["todoList"] = v["todoList"]
		}
		if v["source"] != nil {
			taskItem["source"] = v["source"]
		}
		if v["timezone"] != nil {
			taskItem["timezone"] = v["timezone"]
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskItem)
		ids = append(ids, taskItem["_id"].(string))

	}
	return ids
}

// ============================= USER

func CreateUser(data []map[string]interface{}) {
	for k, v := range data {
		userId := v["phone"]
		language := "vi"
		if v["language"] != nil {
			language = v["language"].(string)
		}
		favouriteTasker := []string{}
		if v["favouriteTasker"] != nil {
			favouriteTasker = v["favouriteTasker"].([]string)
		}
		backList := []string{}
		if v["blackList"] != nil {
			backList = v["blackList"].([]string)
		}

		avgRating := 2.5
		if v["avgRating"] != nil {
			avgRating = v["avgRating"].(float64)
		}

		referralCode := fmt.Sprintf("%s%d", v["type"].(string), k)
		if v["referralCode"] != nil {
			referralCode = v["referralCode"].(string)
		}

		friendCode := fmt.Sprintf("%s%s", v["type"].(string), v["phone"].(string))
		if v["friendCode"] != nil {
			friendCode = v["friendCode"].(string)
		}

		noReceiveNotification := true
		if v["noReceiveNotification"] != nil {
			noReceiveNotification = v["noReceiveNotification"].(bool)
		}

		taskDone := 2
		if v["taskDone"] != nil {
			taskDone = v["taskDone"].(int)
		}

		createdAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		status := globalConstant.USER_STATUS_ACTIVE
		if v["status"] != nil {
			status = v["status"].(string)
		}
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = cast.ToString(v["isoCode"])
		}
		user := map[string]interface{}{
			"username":              v["phone"],
			"type":                  v["type"],
			"status":                status,
			"isoCode":               isoCode,
			"createdAt":             createdAt,
			"phone":                 v["phone"],
			"name":                  v["name"],
			"address":               "104 Mai Thi Luu.",
			"language":              language,
			"friendCode":            friendCode,
			"favouriteTasker":       favouriteTasker,
			"blackList":             backList,
			"noReceiveNotification": noReceiveNotification,
			"referralCode":          referralCode,
			"nDoneTaskInMonth":      0,
			"countryCode":           "+84",
			"avgRating":             avgRating,
			"taskDone":              taskDone,
		}
		if v["countryCode"] != nil {
			user["countryCode"] = v["countryCode"]
		}
		if v["locations"] != nil {
			user["locations"] = v["locations"]
		}
		fAccountId := globalLib.GenerateObjectId()
		if v["fAccountId"] != nil {
			fAccountId = v["fAccountId"].(string)
		} else {
			globalDataAccess.InsertOne(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[isoCode], bson.M{"_id": fAccountId, "FMainAccount": 0, "Promotion": 0})
		}
		if v["taskCancelByTasker"] != nil {
			user["taskCancelByTasker"] = v["taskCancelByTasker"]
		}
		if v["emails"] != nil {
			user["emails"] = v["emails"]
		}
		if v["idNumber"] != nil {
			user["idNumber"] = v["idNumber"]
		}
		if v["point"] != nil {
			user["point"] = v["point"]
		}
		if v["rankInfo"] != nil {
			user["rankInfo"] = v["rankInfo"]
		}
		if v["lastPostedTask"] != nil {
			user["lastPostedTask"] = v["lastPostedTask"]
		}
		if v["workingPlaces"] != nil {
			user["workingPlaces"] = v["workingPlaces"]
		}
		if v["freeSchedule"] != nil {
			user["freeSchedule"] = v["freeSchedule"]
		}
		if v["employeeIds"] != nil {
			user["employeeIds"] = v["employeeIds"]
		}
		if v["voiceCallToken"] != nil {
			user["voiceCallToken"] = v["voiceCallToken"]
		}
		if v["voiceCallTokenV2"] != nil {
			user["voiceCallTokenV2"] = v["voiceCallTokenV2"]
		}
		if v["avatar"] != nil {
			user["avatar"] = v["avatar"]
		}
		if v["badges"] != nil {
			user["badges"] = v["badges"]
		}
		if v["rankInfoByCountry"] != nil {
			user["rankInfoByCountry"] = v["rankInfoByCountry"]
		}
		if v["bPoint"] != nil {
			user["bPoint"] = v["bPoint"]
		}
		if v["hospitalLocations"] != nil {
			user["hospitalLocations"] = v["hospitalLocations"]
		}
		if v["isPremiumTasker"] != nil {
			user["isPremiumTasker"] = v["isPremiumTasker"]
		}
		if v["numberOfLuckyDraws"] != nil {
			user["numberOfLuckyDraws"] = v["numberOfLuckyDraws"]
		}
		if v["cities"] != nil {
			user["cities"] = v["cities"]
		}
		if v["isOldUser"] != nil {
			user["isOldUser"] = v["isOldUser"]
		}
		if v["gender"] != nil {
			user["gender"] = v["gender"]
		}
		if v["firstVerifyAt"] != nil {
			user["firstVerifyAt"] = v["firstVerifyAt"]
		}
		if v["totalTaskDone"] != nil {
			user["totalTaskDone"] = v["totalTaskDone"]
		}
		if v["vatInfo"] != nil {
			user["vatInfo"] = v["vatInfo"]
		}
		if v["rankInfo"] != nil {
			user["rankInfo"] = v["rankInfo"]
		}
		if v["reviewStore"] != nil {
			user["reviewStore"] = v["reviewStore"]
		}
		if v["resetRankHistory"] != nil {
			user["resetRankHistory"] = v["resetRankHistory"]
		}
		if v["favouriteServiceByCountry"] != nil {
			user["favouriteServiceByCountry"] = v["favouriteServiceByCountry"]
		}
		if v["isForcedUpdatePassword"] != nil {
			user["isForcedUpdatePassword"] = v["isForcedUpdatePassword"]
		}
		if v["services"] != nil {
			user["services"] = v["services"]
		}
		if v["homeMovingLocations"] != nil {
			user["homeMovingLocations"] = v["homeMovingLocations"]
		}
		if v["cleaningOption"] != nil {
			user["cleaningOption"] = v["cleaningOption"]
		}
		if v["isBlacklistByOps"] != nil {
			user["isBlacklistByOps"] = v["isBlacklistByOps"]
		}
		if v["isIndefiniteLocking"] != nil {
			user["isIndefiniteLocking"] = v["isIndefiniteLocking"]
		}
		if v["lastLockedAt"] != nil {
			user["lastLockedAt"] = v["lastLockedAt"]
		}
		if v["company"] != nil {
			user["company"] = v["company"]
		}
		if v["isEco"] != nil {
			user["isEco"] = v["isEco"]
		}
		if v["updatePrivacyPolicyAt"] != nil {
			user["updatePrivacyPolicyAt"] = v["updatePrivacyPolicyAt"]
		}
		if v["activatedServices"] != nil {
			user["activatedServices"] = v["activatedServices"]
		}
		if v["housekeepingLocations"] != nil {
			user["housekeepingLocations"] = v["housekeepingLocations"]
		}
		if v["blacklistAskers"] != nil {
			user["blacklistAskers"] = v["blacklistAskers"]
		}
		user["fAccountId"] = fAccountId
		user["_id"] = userId
		modelUser.InsertOne(isoCode, user)
		if v["type"].(string) == globalConstant.USER_TYPE_TASKER {
			serviceName := globalConstant.SERVICE_NAME_HOME_CLEANING
			if v["serviceName"] != nil {
				serviceName = v["serviceName"].(string)
			}
			var service *modelService.Service
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": serviceName}, bson.M{"_id": 1}, &service)
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": service.XId}, bson.M{"$push": bson.M{"taskerList": userId}})
		}
	}
}

// ============================= INCENTIVE

func CreateIncentive(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		incentiveId := globalLib.GenerateObjectId()
		incentive := v
		if incentive["_id"] == nil {
			incentive["_id"] = incentiveId
		}
		if incentive["title"] == nil {
			incentive["title"] = map[string]interface{}{
				"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
				"en": "eGift Cash - Kichi Kichi - 100.000VND",
				"ko": "현금 eGift - Kichi Kichi - 100.000VND",
			}
		}
		if incentive["exchange"] == nil {
			incentive["exchange"] = map[string]interface{}{
				"by":    "POINT",
				"point": 500,
			}
		}
		if incentive["giftInfo"] == nil {
			incentive["giftInfo"] = map[string]interface{}{
				"id":      "2412",
				"cat_id":  "10",
				"gift_id": "1014",
				"price":   "100000",
			}
		}

		createdAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		incentive["createdAt"] = createdAt
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		if v["startDate"] != nil {
			date := strings.Split(v["startDate"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			startDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		incentive["startDate"] = startDate
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 1, 0)
		if v["endDate"] != nil {
			date := strings.Split(v["endDate"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			endDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		incentive["endDate"] = endDate
		if v["codeFromPartner"] != nil {
			incentive["codeFromPartner"] = v["codeFromPartner"]
		}
		if v["status"] != nil {
			incentive["status"] = v["status"]
		} else {
			incentive["status"] = globalConstant.INCENTIVE_STATUS_ACTIVE
		}
		if v["redeemLink"] != nil {
			incentive["redeemLink"] = v["redeemLink"]
		}
		if v["content"] != nil {
			incentive["content"] = v["content"]
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], incentive)
		ids = append(ids, incentive["_id"].(string))
	}
	return ids
}

// ============================= FACCOUNT

func CreateFAccount(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= NOTIFICATION

func CreateNotification(data []map[string]interface{}) []string {
	var ids []string
	for i, v := range data {
		existUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["userPhone"]}, bson.M{"_id": 1, "name": 1})
		v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone).Add(time.Duration(-i) * time.Minute)
		v["_id"] = globalLib.GenerateObjectId()
		v["userId"] = existUser.XId
		globalDataAccess.InsertOne(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateNotificationV2(isoCode string, data []map[string]interface{}) []string {
	var ids []string
	for i, v := range data {
		existUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["userPhone"]}, bson.M{"_id": 1, "name": 1})
		if v["createdAt"] == nil {
			v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone).Add(time.Duration(-i) * time.Minute)
		}
		v["_id"] = globalLib.GenerateObjectId()
		v["userId"] = existUser.XId
		globalDataAccess.InsertOne(globalCollection.COLLECTION_NOTIFICATION[isoCode], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= FATRANSACTION

func CreateFATransaction(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		v["isoCode"] = isoCode

		if isoCode == "" {
			delete(v, "isoCode")
		}
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= Service

func GetService(query interface{}, fields interface{}) *modelService.Service {
	var service *modelService.Service
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &service)
	return service
}

// ============================= GIFT
func CreateGift(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		applyFor := make(map[string]interface{})
		if v["serviceName"] != nil {
			var service *modelService.Service
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": v["serviceName"]}, bson.M{"_id": 1, "text": 1}, &service)
			v["userId"] = existAsker.XId
			applyFor = map[string]interface{}{
				"service": []string{service.XId},
			}
		}
		expired := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["expired"] != nil {
			date := strings.Split(v["expired"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			expired = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["expired"] = expired

		createdAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		if v["applyFor.isAllUsers"] != nil {
			applyFor["isAllUsers"] = true
		}
		if v["applyFor.isApplyForAllService"] != nil {
			applyFor["isApplyForAllService"] = true
		}
		if v["cart"] != nil {
			applyFor["cart"] = true
		}
		v["applyFor"] = applyFor
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		if v["expiredIsInfinite"] != nil || v["expiredIsNil"] != nil {
			delete(v, "expired")
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_GIFT[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= TaskSchedule

func CreateTaskSchedule(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		var service *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": v["serviceName"]}, bson.M{"_id": 1, "text": 1}, &service)
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		weeklyRepeater := []int32{2, 4, 6}
		if v["weeklyRepeater"] != nil {
			weeklyRepeater = v["weeklyRepeater"].([]int32)
		}
		serviceId := service.XId
		if v["serviceId"] != nil {
			serviceId = v["serviceId"].(string)
		}
		scheduleDuration := 0
		if v["scheduleDuration"] != nil {
			scheduleDuration = v["scheduleDuration"].(int)
		}
		status := globalConstant.TASK_SCHEDULE_STATUS_ACTIVE
		if v["status"] != nil {
			status = v["status"].(string)
		}
		beginAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["beginAt"] != nil {
			date := strings.Split(v["beginAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			beginAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}

		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		scheduleTime := globalLib.GetCurrentTime(local.TimeZone)
		if v["scheduleTime"] != nil {
			date := strings.Split(v["scheduleTime"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			scheduleTime = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["askerId"] = existAsker.XId
		v["serviceId"] = serviceId
		v["beginAt"] = beginAt
		v["isoCode"] = isoCode
		v["weeklyRepeater"] = weeklyRepeater
		v["status"] = status
		v["serviceText"] = service.Text
		v["scheduleDuration"] = scheduleDuration
		v["scheduleTime"] = scheduleTime
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= PaymentCard

func CreatePaymentCard(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= Subscription

func CreateSubscription(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		var service *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": v["serviceName"]}, bson.M{"_id": 1, "text": 1}, &service)
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["userId"] = existAsker.XId
		v["serviceId"] = service.XId
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		v["isoCode"] = isoCode
		if v["schedule"] != nil {
			schedules := []time.Time{}
			for _, s := range v["schedule"].([]string) {
				date := strings.Split(s, ",")
				y, _ := strconv.Atoi(date[0])
				m, _ := strconv.Atoi(date[1])
				d, _ := strconv.Atoi(date[2])
				h, _ := strconv.Atoi(date[3])
				mi, _ := strconv.Atoi(date[4])
				schedule := time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
				schedules = append(schedules, schedule)
			}
			v["schedule"] = schedules
		}
		if v["scheduleTime"] != nil {
			v["schedule"] = v["scheduleTime"]
			delete(v, "scheduleTime")
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= Rating

func CreateRating(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["askerId"] = existAsker.XId
		v["taskerId"] = existTasker.XId
		globalDataAccess.InsertOne(globalCollection.COLLECTION_RATING[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= MarketingCampaign

func CreateMarketingCampaign(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		startDate := globalLib.GetCurrentTime(local.TimeZone)
		if v["startDate"] != nil {
			date := strings.Split(v["startDate"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			startDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 7)
		if v["endDate"] != nil {
			date := strings.Split(v["endDate"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			endDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		if v["serviceName"] != nil {
			var service *modelService.Service
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": v["serviceName"]}, bson.M{"_id": 1, "text": 1}, &service)
			v["serviceId"] = service.XId
		}
		v["createdAt"] = createdAt
		v["startDate"] = startDate
		v["endDate"] = endDate

		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreatePointTransaction(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["userId"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["userId"] = existUser.XId
		v["isoCode"] = local.ISO_CODE
		globalDataAccess.InsertOne(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreatePromotionCode(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		ids = append(ids, v["_id"].(string))
		globalDataAccess.InsertOne(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], v)
	}
	return ids
}

func CreateHistoryChatMessage(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["askerId"] = existAsker.XId
		v["askerName"] = existAsker.Name
		v["taskerId"] = existTasker.XId
		v["taskerName"] = existTasker.Name
		globalDataAccess.InsertOne(globalCollection.COLLECTION_HISTORY_CHAT_MESSAGES[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateChatMessageV2(isoCode string, data []map[string]interface{}) []string {
	var insertData []interface{}
	var ids []string
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
		ids = append(ids, v["_id"].(string))
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], insertData)
	return ids
}

func CreateMessage(isoCode string, data []map[string]interface{}) []string {
	var insertData []interface{}
	var ids []string
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
		ids = append(ids, v["_id"].(string))
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], insertData)
	return ids
}

func CreateHousekeeping(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}
func CreateUserActivation(data []interface{}) {
	globalDataAccess.InsertAll(globalCollection.COLLECTION_USER_ACTIVATION[local.ISO_CODE], data)
}

func CreateSubscriptionRequest(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})

		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["askerId"] = existAsker.XId
		v["askerName"] = existAsker.Name
		if v["taskerId"] == nil {
			v["taskerId"] = existTasker.XId
		}
		v["taskerName"] = existTasker.Name
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= RaixPushAppTokens

func CreateRaixPushAppTokens(data []map[string]interface{}) []string {
	var ids []string
	for i, v := range data {
		existUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["userPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone).Add(time.Duration(-i) * time.Minute)
		if v["enabled"] == nil {
			v["enabled"] = true
		}
		if v["appName"] == nil {
			v["appName"] = "bTaskeeAsker"
		}
		delete(v, "userPhone")
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["userId"] = existUser.XId
		globalDataAccess.InsertOne(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= REGISTER_SERVICE

func CreateRegisterService(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		v["isoCode"] = isoCode
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_REGISTER_SERVICE[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateTaskReportTasker(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for i, v := range data {
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["phone"]}, bson.M{"_id": 1, "name": 1})
		if user == nil {
			continue
		}
		id := globalLib.GenerateObjectId()
		insertD := map[string]interface{}{
			"_id":      id,
			"taskerId": user.XId,
			"phone":    user.Phone,
			"name":     user.Name,
			"toDate":   globalLib.GetCurrentTime(local.TimeZone).Add(time.Duration(i) * time.Second),
		}
		if v["numberOfDoneTask"] != nil {
			insertD["numberOfDoneTask"] = v["numberOfDoneTask"]
		}
		if v["avgRating"] != nil {
			insertD["avgRating"] = v["avgRating"]
		}
		if v["totalIncome"] != nil {
			insertD["totalIncome"] = v["totalIncome"]
		}
		if v["goodRating"] != nil {
			insertD["goodRating"] = v["goodRating"]
		}
		insertData = append(insertData, insertD)
		ids = append(ids, id)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TASK_REPORT_TASKER[local.ISO_CODE], insertData)
	return ids
}

// ============================= Reward

func CreateReward(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["userPhone"]}, bson.M{"_id": 1, "name": 1})
		isShow := true
		if v["isShow"] != nil {
			isShow = v["isShow"].(bool)
		}
		isViewed := false
		if v["isViewed"] != nil {
			isViewed = v["isViewed"].(bool)
		}
		v["isShow"] = isShow
		v["isViewed"] = isViewed
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["userId"] = existUser.XId
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_REWARD[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= TaskerTraining

func CreateTaskerTraining(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["taskerId"] = existTasker.XId
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= PurchaseOrder

func CreatePurchaseOrder(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= Transaction

func CreateTransaction(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TRANSACTION[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= TaskerTaskHistory

func CreateTaskerTaskHistory(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_TASK_HISTORY[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ============================= OutstandingPayment

func CreateOutstandingPayment(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		if v["status"] == nil {
			v["status"] = globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW
		}
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_OUTSTANDING_PAYMENT[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func GetServiceChannel(serviceName string, field interface{}) *modelServiceChannel.ServiceChannel {
	var service *modelService.Service
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": serviceName}, bson.M{"_id": 1}, &service)

	var serviceChannel *modelServiceChannel.ServiceChannel
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": service.XId}, field, &serviceChannel)
	return serviceChannel
}

// ======================= SETTINGS

func UpdateSettings(data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, data)
}

func UpdateTaskerSettings(data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, data)
}

func UpdateService(query, data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, data)
}

func UpdateWorkingPlaces(query, data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_WORKINGPLACES[local.ISO_CODE], query, data)
}

func UpdateSubscriptionSetting(data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SUBSCRIPTION_SETTINGS[local.ISO_CODE], bson.M{}, data)
}

func UpdateSettingCountry(query, data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], query, data)
}

func CreateReportTransaction(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		v["isoCode"] = isoCode
		v["_id"] = globalLib.GenerateObjectId()
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE], insertData)
}

func CreatePromotionHistory(data []map[string]interface{}) {
	for _, v := range data {
		globalDataAccess.InsertOne(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], v)
	}
}

func CreateProduct(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		v["_id"] = id
		ids = append(ids, id)
		insertData = append(insertData, v)
	}

	globalDataAccess.InsertAll(globalCollection.COLLECTION_PRODUCT_GROCERY_ASSISTANT[local.ISO_CODE], insertData)
	return ids
}

func CreateShoppingCart(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	now := globalLib.GetCurrentTime(local.TimeZone)
	for _, v := range data {
		if v["createdAt"] == nil {
			v["createdAt"] = now
		}
		if v["updatedAt"] == nil {
			v["updatedAt"] = now
		}
		if v["expiredAt"] == nil {
			v["expiredAt"] = now.AddDate(0, 0, 7)
		}
		id := globalLib.GenerateObjectId()
		v["_id"] = id
		ids = append(ids, id)
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], insertData)
	return ids
}

func CreateRefundRequest(data []map[string]interface{}) []string {
	ids := []string{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		v["_id"] = id
		ids = append(ids, id)
		globalDataAccess.InsertOne(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE], v)
	}
	return ids
}

// ==================== PROMOTION PAYMENT METHOD

func CreatePromotionPaymentMethod(data []map[string]interface{}) {
	for _, v := range data {
		globalDataAccess.InsertOne(globalCollection.COLLECTION_PROMOTION_PAYMENT_METHOD[local.ISO_CODE], v)
	}
}

func CreateStore(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		v["_id"] = id
		ids = append(ids, id)
		insertData = append(insertData, v)
	}

	globalDataAccess.InsertAll(globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE], insertData)
	return ids
}

func CreateGameCampaign(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateLuckyDraw(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		v["_id"] = globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_LUCKY_DRAW[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateTaskerYearEndReport(year int, isoCode string, data []map[string]interface{}) []string {
	insertData := []interface{}{}
	var ids []string
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			v["_id"] = globalLib.GenerateObjectId()
			ids = append(ids, v["_id"].(string))
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(fmt.Sprintf("%s%d", globalCollection.COLLECTION_TASKER_YEAR_END_REPORT[local.ISO_CODE], year), insertData)
	return ids
}

func CreateAskerYearEndReport(year int, data []map[string]interface{}) []string {
	insertData := []interface{}{}
	var ids []string
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			v["_id"] = globalLib.GenerateObjectId()
			ids = append(ids, v["_id"].(string))
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], year), insertData)
	return ids
}

func CreateVNShopeePayTransaction(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_SHOPEEPAY_TRANSACTION[local.ISO_CODE], insertData)
	return ids
}

func CreateTikiMiniAppTransaction(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		if v["_id"] != nil {
			id = v["_id"].(string)
		} else {
			v["_id"] = id
		}
		ids = append(ids, id)
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TIKI_MINI_APP_TRANSACTION, insertData)
	return ids
}

func CreateVNPayTransaction(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for _, v := range data {
		if v["_id"] == nil {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_VN_PAY_TRANSACTION, insertData)
	return ids
}

func CreatePaymentMBTransaction(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for _, v := range data {
		if v["_id"] == nil {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_PAYMENT_MB_TRANSACTION, insertData)
	return ids
}

func CreateZaloPayTransaction(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for _, v := range data {
		if v["isoCode"] == nil {
			v["isoCode"] = local.ISO_CODE
		}
		id := globalLib.GenerateObjectId()
		if v["_id"] != nil {
			id = v["_id"].(string)
		} else {
			v["_id"] = id
		}
		ids = append(ids, id)
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_ZALOPAY_TRANSACTION, insertData)
	return ids
}

func CreateMomoTransaction(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for _, v := range data {
		if v["isoCode"] == nil {
			v["isoCode"] = local.ISO_CODE
		}
		id := globalLib.GenerateObjectId()
		if v["_id"] != nil {
			id = v["_id"].(string)
		} else {
			v["_id"] = id
		}
		ids = append(ids, id)
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_MOMO_TRANSACTION, insertData)
	return ids
}

func CreateVNComboVoucher(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], insertData)
	return ids
}

func CreateVNComboVoucherTransaction(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], insertData)
	return ids
}

func CreateUserComboVoucher(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], insertData)
	return ids
}

func UpdateAskerSetting(isoCode string, data interface{}) {
	collectionName := globalCollection.COLLECTION_ASKER_SETTINGS[local.ISO_CODE]
	isExists, _ := globalDataAccess.IsExistByQuery(collectionName, bson.M{})
	if !isExists {
		globalDataAccess.InsertOne(collectionName, bson.M{"_id": "askerSetting"})
	}
	globalDataAccess.UpdateAllByQuery(collectionName, bson.M{}, data)
}

func CreatePartnerMiniAppSettings(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_PARTNER_MINI_APP_SETTINGS, insertData)
	return ids
}

func CreateAskerReferralSetting(isoCode string, data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_ASKER_REFERRAL_SETTING[local.ISO_CODE], insertData)
}

func CreateAskerReferralCampaign(isoCode string, data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_ASKER_REFERRAL_CAMPAIGN[local.ISO_CODE], insertData)
}

func CreateVNAskerFlashSaleIncentive(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_ASKER_FLASH_SALE_INCENTIVE[local.ISO_CODE], insertData)
	return ids
}

func CreateUserComplaint(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_USER_COMPLAINT[local.ISO_CODE], insertData)
	return ids
}

func CreateVNGameCampaign(data map[string]interface{}) []string {
	data["_id"] = globalLib.GenerateObjectId()
	if data["status"] == nil {
		data["status"] = globalConstant.GAME_CAMPAIGN_STATUS_ACTIVE
	}
	globalDataAccess.InsertOne(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE], data)
	return []string{data["_id"].(string)}
}

func CreateVNUserGameCampaign(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], insertData)
	return ids
}

func PushUserToTester(isoCode string, userIds []string) {
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$push": bson.M{"tester": bson.M{"$each": userIds}}})
}

func PullUserToTester(isoCode string, userIds []string) {
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$pull": bson.M{"tester": bson.M{"$in": userIds}}})
}

func CreatePaymentTransaction(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_PAYMENT_TRANSACTION, insertData)
	return ids
}

func CreateVNServiceGroup(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_SERVICE_GROUP[local.ISO_CODE], insertData)
	return ids
}

func CreateVNSurveySetting(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_SURVEY_SETTING[local.ISO_CODE], insertData)
	return ids
}

func CreateVNSurveyReport(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_SURVEY_REPORT[local.ISO_CODE], insertData)
	return ids
}

func CreateVoteFavTasker(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_VOTE_FAV_TASKER[local.ISO_CODE], insertData)
	return ids
}

func CreateChatConversation(isoCode string, data []map[string]interface{}) []string {
	var ids []string

	messagesByChatid := map[string][]map[string]interface{}{}
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt

		// Generate chatId
		chatId := globalLib.GenerateObjectId()
		ids = append(ids, chatId)
		v["_id"] = chatId

		v["askerId"] = existAsker.XId
		v["askerName"] = existAsker.Name
		v["taskerId"] = existTasker.XId
		v["taskerName"] = existTasker.Name

		// Get data messages to insert to another collection and delete from this collection (Not to insert messages to this collection)
		if v["messages"] != nil {
			for _, vMessages := range cast.ToSlice(v["messages"]) {
				message := toMap(vMessages)
				message["chatId"] = chatId

				if messagesByChatid[chatId] == nil {
					messagesByChatid[chatId] = []map[string]interface{}{}
				}
				messagesByChatid[chatId] = append(messagesByChatid[chatId], message)
			}
			delete(v, "messages")
		}

		// Insert conversation to this collection
		globalDataAccess.InsertOne(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], v)
	}

	// Insert messages to another collection
	if len(messagesByChatid) > 0 {
		for chatId, messages := range messagesByChatid {
			CreateChatMessage(chatId, messages)
		}
	}

	return ids
}

// func convert interface to map
func toMap(data interface{}) map[string]interface{} {
	var result map[string]interface{}
	b, _ := json.Marshal(data)
	json.Unmarshal(b, &result)
	return result
}

func CreateChatMessage(chatId string, data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		if v["chatId"] == nil {
			v["chatId"] = chatId
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE], insertData)
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], chatId, bson.M{"$set": bson.M{"lastMessage": data[len(data)-1]}})
}

func CreateFairyTale(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_FAIRY_TALE[local.ISO_CODE], insertData)
}

func CreatePaymentMethodCampaign(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_PAYMENT_METHOD_CAMPAIGN[local.ISO_CODE], insertData)
	return ids
}

func CreateBusiness(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], insertData)
}

func CreateBusinessLevel(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], insertData)
}

func CreateBusinessMember(data []map[string]interface{}) {
	userMember := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		userMember = append(userMember, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], userMember)
}

func CreateBusinessMemberTransactions(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], insertData)
}

func CreateBusinessTransactions(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], insertData)
}

func CreateCommunitySetting(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_COMMUNITY_SETTING[local.ISO_CODE], insertData)
}

func CreateEventConfig(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_EVENT_CONFIG[local.ISO_CODE], insertData)
	return ids
}

func CreateRedeemGiftTransaction(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], insertData)
	return ids
}

func CreateServicePackage(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_SERVICE_PACKAGE[local.ISO_CODE], insertData)
	return ids
}

func CreateBundleVoucher(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		v["isoCode"] = isoCode

		if isoCode == "" {
			delete(v, "isoCode")
		}
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_BUNDLE_VOUCHER[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateBundleVoucherHistory(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		isoCode := local.ISO_CODE
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}
		v["isoCode"] = isoCode

		if isoCode == "" {
			delete(v, "isoCode")
		}
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}
