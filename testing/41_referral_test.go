package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func Test_Referral(t *testing.T) {
	// =================== GET REFERRAL PROGRAM ================
	apiGetReferralProgram := "/api/v3/api-asker-vn/get-referral-setting"
	apiGetReferralFriends := "/api/v3/api-asker-vn/get-referral-friends"
	// validate
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetReferralProgram), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"isoCode": 1,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})

			Convey("When service handle request if isoCode is required", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if isoCode is incorrect", func() {
				body := map[string]interface{}{
					"isoCode": "ID",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				})
			})
		})
	})

	// user not login -> success get default voucher
	t.Run("2", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)

					// Gift for you
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					// Gift for your friend
					So(checkServiceText(gifts[1]["title"], "vi", "Quà dành cho bạn bè"), ShouldBeTrue)
					So(checkServiceText(gifts[1]["description"], "vi", "Nhận ngay các ưu đãi khi bạn bè đăng ký tài khoản thành công"), ShouldBeTrue)
					gifts1Vouchers := []map[string]interface{}{}
					gifts1VouchersData, _ := json.Marshal(gifts[1]["vouchers"])
					json.Unmarshal(gifts1VouchersData, &gifts1Vouchers)
					So(len(gifts1Vouchers), ShouldEqual, 1)
					So(gifts1Vouchers[0]["amount"], ShouldEqual, 1)
					So(gifts1Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts1Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is not found
	// -> Return default voucher only
	t.Run("3", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": "notfound",
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": "notfound",
						"quantity":    1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM (has ended by endDate)
	// -> Return default voucher only
	t.Run("4", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   "2022,11,02,15,15",
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{{
						"type":   "BTASKEE_VOUCHER",
						"amount": 1.0,
						"value": map[string]interface{}{
							"type":  "MONEY",
							"value": 30000.0,
						},
						"currency": map[string]interface{}{
							"sign": "đ",
							"code": "VND",
						},
						"title": map[string]interface{}{
							"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
							"en": "Discount 30,000 VND for all services of bTaskee",
							"ko": "Discount 30,000 VND for all services of bTaskee",
							"th": "Discount 30,000 VND for all services of bTaskee",
							"id": "Discount 30,000 VND for all services of bTaskee",
						},
						"note": map[string]interface{}{
							"vi": "",
							"en": "",
							"ko": "",
							"th": "",
							"id": "",
						},
						"content": map[string]interface{}{
							"vi": "",
							"en": "",
							"ko": "",
							"th": "",
							"id": "",
						},
						"description": map[string]interface{}{
							"vi": "",
							"en": "",
							"ko": "",
							"th": "",
							"id": "",
						},
						"image": map[string]interface{}{
							"vi": "",
							"en": "",
							"ko": "",
							"th": "",
							"id": "",
						},
						"brandInfo": map[string]interface{}{
							"name": "bTaskee",
							"text": map[string]interface{}{
								"vi": "bTaskee",
								"en": "bTaskee",
								"ko": "bTaskee",
								"th": "bTaskee",
								"id": "bTaskee",
							},
							"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
						},
						"source":     "SYSTEM",
						"expiryDays": 180.0,
						"expiryDate": now,
					},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (has ended by endDate)
	// -> Return default voucher only
	t.Run("5", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})
		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (has ended by Code is used up)
	// -> Return default voucher only
	t.Run("6", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"codeList": []map[string]interface{}{
					{
						"code":   "123123",
						"isUsed": true,
					},
				},
				"endDate": fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":    globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    1,
					},
				},
			},
		})
		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (not return because not have codeList)
	// -> Return default voucher only
	t.Run("7", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    1,
					},
				},
			},
		})
		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (incentive has codeList + enough code, but this bReward is not belong to referral program)
	// -> Return default voucher only
	t.Run("8", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"codeList": []map[string]interface{}{
					{"code": "123123"},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -10),
				"endDate":   now.AddDate(0, 0, -1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 1)
					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM (success)
	// -> Return default voucher + voucher from SYSTEM
	t.Run("9", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "bTaskee",
						"en": "bTaskee",
						"ko": "bTaskee",
						"th": "bTaskee",
						"id": "bTaskee",
					},
					"name": "bTaskee",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    1,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)

					// -------------------- Gift for you
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 2)

					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts0Vouchers[1]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)

					// -------------------- Gift for your friend
					So(checkServiceText(gifts[1]["title"], "vi", "Quà dành cho bạn bè"), ShouldBeTrue)
					So(checkServiceText(gifts[1]["description"], "vi", "Nhận ngay các ưu đãi khi bạn bè đăng ký tài khoản thành công"), ShouldBeTrue)
					gifts1Vouchers := []map[string]interface{}{}
					gifts1VouchersData, _ := json.Marshal(gifts[1]["vouchers"])
					json.Unmarshal(gifts1VouchersData, &gifts1Vouchers)
					So(len(gifts1Vouchers), ShouldEqual, 2)

					So(gifts1Vouchers[0]["amount"], ShouldEqual, 1)
					So(gifts1Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts1Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts1Vouchers[1]["amount"], ShouldEqual, 1)
					So(gifts1Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)
				})
			})
		})
	})

	// user not login
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (success)
	// -> Return default voucher + voucher from SYSTEM_WITH_PARTNER
	t.Run("10", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    3,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["listFriends"], ShouldBeNil)
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)

					// -------------------- Gift for you
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 2)

					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts0Vouchers[1]["amount"], ShouldEqual, 3)
					So(gifts0Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)

					// -------------------- Gift for your friend
					So(checkServiceText(gifts[1]["title"], "vi", "Quà dành cho bạn bè"), ShouldBeTrue)
					So(checkServiceText(gifts[1]["description"], "vi", "Nhận ngay các ưu đãi khi bạn bè đăng ký tài khoản thành công"), ShouldBeTrue)
					gifts1Vouchers := []map[string]interface{}{}
					gifts1VouchersData, _ := json.Marshal(gifts[1]["vouchers"])
					json.Unmarshal(gifts1VouchersData, &gifts1Vouchers)
					So(len(gifts1Vouchers), ShouldEqual, 2)

					So(gifts1Vouchers[0]["amount"], ShouldEqual, 1)
					So(gifts1Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts1Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts1Vouchers[1]["amount"], ShouldEqual, 2)
					So(gifts1Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)
				})
			})
		})
	})

	// user logged in
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (success)
	// -> Return default voucher + voucher from SYSTEM_WITH_PARTNER
	t.Run("11", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone":        "0834567880",
				"name":         "Asker 00",
				"type":         globalConstant.USER_TYPE_ASKER,
				"referralCode": "ABC123",
			},
			{
				"phone":         "0834567881",
				"name":          "Asker 01",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123",
				"totalTaskDone": 10,
				"avatar":        "http://avatar1",
			},
			{
				"phone":         "0834567882",
				"name":          "Asker 02",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123",
				"totalTaskDone": 0,
				"avatar":        "http://avatar2",
			},
			{
				"phone":         "0834567883",
				"name":          "Asker 03",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "Abc123",
				"totalTaskDone": 10,
				"avatar":        "http://avatar3",
			},
			{
				"phone":         "0834567884",
				"name":          "Asker 04",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123456",
				"totalTaskDone": 10,
				"avatar":        "http://avatar4",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			}, {
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
				"isTesting": true,
			}, {
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
				"status":    globalConstant.INCENTIVE_STATUS_INACTIVE,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    3,
					}, {
						"incentiveId": incentiveIds[1],
						"quantity":    3,
					}, {
						"incentiveId": incentiveIds[2],
						"quantity":    3,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
			"userId":  "0834567880",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)

					// -------------------- Gift for you
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 2)

					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts0Vouchers[1]["amount"], ShouldEqual, 3)
					So(gifts0Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)

					// -------------------- Gift for your friend
					So(checkServiceText(gifts[1]["title"], "vi", "Quà dành cho bạn bè"), ShouldBeTrue)
					So(checkServiceText(gifts[1]["description"], "vi", "Nhận ngay các ưu đãi khi bạn bè đăng ký tài khoản thành công"), ShouldBeTrue)
					gifts1Vouchers := []map[string]interface{}{}
					gifts1VouchersData, _ := json.Marshal(gifts[1]["vouchers"])
					json.Unmarshal(gifts1VouchersData, &gifts1Vouchers)
					So(len(gifts1Vouchers), ShouldEqual, 2)

					So(gifts1Vouchers[0]["amount"], ShouldEqual, 1)
					So(gifts1Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts1Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts1Vouchers[1]["amount"], ShouldEqual, 2)
					So(gifts1Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)
				})
			})
		})
	})

	// user logged in
	// have default voucher + bonus voucher from bReward
	// bReward is from SYSTEM_WITH_PARTNER (success)
	// -> Return default voucher + voucher from SYSTEM_WITH_PARTNER (tester)
	t.Run("12", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone":        "0834567880",
				"name":         "Asker 00",
				"type":         globalConstant.USER_TYPE_ASKER,
				"referralCode": "ABC123",
			},
			{
				"phone":         "0834567881",
				"name":          "Asker 01",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123",
				"totalTaskDone": 10,
				"avatar":        "http://avatar1",
			},
			{
				"phone":         "0834567882",
				"name":          "Asker 02",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123",
				"totalTaskDone": 0,
				"avatar":        "http://avatar2",
			},
			{
				"phone":         "0834567883",
				"name":          "Asker 03",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "Abc123",
				"totalTaskDone": 10,
				"avatar":        "http://avatar3",
			},
			{
				"phone":         "0834567884",
				"name":          "Asker 04",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123456",
				"totalTaskDone": 10,
				"avatar":        "http://avatar4",
			},
		})

		UpdateSettings(bson.M{"$push": bson.M{"tester": "0834567880"}})

		now := globalLib.GetCurrentTime(local.TimeZone)
		incentiveIds := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			}, {
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
				"isTesting": true,
			}, {
				"title": map[string]interface{}{
					"vi": "Incentive vi 1",
					"en": "Incentive vi 1",
					"ko": "Incentive vi 1",
					"th": "Incentive vi 1",
					"id": "Incentive vi 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "http://incentive",
					"text": map[string]interface{}{
						"vi": "baemin",
						"en": "baemin",
						"ko": "baemin",
						"th": "baemin",
						"id": "baemin",
					},
					"name": "baemin",
				},
				"codeList": []map[string]interface{}{
					{
						"code": "123",
					},
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", now.Year(), now.Month(), now.Day()+1),
				"from":      globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
				"status":    globalConstant.INCENTIVE_STATUS_INACTIVE,
			},
		})
		CreateAskerReferralSetting(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":   "askerReferralSettings",
				"image": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU",
				"inviter": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 2.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"invitee": map[string]interface{}{
					"defaultGifts": []map[string]interface{}{
						{
							"type":   "BTASKEE_VOUCHER",
							"amount": 1.0,
							"value": map[string]interface{}{
								"type":  "MONEY",
								"value": 30000.0,
							},
							"currency": map[string]interface{}{
								"sign": "đ",
								"code": "VND",
							},
							"title": map[string]interface{}{
								"vi": "Giảm 30,000đ cho tất cả dịch vụ của bTaskee",
								"en": "Discount 30,000 VND for all services of bTaskee",
								"ko": "Discount 30,000 VND for all services of bTaskee",
								"th": "Discount 30,000 VND for all services of bTaskee",
								"id": "Discount 30,000 VND for all services of bTaskee",
							},
							"note": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"content": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"description": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"image": map[string]interface{}{
								"vi": "",
								"en": "",
								"ko": "",
								"th": "",
								"id": "",
							},
							"brandInfo": map[string]interface{}{
								"name": "bTaskee",
								"text": map[string]interface{}{
									"vi": "bTaskee",
									"en": "bTaskee",
									"ko": "bTaskee",
									"th": "bTaskee",
									"id": "bTaskee",
								},
								"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
							},
							"source":     "SYSTEM",
							"expiryDays": 180.0,
							"expiryDate": now,
						},
					},
				},
				"createdAt": now,
			},
		})

		CreateAskerReferralCampaign(local.ISO_CODE, []map[string]interface{}{
			{
				"_id":       "x",
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 1),
				"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
				"inviter": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    3,
					},
					{
						"incentiveId": incentiveIds[1],
						"quantity":    2,
					},
					{
						"incentiveId": incentiveIds[2],
						"quantity":    3,
					},
				},
				"invitee": []map[string]interface{}{
					{
						"incentiveId": incentiveIds[0],
						"quantity":    2,
					},
				},
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
			"userId":  "0834567880",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralProgram), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralProgram, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					So(result["image"], ShouldEqual, "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR2SOcOLKLZkKeOrKxWJ1W9lDmDJXEF9aBAyg&usqp=CAU")
					So(result["gifts"], ShouldNotBeNil)
					gifts := []map[string]interface{}{}
					giftsData, _ := json.Marshal(result["gifts"])
					json.Unmarshal(giftsData, &gifts)
					So(len(gifts), ShouldEqual, 2)

					// -------------------- Gift for you
					So(checkServiceText(gifts[0]["title"], "vi", "Quà dành cho bạn"), ShouldBeTrue)
					So(checkServiceText(gifts[0]["description"], "vi", "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên."), ShouldBeTrue)
					gifts0Vouchers := []map[string]interface{}{}
					gifts0VouchersData, _ := json.Marshal(gifts[0]["vouchers"])
					json.Unmarshal(gifts0VouchersData, &gifts0Vouchers)
					So(len(gifts0Vouchers), ShouldEqual, 3)

					So(gifts0Vouchers[0]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts0Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts0Vouchers[1]["amount"], ShouldEqual, 3)
					So(gifts0Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)

					So(gifts0Vouchers[2]["amount"], ShouldEqual, 2)
					So(gifts0Vouchers[2]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[2]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[2]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)

					// -------------------- Gift for your friend
					So(checkServiceText(gifts[1]["title"], "vi", "Quà dành cho bạn bè"), ShouldBeTrue)
					So(checkServiceText(gifts[1]["description"], "vi", "Nhận ngay các ưu đãi khi bạn bè đăng ký tài khoản thành công"), ShouldBeTrue)
					gifts1Vouchers := []map[string]interface{}{}
					gifts1VouchersData, _ := json.Marshal(gifts[1]["vouchers"])
					json.Unmarshal(gifts1VouchersData, &gifts1Vouchers)
					So(len(gifts1Vouchers), ShouldEqual, 2)

					So(gifts1Vouchers[0]["amount"], ShouldEqual, 1)
					So(gifts1Vouchers[0]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[0]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "bTaskee",
						"text": map[string]interface{}{
							"vi": "bTaskee",
							"en": "bTaskee",
							"ko": "bTaskee",
							"th": "bTaskee",
							"id": "bTaskee",
						},
						"image": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/LG99yorZLWWapC8Az",
					})
					So(checkServiceText(gifts1Vouchers[0]["title"], "vi", "Giảm 30,000đ cho tất cả dịch vụ của bTaskee"), ShouldBeTrue)

					So(gifts1Vouchers[1]["amount"], ShouldEqual, 2)
					So(gifts1Vouchers[1]["brandInfo"], ShouldNotBeNil)
					So(gifts0Vouchers[1]["brandInfo"], ShouldResemble, map[string]interface{}{
						"name": "baemin",
						"text": map[string]interface{}{
							"vi": "baemin",
							"en": "baemin",
							"ko": "baemin",
							"th": "baemin",
							"id": "baemin",
						},
						"image": "http://incentive",
					})
					So(checkServiceText(gifts0Vouchers[1]["title"], "vi", "Incentive vi 1"), ShouldBeTrue)
				})
			})
		})

		UpdateSettings(bson.M{"$pull": bson.M{"tester": "0834567880"}})
	})

	// user logged in
	// -> Return list friends
	t.Run("15", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone":        "0834567880",
				"name":         "Asker 00",
				"type":         globalConstant.USER_TYPE_ASKER,
				"referralCode": "ABC123",
			},
			{
				"phone":         "0834567881",
				"name":          "Asker 01",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123",
				"totalTaskDone": 10,
				"avatar":        "http://avatar1",
			},
			{
				"phone":         "0834567882",
				"name":          "Asker 02",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123",
				"totalTaskDone": 0,
				"avatar":        "http://avatar2",
			},
			{
				"phone":         "0834567883",
				"name":          "Asker 03",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "Abc123",
				"totalTaskDone": 10,
				"avatar":        "http://avatar3",
			},
			{
				"phone":         "0834567884",
				"name":          "Asker 04",
				"type":          globalConstant.USER_TYPE_ASKER,
				"friendCode":    "ABC123456",
				"totalTaskDone": 10,
				"avatar":        "http://avatar4",
			},
		})

		body := map[string]interface{}{
			"userId": "0834567880",
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiGetReferralFriends), t, func() {
			req := httptest.NewRequest("POST", apiGetReferralFriends, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check friend
					So(result["listFriends"], ShouldNotBeNil)
					listFriends := []map[string]interface{}{}
					listFriendsData, _ := json.Marshal(result["listFriends"])
					json.Unmarshal(listFriendsData, &listFriends)
					So(len(listFriends), ShouldEqual, 3)
					for _, v := range listFriends {
						So(v["phone"], ShouldBeIn, []string{"0834567881", "0834567882", "0834567883"})
						So(v["date"], ShouldNotBeNil)
						if v["phone"] == "0834567881" {
							So(v["avatar"], ShouldEqual, "http://avatar1")
							So(v["status"], ShouldEqual, "DONE")
						}
						if v["phone"] == "0834567882" {
							So(v["avatar"], ShouldEqual, "http://avatar2")
							So(v["status"], ShouldEqual, "PROCESSING")
						}
						if v["phone"] == "0834567883" {
							So(v["avatar"], ShouldEqual, "http://avatar3")
							So(v["status"], ShouldEqual, "DONE")
						}
					}
				})
			})
		})
	})
}

func checkServiceText(text interface{}, language string, expect string) bool {
	textData, _ := json.Marshal(text)
	textMap := make(map[string]interface{})
	json.Unmarshal(textData, &textMap)
	return textMap[language] == expect
}
