/*
* @File: 30_outstandingPayment_test.go
* @Description: Handler function, case test
* @CreatedAt: 10/12/2020
* @Author: vinhnt
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

func TestOutstandingPayment(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-outstanding-payment"
		ResetData()

		log.Println("==================================== Validate GetOutstandingPayment")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"UserId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"UserId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-outstanding-payment"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_DONE,
			}, {
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		CreateOutstandingPayment([]map[string]interface{}{
			{
				"charged":       false,
				"cost":          401000,
				"currency":      "VND",
				"cardId":        "o0cj4fi9ihtktxt1e",
				"pspReference":  "2612694315845147",
				"refusalReason": "Refused",
				"askerId":       "0834567890",
				"data": map[string]interface{}{
					"taskId": taskIds[0],
				},
			}, {
				"charged":       false,
				"cost":          123123,
				"status":        globalConstant.OUTSTANDING_PAYMENT_STATUS_RECHARGING,
				"currency":      "VND",
				"cardId":        "o0cj4fi9ihtktxt1e",
				"pspReference":  "2612694315845147",
				"refusalReason": "Refused",
				"askerId":       "0834567890",
				"data": map[string]interface{}{
					"taskId": taskIds[1],
				},
			},
		})
		log.Println("==================================== DoneTaskHistoryByTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(resp, req)
			So(resp.Code, ShouldEqual, 200)

			var respResult []map[string]interface{}
			bytes, _ := io.ReadAll(resp.Body)
			json.Unmarshal(bytes, &respResult)
			So(len(respResult), ShouldEqual, 1)
			So(respResult[0]["status"], ShouldEqual, globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW)
			So(respResult[0]["charged"], ShouldBeFalse)
			So(respResult[0]["cost"], ShouldEqual, 401000)
			So(respResult[0]["askerId"], ShouldEqual, "0834567890")

			respResultMap := []map[string]map[string]interface{}{}
			json.Unmarshal(bytes, &respResultMap)
			So(respResultMap[0]["serviceText"]["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
		})
	})
}
