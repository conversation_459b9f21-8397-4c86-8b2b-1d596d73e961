/*
* @File: 24_user_test.go
* @Description: Handler function, case test
* @CreatedAt: 27/10/2020
* @Author: vinhnt
* @UpdatedAt: 29/12/2020
* @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelFinancialAccount "gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelUserLocationHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userLocationHistory"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestUser(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== GetUser")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetUser), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when expired token", func() {
				ResetData()

				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone":        "**********",
						"name":         "Asker 01",
						"type":         globalConstant.USER_TYPE_ASKER,
						"referralCode": "CODE1",
					},
				})
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 401)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TOKEN_EXPIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TOKEN_EXPIRED.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetUser")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"isPremiumTasker": true,
				"referralCode":    "CODE1",
				"isEco":           true,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					}, {
						"_id":          "678",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      "TH",
						"city":         "Hồ Chí Minh",
						"district":     "Quận Cai Bang",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      globalConstant.ISO_CODE_TH,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
						"isDefault":    true,
					}, {
						"_id":          "9874",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      "TH",
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      globalConstant.ISO_CODE_TH,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					},
				},
				"hospitalLocations": []map[string]interface{}{
					{
						"_id":          "9874",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      "TH",
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Benh vien Bach mai",
						"countryCode":  "+84",
						"isoCode":      globalConstant.ISO_CODE_TH,
						"description":  "12",
					},
					{
						"_id":          "9999",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      "TH",
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Benh vien Bach mai",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
				"homeMovingLocations": []map[string]interface{}{
					{
						"_id":          "8888",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      "TH",
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Noi di TH",
						"countryCode":  "+84",
						"isoCode":      globalConstant.ISO_CODE_TH,
						"description":  "12",
					},
					{
						"_id":          "8887",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Noi di",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
					{
						"_id":          "8886",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Noi den",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "8888",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      "TH",
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Noi di TH",
						"countryCode":  "+84",
						"isoCode":      globalConstant.ISO_CODE_TH,
						"description":  "12",
						"locationName": "Noi di TH",
						"images": []map[string]interface{}{
							{
								"name": "Phòng Đơn",
								"type": "HOTEL",
								"images": []string{
									"https://s3.aws.com/image/123",
									"https://s3.aws.com/image/456",
									"https://s3.aws.com/image/789",
								},
							},
						},
					},
					{
						"_id":          "8887",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Noi di",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
						"locationName": "Noi di",
						"images": []map[string]interface{}{
							{
								"name": "Phòng Đơn",
								"type": "HOTEL",
								"images": []string{
									"https://s3.aws.com/image/123",
									"https://s3.aws.com/image/456",
									"https://s3.aws.com/image/789",
								},
							},
						},
					},
					{
						"_id":          "8886",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận HongKong",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"phoneNumber":  "**********",
						"shortAddress": "Noi den",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
						"locationName": "Noi den",
						"images": []map[string]interface{}{
							{
								"name": "Phòng Đơn",
								"type": "HOTEL",
								"images": []string{
									"https://s3.aws.com/image/123",
									"https://s3.aws.com/image/456",
									"https://s3.aws.com/image/789",
								},
							},
						},
					},
				},
				"point": 1,
				"rankInfo": map[string]interface{}{
					"point": 1,
				},
				"lastPostedTask": globalLib.GetCurrentTime(local.TimeZone),
				"voiceCallToken": map[string]interface{}{
					"status": globalConstant.USER_STATUS_ACTIVE,
					"token":  "abcxyz123",
				},
				"voiceCallTokenV2": map[string]interface{}{
					"status": globalConstant.USER_STATUS_ACTIVE,
					"token":  "abcxyz123",
				},
				"isoCode":            local.ISO_CODE,
				"numberOfLuckyDraws": 10,
				"cities": []map[string]interface{}{
					{
						"city":    "Hồ Chí Minh",
						"country": "Việt Nam",
					}, {
						"city":    "Hà Nội",
						"country": "Việt Nam",
					},
				},
				"gender": "MALE",
				"reviewStore": map[string]interface{}{
					"count":         1,
					"isReviewStore": true,
				},
				"services": map[string]interface{}{
					"resume": map[string]interface{}{
						"loginTokens": []map[string]interface{}{
							{"hashedToken": "abcxyz123"},
						},
					},
				},
				"updatePrivacyPolicyAt": globalLib.GetCurrentTime(local.TimeZone),
				"activatedServices": []map[string]interface{}{
					{
						"serviceName": "HOME_CLEANING",
					},
				},
			}, {
				"phone":      "0834567810",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "CODE1",
			},
		})

		// now := globalLib.GetCurrentTime(local.TimeZone)
		// gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
		// 	"startDate": now.AddDate(0, 0, -1),
		// 	"endDate":   now.AddDate(0, 0, 1),
		// 	"accumulatedSpinMissions": []map[string]interface{}{
		// 		{
		// 			"action":       "LOGIN",
		// 			"numberOfSpin": 1,
		// 			"applyFor":     "NEW",
		// 		},
		// 	},
		// })

		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "**********",
				"status": "ACTIVE",
				"bPay":   5000000,
				"revokeSetting": map[string]interface{}{
					"status":    "INACTIVE",
					"isDisable": true,
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["fAccountId"], ShouldNotBeEmpty)
					So(respResult["name"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult["type"], ShouldEqual, globalConstant.USER_TYPE_ASKER)
					So(respResult["taskDone"], ShouldEqual, 2)
					So(respResult["locations"], ShouldNotBeNil)
					So(respResult["point"], ShouldEqual, 1)
					So(respResult["gender"], ShouldEqual, "MALE")
					So(respResult["rankInfo"], ShouldNotBeNil)
					So(respResult["lastPostedTask"], ShouldNotBeNil)
					So(respResultM["reviewStore"]["count"], ShouldEqual, 1)
					So(respResultM["reviewStore"]["isReviewStore"], ShouldBeTrue)
					So(respResult["voiceCallToken"], ShouldNotBeNil)
					So(respResultM["voiceCallToken"]["token"], ShouldEqual, "abcxyz123")
					So(respResult["voiceCallTokenV2"], ShouldNotBeNil)
					So(respResultM["voiceCallTokenV2"]["token"], ShouldEqual, "abcxyz123")
					So(respResult["isPremiumTasker"], ShouldBeTrue)
					So(respResult["referralCode"], ShouldNotEqual, "CODE1")
					So(respResult["isEco"], ShouldEqual, true)
					So(len(respResult["locations"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResult["locations"].([]interface{}) {
						So(v.(map[string]interface{})["_id"], ShouldBeIn, []string{"123", "456"})
						So(v.(map[string]interface{})["homeType"], ShouldEqual, globalConstant.HOME_TYPE_HOME)
					}
					So(len(respResult["cities"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResult["cities"].([]interface{}) {
						So(v.(map[string]interface{})["city"], ShouldBeIn, []string{"Hồ Chí Minh", "Hà Nội"})
						So(v.(map[string]interface{})["country"], ShouldEqual, "Việt Nam")
					}
					So(len(respResult["hospitalLocations"].([]interface{})), ShouldEqual, 1)
					v := respResult["hospitalLocations"].([]interface{})
					So(v[0].(map[string]interface{})["_id"], ShouldEqual, "9999")
					So(v[0].(map[string]interface{})["shortAddress"], ShouldEqual, "Benh vien Bach mai")
					So(v[0].(map[string]interface{})["address"], ShouldEqual, "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam")
					So(v[0].(map[string]interface{})["isoCode"], ShouldEqual, local.ISO_CODE)

					So(len(respResult["homeMovingLocations"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResult["homeMovingLocations"].([]interface{}) {
						So(v.(map[string]interface{})["_id"], ShouldBeIn, []string{"8887", "8886"})
						So(v.(map[string]interface{})["shortAddress"], ShouldBeIn, []string{"Noi den", "Noi di"})
						So(v.(map[string]interface{})["address"], ShouldEqual, "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam")
						So(v.(map[string]interface{})["isoCode"], ShouldEqual, local.ISO_CODE)
					}

					So(len(respResult["housekeepingLocations"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResult["housekeepingLocations"].([]interface{}) {
						So(v.(map[string]interface{})["_id"], ShouldBeIn, []string{"8887", "8886"})
						So(v.(map[string]interface{})["locationName"], ShouldBeIn, []string{"Noi den", "Noi di"})
						So(v.(map[string]interface{})["shortAddress"], ShouldBeIn, []string{"Noi den", "Noi di"})
						So(v.(map[string]interface{})["address"], ShouldEqual, "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam")
						So(v.(map[string]interface{})["isoCode"], ShouldEqual, local.ISO_CODE)
						for _, img := range v.(map[string]interface{})["images"].([]interface{}) {
							image := cast.ToStringMap(img)
							So(image["name"], ShouldEqual, "Phòng Đơn")
							So(image["type"], ShouldEqual, "HOTEL")
							So(len(image["images"].([]interface{})), ShouldEqual, 3)
						}
					}

					So(respResult["numberOfLuckyDraws"], ShouldEqual, 10)
					So(respResult["updatePrivacyPolicyAt"], ShouldNotBeNil)
					activatedServices := []map[string]interface{}{}
					b, _ := json.Marshal(respResult["activatedServices"])
					json.Unmarshal(b, &activatedServices)
					So(len(activatedServices), ShouldEqual, 1)
					So(activatedServices[0]["serviceName"], ShouldEqual, "HOME_CLEANING")

					time.Sleep(2 * time.Second)

					asker, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"referralCode": 1})
					So(asker.ReferralCode, ShouldEqual, respResult["referralCode"])

					askerF, _ := modelUser.GetOneById(local.ISO_CODE, "0834567810", bson.M{"friendCode": 1})
					So(askerF.FriendCode, ShouldEqual, respResult["referralCode"])

					So(respResult["business"], ShouldNotBeNil)
					So(respResultM["business"]["_id"], ShouldEqual, "**********")
					So(respResultM["business"]["bPay"], ShouldEqual, 5000000)
					So(respResultM["business"]["isDisableRevoke"], ShouldEqual, true)
					So(respResultM["business"]["joinedDate"], ShouldNotBeNil)
					So(respResult["isBusiness"], ShouldEqual, true)
					So(respResult["isBusinessMember"], ShouldEqual, false)
					// Check number of spin
					// NOTE: Ngọc comment source check này vì campaign noel 2024 không check điều kiện nữa, source bên event-vn đã được cập nhật nên test này sẽ chạy sai
					// time.Sleep(1 * time.Second)
					// var userGameCampaign *modelUserGameCampaign.UserGameCampaign
					// globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE],
					// 	bson.M{
					// 		"userId":         "**********",
					// 		"gameCampaignId": gameCampaignIds[0],
					// 	},
					// 	bson.M{
					// 		"numberOfSpin":          1,
					// 		"accumulateSpinHistory": 1,
					// 	},
					// 	&userGameCampaign,
					// )
					// So(userGameCampaign, ShouldNotBeNil)
					// So(userGameCampaign.NumberOfSpin, ShouldEqual, 1)
					// So(len(userGameCampaign.AccumulateSpinHistory), ShouldEqual, 1)
					// So(userGameCampaign.AccumulateSpinHistory[0].Action, ShouldEqual, "LOGIN")
					// So(userGameCampaign.AccumulateSpinHistory[0].CreatedAt, ShouldNotBeNil)
					// So(userGameCampaign.AccumulateSpinHistory[0].Spin, ShouldEqual, 1)
					// So(userGameCampaign.AccumulateSpinHistory[0].TaskId, ShouldEqual, "")
				})
			})

			modelUser.UpdateOneByQuery(local.ISO_CODE, bson.M{"phone": "**********"}, bson.M{"$unset": bson.M{"locations": 1}})

			Convey("Have data without locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["locations"], ShouldNotBeNil)
					So(len(respResult["locations"].([]interface{})), ShouldEqual, 0)
				})
			})
		})
	})

	t.Run("2.1", func(t *testing.T) {
		log.Println("==================================== GetUser have point but not have rankInfo")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"point":   1,
				"isoCode": local.ISO_CODE,
				"services": map[string]interface{}{
					"resume": map[string]interface{}{
						"loginTokens": []map[string]interface{}{
							{"hashedToken": "abcxyz123"},
						},
					},
				},
			},
		})

		UpdateSettings(bson.M{
			"$set": bson.M{
				"rankSetting": []map[string]interface{}{
					{
						"rankName": "MEMBER",
						"point":    0,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Thành viên",
							"en": "Member",
							"ko": "회원",
							"th": "สมาชิก",
						},
						"color": "#ff8228",
					},
					{
						"rankName": "SILVER",
						"point":    200,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Bạc",
							"en": "Silver",
							"ko": "실버",
							"th": "ระดับ Silver",
						},
						"color": "#a0a1a5",
					},
					{
						"rankName": "GOLD",
						"point":    600,
						"rate":     1.2,
						"text": map[string]interface{}{
							"vi": "Vàng",
							"en": "Gold",
							"ko": "금",
							"th": "ระดับ Gold",
						},
						"color": "#fccb2b",
					},
					{
						"rankName": "PLATINUM",
						"point":    3000,
						"rate":     1.4,
						"text": map[string]interface{}{
							"vi": "Bạch kim",
							"en": "Platinum",
							"ko": "플래티넘",
							"th": "ระดับ Platinum",
						},
						"color": "#41cbce",
					},
				},
			},
		})

		defer UpdateSettings(bson.M{"$unset": bson.M{"rankSetting": 1}})

		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["fAccountId"], ShouldNotBeEmpty)
					So(respResult["name"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult["type"], ShouldEqual, globalConstant.USER_TYPE_ASKER)
					So(respResult["point"], ShouldEqual, 1)
					rankInfo := cast.ToStringMap(respResult["rankInfo"])
					So(rankInfo["point"], ShouldEqual, 1)
					So(rankInfo["rankName"], ShouldEqual, "MEMBER")
					So(rankInfo["text"], ShouldResemble, map[string]interface{}{
						"vi": "Thành viên",
						"en": "Member",
						"ko": "회원",
						"th": "สมาชิก",
					})
				})
			})
		})
	})

	// get user return business info for user member
	t.Run("2.2", func(t *testing.T) {
		log.Println("==================================== GetUser")
		apiGetUser := "/api/v4/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
			{
				"phone":   "0834567891",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"services": map[string]interface{}{
					"resume": map[string]interface{}{
						"loginTokens": []map[string]interface{}{
							{"hashedToken": "abcxyz123"},
						},
					},
				},
			},
		})

		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "**********",
				"status": "ACTIVE",
				"name":   "Doanh nghiep",
				"bPay":   5000000,
				"revokeSetting": map[string]interface{}{
					"status":    "INACTIVE",
					"isDisable": true,
				},
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567891",
				"businessId": "**********",
				"userId":     "0834567891",
				"status":     "ACTIVE",
				"createdAt":  globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "0834567891",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)

					So(respResult["business"], ShouldNotBeNil)
					So(respResultM["business"]["_id"], ShouldEqual, "**********")
					So(respResultM["business"]["bPay"], ShouldEqual, 5000000)
					So(respResultM["business"]["isDisableRevoke"], ShouldEqual, true)
					So(respResultM["business"]["joinedDate"], ShouldNotBeNil)
					So(respResult["isBusiness"], ShouldEqual, false)
					So(respResult["isBusinessMember"], ShouldEqual, true)
					So(respResult["member"], ShouldNotBeNil)
					So(respResultM["member"]["_id"], ShouldEqual, "0834567891")
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetFavoriteTasker")
		apiGetFavoriteTasker := "/api/v3/api-asker-vn/get-favorite-tasker"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFavoriteTasker), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetFavoriteTasker")
		apiGetFavoriteTasker := "/api/v3/api-asker-vn/get-favorite-tasker"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714"},
				"blackList":       []string{"0823456716", "0823456717"},
			}, {
				"phone":           "**********",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
				"taskDone":        10,
			}, {
				"phone":           "0823456713",
				"name":            "Tasker 013",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": false,
				"taskDone":        20,
			}, {
				"phone":   "0823456714",
				"name":    "Tasker 014",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": globalConstant.ISO_CODE_TH,
			}, {
				"phone": "0823456715",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456716",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0823456717",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456715",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456716",
					}, {
						"taskerId": "0823456714",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456717",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFavoriteTasker), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult["allTaskers"]), ShouldEqual, 2)
					So(respResult["allTaskers"][1]["name"], ShouldEqual, "Tasker 01")
					So(respResult["allTaskers"][1]["_id"], ShouldEqual, "**********")
					So(respResult["allTaskers"][1]["isPremiumTasker"], ShouldBeTrue)
					So(respResult["allTaskers"][0]["name"], ShouldEqual, "Tasker 015")
					So(respResult["allTaskers"][0]["_id"], ShouldEqual, "0823456715")
					So(len(respResult["favouriteTaskers"]), ShouldEqual, 2)
					So(respResult["favouriteTaskers"][0]["name"], ShouldEqual, "Tasker 012")
					So(respResult["favouriteTaskers"][0]["_id"], ShouldEqual, "0823456710")
					So(respResult["favouriteTaskers"][0]["taskDone"], ShouldEqual, 10)
					So(respResult["favouriteTaskers"][0]["isPremiumTasker"], ShouldBeTrue)

					So(respResult["favouriteTaskers"][1]["name"], ShouldEqual, "Tasker 013")
					So(respResult["favouriteTaskers"][1]["_id"], ShouldEqual, "0823456713")
					So(respResult["favouriteTaskers"][1]["taskDone"], ShouldEqual, 20)
					So(respResult["favouriteTaskers"][1]["isPremiumTasker"], ShouldBeNil)
				})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		apiAddFavoriteTasker := "/api/v3/api-asker-vn/add-favorite-tasker"
		log.Println("==================================== AddFavoriteTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddFavoriteTasker), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":    "",
					"taskerIds": []string{"**********", "0823456710", "0823456713"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskerIds blank", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when len taskerIds = 0", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.Message)
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		apiAddFavoriteTasker := "/api/v3/api-asker-vn/add-favorite-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456710", "0823456713"},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456710",
				"name":  "Tasker 012",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456713",
				"name":  "Tasker 013",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddFavoriteTasker), t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"taskerIds": []string{"**********", "0823456710", "0823456713"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddFavoriteTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Add Favorite Tasker", func() {
					So(resp.Code, ShouldEqual, 200)
					chatConversations, _ := pkgChatMessage.GetChatConversations(local.ISO_CODE, bson.M{"askerId": "**********"}, bson.M{})
					So(len(chatConversations), ShouldEqual, 3)
					So(chatConversations[0].AskerId, ShouldEqual, "**********")
					So(chatConversations[0].TaskerId, ShouldBeIn, []string{"**********", "0823456710", "0823456713"})

					// Check notification send message
					var chatNotifications []*notification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{}, bson.M{}, &chatNotifications)
					So(len(chatNotifications), ShouldEqual, 3)
					So(chatNotifications[0].Type, ShouldEqual, 28)
					So(chatNotifications[0].Description, ShouldEqual, "Bạn có tin nhắn mới.")
					So(chatNotifications[0].NavigateTo, ShouldEqual, "Chat")
					So(chatNotifications[0].UserId, ShouldEqual, "**********")
				})

				Convey("Then check database to test Add Favorite Tasker", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{"favouriteTasker": 1, "blackList": 1})
					for _, v := range result.FavouriteTasker {
						So(v, ShouldBeIn, []string{"**********", "0823456710", "0823456713"})
					}
					for _, v := range result.BlackList {
						So(v, ShouldNotBeIn, []string{"**********", "0823456710", "0823456713"})
					}
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		apiRemoveFavoriteTasker := "/api/v3/api-asker-vn/remove-favorite-tasker"
		log.Println("==================================== RemoveFavoriteTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveFavoriteTasker), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":    "",
					"taskerIds": []string{"**********", "0823456710", "0823456713"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskerIds blank", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when len taskerIds = 0", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.Message)
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		apiRemoveFavoriteTasker := "/api/v3/api-asker-vn/remove-favorite-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713"},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456710",
				"name":  "Tasker 012",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456713",
				"name":  "Tasker 013",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveFavoriteTasker), t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"taskerIds": []string{"**********", "0823456710", "0823456713"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiRemoveFavoriteTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Remove Favorite Tasker", func() {
					So(resp.Code, ShouldEqual, 200)
				})

				Convey("Then check database to test Remove Favorite Tasker", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{"favouriteTasker": 1})
					for _, v := range result.FavouriteTasker {
						So(v, ShouldNotBeIn, []string{"**********", "0823456710", "0823456713"})
					}
				})
			})
		})
	})

	t.Run("9", func(t *testing.T) {
		apiAddBlackListTasker := "/api/v3/api-asker-vn/add-blackList-tasker"
		log.Println("==================================== AddBlackListTasker")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddBlackListTasker), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":    "",
					"taskerIds": []string{"0823456710", "0823456713"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskerIds blank", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when len taskerIds = 0", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.Message)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		apiAddBlackListTasker := "/api/v3/api-asker-vn/add-blackList-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"**********", "0823456710", "0823456713"},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456710",
				"name":  "Tasker 012",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456713",
				"name":  "Tasker 013",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddBlackListTasker), t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"taskerIds": []string{"0823456710", "0823456713"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddBlackListTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Add BlackList Tasker", func() {
					So(resp.Code, ShouldEqual, 200)
				})

				Convey("Then check database to test Add BlackList Tasker", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{"favouriteTasker": 1, "blackList": 1})
					for _, v := range result.FavouriteTasker {
						So(v, ShouldNotBeIn, []string{"0823456710", "0823456713"})
						So(v, ShouldBeIn, []string{"**********"})
					}
					for _, v := range result.BlackList {
						So(v, ShouldBeIn, []string{"0823456710", "0823456713"})
					}
				})
			})
		})
	})

	t.Run("11", func(t *testing.T) {
		apiRemoveTaskerFromBlackList := "/api/v3/api-asker-vn/remove-tasker-from-blackList"
		log.Println("==================================== RemoveTaskerFromBlackList")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveTaskerFromBlackList), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveTaskerFromBlackList, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":    "",
					"taskerIds": []string{"0823456710", "0823456713"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveTaskerFromBlackList, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskerIds blank", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveTaskerFromBlackList, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when len taskerIds = 0", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveTaskerFromBlackList, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ARRAY_TASKER_IDS_REQUIRED.Message)
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		apiRemoveTaskerFromBlackList := "/api/v3/api-asker-vn/remove-tasker-from-blackList"
		Convey("Remove Tasker From BlackList", t, func() {
			ResetData()

			//Create User
			CreateUser([]map[string]interface{}{
				{
					"phone":     "**********",
					"name":      "Asker 01",
					"type":      globalConstant.USER_TYPE_ASKER,
					"blackList": []string{"**********", "0823456710", "0823456713"},
				}, {
					"phone": "**********",
					"name":  "Tasker 01",
					"type":  globalConstant.USER_TYPE_TASKER,
				}, {
					"phone": "0823456710",
					"name":  "Tasker 012",
					"type":  globalConstant.USER_TYPE_TASKER,
				}, {
					"phone": "0823456713",
					"name":  "Tasker 013",
					"type":  globalConstant.USER_TYPE_TASKER,
				},
			})

			Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveTaskerFromBlackList), func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": []string{"0823456710", "0823456713"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveTaskerFromBlackList, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove BlackList From Tasker", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database to test Remove BlackList From Tasker", func() {
						result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{"blackList": 1})
						for _, v := range result.BlackList {
							So(v, ShouldNotBeIn, []string{"0823456710", "0823456713"})
							So(v, ShouldBeIn, []string{"**********"})
						}
					})
				})
			})
		})
	})
	t.Run("13", func(t *testing.T) {
		apiRemoveTaskerFromBlackList := "/api/v3/api-asker-vn/remove-tasker-from-blackList"
		Convey("Remove All Tasker From BlackList", t, func() {
			ResetData()

			//Create User
			CreateUser([]map[string]interface{}{
				{
					"phone":     "**********",
					"name":      "Asker 01",
					"type":      globalConstant.USER_TYPE_ASKER,
					"blackList": []string{"**********", "0823456710", "0823456713"},
				}, {
					"phone": "**********",
					"type":  globalConstant.USER_TYPE_TASKER,
				}, {
					"phone": "0823456713",
					"name":  "Tasker 013",
					"type":  globalConstant.USER_TYPE_TASKER,
				},
			})

			Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveTaskerFromBlackList), func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"taskerIds": []string{"**********", "0823456710", "0823456713"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveTaskerFromBlackList, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test Remove BlackList From Tasker", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database to test Remove BlackList From Tasker", func() {
						result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{"blackList": 1})
						//result, _ := repoUser.New().GetById("**********", bson.M{"blackList": 1})
						So(len(result.BlackList), ShouldEqual, 0)
					})
				})
			})
		})
	})

	t.Run("14", func(t *testing.T) {
		apiGetSuggestBlackListTaskers := "/api/v3/api-asker-vn/get-suggest-blacklist-taskers"
		log.Println("==================================== GetSuggestBlackListTaskers")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetSuggestBlackListTaskers), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSuggestBlackListTaskers, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSuggestBlackListTaskers, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetSuggestBlackListTaskers, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("15", func(t *testing.T) {
		apiGetSuggestBlackListTaskers := "/api/v3/api-asker-vn/get-suggest-blacklist-taskers"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"blackList":       []string{"0823456713"},
				"favouriteTasker": []string{"0823456716"},
			}, {
				"phone":           "**********",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"avgRating":       3.0,
				"isPremiumTasker": true,
				"taskDone":        1,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"avgRating":       3.5,
				"isPremiumTasker": false,
				"taskDone":        2,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"taskDone":  3,
			}, {
				"phone":     "0823456714",
				"name":      "Tasker 014",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"isoCode":   globalConstant.ISO_CODE_TH,
				"taskDone":  4,
			},
		})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
						"name":     "Kaliser",
						"avatar":   "/avatars/avatarDefault.png",
						"taskDone": 3,
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,20",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456710",
						"name":     "Kaliser",
						"avatar":   "/avatars/avatarDefault.png",
						"taskDone": 3,
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,25",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456710",
						"name":     "Kaliser",
						"avatar":   "/avatars/avatarDefault.png",
						"taskDone": 3,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetSuggestBlackListTaskers), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetSuggestBlackListTaskers, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Add Favorite Tasker", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["name"], ShouldEqual, "Tasker 012")
					So(respResult[0]["avgRating"], ShouldEqual, 3.5)
					So(respResult[0]["isPremiumTasker"], ShouldBeNil)
					So(respResult[0]["taskDone"], ShouldEqual, 2)
					So(respResult[1]["name"], ShouldEqual, "Tasker 01")
					So(respResult[1]["avgRating"], ShouldEqual, 3.0)
					So(respResult[1]["isPremiumTasker"], ShouldBeTrue)
					So(respResult[1]["taskDone"], ShouldEqual, 1)
				})
			})
		})
	})

	t.Run("16", func(t *testing.T) {
		apiGetBlackListTasker := "/api/v3/api-asker-vn/get-blacklist-taskers"
		log.Println("==================================== GetBlackListTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBlackListTasker), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetBlackListTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("17", func(t *testing.T) {
		apiGetBlackListTasker := "/api/v3/api-asker-vn/get-blacklist-taskers"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456710", "0823456713", "0823456714"},
			}, {
				"phone":     "**********",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.5,
				"taskDone":  20,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"avgRating":       3.5,
				"taskDone":        30,
				"isPremiumTasker": true,
			}, {
				"phone":           "0823456713",
				"name":            "Tasker 013",
				"type":            globalConstant.USER_TYPE_TASKER,
				"avgRating":       4.0,
				"taskDone":        20,
				"isPremiumTasker": false,
			}, {
				"phone":     "0823456714",
				"name":      "Tasker 014",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"isoCode":   globalConstant.ISO_CODE_TH,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetBlackListTasker), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetBlackListTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get BlackList Tasker", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 3)
					So(respResult[0]["name"], ShouldEqual, "Tasker 012")
					So(respResult[0]["avgRating"], ShouldEqual, 3.5)
					So(respResult[0]["taskDone"], ShouldEqual, 30)
					So(respResult[0]["isPremiumTasker"], ShouldBeTrue)
					So(respResult[1]["name"], ShouldEqual, "Tasker 013")
					So(respResult[1]["avgRating"], ShouldEqual, 4.0)
					So(respResult[1]["taskDone"], ShouldEqual, 20)
					So(respResult[1]["isPremiumTasker"], ShouldBeNil)
				})
			})
		})
	})

	t.Run("18", func(t *testing.T) {
		apiUpdateUserCountry := "/api/v3/api-asker-vn/update-user-country"
		log.Println("==================================== UpdateUserCountry")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserCountry), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": globalConstant.ISO_CODE_TH,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
	})
	t.Run("19", func(t *testing.T) {
		apiUpdateUserCountry := "/api/v3/api-asker-vn/update-user-country"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserCountry), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": globalConstant.ISO_CODE_TH,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update User Country", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update User Country", func() {
				result, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": []string{"**********", "**********"}}}, bson.M{"_id": 1, "isoCode": 1, "fAccountId": 1})
				for _, v := range result {
					if v.XId == "**********" {
						So(v.IsoCode, ShouldEqual, globalConstant.ISO_CODE_TH)
						fAccount, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[globalConstant.ISO_CODE_TH], bson.M{"_id": v.FAccountId}, bson.M{})
						So(fAccount, ShouldNotBeNil)
						So(fAccount["userId"].(string), ShouldEqual, "**********")
						So(fAccount["TH_FMainAccount"].(float64), ShouldEqual, 0)
						So(fAccount["TH_Promotion"].(float64), ShouldEqual, 0)
					} else {
						So(v.IsoCode, ShouldEqual, local.ISO_CODE)
					}
				}
			})
		})
	})

	t.Run("19.1", func(t *testing.T) {
		apiUpdateUserCountry := "/api/v3/api-asker-vn/update-user-country"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserCountry), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": globalConstant.ISO_CODE_INDO,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update User Country", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update User Country", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "isoCode": 1, "fAccountId": 1})
				So(result.IsoCode, ShouldEqual, globalConstant.ISO_CODE_INDO)
				fAccount, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[globalConstant.ISO_CODE_INDO], bson.M{"_id": result.FAccountId}, bson.M{})
				So(fAccount, ShouldNotBeNil)
				So(fAccount["userId"].(string), ShouldEqual, "**********")
				So(fAccount["ID_FMainAccount"].(float64), ShouldEqual, 0)
				So(fAccount["ID_Promotion"].(float64), ShouldEqual, 0)
			})
		})
	})

	// NOTE: Tạm thời không update sang MY được
	t.Run("19.2", func(t *testing.T) {
		apiUpdateUserCountry := "/api/v3/api-asker-vn/update-user-country"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserCountry), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": globalConstant.ISO_CODE_MY,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUpdateUserCountry, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update User Country", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)
				result := map[string]map[string]interface{}{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &result)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_COMING_SOON.ErrorCode)
			})
		})
	})

	t.Run("20", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-location-asker"
		log.Println("==================================== AddLocationUser")

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiAddLocationUser), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when lat is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          nil,
					"lng":          2,
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"homeType":     globalConstant.HOME_TYPE_HOME,
					"description":  "Test desc 1",
					"isDefault":    true,
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when phoneNumber is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"country":      local.ISO_CODE,
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"homeType":     globalConstant.HOME_TYPE_HOME,
					"description":  "Test desc 1",
					"isDefault":    true,
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when multiple fields is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isDefault":    true,
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when country is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"homeType":     globalConstant.HOME_TYPE_HOME,
					"description":  "Test desc 1",
					"isDefault":    true,
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})
		})
	})
	t.Run("21", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-location-asker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    true,
					},
				},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddLocationUser), t, func() {
			body := map[string]interface{}{
				"userId":       "**********",
				"lat":          1,
				"lng":          2,
				"country":      local.ISO_CODE,
				"city":         "Hồ Chí Minh",
				"district":     "1",
				"address":      "104 Mai Thi Luu.",
				"contact":      "Test Name",
				"phoneNumber":  "0829428713",
				"shortAddress": "104 Mai Thi Luu, HCM",
				"countryCode":  globalConstant.COUNTRY_CODE_VN,
				"homeType":     globalConstant.HOME_TYPE_HOME,
				"description":  "Test desc 1",
				"isDefault":    true,
				"isoCode":      local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Add Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "locations": 1})
				for _, v := range result.Locations {
					if v.XId != "123" && v.XId != "456" {
						So(v.Lat, ShouldEqual, 1)
						So(v.Lng, ShouldEqual, 2)
						So(v.IsDefault, ShouldBeTrue)
						So(v.Country, ShouldEqual, local.ISO_CODE)
						So(v.City, ShouldEqual, "Hồ Chí Minh")
						So(v.District, ShouldEqual, "1")
						So(v.Address, ShouldEqual, "104 Mai Thi Luu.")
						So(v.Contact, ShouldEqual, "Test Name")
						So(v.PhoneNumber, ShouldEqual, "0829428713")
						So(v.ShortAddress, ShouldEqual, "104 Mai Thi Luu, HCM")
						So(v.CountryCode, ShouldEqual, globalConstant.COUNTRY_CODE_VN)
						So(v.HomeType, ShouldEqual, globalConstant.HOME_TYPE_HOME)
						So(v.Description, ShouldEqual, "Test desc 1")
						So(v.IsoCode, ShouldEqual, local.ISO_CODE)
					} else {
						So(v.IsDefault, ShouldBeFalse)
					}
				}
			})
		})
	})

	t.Run("22", func(t *testing.T) {
		apiGetListUser := "/api/v3/api-asker-vn/get-list-users"
		Convey(fmt.Sprintf("Given a HTTP request for %s with userIds", apiGetListUser), t, func() {
			Convey("Then check the response to test Get List User if body blank", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				respResult := map[string]map[string]interface{}{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Then check the response to test Get List User if userIds empty", func() {
				body := map[string]interface{}{
					"userIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				respResult := map[string]map[string]interface{}{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("23", func(t *testing.T) {
		apiGetListUser := "/api/v3/api-asker-vn/get-list-users"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "034567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s with userIds", apiGetListUser), t, func() {
			Convey("Then check the response to test Get List User", func() {
				body := map[string]interface{}{
					"userIds": []string{"*********", "034567891"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				respResult := []map[string]interface{}{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult[0]["phone"], ShouldEqual, "*********")
				So(respResult[0]["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
				So(respResult[0]["_id"], ShouldEqual, "*********")
				So(respResult[0]["referralCode"], ShouldEqual, "ASKER0")
				So(respResult[0]["language"], ShouldEqual, globalConstant.LANG_VI)
				So(respResult[0]["fAccountId"], ShouldNotBeNil)
				So(respResult[0]["address"], ShouldEqual, "104 Mai Thi Luu.")
				So(respResult[0]["username"], ShouldEqual, "*********")
				So(respResult[0]["countryCode"], ShouldEqual, globalConstant.COUNTRY_CODE_VN)
				So(respResult[0]["isoCode"], ShouldEqual, local.ISO_CODE)
			})
		})
	})

	t.Run("24", func(t *testing.T) {
		apiUpdateUserInfo := "/api/v3/api-asker-vn/update-user-info"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserInfo), t, func() {
			Convey("Check the result to test UpdateUserInfo if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
					"name":   "Askkk",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test UpdateUserInfo", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the result to test UpdateUserInfo if name empty", func() {
				body := map[string]interface{}{
					"userId": "*********",
					"name":   "",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test UpdateUserInfo", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				})
				Convey("Then check user in database", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
					So(result.Name, ShouldNotEqual, "Askkk")
					So(result.Emails, ShouldBeNil)
				})
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 12311123123,
					"name":   "",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when EMAIL EXIST", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone": "*********",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
					}, {
						"phone": "034567880",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
						"emails": map[string]interface{}{
							"address": "<EMAIL>",
						},
					},
				})
				body := map[string]interface{}{
					"userId": "*********",
					"name":   "test name",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_EXIST.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_EMAIL_EXIST.Message)
			})
		})
	})
	t.Run("25", func(t *testing.T) {
		apiUpdateUserInfo := "/api/v3/api-asker-vn/update-user-info"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserInfo), t, func() {
			Convey("Check the result to test UpdateUserInfo", func() {
				body := map[string]interface{}{
					"userId":       "*********",
					"name":         "Askkk",
					"email":        "<EMAIL>",
					"introduction": "introduction test",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(b), ShouldEqual, "null")
				})
				Convey("Check user in database", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
					So(result.Name, ShouldEqual, "Asker 01")
					So(result.Emails[0].Address, ShouldEqual, "<EMAIL>")
				})
			})
		})
	})

	// Validate update user info when reqBody has phone number but not have countryCode
	t.Run("25.1", func(t *testing.T) {
		apiUpdateUserInfo := "/api/v3/api-asker-vn/update-user-info"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserInfo), t, func() {
			Convey("Check the result to test UpdateUserInfo", func() {
				body := map[string]interface{}{
					"userId":      "*********",
					"phone":       "034567891",
					"countryCode": "+62",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Check the response", func() {
					So(resp.Code, ShouldEqual, 500)
					result := map[string]map[string]interface{}{}
					b, _ := io.ReadAll(resp.Body)
					json.Unmarshal(b, &result)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_COUNTRY_CODE_INCORRECT.ErrorCode)

					user, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
					So(user.Name, ShouldEqual, "Asker 01")
					So(user.Emails, ShouldBeNil)
					So(user.Phone, ShouldEqual, "*********")
				})
			})
		})
	})

	// Update phone number success
	t.Run("25.2", func(t *testing.T) {
		apiUpdateUserInfo := "/api/v3/api-asker-vn/update-user-info"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserInfo), t, func() {
			Convey("Check the result to test UpdateUserInfo", func() {
				body := map[string]interface{}{
					"userId":      "*********",
					"phone":       "034567891",
					"countryCode": "+84",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Check the response", func() {
					So(resp.Code, ShouldEqual, 200)

					user, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
					So(user.Name, ShouldEqual, "Asker 01")
					So(user.Emails, ShouldBeNil)
					So(user.Phone, ShouldEqual, "034567891")
					So(user.CountryCode, ShouldEqual, "+84")
				})
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		apiUpdateUserInfo := "/api/v3/api-asker-vn/migration-user-before-login"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserInfo), t, func() {
			Convey("Check the response if body blank", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if phone empty", func() {
				body := map[string]interface{}{
					"phone":   "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
				})
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"phone": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId not found", func() {
				body := map[string]interface{}{
					"phone": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("27", func(t *testing.T) {
		apiUpdateUserInfo := "/api/v3/api-asker-vn/migration-user-before-login"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "*********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserInfo), t, func() {
			Convey("Check the response with valid body", func() {
				body := map[string]interface{}{
					"phone":       "*********",
					"isoCode":     local.ISO_CODE,
					"countryCode": "+84",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserInfo, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(b), ShouldEqual, "null")
				})

				Convey("Then check user in database", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["phone"].(string), bson.M{})
					So(result.GetUsername(), ShouldEqual, "+8434567890")
				})
			})
		})
	})

	t.Run("28", func(t *testing.T) {
		apiUpdateUserLannguage := "/api/v3/api-asker-vn/update-user-language"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserLannguage), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserLannguage, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId blank", func() {
				body := map[string]interface{}{
					"userId":   "",
					"language": globalConstant.LANG_VI,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserLannguage, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test UpdateUserLanguage", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if language blank", func() {
				body := map[string]interface{}{
					"userId":   "**********",
					"language": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserLannguage, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test UpdateUserLanguage", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_LANGUAGE_REQUIRED.ErrorCode)
				})
			})
		})
	})
	t.Run("29", func(t *testing.T) {
		apiUpdateUserLannguage := "/api/v3/api-asker-vn/update-user-language"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateUserLannguage), t, func() {
			Convey("Check the response if body is valid", func() {
				body := map[string]interface{}{
					"userId":   "**********",
					"language": "en",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateUserLannguage, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test UpdateUserLannguage", func() {
					b, _ := io.ReadAll(resp.Body)
					So(resp.Code, ShouldEqual, 200)
					So(string(b), ShouldEqual, "null")
				})

				Convey("Then check user in database", func() {
					result, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
					So(result.GetLanguage(), ShouldEqual, "en")
				})
			})
		})
	})

	t.Run("30", func(t *testing.T) {
		apiGetAllMoney := "/api/v3/api-asker-vn/get-all-money"
		Convey(fmt.Sprintf("Given a HTTP request for %s with valid params", apiGetAllMoney), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"referralCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetAllMoney, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Then check the response to test GetAllMoney with empty referralCode", func() {
				body := map[string]interface{}{
					"referralCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetAllMoney, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_REFERRAL_CODE_REQUIRED.ErrorCode)

			})
		})
	})
	t.Run("32", func(t *testing.T) {
		apiGetListUser := "/api/v3/api-asker-vn/get-user-by-phone"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListUser), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"phone": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Then check the response to test GetUserByPhone if phone empty", func() {
				body := map[string]interface{}{
					"phone":       "",
					"countryCode": globalConstant.COUNTRY_CODE_VN,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
			})
			Convey("Then check the response to test GetUserByPhone if countryCode empty", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COUNTRY_CODE_REQUIRED.ErrorCode)
			})
			Convey("Then check the response to test GetUserByPhone if countryCode invalid", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "AH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COUNTRY_CODE_INCORRECT.ErrorCode)
			})
			Convey("Then check the response to test GetUserByPhone if phone invalid", func() {
				body := map[string]interface{}{
					"phone":       "04634567890",
					"countryCode": "+84",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PHONE_INVALID.ErrorCode)
			})
		})
	})
	t.Run("33", func(t *testing.T) {
		apiGetListUser := "/api/v3/api-asker-vn/get-user-by-phone"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListUser), t, func() {
			Convey("Then check the response to test GetUserByPhone with valid params", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": globalConstant.COUNTRY_CODE_VN,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
				So(respResult["name"], ShouldEqual, "Asker 01")
				So(respResult["_id"], ShouldEqual, "**********")
				So(respResult["type"], ShouldEqual, globalConstant.USER_TYPE_ASKER)
				So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
			})
		})
	})

	t.Run("34", func(t *testing.T) {
		apiCheckUserContactV2 := "/api/v3/api-asker-vn/check-user-contact-v2"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCheckUserContactV2), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"phone": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Then check the response to test CheckUserContactV2 if body blank", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COUNTRY_CODE_REQUIRED.ErrorCode)
			})
			Convey("Then check the response to test CheckUserContactV2 if phone empty", func() {
				body := map[string]interface{}{
					"phone":       "",
					"countryCode": globalConstant.COUNTRY_CODE_VN,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
			})
			Convey("Then check the response to test CheckUserContactV2 if countryCode empty", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 400)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COUNTRY_CODE_REQUIRED.ErrorCode)
			})
			Convey("Then check the response to test CheckUserContactV2 if countryCode incorrect", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "AH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_COUNTRY_CODE_INCORRECT.ErrorCode)
			})
			Convey("Then check the response to test CheckUserContactV2 if phone invalid", func() {
				body := map[string]interface{}{
					"phone":       "04634567890",
					"countryCode": globalConstant.COUNTRY_CODE_VN,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PHONE_INVALID.ErrorCode)
			})
			Convey("Then check the response to test ERROR_ID_NUMBER_EXISTS", func() {
				ResetData()
				CreateUser([]map[string]interface{}{
					{
						"phone":    "**********",
						"name":     "Asker 01",
						"type":     globalConstant.USER_TYPE_ASKER,
						"idNumber": "123",
					},
				})

				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"id":          "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_NUMBER_EXISTS.ErrorCode)
			})
			Convey("Then check the response to test params email", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"email":       "ç$€§/azgmail.com",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_INVALID.ErrorCode)
			})
			Convey("Then check the response to test params email 2", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"email":       "abcd@gmail_yahoo.com",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_INVALID.ErrorCode)
			})
			Convey("Then check the response to test params email 3", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"email":       "abcd@",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_INVALID.ErrorCode)
			})
			Convey("Then check the response to test params email 4", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"email":       "@gmail.com",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 500)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_INVALID.ErrorCode)
			})
		})
	})
	t.Run("35", func(t *testing.T) {
		apiCheckUserContactV2 := "/api/v3/api-asker-vn/check-user-contact-v2"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		body := map[string]interface{}{
			"phone":   "**********",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/migration-user-before-login", bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()
		service.NewRouter().ServeHTTP(resp, req)
		Convey(fmt.Sprintf("Given a HTTP request for %s if body not have email", apiCheckUserContactV2), t, func() {
			Convey("Then check the response to test CheckUserContactV2 with valid params", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"email":       "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
				So(respResult["_id"], ShouldEqual, "**********")
				So(respResult["type"], ShouldEqual, globalConstant.USER_TYPE_ASKER)
			})
		})
	})
	t.Run("35.1", func(t *testing.T) {
		apiCheckUserContactV2 := "/api/v3/api-asker-vn/check-user-contact-v2"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"emails": []map[string]interface{}{
					{
						"address":  "<EMAIL>",
						"verified": true,
					},
				},
			},
		})
		body := map[string]interface{}{
			"phone":   "**********",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", "/api/v3/api-asker-vn/migration-user-before-login", bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()
		service.NewRouter().ServeHTTP(resp, req)
		Convey(fmt.Sprintf("Given a HTTP request for %s if body have email", apiCheckUserContactV2), t, func() {
			Convey("Then check the response to test CheckUserContactV2", func() {
				body := map[string]interface{}{
					"phone":       "**********",
					"countryCode": "+84",
					"email":       "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCheckUserContactV2, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				var m map[string]map[string]interface{}
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_INVALID.ErrorCode)
			})
		})
	})

	t.Run("36", func(t *testing.T) {
		apiGetListEmployeesFromTaskerID := "/api/v3/api-asker-vn/get-list-employees-from-tasker-id"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListEmployeesFromTaskerID), t, func() {
			Convey("Check the response if userIds empty", func() {
				body := map[string]interface{}{
					"userIds": []string{},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListEmployeesFromTaskerID, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if param is invalid", func() {
				body := map[string]interface{}{
					"userIds": 12123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListEmployeesFromTaskerID, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})

		})
	})
	t.Run("37", func(t *testing.T) {
		apiGetListEmployeesFromTaskerID := "/api/v3/api-asker-vn/get-list-employees-from-tasker-id"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567891",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "**********",
				"askerPhone":  "0834567891",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskerPhone": "**********",
				"askerPhone":  "0834567891",
				"rate":        2.5,
				"review":      "Làm tốt. Lịch sự",
			},
			{
				"taskerPhone": "0834567892",
				"askerPhone":  "0834567891",
				"rate":        1,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
			}, {
				"taskerPhone": "0834567892",
				"askerPhone":  "0834567891",
				"rate":        2.5,
				"review":      "Làm tốt. Lịch sự",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListEmployeesFromTaskerID), t, func() {
			Convey("Check the response with valid body", func() {
				body := map[string]interface{}{
					"userIds": []string{"**********", "0834567892"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListEmployeesFromTaskerID, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := []map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 200)
					So(m[0]["_id"], ShouldEqual, "**********")
					So(m[0]["numberOfReviews"], ShouldEqual, 2)
					So(m[0]["phone"], ShouldEqual, "**********")
					So(m[0]["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
				})
			})
		})
	})

	t.Run("40", func(t *testing.T) {
		apiSaveUserHistory := "/api/v3/api-asker-vn/set-user-location-history"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSaveUserHistory), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if Time is nil", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"time":   nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_TIME_REQUIRED.ErrorCode)
			})
			Convey("Check the response if Lat=0", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"time":   "2020-11-11T12:42:31Z",
					"lat":    0,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_LAT_LNG_REQUIRED.ErrorCode)
			})
			Convey("Check the response if Lng=0", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"time":   "2020-11-11T12:42:31Z",
					"lat":    10.769088,
					"lng":    0,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_LAT_LNG_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("41", func(t *testing.T) {
		apiSaveUserHistory := "/api/v3/api-asker-vn/set-user-location-history"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSaveUserHistory), t, func() {
			Convey("Check the response with valid body", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"time":   "2020-11-11T12:42:31Z",
					"lat":    10.769088,
					"lng":    106.659464,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
				})
				Convey("Check the database", func() {
					var result *modelUserLocationHistory.UserLocationHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE], bson.M{"userId": body["userId"].(string)}, bson.M{}, &result)
					So(result.GetHistory()[0].Lat, ShouldEqual, body["lat"])
					So(result.GetHistory()[0].Lng, ShouldEqual, body["lng"])
				})
			})
		})
	})
	t.Run("42", func(t *testing.T) {
		apiSaveUserHistory := "/api/v3/api-asker-vn/set-user-location-history"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		globalDataAccess.InsertAll(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE], []interface{}{
			map[string]interface{}{
				"_id":   "x5fab4cf3ab3f76fbd10a203a",
				"phone": "**********",
				"history": []map[string]interface{}{
					{
						"time": map[string]interface{}{
							"seconds": 1605098551,
							"nanos":   0,
						},
						"lat": 10.769088,
						"lng": 106.659464,
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSaveUserHistory), t, func() {
			Convey("Check the response with valid body", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"time":   "2020-11-11T12:42:31Z",
					"lat":    10.769088,
					"lng":    106.659464,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSaveUserHistory, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
				})
				Convey("Check the database", func() {
					var result *modelUserLocationHistory.UserLocationHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE], bson.M{"userId": body["userId"].(string)}, bson.M{}, &result)
					So(result.GetHistory()[1].Lat, ShouldEqual, body["lat"])
					So(result.GetHistory()[1].Lng, ShouldEqual, body["lng"])
				})
			})
		})
	})

	t.Run("43", func(t *testing.T) {
		apiLockAbleDoQuiz := "/api/v3/api-asker-vn/lock-able-do-quizz"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiLockAbleDoQuiz), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiLockAbleDoQuiz, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiLockAbleDoQuiz, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("44", func(t *testing.T) {
		apiLockAbleDoQuiz := "/api/v3/api-asker-vn/lock-able-do-quizz"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiLockAbleDoQuiz), t, func() {
			Convey("Check the response if user not exist", func() {
				body := map[string]interface{}{
					"userId": "12123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiLockAbleDoQuiz, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("45", func(t *testing.T) {
		apiLockAbleDoQuiz := "/api/v3/api-asker-vn/lock-able-do-quizz"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiLockAbleDoQuiz), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiLockAbleDoQuiz, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			b, _ := io.ReadAll(res.Body)
			So(res.Code, ShouldEqual, 200)
			So(string(b), ShouldEqual, "null")
			Convey("Check the response", func() {
				So(res.Code, ShouldEqual, 200)
				So(string(b), ShouldEqual, "null")
			})
			Convey("Check user in database", func() {
				user, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
				So(user.TrainingPermission.AbleDoQuizz, ShouldBeFalse)
				So(user.TrainingPermission.ResetData, ShouldBeFalse)
			})
		})
	})

	t.Run("46", func(t *testing.T) {
		apiLockResetClientData := "/api/v3/api-asker-vn/lock-reset-client-data"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiLockResetClientData), t, func() {
			Convey("Check the response can not parse params", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiLockResetClientData, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiLockResetClientData, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("47", func(t *testing.T) {
		apiLockResetClientData := "/api/v3/api-asker-vn/lock-reset-client-data"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiLockResetClientData), t, func() {
			Convey("Check the response if user not exist", func() {
				ResetData()
				body := map[string]interface{}{
					"userId": "12123123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiLockResetClientData, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("48", func(t *testing.T) {
		apiLockResetClientData := "/api/v3/api-asker-vn/lock-reset-client-data"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiLockResetClientData), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiLockResetClientData, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			b, _ := io.ReadAll(res.Body)
			Convey("Check the response", func() {
				So(res.Code, ShouldEqual, 200)
				So(string(b), ShouldEqual, "null")
			})
			Convey("Check user in database", func() {
				user, _ := modelUser.GetOneById(local.ISO_CODE, body["userId"].(string), bson.M{})
				So(user.TrainingPermission.ResetData, ShouldBeFalse)
			})
		})
	})

	t.Run("49", func(t *testing.T) {
		apiUpdateFreeSchedule := "/api/v3/api-asker-vn/update-free-schedule"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateFreeSchedule), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateFreeSchedule, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateFreeSchedule, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateFreeSchedule), t, func() {
			Convey("Check the response if FreeSchedule is nil", func() {
				body := map[string]interface{}{
					"userId":       "12123123",
					"freeSchedule": nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateFreeSchedule, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_FREE_SCHEDULE_REQUIRED.ErrorCode)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateFreeSchedule), t, func() {
			Convey("Check the response user not found", func() {
				body := map[string]interface{}{
					"userId": "12123123",
					"freeSchedule": map[int]interface{}{
						0: 1231231,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateFreeSchedule, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})
	t.Run("50", func(t *testing.T) {
		apiUpdateFreeSchedule := "/api/v3/api-asker-vn/update-free-schedule"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateFreeSchedule), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"freeSchedule": map[int]interface{}{
						0: 1231231,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateFreeSchedule, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the HTTP response", func() {
					b, _ := io.ReadAll(res.Body)
					So(res.Code, ShouldEqual, 200)
					So(string(b), ShouldEqual, "null")
				})
				Convey("Check user in database", func() {
					user, _ := modelUser.GetOneByQueryMap(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{})
					So(user["freeSchedule"], ShouldNotBeNil)
					b, _ := json.Marshal(user["freeSchedule"])
					temp := make(map[int]interface{})
					json.Unmarshal(b, &temp)
					So(temp[0], ShouldEqual, 1231231)
				})
			})
		})
	})

	t.Run("50.1", func(t *testing.T) {
		apiUpdateFreeSchedule := "/api/v3/api-asker-vn/update-free-schedule"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"freeSchedule": map[string]interface{}{
					"isTaskerUpdate": true,
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateFreeSchedule), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"freeSchedule": map[int]interface{}{
						0: 1231231,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateFreeSchedule, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				Convey("Check the HTTP response", func() {
					b, _ := io.ReadAll(res.Body)
					So(res.Code, ShouldEqual, 200)
					So(string(b), ShouldEqual, "null")
				})
				Convey("Check user in database", func() {
					user, _ := modelUser.GetOneByQueryMap(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{})
					So(user["freeSchedule"], ShouldNotBeNil)
					b, _ := json.Marshal(user["freeSchedule"])
					temp := make(map[string]interface{})
					json.Unmarshal(b, &temp)
					So(temp["isTaskerUpdate"].(bool), ShouldBeTrue)
				})
			})
		})
	})

	t.Run("51", func(t *testing.T) {
		apiURL := subPath + "/clear-wrong-account"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if phone is empty", func() {
				body := map[string]interface{}{
					"phone": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("52", func(t *testing.T) {
		apiURL := subPath + "/clear-wrong-account"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":  "**********",
				"name":   "Asker 01",
				"type":   globalConstant.USER_TYPE_TASKER,
				"status": globalConstant.USER_STATUS_INACTIVE,
			},
		})
		user, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"faccountId": 1})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response", func() {
				body := map[string]interface{}{
					"phone": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				outputUser, _ := modelUser.GetOneById(local.ISO_CODE, user.XId, bson.M{"faccountId": 1})
				So(outputUser, ShouldBeNil)
				var faccount *modelFinancialAccount.FinancialAccountVN
				globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user.FAccountId, bson.M{"_id": 1}, &faccount)
				So(faccount, ShouldBeNil)
				var fatransactinos []*modelFATransaction.FinancialAccountTransaction
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId}, bson.M{"_id": 1}, &fatransactinos)
				So(fatransactinos, ShouldBeNil)
			})
		})
	})
	t.Run("53", func(t *testing.T) {
		apiURL := subPath + "/get-cancel-task-history"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("54", func(t *testing.T) {
		apiURL := subPath + "/get-cancel-task-history"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456713"},
			}, {
				"phone":     "**********",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"taskCancelByTasker": []map[string]interface{}{
					{
						"cancelDate": globalLib.GetCurrentTime(local.TimeZone).Add(-24 * time.Hour),
					}, {
						"cancelDate": globalLib.GetCurrentTime(local.TimeZone).Add(-48 * time.Hour),
					}, {
						"cancelDate": globalLib.GetCurrentTime(local.TimeZone).Add(-30 * time.Hour),
					}, {
						"cancelDate": globalLib.GetCurrentTime(local.TimeZone).Add(-21 * time.Hour),
					},
				},
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 012",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.5,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			m := []map[string]string{}
			json.Unmarshal(b, &m)
			So(len(m), ShouldEqual, 4)
			timeFromString := func(time string) time.Time {
				return globalLib.ParseDateFromString(time, local.TimeZone)
			}
			So(timeFromString(m[0]["cancelDate"]).Sub(timeFromString(m[1]["cancelDate"])), ShouldBeGreaterThan, 0)
			So(timeFromString(m[1]["cancelDate"]).Sub(timeFromString(m[2]["cancelDate"])), ShouldBeGreaterThan, 0)
			So(timeFromString(m[2]["cancelDate"]).Sub(timeFromString(m[3]["cancelDate"])), ShouldBeGreaterThan, 0)
		})
	})

	t.Run("54.1", func(t *testing.T) {
		apiURL := subPath + "/get-cancel-task-history"
		ResetData()
		var arrTaskCancel []map[string]interface{}
		for i := 0; i < 150; i++ {
			taskCancel := make(map[string]interface{})
			taskCancel["cancelDate"] = globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -i, 0)
			arrTaskCancel = append(arrTaskCancel, taskCancel)
		}
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456713"},
			}, {
				"phone":              "**********",
				"name":               "Tasker 01",
				"type":               globalConstant.USER_TYPE_TASKER,
				"avgRating":          3.0,
				"taskCancelByTasker": arrTaskCancel,
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 012",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.5,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			m := []map[string]string{}
			json.Unmarshal(b, &m)
			So(len(m), ShouldEqual, 100)
			timeFromString := func(time string) time.Time {
				return globalLib.ParseDateFromString(time, local.TimeZone)
			}
			So(timeFromString(m[0]["cancelDate"]).Sub(timeFromString(m[1]["cancelDate"])), ShouldBeGreaterThan, 0)
			So(timeFromString(m[1]["cancelDate"]).Sub(timeFromString(m[2]["cancelDate"])), ShouldBeGreaterThan, 0)
			So(timeFromString(m[2]["cancelDate"]).Sub(timeFromString(m[3]["cancelDate"])), ShouldBeGreaterThan, 0)
		})
	})
	t.Run("57", func(t *testing.T) {
		apiURL := subPath + "/update-user-last-online-native"
		Convey(fmt.Sprintf("Given a HTTP for %s", apiURL), t, func() {
			Convey("Check the response if param is invalid", func() {
				body := map[string]interface{}{
					"userId": 1111111,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
		})
	})
	t.Run("58", func(t *testing.T) {
		apiURL := subPath + "/update-user-last-online-native"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456710", "0823456713"},
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "**********"}, bson.M{"_id": 1})
		Convey(fmt.Sprintf("Given a HTTP for %s", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": user.XId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)

				b := globalLib.GetCurrentTime(local.TimeZone).Add(-10 * time.Second)
				a := globalLib.GetCurrentTime(local.TimeZone)
				result, _ := modelUser.GetOneByQuery(
					local.ISO_CODE,
					bson.M{"_id": user.XId, "lastOnline": bson.M{"$gte": b, "$lte": a}},
					bson.M{"lastOnline": 1},
				)
				So(result, ShouldNotBeNil)
			})
		})
	})

	t.Run("59", func(t *testing.T) {
		apiURL := subPath + "/notify-incomming-call"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if param is invalid", func() {
				body := map[string]interface{}{
					"userId": 1111111,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if data is nil", func() {
				body := map[string]interface{}{
					"userId": "123123",
					"data":   nil,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 500)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
			})
			Convey("Check the response if the param is valid", func() {
				body := map[string]interface{}{
					"userId": "123123",
					"data": map[string]interface{}{
						"fromAlias": "tbns",
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
			})
		})
	})

	t.Run("60", func(t *testing.T) {
		apiURL := subPath + "/get-language-asker"
		Convey(fmt.Sprintf("Given a HTTP request for %s, validate", apiURL), t, func() {
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if params is invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})
	})
	t.Run("61", func(t *testing.T) {
		apiURL := subPath + "/get-language-asker"
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456713"},
			}, {
				"phone":     "**********",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 012",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.5,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
			},
		})
		user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "**********"}, bson.M{})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if body not have text", func() {
				body := map[string]interface{}{
					"userId": user.XId,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := ""
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 200)
				So(result, ShouldEqual, user.Language)
			})
		})
	})
	t.Run("62", func(t *testing.T) {
		apiURL := subPath + "/get-language-asker"
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456713"},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if body have text", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"text":   "Hello",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := ""
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 200)
				//TODO: Khong dung duoc key google translate
			})
		})
	})

	t.Run("63", func(t *testing.T) {
		apiURL := subPath + "/update-user-avatar"
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456713"},
			}, {
				"phone":     "**********",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 012",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.5,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if body have text", func() {
				body := map[string]interface{}{
					"userId":    "**********",
					"avatarUrl": "avatar Test",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
			})
			Convey("Check the database", func() {
				user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "**********"}, bson.M{"avatar": 1})
				So(user.Avatar, ShouldEqual, "avatar Test")
			})
		})
	})

	t.Run("63.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-user-avatar"
		log.Println("==================================== GetBlackListTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when AvatarUrl blank", func() {
				body := map[string]interface{}{
					"userId":    "123",
					"avatarUrl": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_AVATAR_URL_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_AVATAR_URL_REQUIRED.Message)
			})
		})
	})

	t.Run("64", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-location-asker"
		log.Println("==================================== Validate UpdateLocationUser")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params data is nil", func() {
				body := map[string]interface{}{
					"userId":     "**********",
					"locationId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when userId is nil", func() {
				body := map[string]interface{}{
					"locationId":   "123",
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params locationId is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("65", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-location-asker"
		log.Println("==================================== UpdateLocationUser")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    true,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":     "**********",
				"locationId": "123",
				"district":   "Quận 10",
				"city":       "Ha Noi",
				"isDefault":  true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "locations": 1})
				So(result.XId, ShouldEqual, "**********")
				So(result.Locations[0].XId, ShouldEqual, "123")
				So(result.Locations[0].District, ShouldEqual, "Quận 10")
				So(result.Locations[0].City, ShouldEqual, "Ha Noi")
				So(result.Locations[0].IsDefault, ShouldBeTrue)
				So(result.Locations[1].IsDefault, ShouldBeFalse)
			})
		})
	})

	t.Run("66", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/delete-location-asker"
		log.Println("==================================== Validate DeleteLocationUser")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params data is nil", func() {
				body := map[string]interface{}{
					"userId":     "",
					"locationId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params locationId is nil", func() {
				body := map[string]interface{}{
					"userId":     "**********",
					"locationId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("67", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/delete-location-asker"
		log.Println("==================================== DeteleLocationUser")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":     "**********",
				"locationId": "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "locations": 1})
				So(result.XId, ShouldEqual, "**********")
				So(len(result.Locations), ShouldEqual, 1)
				So(result.Locations[0].XId, ShouldEqual, "456")
			})
		})
	})

	t.Run("68", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-user-status"
		log.Println("==================================== Validate UpdateUserStatus")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params userId is blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"type":   globalConstant.USER_TYPE_ASKER,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params type is blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"type":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_TYPE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_TYPE_REQUIRED.Message)
			})

			Convey("Check request when params type is incorrect", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"type":   "abc",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_TYPE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_TYPE_INCORRECT.Message)
			})

			Convey("Check request when params status is incorrect with type ASKER", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"type":   globalConstant.USER_TYPE_ASKER,
					"status": "abc",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_STATUS_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_STATUS_INCORRECT.Message)
			})

			Convey("Check request when params status is incorrect with type TASKER", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"type":   globalConstant.USER_TYPE_TASKER,
					"status": "abc",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_STATUS_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_STATUS_INCORRECT.Message)
			})
		})
	})

	t.Run("69", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-user-status"
		log.Println("==================================== UpdateUserStatus")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"type":   globalConstant.USER_TYPE_ASKER,
				"status": globalConstant.USER_STATUS_INACTIVE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update User Status", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update User Status", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "status": 1})
				So(result.XId, ShouldEqual, "**********")
				So(result.Status, ShouldEqual, globalConstant.USER_STATUS_INACTIVE)
			})
		})
	})

	t.Run("70", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-user-status"
		log.Println("==================================== UpdateUserStatus Tasker")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567880",
				"type":   globalConstant.USER_TYPE_TASKER,
				"status": globalConstant.USER_STATUS_BLOCKED,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update User Status", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update User Status", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "0834567880", bson.M{"_id": 1, "status": 1})
				So(result.XId, ShouldEqual, "0834567880")
				So(result.Status, ShouldEqual, globalConstant.USER_STATUS_BLOCKED)
			})
		})
	})

	t.Run("71", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-number-shared"
		log.Println("==================================== Validate GetNumberShared")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params can not parse", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId is blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when user not Found", func() {
				body := map[string]interface{}{
					"userId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})
		})
	})

	t.Run("72", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-number-shared"
		log.Println("==================================== GetNumberShared")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":        "**********",
				"name":         "Asker 01",
				"type":         globalConstant.USER_TYPE_ASKER,
				"referralCode": "referralCode123",
			}, {
				"phone":      "0834567891",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "referralCode123",
			}, {
				"phone":      "0834567892",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "referralCode123",
			}, {
				"phone": "0834567893",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["total"], ShouldEqual, 2)
			})
		})
	})
	t.Run("73", func(t *testing.T) {
		log.Println("==================================== GetUser: User status Inactive")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 1,
				"rankInfo": map[string]interface{}{
					"point": 1,
				},
				"lastPostedTask": globalLib.GetCurrentTime(local.TimeZone),
				"voiceCallToken": map[string]interface{}{
					"status": globalConstant.USER_STATUS_ACTIVE,
					"token":  "abcxyz123",
				},
				"status": globalConstant.USER_STATUS_INACTIVE,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})
		})
	})
	t.Run("76", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"services": map[string]interface{}{
					"resume": map[string]interface{}{
						"loginTokens": []map[string]interface{}{
							{"hashedToken": "abcxyz123"},
						},
					},
				},
				"status": globalConstant.USER_STATUS_DISABLED,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					},
				},
				"isoCode":        local.ISO_CODE,
				"lastPostedTask": globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["fAccountId"], ShouldNotBeEmpty)
					So(respResult["name"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_DISABLED)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult["type"], ShouldEqual, globalConstant.USER_TYPE_ASKER)
				})
			})
		})
	})

	t.Run("76.1", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":  "**********",
				"name":   "Asker 01",
				"type":   globalConstant.USER_TYPE_ASKER,
				"status": globalConstant.USER_STATUS_ACTIVE,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					},
				},
				"isoCode":                local.ISO_CODE,
				"lastPostedTask":         globalLib.GetCurrentTime(local.TimeZone),
				"isForcedUpdatePassword": true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 500)
					So(respResultM["error"]["code"], ShouldEqual, lib.ERROR_FORCED_UPDATE_PASSWORD.ErrorCode)
				})
			})
		})
	})
	// Check userId from jwt header
	t.Run("76.2", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v4/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":  "**********",
				"name":   "Asker 01",
				"type":   globalConstant.USER_TYPE_ASKER,
				"status": globalConstant.USER_STATUS_ACTIVE,
				"locations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
						"isDefault":    false,
					},
				},
				"isoCode":                local.ISO_CODE,
				"lastPostedTask":         globalLib.GetCurrentTime(local.TimeZone),
				"isForcedUpdatePassword": true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
				"uid": "**********",
			})
			tokenString, _ := token.SignedString([]byte("secret"))
			req.Header.Set("Authorization", "Bearer "+tokenString)
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 500)
					So(respResultM["error"]["code"], ShouldEqual, lib.ERROR_FORCED_UPDATE_PASSWORD.ErrorCode)
				})
			})
		})
	})

	t.Run("77", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/add-favourite-services"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check the response if body blank", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response user id require", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if list service empty", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_LIST_SERVICE_REQUIRED.ErrorCode)
				})
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":              "123",
					"isoCode":             local.ISO_CODE,
					"favouriteServiceIds": []string{"123", "234", "345", "456", "567", "789", "134", "876", "8769"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_EXCEEDED_QUANTITY_OF_SERVICES.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_EXCEEDED_QUANTITY_OF_SERVICES.Message)
			})
			Convey("Check the response if update failed", func() {
				body := map[string]interface{}{
					"userId":              "123",
					"isoCode":             local.ISO_CODE,
					"favouriteServiceIds": []string{"123", "234", "345", "456", "567", "789"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
			Convey("Check the response if userId not found", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone": "**********",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
						"point": 1,
						"rankInfo": map[string]interface{}{
							"point": 1,
						},
					},
				})
				body := map[string]interface{}{
					"userId":              "**********",
					"isoCode":             local.ISO_CODE,
					"favouriteServiceIds": []string{"123", "234", "345", "456", "567", "789"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)
				asker, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"favouriteServiceByCountry": 1})
				So(asker.FavouriteServiceByCountry.VN, ShouldResemble, []string{"123", "234", "345", "456", "567", "789"})
			})
		})
	})

	// ===================== GET MEMBER INFO ================
	// Validate
	t.Run("78", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-member-info"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check the response if body blank", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if userId not found", func() {
				ResetData()
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 404)
				b, _ := io.ReadAll(resp.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})

	// Get Member Info (Case note have setting) -> Get current info only (isoCode VN)
	t.Run("79", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-member-info"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"point":   1000,
				"rankInfo": map[string]interface{}{
					"point":    200,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Bạc",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					result := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &result)

					So(result["currentPoint"], ShouldEqual, 1000)
					So(result["currentRankInfo"], ShouldNotBeNil)
					currentRankInfo := cast.ToStringMap(result["currentRankInfo"])
					So(currentRankInfo["point"], ShouldEqual, 200)
					So(currentRankInfo["rankName"], ShouldEqual, "SILVER")
					So(cast.ToStringMap(currentRankInfo["text"])["vi"], ShouldEqual, "Bạc")
					So(result["bPointNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointNote"])["vi"], ShouldEqual, "Tích lũy thêm bPoint để đổi ưu đãi không giới hạn từ bRewards.")
					So(result["bPointResetNote"], ShouldBeNil)
					So(result["nextRankInfo"], ShouldBeNil)
				})
			})
		})
	})

	// Get Member Info (isoCode VN) (Case current rank of user is max rank in setting)
	t.Run("80", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-member-info"
		ResetData()

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		expectedDate := globalLib.ParseDateFromString(fmt.Sprintf("%d-01-02", currentTime.Year()), local.TimeZone)
		UpdateSettings(bson.M{
			"$set": bson.M{
				"giftSetting.rankingCycle.expectedDate": expectedDate,
				"giftSetting.phaseResetPoint":           2,
				"rankSetting": []map[string]interface{}{
					{
						"rankName": "MEMBER",
						"point":    0,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Thành viên",
							"en": "Member",
							"ko": "회원",
							"th": "สมาชิก",
						},
						"color": "#ff8228",
					},
					{
						"rankName": "SILVER",
						"point":    200,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Bạc",
							"en": "Silver",
							"ko": "실버",
							"th": "ระดับ Silver",
						},
						"color": "#a0a1a5",
					},
					{
						"rankName": "GOLD",
						"point":    600,
						"rate":     1.2,
						"text": map[string]interface{}{
							"vi": "Vàng",
							"en": "Gold",
							"ko": "금",
							"th": "ระดับ Gold",
						},
						"color": "#fccb2b",
					},
					{
						"rankName": "PLATINUM",
						"point":    3000,
						"rate":     1.4,
						"text": map[string]interface{}{
							"vi": "Bạch kim",
							"en": "Platinum",
							"ko": "플래티넘",
							"th": "ระดับ Platinum",
						},
						"color": "#41cbce",
					},
				},
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"point":   1000,
				"rankInfo": map[string]interface{}{
					"point":    3000,
					"rankName": "PLATINUM",
					"text": map[string]interface{}{
						"vi": "Bạch kim",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					result := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &result)

					So(result["currentPoint"], ShouldEqual, 1000)
					So(result["currentRankInfo"], ShouldNotBeNil)
					currentRankInfo := cast.ToStringMap(result["currentRankInfo"])
					So(currentRankInfo["point"], ShouldEqual, 3000)
					So(currentRankInfo["rankName"], ShouldEqual, "PLATINUM")
					So(cast.ToStringMap(currentRankInfo["text"])["vi"], ShouldEqual, "Bạch kim")
					So(result["bPointNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointNote"])["vi"], ShouldEqual, "Tích lũy thêm bPoint để đổi ưu đãi không giới hạn từ bRewards.")
					So(result["bPointResetNote"], ShouldBeNil)
					So(result["nextRankInfo"], ShouldBeNil)
				})
			})
		})
	})

	// Get Member Info (isoCode VN) (Case current rank of user >= rank after last reset bPoint)
	t.Run("81", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-member-info"
		ResetData()

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		expectedDate := globalLib.ParseDateFromString(fmt.Sprintf("%d-01-02", currentTime.Year()), local.TimeZone)
		UpdateSettings(bson.M{
			"$set": bson.M{
				"giftSetting.rankingCycle.expectedDate": expectedDate,
				"giftSetting.phaseResetPoint":           2,
				"rankSetting": []map[string]interface{}{
					{
						"rankName": "MEMBER",
						"point":    0,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Thành viên",
							"en": "Member",
							"ko": "회원",
							"th": "สมาชิก",
						},
						"color": "#ff8228",
					},
					{
						"rankName": "SILVER",
						"point":    200,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Bạc",
							"en": "Silver",
							"ko": "실버",
							"th": "ระดับ Silver",
						},
						"color": "#a0a1a5",
					},
					{
						"rankName": "GOLD",
						"point":    600,
						"rate":     1.2,
						"text": map[string]interface{}{
							"vi": "Vàng",
							"en": "Gold",
							"ko": "금",
							"th": "ระดับ Gold",
						},
						"color": "#fccb2b",
					},
					{
						"rankName": "PLATINUM",
						"point":    3000,
						"rate":     1.4,
						"text": map[string]interface{}{
							"vi": "Bạch kim",
							"en": "Platinum",
							"ko": "플래티넘",
							"th": "ระดับ Platinum",
						},
						"color": "#41cbce",
					},
				},
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"point":   1000,
				"rankInfo": map[string]interface{}{
					"point":    1000,
					"rankName": "GOLD",
					"text": map[string]interface{}{
						"vi": "Vàng",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)
				UpdateSettings(bson.M{"$unset": bson.M{"giftSetting": 1, "rankSetting": 1}})

				Convey("Then check the response to test Get User by userId", func() {
					result := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &result)

					So(result["currentPoint"], ShouldEqual, 1000)

					So(result["currentRankInfo"], ShouldNotBeNil)
					currentRankInfo := cast.ToStringMap(result["currentRankInfo"])
					So(currentRankInfo["rankName"], ShouldEqual, "GOLD")
					So(currentRankInfo["point"], ShouldEqual, 1000)
					So(currentRankInfo["text"], ShouldNotBeNil)
					So(cast.ToStringMap(currentRankInfo["text"])["vi"], ShouldEqual, "Vàng")

					So(result["bPointNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointNote"])["vi"], ShouldEqual, "Tích lũy thêm 2,000 bPoint để thăng hạng Bạch kim")

					So(result["nextRankInfo"], ShouldNotBeNil)
					So(cast.ToStringMap(result["nextRankInfo"])["point"], ShouldEqual, 3000)
					So(cast.ToStringMap(result["nextRankInfo"])["rankName"], ShouldEqual, "PLATINUM")
					So(cast.ToStringMap(result["nextRankInfo"])["rate"], ShouldEqual, 1.4)
					So(cast.ToStringMap(result["nextRankInfo"])["color"], ShouldEqual, "#41cbce")
					So(cast.ToStringMap(cast.ToStringMap(result["nextRankInfo"])["text"])["vi"], ShouldEqual, "Bạch kim")

					So(result["bPointResetNote"], ShouldBeNil)
				})
			})
		})
	})

	// Get Member Info (isoCode VN) (Case current rank of user < rank after last reset bPoint)
	t.Run("82", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-member-info"
		ResetData()

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		expectedDate := globalLib.ParseDateFromString(fmt.Sprintf("%d-01-02", currentTime.Year()), local.TimeZone)
		UpdateSettings(bson.M{
			"$set": bson.M{
				"giftSetting.rankingCycle.expectedDate": expectedDate,
				"giftSetting.phaseResetPoint":           2,
				"rankSetting": []map[string]interface{}{
					{
						"rankName": "MEMBER",
						"point":    0,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Thành viên",
							"en": "Member",
							"ko": "회원",
							"th": "สมาชิก",
						},
						"color": "#ff8228",
					},
					{
						"rankName": "SILVER",
						"point":    200,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Bạc",
							"en": "Silver",
							"ko": "실버",
							"th": "ระดับ Silver",
						},
						"color": "#a0a1a5",
					},
					{
						"rankName": "GOLD",
						"point":    600,
						"rate":     1.2,
						"text": map[string]interface{}{
							"vi": "Vàng",
							"en": "Gold",
							"ko": "금",
							"th": "ระดับ Gold",
						},
						"color": "#fccb2b",
					},
					{
						"rankName": "PLATINUM",
						"point":    3000,
						"rate":     1.4,
						"text": map[string]interface{}{
							"vi": "Bạch kim",
							"en": "Platinum",
							"ko": "플래티넘",
							"th": "ระดับ Platinum",
						},
						"color": "#41cbce",
					},
				},
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"point":   500,
				"rankInfo": map[string]interface{}{
					"point":    500,
					"rankName": "GOLD",
					"text": map[string]interface{}{
						"vi": "Vàng",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)
				UpdateSettings(bson.M{"$unset": bson.M{"giftSetting": 1, "rankSetting": 1}})

				Convey("Then check the response to test Get User by userId", func() {
					result := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &result)

					So(result["currentPoint"], ShouldEqual, 500)

					So(result["currentRankInfo"], ShouldNotBeNil)
					currentRankInfo := cast.ToStringMap(result["currentRankInfo"])
					So(currentRankInfo["rankName"], ShouldEqual, "GOLD")
					So(currentRankInfo["point"], ShouldEqual, 500)
					So(currentRankInfo["text"], ShouldNotBeNil)
					So(cast.ToStringMap(currentRankInfo["text"])["vi"], ShouldEqual, "Vàng")

					So(result["bPointNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointNote"])["vi"], ShouldEqual, "Tích lũy thêm 100 bPoint để duy trì hạng hiện tại ở kỳ tiếp theo")

					So(result["nextRankInfo"], ShouldNotBeNil)
					So(cast.ToStringMap(result["nextRankInfo"])["point"], ShouldEqual, 600)
					So(cast.ToStringMap(result["nextRankInfo"])["rankName"], ShouldEqual, "GOLD")
					So(cast.ToStringMap(result["nextRankInfo"])["rate"], ShouldEqual, 1.2)
					So(cast.ToStringMap(result["nextRankInfo"])["color"], ShouldEqual, "#fccb2b")
					So(cast.ToStringMap(cast.ToStringMap(result["nextRankInfo"])["text"])["vi"], ShouldEqual, "Vàng")

					So(result["bPointResetNote"], ShouldBeNil)
				})
			})
		})
	})

	// Get Member Info (isoCode VN) (Case current rank of user < rank after last reset bPoint) (Case has bPoint will be reseted after next phase)
	t.Run("83", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-member-info"
		ResetData()

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		expectedDate := currentTime.AddDate(0, 6, 0)
		UpdateSettings(bson.M{
			"$set": bson.M{
				"giftSetting.rankingCycle.expectedDate": expectedDate,
				"giftSetting.phaseResetPoint":           2,
				"rankSetting": []map[string]interface{}{
					{
						"rankName": "MEMBER",
						"point":    0,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Thành viên",
							"en": "Member",
							"ko": "회원",
							"th": "สมาชิก",
						},
						"color": "#ff8228",
					},
					{
						"rankName": "SILVER",
						"point":    200,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Bạc",
							"en": "Silver",
							"ko": "실버",
							"th": "ระดับ Silver",
						},
						"color": "#a0a1a5",
					},
					{
						"rankName": "GOLD",
						"point":    600,
						"rate":     1.2,
						"text": map[string]interface{}{
							"vi": "Vàng",
							"en": "Gold",
							"ko": "금",
							"th": "ระดับ Gold",
						},
						"color": "#fccb2b",
					},
					{
						"rankName": "PLATINUM",
						"point":    3000,
						"rate":     1.4,
						"text": map[string]interface{}{
							"vi": "Bạch kim",
							"en": "Platinum",
							"ko": "플래티넘",
							"th": "ระดับ Platinum",
						},
						"color": "#41cbce",
					},
				},
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"point":   1000,
				"rankInfo": map[string]interface{}{
					"point":    450,
					"rankName": "GOLD",
					"text": map[string]interface{}{
						"vi": "Vàng",
					},
				},
				"resetRankHistory": []map[string]interface{}{
					{
						"point": 300,
					},
				},
			},
		})

		CreatePointTransaction([]map[string]interface{}{
			{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
				"source": map[string]interface{}{
					"name": "RESET_BPOINT_PHASE_2",
				},
				"newPoint": 700,
			},
		})

		// usedPoint = 700 + 450 - 1000 = 150
		// bPointWillBeReset = 700 - usedPoint = 550
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)
				UpdateSettings(bson.M{"$unset": bson.M{"giftSetting": 1, "rankSetting": 1}})

				Convey("Then check the response to test Get User by userId", func() {
					result := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &result)

					So(result["currentPoint"], ShouldEqual, 1000)

					So(result["currentRankInfo"], ShouldNotBeNil)
					currentRankInfo := cast.ToStringMap(result["currentRankInfo"])
					So(currentRankInfo["rankName"], ShouldEqual, "GOLD")
					So(currentRankInfo["point"], ShouldEqual, 450)
					So(currentRankInfo["text"], ShouldNotBeNil)
					So(cast.ToStringMap(currentRankInfo["text"])["vi"], ShouldEqual, "Vàng")

					So(result["bPointNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointNote"])["vi"], ShouldEqual, "Tích lũy thêm 150 bPoint để duy trì hạng hiện tại ở kỳ tiếp theo")

					So(result["nextRankInfo"], ShouldNotBeNil)
					So(cast.ToStringMap(result["nextRankInfo"])["point"], ShouldEqual, 600)
					So(cast.ToStringMap(result["nextRankInfo"])["rankName"], ShouldEqual, "GOLD")
					So(cast.ToStringMap(result["nextRankInfo"])["rate"], ShouldEqual, 1.2)
					So(cast.ToStringMap(result["nextRankInfo"])["color"], ShouldEqual, "#fccb2b")
					So(cast.ToStringMap(cast.ToStringMap(result["nextRankInfo"])["text"])["vi"], ShouldEqual, "Vàng")

					So(result["bPointResetNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointResetNote"])["vi"], ShouldEqual, fmt.Sprintf("550 bPoint sẽ hết hạn vào %s", expectedDate.Format("02/01/2006")))
				})
			})
		})
	})
	//// HOME MOVING LOCATION
	t.Run("85", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-home-moving-location"
		log.Println("==================================== Validate UpdateLocationHomeMoving")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params data is nil", func() {
				body := map[string]interface{}{
					"userId":     "**********",
					"locationId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when userId is nil", func() {
				body := map[string]interface{}{
					"locationId":   "123",
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params locationId is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("86", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-home-moving-location"
		log.Println("==================================== UpdateLocationHomeMoving")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"homeMovingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":     "**********",
				"locationId": "123",
				"district":   "Quận 10",
				"city":       "Ha Noi",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "homeMovingLocations": 1})
				So(result.XId, ShouldEqual, "**********")
				So(len(result.HomeMovingLocations), ShouldEqual, 2)
				So(result.HomeMovingLocations[0].XId, ShouldEqual, "123")
				So(result.HomeMovingLocations[0].District, ShouldEqual, "Quận 10")
				So(result.HomeMovingLocations[0].City, ShouldEqual, "Ha Noi")
			})
		})
	})

	t.Run("87", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-home-moving-location"
		log.Println("==================================== AddLocationHomeMoving")

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiAddLocationUser), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when lat is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          nil,
					"lng":          2,
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when phoneNumber is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"country":      local.ISO_CODE,
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when multiple fields is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when country is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})
		})
	})
	t.Run("88", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-home-moving-location"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"homeMovingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddLocationUser), t, func() {
			body := map[string]interface{}{
				"userId":       "**********",
				"lat":          1,
				"lng":          2,
				"country":      local.ISO_CODE,
				"city":         "Hồ Chí Minh",
				"district":     "1",
				"address":      "104 Mai Thi Luu.",
				"contact":      "Test Name",
				"phoneNumber":  "0829428713",
				"shortAddress": "104 Mai Thi Luu, HCM",
				"countryCode":  globalConstant.COUNTRY_CODE_VN,
				"description":  "Test desc 1",
				"isoCode":      local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Add Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "homeMovingLocations": 1})
				So(len(result.HomeMovingLocations), ShouldEqual, 3)
				So(result.HomeMovingLocations[2].Lat, ShouldEqual, 1)
				So(result.HomeMovingLocations[2].Lng, ShouldEqual, 2)
				So(result.HomeMovingLocations[2].Country, ShouldEqual, local.ISO_CODE)
				So(result.HomeMovingLocations[2].City, ShouldEqual, "Hồ Chí Minh")
				So(result.HomeMovingLocations[2].District, ShouldEqual, "1")
				So(result.HomeMovingLocations[2].Address, ShouldEqual, "104 Mai Thi Luu.")
				So(result.HomeMovingLocations[2].Contact, ShouldEqual, "Test Name")
				So(result.HomeMovingLocations[2].PhoneNumber, ShouldEqual, "0829428713")
				So(result.HomeMovingLocations[2].ShortAddress, ShouldEqual, "104 Mai Thi Luu, HCM")
				So(result.HomeMovingLocations[2].CountryCode, ShouldEqual, globalConstant.COUNTRY_CODE_VN)
				So(result.HomeMovingLocations[2].Description, ShouldEqual, "Test desc 1")
				So(result.HomeMovingLocations[2].IsoCode, ShouldEqual, local.ISO_CODE)
			})
		})
	})

	t.Run("89", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/delete-home-moving-location"
		log.Println("==================================== Validate DeleteLocationUser")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params data is nil", func() {
				body := map[string]interface{}{
					"userId":     "",
					"locationId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params locationId is nil", func() {
				body := map[string]interface{}{
					"userId":     "**********",
					"locationId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("90", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/delete-home-moving-location"
		log.Println("==================================== DeteleLocationUser")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"homeMovingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":     "**********",
				"locationId": "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "homeMovingLocations": 1})
				So(result.XId, ShouldEqual, "**********")
				So(len(result.HomeMovingLocations), ShouldEqual, 1)
				So(result.HomeMovingLocations[0].XId, ShouldEqual, "456")
			})
		})
	})
	t.Run("91", func(t *testing.T) {
		log.Println("==================================== GetFavoriteTaskerV2")
		apiGetFavoriteTasker := "/api/v3/api-asker-vn/get-favorite-tasker-v2"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFavoriteTasker), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("92", func(t *testing.T) {
		log.Println("==================================== GetFavoriteTaskerV2")
		apiGetFavoriteTasker := "/api/v3/api-asker-vn/get-favorite-tasker-v2"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714", "0823456715", "0823456716"},
			}, {
				"phone":           "**********",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
				"avgRating":       5.0,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
				"taskDone":        10,
				"avgRating":       5.0,
				"cleaningOption": map[string]interface{}{
					"hasCleaningKit": true, // ccdc
					"hasKitBag":      true, // balo
				},
			}, {
				"phone":           "0823456713",
				"name":            "Tasker 013",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": false,
				"taskDone":        20,
				"avgRating":       4.2,
			}, {
				"phone":   "0823456714",
				"name":    "Tasker 014",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": globalConstant.ISO_CODE_TH,
			}, {
				"phone":     "0823456715",
				"name":      "Tasker 015",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.2,
			}, {
				"phone":     "0823456716",
				"name":      "Tasker 016",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.7,
			},
			{
				"phone": "0823456717",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456713",
					},
				},
				"date": "2020,11,02,15,20",
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetFavoriteTasker), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetFavoriteTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 4)
					So(respResult[0]["name"], ShouldEqual, "Tasker 012")
					So(respResult[0]["_id"], ShouldEqual, "0823456710")
					So(respResult[0]["taskDone"], ShouldEqual, 10)
					So(respResult[0]["isPremiumTasker"], ShouldBeTrue)
					So(respResult[0]["hasCleaningKit"], ShouldBeTrue)
					So(respResult[0]["lastPostedTask"], ShouldBeNil)
					So(respResult[1]["name"], ShouldEqual, "Tasker 013")
					So(respResult[1]["_id"], ShouldEqual, "0823456713")
					So(respResult[1]["taskDone"], ShouldEqual, 20)
					So(respResult[1]["isPremiumTasker"], ShouldBeNil)
					So(respResult[1]["lastPostedTask"], ShouldNotBeNil)
					So(respResult[2]["name"], ShouldEqual, "Tasker 016")
					So(respResult[2]["_id"], ShouldEqual, "0823456716")
				})
			})
		})
	})

	t.Run("93", func(t *testing.T) {
		log.Println("==================================== GetListTaskerWorked")
		apiGetListTaskerWorked := "/api/v3/api-asker-vn/get-list-tasker-worked"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListTaskerWorked), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListTaskerWorked, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListTaskerWorked, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetListTaskerWorked, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("94", func(t *testing.T) {
		log.Println("==================================== GetListTaskerWorked")
		apiGetListTaskerWorked := "/api/v3/api-asker-vn/get-list-tasker-worked"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456710", "0823456713", "0823456714"},
				"blackList":       []string{"0823456716", "0823456717"},
			}, {
				"phone":           "**********",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"taskDone":        10,
				"isPremiumTasker": true,
			}, {
				"phone":           "0823456710",
				"name":            "Tasker 012",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
				"taskDone":        10,
			}, {
				"phone":           "0823456713",
				"name":            "Tasker 013",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": false,
				"taskDone":        20,
			}, {
				"phone":   "0823456714",
				"name":    "Tasker 014",
				"type":    globalConstant.USER_TYPE_TASKER,
				"isoCode": globalConstant.ISO_CODE_TH,
			}, {
				"phone": "0823456715",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0823456716",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0823456717",
				"name":  "Tasker 015",
				"type":  globalConstant.USER_TYPE_TASKER,
			}})

		//Create Task
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456715",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 02",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456716",
					}, {
						"taskerId": "0823456714",
					},
				},
			}, {
				"askerPhone":  "**********",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 03",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456717",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListTaskerWorked), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetListTaskerWorked, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[1]["name"], ShouldEqual, "Tasker 01")
					So(respResult[1]["_id"], ShouldEqual, "**********")
					So(respResult[1]["isPremiumTasker"], ShouldBeTrue)
					So(respResult[0]["name"], ShouldEqual, "Tasker 015")
					So(respResult[0]["_id"], ShouldEqual, "0823456715")
				})
			})
		})
	})
	// Get Member Info not have rankInfo
	t.Run("95", func(t *testing.T) {
		log.Println("==================================== GetUser status Disable")
		apiGetUser := "/api/v3/api-asker-vn/get-member-info"
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		expectedDate := globalLib.ParseDateFromString(fmt.Sprintf("%d-01-02", currentTime.Year()), local.TimeZone)
		UpdateSettings(bson.M{
			"$set": bson.M{
				"giftSetting.rankingCycle.expectedDate": expectedDate,
				"giftSetting.phaseResetPoint":           2,
				"rankSetting": []map[string]interface{}{
					{
						"rankName": "MEMBER",
						"point":    0,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Thành viên",
							"en": "Member",
							"ko": "회원",
							"th": "สมาชิก",
						},
						"color": "#ff8228",
					},
					{
						"rankName": "SILVER",
						"point":    200,
						"rate":     1,
						"text": map[string]interface{}{
							"vi": "Bạc",
							"en": "Silver",
							"ko": "실버",
							"th": "ระดับ Silver",
						},
						"color": "#a0a1a5",
					},
					{
						"rankName": "GOLD",
						"point":    600,
						"rate":     1.2,
						"text": map[string]interface{}{
							"vi": "Vàng",
							"en": "Gold",
							"ko": "금",
							"th": "ระดับ Gold",
						},
						"color": "#fccb2b",
					},
					{
						"rankName": "PLATINUM",
						"point":    3000,
						"rate":     1.4,
						"text": map[string]interface{}{
							"vi": "Bạch kim",
							"en": "Platinum",
							"ko": "플래티넘",
							"th": "ระดับ Platinum",
						},
						"color": "#41cbce",
					},
				},
			},
		})
		defer UpdateSettings(bson.M{"$unset": bson.M{"giftSetting": 1, "rankSetting": 1}})
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":   "**********",
				"name":    "Asker 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					result := make(map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &result)

					So(result["currentPoint"], ShouldEqual, 0)
					So(result["currentRankInfo"], ShouldNotBeNil)
					currentRankInfo := cast.ToStringMap(result["currentRankInfo"])
					So(currentRankInfo["rankName"], ShouldEqual, "MEMBER")
					So(cast.ToStringMap(currentRankInfo["text"])["vi"], ShouldEqual, "Thành viên")
					So(result["bPointNote"], ShouldNotBeNil)
					So(cast.ToStringMap(result["bPointNote"])["vi"], ShouldEqual, "Tích lũy thêm 200 bPoint để thăng hạng Bạc")
					So(result["bPointResetNote"], ShouldBeNil)
					So(result["nextRankInfo"], ShouldNotBeNil)
					nextRankInfo := cast.ToStringMap(result["nextRankInfo"])
					So(nextRankInfo["rankName"], ShouldEqual, "SILVER")
					So(cast.ToStringMap(nextRankInfo["text"])["vi"], ShouldEqual, "Bạc")
				})
			})
		})
	})
	//// HOUSEKEEPING LOCATION
	t.Run("96", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-housekeeping-location"
		log.Println("==================================== Validate UpdateLocationHousekeeping")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params data is nil", func() {
				body := map[string]interface{}{
					"userId":     "**********",
					"locationId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_INVALID.Message)
			})

			Convey("Check request when userId is nil", func() {
				body := map[string]interface{}{
					"locationId":   "123",
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params locationId is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("97", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/update-housekeeping-location"
		log.Println("==================================== UpdateLocationHousekeeping")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":     "**********",
				"locationId": "123",
				"district":   "Quận 10",
				"city":       "Ha Noi",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "housekeepingLocations": 1})
				So(result.XId, ShouldEqual, "**********")
				So(len(result.HousekeepingLocations), ShouldEqual, 2)
				So(result.HousekeepingLocations[0].XId, ShouldEqual, "123")
				So(result.HousekeepingLocations[0].District, ShouldEqual, "Quận 10")
				So(result.HousekeepingLocations[0].City, ShouldEqual, "Ha Noi")
			})
		})
	})

	t.Run("98", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-housekeeping-location"
		log.Println("==================================== AddLocationHousekeeping")

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiAddLocationUser), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when lat is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          nil,
					"lng":          2,
					"country":      local.ISO_CODE,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.Message)
			})

			Convey("Check request when phoneNumber is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"country":      local.ISO_CODE,
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.Message)
			})

			Convey("Check request when multiple fields is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.Message)
			})

			Convey("Check request when country is nil", func() {
				body := map[string]interface{}{
					"userId":       "**********",
					"lat":          1,
					"lng":          2,
					"city":         "Hồ Chí Minh",
					"district":     "1",
					"address":      "104 Mai Thi Luu.",
					"contact":      "Test Name",
					"phoneNumber":  "0829428713",
					"shortAddress": "104 Mai Thi Luu, HCM",
					"countryCode":  globalConstant.COUNTRY_CODE_VN,
					"description":  "Test desc 1",
					"isoCode":      local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_INFO_INVALID.Message)
			})
		})
	})
	t.Run("99", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-housekeeping-location"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddLocationUser), t, func() {
			body := map[string]interface{}{
				"userId":       "**********",
				"lat":          1,
				"lng":          2,
				"country":      local.ISO_CODE,
				"city":         "Hồ Chí Minh",
				"district":     "1",
				"address":      "104 Mai Thi Luu.",
				"contact":      "Test Name",
				"phoneNumber":  "0829428713",
				"shortAddress": "104 Mai Thi Luu, HCM",
				"countryCode":  globalConstant.COUNTRY_CODE_VN,
				"description":  "Test desc 1",
				"isoCode":      local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Add Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "housekeepingLocations": 1})
				So(len(result.HousekeepingLocations), ShouldEqual, 2)
				So(result.HousekeepingLocations[1].Lat, ShouldEqual, 1)
				So(result.HousekeepingLocations[1].Lng, ShouldEqual, 2)
				So(result.HousekeepingLocations[1].Country, ShouldEqual, local.ISO_CODE)
				So(result.HousekeepingLocations[1].City, ShouldEqual, "Hồ Chí Minh")
				So(result.HousekeepingLocations[1].District, ShouldEqual, "1")
				So(result.HousekeepingLocations[1].Address, ShouldEqual, "104 Mai Thi Luu.")
				So(result.HousekeepingLocations[1].Contact, ShouldEqual, "Test Name")
				So(result.HousekeepingLocations[1].PhoneNumber, ShouldEqual, "0829428713")
				So(result.HousekeepingLocations[1].ShortAddress, ShouldEqual, "104 Mai Thi Luu, HCM")
				So(result.HousekeepingLocations[1].CountryCode, ShouldEqual, globalConstant.COUNTRY_CODE_VN)
				So(result.HousekeepingLocations[1].Description, ShouldEqual, "Test desc 1")
				So(result.HousekeepingLocations[1].IsoCode, ShouldEqual, local.ISO_CODE)
			})
		})
	})
	// Insert without description
	t.Run("99.1", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-housekeeping-location"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddLocationUser), t, func() {
			body := map[string]interface{}{
				"userId":       "**********",
				"lat":          1,
				"lng":          2,
				"country":      local.ISO_CODE,
				"city":         "Hồ Chí Minh",
				"district":     "1",
				"address":      "104 Mai Thi Luu.",
				"contact":      "Test Name",
				"phoneNumber":  "0829428713",
				"shortAddress": "104 Mai Thi Luu, HCM",
				"countryCode":  globalConstant.COUNTRY_CODE_VN,
				"isoCode":      local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Add Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "housekeepingLocations": 1})
				So(len(result.HousekeepingLocations), ShouldEqual, 2)
				So(result.HousekeepingLocations[1].Lat, ShouldEqual, 1)
				So(result.HousekeepingLocations[1].Lng, ShouldEqual, 2)
				So(result.HousekeepingLocations[1].Country, ShouldEqual, local.ISO_CODE)
				So(result.HousekeepingLocations[1].City, ShouldEqual, "Hồ Chí Minh")
				So(result.HousekeepingLocations[1].District, ShouldEqual, "1")
				So(result.HousekeepingLocations[1].Address, ShouldEqual, "104 Mai Thi Luu.")
				So(result.HousekeepingLocations[1].Contact, ShouldEqual, "Test Name")
				So(result.HousekeepingLocations[1].PhoneNumber, ShouldEqual, "0829428713")
				So(result.HousekeepingLocations[1].ShortAddress, ShouldEqual, "104 Mai Thi Luu, HCM")
				So(result.HousekeepingLocations[1].CountryCode, ShouldEqual, globalConstant.COUNTRY_CODE_VN)
				So(result.HousekeepingLocations[1].IsoCode, ShouldEqual, local.ISO_CODE)
			})
		})
	})

	// Insert With duplicate long,lat
	t.Run("99.2", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-housekeeping-location"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddLocationUser), t, func() {
			body := map[string]interface{}{
				"userId":       "**********",
				"lat":          10.7394465,
				"lng":          106.696324,
				"country":      local.ISO_CODE,
				"city":         "Hồ Chí Minh",
				"district":     "1",
				"address":      "104 Mai Thi Luu.",
				"contact":      "Test Name",
				"phoneNumber":  "0829428713",
				"shortAddress": "104 Mai Thi Luu, HCM",
				"countryCode":  globalConstant.COUNTRY_CODE_VN,
				"isoCode":      local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ADDRESS_IN_USE.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ADDRESS_IN_USE.Message)
			})
		})
	})

	// Insert With duplicate address
	t.Run("99.3", func(t *testing.T) {
		apiAddLocationUser := "/api/v3/api-asker-vn/add-housekeeping-location"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"description":  "12",
					},
				},
			}, {
				"phone": "**********",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAddLocationUser), t, func() {
			body := map[string]interface{}{
				"userId":       "**********",
				"lat":          10.7394465,
				"lng":          106.696324,
				"country":      local.ISO_CODE,
				"city":         "Hồ Chí Minh",
				"district":     "1",
				"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
				"contact":      "Test Name",
				"phoneNumber":  "0829428713",
				"shortAddress": "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
				"countryCode":  globalConstant.COUNTRY_CODE_VN,
				"isoCode":      local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiAddLocationUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Add Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ADDRESS_IN_USE.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ADDRESS_IN_USE.Message)
			})
		})
	})

	t.Run("100", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/delete-housekeeping-location"
		log.Println("==================================== Validate DeleteLocationUser")
		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s Check params", apiUrl), t, func() {
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params data is nil", func() {
				body := map[string]interface{}{
					"userId":     "",
					"locationId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when params locationId is nil", func() {
				body := map[string]interface{}{
					"userId":     "**********",
					"locationId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LOCATION_ID_REQUIRED.Message)
			})
		})
	})

	t.Run("101", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/delete-housekeeping-location"
		log.Println("==================================== DeteleLocationUser")

		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"housekeepingLocations": []map[string]interface{}{
					{
						"_id":          "123",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					}, {
						"_id":          "456",
						"lat":          10.7394465,
						"lng":          106.696324,
						"country":      local.ISO_CODE,
						"city":         "Hồ Chí Minh",
						"district":     "Quận 7",
						"address":      "bTaskee, Đường D1, Khu đô thị Him Lam, Tân Hưng, Quận 7, Thành phố Hồ Chí Minh, Việt Nam",
						"contact":      "Toàn Nguyễn",
						"phoneNumber":  "**********",
						"shortAddress": "69 Đường D1",
						"countryCode":  "+84",
						"isoCode":      local.ISO_CODE,
						"homeType":     "HOME",
						"description":  "12",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":     "**********",
				"locationId": "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Location User", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})
			Convey("Then check database to test Update Location User", func() {
				result, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"_id": 1, "housekeepingLocations": 1})
				So(result.XId, ShouldEqual, "**********")
				So(len(result.HousekeepingLocations), ShouldEqual, 1)
				So(result.HousekeepingLocations[0].XId, ShouldEqual, "456")
			})
		})
	})
	t.Run("102", func(t *testing.T) {
		apiSendNotiToRemindTasker := "/api/v3/api-asker-vn/send-noti-to-remind-favourite-tasker"
		log.Println("==================================== SendNotiToRemindTasker")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendNotiToRemindTasker), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.Message)
			})

			Convey("chatId empty", func() {
				body := map[string]interface{}{
					"chatId":   "",
					"taskerId": "**********",
					"userId":   "",
					"taskId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CHAT_ID_REQUIRED.Message)
			})

			Convey("taskerId empty", func() {
				body := map[string]interface{}{
					"chatId":   "chatid",
					"taskerId": "",
					"userId":   "**********",
					"taskId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.Message)
			})

			Convey("userId empty", func() {
				body := map[string]interface{}{
					"chatId":   "chatid",
					"taskerId": "taskerId",
					"userId":   "",
					"taskId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("taskId empty", func() {
				body := map[string]interface{}{
					"chatId":   "chatid",
					"taskerId": "taskerId",
					"userId":   "userId",
					"taskId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_ID_REQUIRED.Message)
			})
		})
	})
	//send noti for task have no date options
	t.Run("103", func(t *testing.T) {
		apiSendNotiToRemindTasker := "/api/v3/api-asker-vn/send-noti-to-remind-favourite-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567891",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456710", "0823456713"},
			}, {
				"phone": "0834567892",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := date.Date()
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":    "0834567891",
				"description":   "Task 01",
				"viewedTaskers": []string{"0834567892"},
				"contactName":   "contactName 01",
				"forceTasker": map[string]interface{}{
					"taskerId": "0834567892",
					"name":     "Tasker 01",
				},
				"taskPlace": map[string]interface{}{
					"district": "Q9",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d+1, 8, 0),
				"duration": 2,
				"lat":      10.741432,
				"lng":      106.702033,
				"timezone": "VN",
			},
		})
		chatIds := CreateChatConversation(globalConstant.ISO_CODE_VN, []map[string]interface{}{
			{
				"askerPhone":  "0834567891",
				"taskerPhone": "0834567892",
				"askerName":   "Asker 01",
				"taskerName":  "Tasker 01",
				"messages": []map[string]interface{}{
					{
						"_id":    "x63db5fc8e4ca3a9d185346f4",
						"from":   "SYSTEM",
						"isRead": false,
						"type":   globalConstant.TYPE_MESSAGE_REMIND_AFTER_15_MINUTES_CREATE_TASK,
						"taskRequestData": map[string]interface{}{
							"taskInfo": map[string]interface{}{
								"taskId": taskIds[0],
							},
						},
						"messageBySystem": map[string]interface{}{
							"sendTo": "ASKER",
						},
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0834567891",
					},
					{
						"_id": "0834567892",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendNotiToRemindTasker), t, func() {
			body := map[string]interface{}{
				"chatId":   chatIds[0],
				"taskerId": "0834567892",
				"userId":   "0834567891",
				"taskId":   taskIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Add Favorite Tasker", func() {
					time.Sleep(1 * time.Second)
					pkgChatMessage.UpdateChatMessages(local.ISO_CODE, bson.M{"_id": "x63db5fc8e4ca3a9d185346f4"}, bson.M{"$set": bson.M{"chatId": chatIds[0]}})
					So(resp.Code, ShouldEqual, 200)
					result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": chatIds[0]}, bson.M{"askerId": 1, "taskerId": 1, "taskId": 1, "messages": 1})
					So(len(result.Messages), ShouldBeGreaterThan, 0)
					So(result.AskerId, ShouldEqual, "0834567891")
					So(result.TaskerId, ShouldEqual, "0834567892")
					So(result.Messages[0].From, ShouldEqual, "SYSTEM")
					So(result.Messages[0].TaskRequestData.Status, ShouldEqual, "SENT_NOTI")
					So(result.Messages[1].From, ShouldEqual, "SYSTEM")
					So(result.Messages[1].MessageBySystem.Title.Vi, ShouldEqual, "Khách hàng đã gửi lời nhắc cho bạn")
					So(result.Messages[1].MessageBySystem.Text.Vi, ShouldEqual, "Vui lòng phản hồi công việc mà khách hàng đã đặt cho bạn. Nếu khung giờ khách hàng đề xuất không phù hợp, hãy chọn một khung giờ khác để đề xuất với khách hàng.")
					So(result.Messages[1].MessageBySystem.SendTo, ShouldEqual, "TASKER")
					So(result.Messages[1].MessageBySystem.Key, ShouldEqual, "BOOK_WITH_FAV")
					So(result.Messages[1].MessageBySystem.Actions[0].Type, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY)
					So(result.Messages[1].MessageBySystem.Actions[0].Key, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CHOOSE_OTHER_TIME)
					So(result.Messages[1].MessageBySystem.Actions[1].Type, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY)
					So(result.Messages[1].MessageBySystem.Actions[1].Key, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_ACCEPT_TASK)
					So(result.Messages[1].TaskRequestData.TaskInfo.TaskId, ShouldEqual, taskIds[0])
					So(result.Messages[1].TaskRequestData.TaskInfo.Duration, ShouldEqual, 2)
					So(result.Messages[1].TaskRequestData.TaskInfo.Lat, ShouldEqual, 10.741432)
					So(result.Messages[1].TaskRequestData.TaskInfo.Lng, ShouldEqual, 106.702033)
					So(result.Messages[1].TaskRequestData.TaskInfo.Timezone, ShouldEqual, "VN")
					So(result.Messages[1].TaskRequestData.TaskInfo.ServiceText.Vi, ShouldEqual, "Dọn dẹp nhà")
					So(result.Messages[1].TaskRequestData.TaskInfo.ServiceId, ShouldEqual, "pcZRQ6PqmjrAPe5gt")
					So(result.Messages[1].TaskRequestData.TaskInfo.District, ShouldEqual, "Q9")

					// Check notification send message
					var chatNotification *notification.Notification
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"chatId": chatIds[0]}, bson.M{}, &chatNotification)
					So(chatNotification, ShouldNotBeNil)
					So(chatNotification.UserId, ShouldEqual, "0834567892")
					So(chatNotification.ChatId, ShouldEqual, chatIds[0])
					So(chatNotification.Type, ShouldEqual, 28)
					So(chatNotification.Description, ShouldEqual, "Bạn có tin nhắn mới.")
					So(chatNotification.NavigateTo, ShouldEqual, "Chat")
				})
			})
		})
	})

	//send noti for task have no date options
	t.Run("104", func(t *testing.T) {
		apiSendNotiToRemindTasker := "/api/v3/api-asker-vn/send-noti-to-remind-favourite-tasker"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567891",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"blackList": []string{"0823456710", "0823456713"},
			}, {
				"phone": "0834567892",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone)
		y, m, d := date.Date()
		taskDate := time.Date(date.Year(), date.Month(), date.Day(), 10, 0, 0, 0, local.TimeZone).AddDate(0, 0, 1)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName":   globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":    "0834567891",
				"description":   "Task 01",
				"viewedTaskers": []string{"0834567892"},
				"contactName":   "contactName 01",
				"forceTasker": map[string]interface{}{
					"taskerId": "0834567892",
					"name":     "Tasker 01",
				},
				"taskPlace": map[string]interface{}{
					"district": "Q9",
				},
				"date":     fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d+1, 8, 0),
				"duration": 2,
				"lat":      10.741432,
				"lng":      106.702033,
				"timezone": "VN",
				"dateOptions": []map[string]interface{}{
					{
						"_id": "123",
						"costDetail": map[string]interface{}{
							"baseCost":  200000,
							"cost":      200000,
							"finalCost": 200000,
						},
						"isDefault": true,
						"date":      taskDate.Add(time.Hour * 6),
					}, {
						"_id": "456",
						"costDetail": map[string]interface{}{
							"baseCost":  200000,
							"cost":      200000,
							"finalCost": 200000,
						},
						"date": taskDate.Add(time.Hour * 8),
					}, {
						"_id": "678",
						"costDetail": map[string]interface{}{
							"baseCost":  200000,
							"cost":      200000,
							"finalCost": 200000,
						},
						"date": taskDate.Add(time.Hour * 10),
					},
				},
			},
		})
		chatIds := CreateChatConversation(globalConstant.ISO_CODE_VN, []map[string]interface{}{
			{
				"askerPhone":  "0834567891",
				"taskerPhone": "0834567892",
				"askerName":   "Asker 01",
				"taskerName":  "Tasker 01",
				"messages": []map[string]interface{}{
					{
						"_id":    "x63db5fc8e4ca3a9d185346f4",
						"from":   "SYSTEM",
						"isRead": false,
						"type":   globalConstant.TYPE_MESSAGE_REMIND_AFTER_15_MINUTES_CREATE_TASK,
						"taskRequestData": map[string]interface{}{
							"taskInfo": map[string]interface{}{
								"taskId": taskIds[0],
							},
						},
						"messageBySystem": map[string]interface{}{
							"sendTo": "TASKER",
							"key":    "BOOK_WITH_FAV",
							"taskRequestData": map[string]interface{}{
								"taskInfo": map[string]interface{}{
									"taskId": taskIds[0],
								},
							},
							"actions": []map[string]interface{}{
								{
									"type": globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY,
									"key":  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CHOOSE_OTHER_TIME,
								},
								{
									"type": globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY,
									"key":  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_ACCEPT_TASK,
								},
							},
						},
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0834567891",
					},
					{
						"_id": "0834567892",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendNotiToRemindTasker), t, func() {
			body := map[string]interface{}{
				"chatId":   chatIds[0],
				"taskerId": "0834567892",
				"userId":   "0834567891",
				"taskId":   taskIds[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiSendNotiToRemindTasker, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Add Favorite Tasker", func() {
					time.Sleep(1 * time.Second)
					pkgChatMessage.UpdateChatMessages(local.ISO_CODE, bson.M{"_id": "x63db5fc8e4ca3a9d185346f4"}, bson.M{"$set": bson.M{"chatId": chatIds[0]}})
					So(resp.Code, ShouldEqual, 200)
					result, _ := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": chatIds[0]}, bson.M{"askerId": 1, "taskerId": 1, "taskId": 1, "messages": 1})
					So(len(result.Messages), ShouldBeGreaterThan, 0)
					So(result.AskerId, ShouldEqual, "0834567891")
					So(result.TaskerId, ShouldEqual, "0834567892")
					So(result.Messages[0].From, ShouldEqual, "SYSTEM")
					So(result.Messages[0].MessageBySystem.SendTo, ShouldEqual, "TASKER")
					So(result.Messages[0].MessageBySystem.Key, ShouldEqual, "BOOK_WITH_FAV")
					So(result.Messages[0].MessageBySystem.Actions[0].IsDisabled, ShouldEqual, true)
					So(result.Messages[0].MessageBySystem.Actions[1].IsDisabled, ShouldEqual, true)
					So(result.Messages[1].From, ShouldEqual, "SYSTEM")
					So(result.Messages[1].MessageBySystem.Title.Vi, ShouldEqual, "Khách hàng đã gửi lời nhắc cho bạn")
					So(result.Messages[1].MessageBySystem.Text.Vi, ShouldEqual, "Vui lòng phản hồi công việc mà khách hàng đã đặt cho bạn. Nếu khung giờ khách hàng đề xuất không phù hợp, hãy chọn một khung giờ khác để đề xuất với khách hàng.")
					So(result.Messages[1].MessageBySystem.SendTo, ShouldEqual, "TASKER")
					So(result.Messages[1].MessageBySystem.Key, ShouldEqual, "BOOK_WITH_FAV")
					So(result.Messages[1].MessageBySystem.Actions[0].Type, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY)
					So(result.Messages[1].MessageBySystem.Actions[0].Key, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CHOOSE_OTHER_TIME)
					So(result.Messages[1].MessageBySystem.Actions[1].Type, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY)
					So(result.Messages[1].MessageBySystem.Actions[1].Key, ShouldEqual, globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_ACCEPT_TASK)
					So(result.Messages[1].TaskRequestData.TaskInfo.TaskId, ShouldEqual, taskIds[0])
					So(result.Messages[1].TaskRequestData.TaskInfo.Duration, ShouldEqual, 2)
					So(result.Messages[1].TaskRequestData.TaskInfo.Lat, ShouldEqual, 10.741432)
					So(result.Messages[1].TaskRequestData.TaskInfo.Lng, ShouldEqual, 106.702033)
					So(result.Messages[1].TaskRequestData.TaskInfo.Timezone, ShouldEqual, "VN")
					So(result.Messages[1].TaskRequestData.TaskInfo.ServiceText.Vi, ShouldEqual, "Dọn dẹp nhà")
					So(result.Messages[1].TaskRequestData.TaskInfo.ServiceId, ShouldEqual, "pcZRQ6PqmjrAPe5gt")
					So(result.Messages[1].TaskRequestData.TaskInfo.District, ShouldEqual, "Q9")

					// Check notification send message
					var chatNotification *notification.Notification
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"chatId": chatIds[0]}, bson.M{}, &chatNotification)
					So(chatNotification, ShouldNotBeNil)
					So(chatNotification.UserId, ShouldEqual, "0834567892")
					So(chatNotification.ChatId, ShouldEqual, chatIds[0])
					So(chatNotification.Type, ShouldEqual, 28)
					So(chatNotification.Description, ShouldEqual, "Bạn có tin nhắn mới.")
					So(chatNotification.NavigateTo, ShouldEqual, "Chat")
				})
			})
		})
	})

	// Check Community Setting
	t.Run("105", func(t *testing.T) {
		log.Println("==================================== Check Community Setting")
		apiGetUser := "/api/v3/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":        "**********",
				"name":         "Asker 01",
				"type":         globalConstant.USER_TYPE_ASKER,
				"referralCode": "CODE1",
				"services": map[string]interface{}{
					"resume": map[string]interface{}{
						"loginTokens": []map[string]interface{}{
							{"hashedToken": "abcxyz123"},
						},
					},
				},
				"updatePrivacyPolicyAt": globalLib.GetCurrentTime(local.TimeZone),
			}, {
				"phone":      "0834567810",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "CODE1",
			},
		})

		CreateCommunitySetting([]map[string]interface{}{
			{
				"isTesting": true,
				"tester":    []string{"**********"},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["name"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["isTesterCommunity"], ShouldEqual, true)

				})
			})
		})
	})

	t.Run("106", func(t *testing.T) {
		apiUrl := "/api/v5/api-asker-vn/add-activated-services"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check the response user id require", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if list service empty", func() {
				body := map[string]interface{}{
					"userId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					b, _ := io.ReadAll(resp.Body)
					m := map[string]map[string]interface{}{}
					json.Unmarshal(b, &m)
					So(resp.Code, ShouldEqual, 400)
					So(m["error"]["code"], ShouldEqual, lib.ERROR_LIST_SERVICE_REQUIRED.ErrorCode)
				})
			})
			Convey("Check the response if update failed", func() {
				body := map[string]interface{}{
					"userId":            "123",
					"isoCode":           local.ISO_CODE,
					"activatedServices": []string{"123", "234", "345", "456", "567", "789"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				b, _ := io.ReadAll(resp.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(resp.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
			Convey("Check the response if userId not found", func() {
				ResetData()
				//Create User
				CreateUser([]map[string]interface{}{
					{
						"phone": "**********",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
						"point": 1,
						"rankInfo": map[string]interface{}{
							"point": 1,
						},
						"activatedServices": []map[string]interface{}{
							{
								"serviceName": "HOME_CLEANING",
							},
						},
					},
				})
				body := map[string]interface{}{
					"userId":            "**********",
					"isoCode":           local.ISO_CODE,
					"activatedServices": []string{"HOME_CLEANING", "CHILD_CARE", "PATIENT_CARE"},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)
				var asker *modelUser.Users
				globalDataAccess.GetOneById(globalCollection.COLLECTION_USERS, "**********", bson.M{"activatedServices": 1}, &asker)
				So(len(asker.ActivatedServices), ShouldEqual, 3)
				So(asker.ActivatedServices[0].ServiceName, ShouldEqual, "HOME_CLEANING")
				So(asker.ActivatedServices[1].ServiceName, ShouldEqual, "CHILD_CARE")
				So(asker.ActivatedServices[2].ServiceName, ShouldEqual, "PATIENT_CARE")
			})
		})
	})

	// check new field api V4
	t.Run("107", func(t *testing.T) {
		log.Println("==================================== GetUser Check new field")
		apiGetUser := "/api/v4/api-asker-vn/get-user"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "**********",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"isPremiumTasker": true,
				"referralCode":    "CODE1",
				"isEco":           true,
				"point":           1,
				"rankInfo": map[string]interface{}{
					"point": 1,
				},
				"lastPostedTask": globalLib.GetCurrentTime(local.TimeZone),
				"voiceCallToken": map[string]interface{}{
					"status": globalConstant.USER_STATUS_ACTIVE,
					"token":  "abcxyz123",
				},
				"voiceCallTokenV2": map[string]interface{}{
					"status": globalConstant.USER_STATUS_ACTIVE,
					"token":  "voiceCallTokenV2abc",
				},
				"isoCode":            local.ISO_CODE,
				"numberOfLuckyDraws": 10,
				"cities": []map[string]interface{}{
					{
						"city":    "Hồ Chí Minh",
						"country": "Việt Nam",
					}, {
						"city":    "Hà Nội",
						"country": "Việt Nam",
					},
				},
				"gender": "MALE",
				"reviewStore": map[string]interface{}{
					"count":         1,
					"isReviewStore": true,
				},
				"updatePrivacyPolicyAt": globalLib.GetCurrentTime(local.TimeZone),
			}, {
				"phone":      "0834567810",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "CODE1",
			},
		})

		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "**********",
				"status": "ACTIVE",
				"bPay":   5000000,
				"revokeSetting": map[string]interface{}{
					"status":    "INACTIVE",
					"isDisable": true,
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiGetUser), t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetUser, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Have data include locations", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get User by userId", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["fAccountId"], ShouldNotBeEmpty)
					So(respResult["name"], ShouldEqual, "Asker 01")
					So(respResult["phone"], ShouldEqual, "**********")
					So(respResult["status"], ShouldEqual, globalConstant.USER_STATUS_ACTIVE)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult["type"], ShouldEqual, globalConstant.USER_TYPE_ASKER)
					So(respResult["taskDone"], ShouldEqual, 2)
					So(respResult["locations"], ShouldNotBeNil)
					So(respResult["point"], ShouldEqual, 1)
					So(respResult["gender"], ShouldEqual, "MALE")
					So(respResult["rankInfo"], ShouldNotBeNil)
					So(respResult["lastPostedTask"], ShouldNotBeNil)
					So(respResultM["reviewStore"]["count"], ShouldEqual, 1)
					So(respResultM["reviewStore"]["isReviewStore"], ShouldBeTrue)
					So(respResult["voiceCallToken"], ShouldNotBeNil)
					So(respResultM["voiceCallToken"]["token"], ShouldEqual, "abcxyz123")
					So(respResult["voiceCallTokenV2"], ShouldNotBeNil)
					So(respResultM["voiceCallTokenV2"]["token"], ShouldEqual, "voiceCallTokenV2abc")
					So(respResult["isPremiumTasker"], ShouldBeTrue)
					So(respResult["referralCode"], ShouldNotEqual, "CODE1")
					So(respResult["isEco"], ShouldEqual, true)
				})
			})
		})
	})
}
