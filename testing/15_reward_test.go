/*
* @File: 15_reward_test.go
* @Description: Handler function, case test
* @CreatedAt: 20/11/2020
* @Author: vinhnt
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelReward "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reward"
	"go.mongodb.org/mongo-driver/bson"
)

func TestReward(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetReward")
		// GetReward
		apiUrl := "/api/v3/api-asker-vn/get-reward"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId": 132213,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetReward")
		// GetReward
		apiUrl := "/api/v3/api-asker-vn/get-reward"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Reward
		ids := CreateReward([]map[string]interface{}{
			{
				"userPhone": "0834567890",
				"type":      "Type Test 01",
			}, {
				"userPhone": "0834567810",
				"type":      "Type Test 02",
				"isShow":    false,
			}, {
				"userPhone": "0834567820",
				"type":      "Type Test 03",
				"isViewed":  true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Reward", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["_id"], ShouldEqual, ids[0])
					So(respResult["isShow"], ShouldBeTrue)
					So(respResult["type"], ShouldEqual, "Type Test 01")
					So(respResult["userId"], ShouldEqual, "0834567890")
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== GetReward")
		// GetReward
		apiUrl := "/api/v3/api-asker-vn/get-reward"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Reward
		CreateReward([]map[string]interface{}{
			{
				"userPhone": "0834567890",
				"type":      "Type Test 01",
			}, {
				"userPhone": "0834567810",
				"type":      "Type Test 02",
				"isShow":    false,
			}, {
				"userPhone": "0834567820",
				"type":      "Type Test 03",
				"isViewed":  true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567810",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Reward", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeEmpty)
				})
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== GetReward")
		// GetReward
		apiUrl := "/api/v3/api-asker-vn/get-reward"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Reward
		CreateReward([]map[string]interface{}{
			{
				"userPhone": "0834567890",
				"type":      "Type Test 01",
			}, {
				"userPhone": "0834567810",
				"type":      "Type Test 02",
				"isShow":    false,
			}, {
				"userPhone": "0834567820",
				"type":      "Type Test 03",
				"isViewed":  true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567820",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Reward", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldBeEmpty)
				})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		log.Println("==================================== Validate ViewReward")
		// ViewReward
		apiUrl := "/api/v3/api-asker-vn/view-reward"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"id": 12312312132,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== ViewReward")
		// ViewReward
		apiUrl := "/api/v3/api-asker-vn/view-reward"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		//Create Reward
		ids := CreateReward([]map[string]interface{}{
			{
				"userPhone": "0834567890",
				"type":      "Type Test 01",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by id", apiUrl), t, func() {
			body := map[string]interface{}{
				"id": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test View Reward", func() {
					So(resp.Code, ShouldEqual, 200)
				})
				Convey("Then check database View Reward", func() {
					var reward *modelReward.Reward
					globalDataAccess.GetOneById(globalCollection.COLLECTION_REWARD[local.ISO_CODE], ids[0], bson.M{"isViewed": 1}, &reward)
					So(reward.IsViewed, ShouldBeTrue)
				})
			})
		})
	})
}
