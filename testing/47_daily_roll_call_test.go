package testing

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"go.mongodb.org/mongo-driver/bson"
)

func TestDailyRollCall(t *testing.T) {
	apiURL := "/api/v3/api-asker-vn/daily-roll-call"
	// Validate
	t.Run("1", func(t *testing.T) {
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":         "",
				"gameCampaignId": "xx",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 400)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})
		Convey("Check the response if req gameCampaignId is empty", t, func() {
			body := map[string]interface{}{
				"userId":         "xx",
				"gameCampaignId": "",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 400)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_GAME_CAMPAIGN_ID_REQUIRED.ErrorCode)
		})
	})

	// User not found
	t.Run("2", func(t *testing.T) {
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":         "xx",
				"gameCampaignId": "xx",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 404)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
		})
	})
	// mark roll
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== mark roll")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"startDate": now.AddDate(0, 0, -1),
			"endDate":   now.AddDate(0, 0, 1),
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "ROLL_CALL",
					"numberOfSpin": 1,
					"applyFor":     "BOTH",
				},
				{
					"action":       "ROLL_CALL_AT_NOEL",
					"numberOfSpin": 2,
					"applyFor":     "BOTH",
				},
			},
		})

		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			time.Sleep(3 * time.Second)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 200)
			So(resp["error"]["code"], ShouldBeNil)

			var userGameCampaign *modelUserGameCampaign.UserGameCampaign
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], bson.M{}, bson.M{}, &userGameCampaign)
			So(userGameCampaign.AccumulateSpinHistory[0].Action, ShouldBeIn, []string{"ROLL_CALL", "ROLL_CALL_AT_NOEL"})
			So(userGameCampaign.AccumulateSpinHistory[0].Spin, ShouldBeIn, []int32{1, 2})
			So(userGameCampaign.UserId, ShouldEqual, "0834567890")
		})
	})

	// game campaign not started
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== mark roll")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentDate := globalLib.GetCurrentTime(local.TimeZone)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"startDate": time.Date(currentDate.Year()+1, 1, 1, 0, 0, 0, 0, local.TimeZone),
			"endDate":   time.Date(currentDate.Year()+1, 1, 31, 0, 0, 0, 0, local.TimeZone),
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "ROLL_CALL",
					"numberOfSpin": 1,
					"applyFor":     "BOTH",
				},
				{
					"action":       "ROLL_CALL_AT_NOEL",
					"numberOfSpin": 2,
					"applyFor":     "BOTH",
				},
			},
		})

		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			time.Sleep(3 * time.Second)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 500)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_GAME_CAMPAIGN_NOT_STARTED.ErrorCode)
		})
	})

	// game campaign ended
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== mark roll")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentDate := globalLib.GetCurrentTime(local.TimeZone)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"startDate": time.Date(currentDate.Year()-1, 1, 1, 0, 0, 0, 0, local.TimeZone),
			"endDate":   time.Date(currentDate.Year()-1, 1, 31, 0, 0, 0, 0, local.TimeZone),
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "ROLL_CALL",
					"numberOfSpin": 1,
					"applyFor":     "BOTH",
				},
				{
					"action":       "ROLL_CALL_AT_NOEL",
					"numberOfSpin": 2,
					"applyFor":     "BOTH",
				},
			},
		})

		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			time.Sleep(3 * time.Second)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 500)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_GAME_CAMPAIGN_ENDED.ErrorCode)
		})
	})
}
