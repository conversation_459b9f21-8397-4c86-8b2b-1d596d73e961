package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelBusinessMemberTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMemberTransaction"
	modelBusinessTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

func TestBusiness2(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate addMembersToBusiness")
		apiUrl := "/api/v3/api-asker-vn/add-members-to-business"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_MEMBER_INFOS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_MEMBER_INFOS_REQUIRED.Message)
			})

			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"memberInfos": []map[string]interface{}{
						{
							"userId":  "0834567892",
							"levelId": "level1",
						}, {
							"userId":  "0834567893",
							"levelId": "",
						}, {
							"userId":  "0834567894",
							"levelId": "level2",
						}, {
							"userId":  "0834567895",
							"levelId": "level2",
						},
					},
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Test addMembersToBusiness")
		apiUrl := "/api/v3/api-asker-vn/add-members-to-business"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"memberInfos": []map[string]interface{}{
					{
						"userId":  "0834567892",
						"phone":   "0834567892",
						"levelId": "level1",
					}, {
						"userId":  "0834567893",
						"phone":   "0834567893",
						"levelId": "",
					}, {
						"userId":  "0834567894",
						"phone":   "0834567894",
						"levelId": "level2",
					}, {
						"userId":  "0834567895",
						"phone":   "0834567895",
						"levelId": "",
					},
				},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResults := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResults)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResults), ShouldEqual, 2)
					for _, v := range respResults {
						if v["error"] == "LEVEL_INVALID" {
							So(v["phone"], ShouldEqual, "0834567894")
						}
						if v["error"] == "IS_MEMBER" {
							So(v["phone"], ShouldEqual, "0834567895")
						}
					}
					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"userId": 1, "levelId": 1})
					So(len(members), ShouldEqual, 3)
					for _, v := range members {
						So(v.UserId, ShouldBeIn, []string{"0834567892", "0834567893", "0834567895"})
						if v.UserId == "0834567892" {
							So(v.LevelId, ShouldEqual, "level1")
						} else {
							So(v.LevelId, ShouldEqual, "")
						}
					}
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Validate verify members")
		apiUrl := "/api/v3/api-asker-vn/verify-members"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_PHONE_REQUIRED.Message)
			})

			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"phones":     []string{"123"},
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Test verify members")
		apiUrl := "/api/v3/api-asker-vn/verify-members"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
			}, {
				"businessId": "123",
				"userId":     "0834567894",
				"status":     "ACTIVE",
			}, {
				"businessId": "123",
				"userId":     "0834567812",
				"status":     "ACTIVE",
				"levelId":    "level2",
			}, {
				"businessId": "123",
				"userId":     "0834567813",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"businessId": "123",
				"userId":     "0834567814",
				"status":     "ACTIVE",
				"levelId":    "level2",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"phones":     []string{"0834567891", "0834567892", "0834567893", "0834567894", "0834567895"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResults := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResults)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResults["levels"].([]interface{})), ShouldEqual, 2)
					So(len(respResults["users"].([]interface{})), ShouldEqual, 5)
					for _, v := range respResults["levels"].([]interface{}) {
						data := cast.ToStringMap(v)
						So(data["_id"], ShouldBeIn, []string{"level1", "level2"})
						So(data["name"], ShouldBeIn, []string{"level1", "level2"})
						if data["_id"] == "level1" {
							So(data["status"], ShouldEqual, "ACTIVE")
							So(data["memberCount"], ShouldEqual, 1)
						} else {
							So(data["status"], ShouldEqual, "ACTIVE")
							So(data["memberCount"], ShouldEqual, 2)
						}
					}
					for _, v := range respResults["users"].([]interface{}) {
						data := cast.ToStringMap(v)
						So(data["phone"], ShouldBeIn, []string{"0834567891", "0834567892", "0834567893", "0834567894", "0834567895"})
						switch data["phone"] {
						case "0834567891":
							So(data["phone"], ShouldEqual, "0834567891")
							So(data["status"], ShouldEqual, "NOT_REGISTERED")
						case "0834567892":
							So(data["name"], ShouldEqual, "Asker 02")
							So(data["phone"], ShouldEqual, "0834567892")
							So(data["status"], ShouldEqual, "REGISTERED")
						case "0834567893":
							So(data["name"], ShouldEqual, "Asker 03")
							So(data["phone"], ShouldEqual, "0834567893")
							So(data["status"], ShouldEqual, "REGISTERED")
						case "0834567894":
							So(data["phone"], ShouldEqual, "0834567894")
							So(data["status"], ShouldEqual, "IS_OTHER_MEMBER")
						case "0834567895":
							So(data["phone"], ShouldEqual, "0834567895")
							So(data["status"], ShouldEqual, "IS_MEMBER")
						}
					}
				})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		log.Println("==================================== Validate addMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/add-members-level"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when memberIds blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_MEMBER_IDS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_MEMBER_IDS_REQUIRED.Message)
			})
			Convey("Check request when levelId blank", func() {
				body := map[string]interface{}{
					"memberIds":  []string{"123"},
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.Message)
			})
			Convey("Check request when businessId blank", func() {
				body := map[string]interface{}{
					"memberIds":  []string{"123"},
					"levelId":    "123",
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== Test addMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/add-members-level"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"memberIds":  []string{"0834567891", "0834567892", "0834567893", "0834567894", "0834567895"},
				"businessId": "0834567890",
				"levelId":    "level1",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResults := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResults)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResults), ShouldEqual, 4)
					for _, v := range respResults {
						switch v["memberId"] {
						case "0834567891":
							So(v["error"], ShouldEqual, "MEMBER_INVALID")
						case "0834567892":
							So(v["error"], ShouldEqual, "MEMBER_INVALID")
						case "0834567894":
							So(v["error"], ShouldEqual, "MEMBER_LEVEL_EXIST")
						case "0834567895":
							So(v["error"], ShouldEqual, "MEMBER_LEVEL_EXIST")
						}
					}
					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"userId": 1, "levelId": 1})
					So(len(members), ShouldEqual, 4)
					for _, v := range members {
						if v.UserId == "0834567893" || v.UserId == "0834567895" {
							So(v.LevelId, ShouldEqual, "level1")
						} else if v.UserId == "0834567894" {
							So(v.LevelId, ShouldEqual, "level2")
						} else {
							So(v.LevelId, ShouldEqual, "")
						}
					}

					memberMap, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"userId": bson.M{"$in": []string{"0834567893"}}}, bson.M{"changeHistories": 1})
					for _, v := range memberMap {
						So(len(v["changeHistories"].([]interface{})), ShouldEqual, 1)
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["key"], ShouldEqual, "ADD_LEVEL")
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["content"].(map[string]interface{})["levelId"], ShouldEqual, "level1")
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["createdBy"], ShouldEqual, "0834567890")
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["createdAt"], ShouldNotBeNil)
					}
				})
			})
		})
	})

	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate removeMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/remove-members-level"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when memberIds blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_MEMBER_IDS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_MEMBER_IDS_REQUIRED.Message)
			})
			Convey("Check request when businessId blank", func() {
				body := map[string]interface{}{
					"memberIds":  []string{"123"},
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		log.Println("==================================== Test removeMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/remove-members-level"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"name":   "ABC",
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"memberIds":  []string{"0834567893", "0834567892", "0834567895"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResults := []map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResults)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResults), ShouldEqual, 1)
					for _, v := range respResults {
						switch v["memberId"] {
						case "0834567892":
							So(v["error"], ShouldEqual, "MEMBER_INVALID")
						}
					}
					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"userId": 1, "levelId": 1})
					So(len(members), ShouldEqual, 4)
					for _, v := range members {
						if v.UserId == "0834567893" || v.UserId == "0834567895" {
							So(v.LevelId, ShouldEqual, "")
						}
					}

					memberMap, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"userId": bson.M{"$in": []string{"0834567893", "0834567895"}}}, bson.M{"changeHistories": 1})
					for _, v := range memberMap {
						So(len(v["changeHistories"].([]interface{})), ShouldEqual, 1)
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["key"], ShouldEqual, "REMOVE_LEVEL")
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["content"].(map[string]interface{})["levelId"], ShouldEqual, "level1")
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["createdBy"], ShouldEqual, "0834567890")
						So(v["changeHistories"].([]interface{})[0].(map[string]interface{})["createdAt"], ShouldNotBeNil)
					}
				})
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("==================================== Validate getTotalTopupbPay")
		apiUrl := "/api/v3/api-asker-vn/get-total-topup-bpay"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when businessId blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
			Convey("Check request when levelId blank", func() {
				body := map[string]interface{}{
					"businessId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.Message)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("==================================== Test getTotalTopupbPay")
		apiUrl := "/api/v3/api-asker-vn/get-total-topup-bpay"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   5000000,
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
				"levelId":    "level1",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"levelIds":   []string{"level1", "level2"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["bPay"], ShouldEqual, 5000000)
					So(respResult["bPayTopup"], ShouldEqual, 2350000)
					So(respResult["remain"], ShouldEqual, 5000000-2350000)
				})
			})
		})
	})

	t.Run("11", func(t *testing.T) {
		log.Println("==================================== Validate getTotalRevokebPay")
		apiUrl := "/api/v3/api-asker-vn/get-total-revoke-bpay"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when businessId blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
			Convey("Check request when levelId blank", func() {
				body := map[string]interface{}{
					"businessId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.Message)
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		log.Println("==================================== Test getTotalRevokebPay")
		apiUrl := "/api/v3/api-asker-vn/get-total-revoke-bpay"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   5000000,
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       200000,
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
				"bPay":       300000,
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       500000,
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
				"levelId":    "level1",
				"bPay":       700000,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"levelIds":   []string{"level1", "level2"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["bPay"], ShouldEqual, 5000000)
					So(respResult["bPayRevoke"], ShouldEqual, 1000000)
					So(respResult["remain"], ShouldEqual, 5000000+1000000)
				})
			})
		})
	})

	t.Run("13", func(t *testing.T) {
		log.Println("==================================== Validate topupMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/topup-members-level"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when businessId blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
			Convey("Check request when levelId blank", func() {
				body := map[string]interface{}{
					"businessId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.Message)
			})
		})
	})
	t.Run("14", func(t *testing.T) {
		log.Println("==================================== Test topupMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/topup-members-level"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   5000000,
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       200000,
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
				"bPay":       300000,
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       500000,
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
				"levelId":    "level1",
				"bPay":       700000,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"levelIds":   []string{"level1", "level2"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					time.Sleep(2 * time.Second)
					business, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": "0834567890"}, bson.M{"_id": 1, "bPay": 1})
					So(business.BPay, ShouldEqual, 5000000-(675000*2)-1000000)

					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"userId": 1, "levelId": 1, "bPay": 1, "status": 1})
					So(len(members), ShouldEqual, 4)
					for _, v := range members {
						switch v.UserId {
						case "0834567895":
							So(v.BPay, ShouldEqual, 200000+675000)
						case "0834567894":
							So(v.BPay, ShouldEqual, 300000+1000000)
						case "0834567893":
							So(v.BPay, ShouldEqual, 500000+675000)
						case "0834567892":
							So(v.Status, ShouldEqual, "INACTIVE")
							So(v.BPay, ShouldEqual, 700000)
						}
					}

					bTrans, _ := modelBusinessTransaction.GetAll(local.ISO_CODE, bson.M{}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bTrans), ShouldEqual, 3)
					for _, v := range bTrans {
						So(v.Type, ShouldEqual, "C")
						So(v.Name, ShouldEqual, "TOP_UP_BPAY_MEMBER")
						switch v.MemberId {
						case "0834567895":
							So(v.Amount, ShouldEqual, 675000)
						case "0834567894":
							So(v.Amount, ShouldEqual, 1000000)
						case "0834567893":
							So(v.Amount, ShouldEqual, 675000)
						}
					}

					bmTrans, _ := modelBusinessMemberTransaction.GetAll(local.ISO_CODE, bson.M{}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bmTrans), ShouldEqual, 3)
					for _, v := range bmTrans {
						So(v.Type, ShouldEqual, "D")
						So(v.Name, ShouldEqual, "TOP_UP_BPAY_BY_BUSINESS")
						switch v.MemberId {
						case "0834567895":
							So(v.Amount, ShouldEqual, 675000)
						case "0834567894":
							So(v.Amount, ShouldEqual, 1000000)
						case "0834567893":
							So(v.Amount, ShouldEqual, 675000)
						}
					}
				})
			})
		})
	})

	t.Run("15", func(t *testing.T) {
		log.Println("==================================== Validate revokeMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/revoke-members-level"
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"businessId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when businessId blank", func() {
				body := map[string]interface{}{
					"businessId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BUSINESS_REQUIRED.Message)
			})
			Convey("Check request when levelId blank", func() {
				body := map[string]interface{}{
					"businessId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_LEVEL_REQUIRED.Message)
			})
		})
	})
	t.Run("16", func(t *testing.T) {
		log.Println("==================================== Test revokeMembersLevel")
		apiUrl := "/api/v3/api-asker-vn/revoke-members-level"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   5000000,
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       200000,
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
				"bPay":       300000,
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       500000,
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
				"levelId":    "level1",
				"bPay":       700000,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"levelIds":   []string{"level1", "level2"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					time.Sleep(2 * time.Second)
					business, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": "0834567890"}, bson.M{"_id": 1, "bPay": 1})
					So(business.BPay, ShouldEqual, 5000000+200000+300000+500000)

					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"userId": 1, "levelId": 1, "bPay": 1, "status": 1})
					So(len(members), ShouldEqual, 4)
					for _, v := range members {
						switch v.UserId {
						case "0834567895":
							So(v.BPay, ShouldEqual, 0)
						case "0834567894":
							So(v.BPay, ShouldEqual, 0)
						case "0834567893":
							So(v.BPay, ShouldEqual, 0)
						case "0834567892":
							So(v.Status, ShouldEqual, "INACTIVE")
							So(v.BPay, ShouldEqual, 700000)
						}
					}

					bTrans, _ := modelBusinessTransaction.GetAll(local.ISO_CODE, bson.M{}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bTrans), ShouldEqual, 3)
					for _, v := range bTrans {
						So(v.Type, ShouldEqual, "D")
						So(v.Name, ShouldEqual, "REVOKE_BPAY_MEMBER")
						switch v.MemberId {
						case "0834567895":
							So(v.Amount, ShouldEqual, 200000)
						case "0834567894":
							So(v.Amount, ShouldEqual, 300000)
						case "0834567893":
							So(v.Amount, ShouldEqual, 500000)
						}
					}

					bmTrans, _ := modelBusinessMemberTransaction.GetAll(local.ISO_CODE, bson.M{}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bmTrans), ShouldEqual, 3)
					for _, v := range bmTrans {
						So(v.Type, ShouldEqual, "C")
						So(v.Name, ShouldEqual, "REVOKE_BPAY_BY_BUSINESS")
						switch v.MemberId {
						case "0834567895":
							So(v.Amount, ShouldEqual, 200000)
						case "0834567894":
							So(v.Amount, ShouldEqual, 300000)
						case "0834567893":
							So(v.Amount, ShouldEqual, 500000)
						}
					}
				})
			})
		})
	})
	t.Run("17", func(t *testing.T) {
		log.Println("==================================== Test get business top up setting info")
		apiUrl := "/api/v3/api-asker-vn/get-business-topup-setting-info"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		nextTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 10)
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   5000000,
				"topUpSetting": map[string]interface{}{
					"nextTime":   nextTime,
					"period":     60,
					"dayInMonth": "5",
					"status":     "ACTIVE",
				},
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
				"levelId":    "level1",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"levelIds":   []string{"level1", "level2"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["amount"], ShouldEqual, 0)
					So(respResult["isNotEnoughMoney"], ShouldEqual, false)
					So(respResult["nextTime"], ShouldEqual, nextTime.Format(globalConstant.LAYOUT_DATE))
					So(respResult["remainingDay"], ShouldEqual, 10)
					So(respResult["dayInMonth"], ShouldEqual, "5")
				})
			})
		})
	})
	t.Run("17.1", func(t *testing.T) {
		log.Println("==================================== Test get business top up setting info")
		apiUrl := "/api/v3/api-asker-vn/get-business-topup-setting-info"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		nextTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 10)
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   1350000,
				"topUpSetting": map[string]interface{}{
					"nextTime":   nextTime,
					"period":     60,
					"dayInMonth": "5",
					"status":     "ACTIVE",
				},
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "INACTIVE",
				"levelId":    "level1",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			body := map[string]interface{}{
				"levelIds":   []string{"level1", "level2"},
				"businessId": "0834567890",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Favorite Tasker", func() {
					respResult := map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["amount"], ShouldEqual, 2350000-1350000)
					So(respResult["isNotEnoughMoney"], ShouldEqual, true)
					So(respResult["nextTime"], ShouldEqual, nextTime.Format(globalConstant.LAYOUT_DATE))
					So(respResult["remainingDay"], ShouldEqual, 10)
				})
			})
		})
	})
}
