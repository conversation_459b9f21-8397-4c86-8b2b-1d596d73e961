/*
 * @File: 11_paymentCard_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 30/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func TestPaymentList(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate GetListCard")
		// GetListCard
		apiURL := "/api/v3/api-asker-vn/get-list-payment"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":  123,
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "HA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
	})
	// Check trường hợp enableCardPayment:false nhưng vẫn để khách hàng lấy danh sách thẻ đã tích hợp.
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== GetListCard")
		// GetListCard
		apiGetListCard := "/api/v3/api-asker-vn/get-list-payment"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create PaymentCard
		now := globalLib.GetCurrentTime(local.TimeZone)
		nextMonth := now.AddDate(0, 1, 0)
		CreatePaymentCard([]map[string]interface{}{
			{
				"userId":        "**********",
				"number":        "5375",
				"expiryMonth":   fmt.Sprintf("%d", nextMonth.Month()),
				"expiryYear":    fmt.Sprintf("%d", nextMonth.Year()),
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
			},
		})

		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_CARD_PAYMENT_CONFIG[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"$set": bson.M{"enableCardPayment": false}})
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$push": bson.M{"tester": "1"}})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListCard), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Card", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)

					cards := []map[string]interface{}{}
					cardsData, _ := json.Marshal(respResult["cards"])
					json.Unmarshal(cardsData, &cards)
					So(len(cards), ShouldEqual, 1)
					So(cards[0]["number"], ShouldEqual, "5375")
				})
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		log.Println("==================================== GetListCard not expired")
		// GetListCard
		apiGetListCard := "/api/v3/api-asker-vn/get-list-payment"
		ResetData()
		// Create FinancialAccount
		fAccountIds := CreateFAccount([]map[string]interface{}{
			{
				"FMainAccount": 650000,
			},
		})

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAccountIds[0],
			},
		})

		//Create PaymentCard
		now := globalLib.GetCurrentTime(local.TimeZone)
		nextMonth := now.AddDate(0, 1, 0)
		CreatePaymentCard([]map[string]interface{}{
			{
				"userId":        "**********",
				"number":        "5375",
				"expiryMonth":   fmt.Sprintf("%d", now.Month()-1),
				"expiryYear":    fmt.Sprintf("%d", now.Year()),
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
				"isoCode":       local.ISO_CODE,
				"status":        globalConstant.PAYMENT_CARD_STATUS_ACTIVE,
			}, {
				"userId":        "**********",
				"number":        "5373",
				"expiryMonth":   fmt.Sprintf("%d", nextMonth.Month()),
				"expiryYear":    fmt.Sprintf("%d", nextMonth.Year()),
				"holderName":    "Dr. Nickolas Mills",
				"type":          "visa",
				"isAuthorise3D": true,
				"shopperIP":     "**************",
				"isoCode":       local.ISO_CODE,
				"status":        globalConstant.PAYMENT_CARD_STATUS_ACTIVE,
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -5)
		y, m, d := date.Date()
		CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d, date.Hour(), date.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
				"isoCode": local.ISO_CODE,
				"costDetail": map[string]interface{}{
					"finalCost": 300000,
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_SHOPEE_PAY,
				},
			}, {
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d, date.Hour(), date.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"isoCode": local.ISO_CODE,
				"costDetail": map[string]interface{}{
					"finalCost": 300000,
				},
			}, {
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"askerPhone":  "**********",
				"description": "Task 01",
				"status":      globalConstant.TASK_STATUS_WAITING,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", y, m, d, date.Hour(), date.Minute()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"isoCode": local.ISO_CODE,
				"costDetail": map[string]interface{}{
					"finalCost": 300000,
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetListCard), t, func() {
			body := map[string]interface{}{
				"userId":  "**********",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiGetListCard, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get List Card", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult, ShouldNotBeNil)

					cards := []map[string]interface{}{}
					cardsData, _ := json.Marshal(respResult["cards"])
					json.Unmarshal(cardsData, &cards)
					So(len(cards), ShouldEqual, 2)
					So(cards[0]["number"], ShouldEqual, "5375")
					So(cards[0]["status"], ShouldEqual, globalConstant.PAYMENT_CARD_STATUS_EXPIRED)
					So(cards[1]["number"], ShouldEqual, "5373")
					So(cards[1]["status"], ShouldEqual, globalConstant.PAYMENT_CARD_STATUS_ACTIVE)

					So(respResult["bPayBalance"], ShouldEqual, 50000)
				})
			})
		})
	})
}
