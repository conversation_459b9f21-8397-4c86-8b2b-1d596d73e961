package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	getListBusinessTransaction "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/businessTransaction/listBusinessTransaction"
	listMembersByLevel "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member/listMemberByLevel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/memberTransaction/getListMemberTransaction"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func TestBusiness(t *testing.T) {
	// ================= 1. create business user ==================
	apiCreateBusinessUser := "/api/v3/api-asker-vn/create-business"
	// validate data
	t.Run("1.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			Convey("When service handle request if request params missing userId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing name", func() {
				body := map[string]interface{}{
					"userId": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing email", func() {
				body := map[string]interface{}{
					"userId": "xxx",
					"name":   "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// user not found
	t.Run("1.2", func(t *testing.T) {
		log.Println("==================================== User not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			Convey("When service handle request if user not found", func() {
				body := map[string]interface{}{
					"userId": "xxx",
					"name":   "xxx",
					"email":  "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// business already exist
	t.Run("1.3", func(t *testing.T) {
		log.Println("==================================== business already exist")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			CreateUser([]map[string]interface{}{
				{
					"phone": "**********",
					"name":  "Asker 01",
					"type":  globalConstant.USER_TYPE_ASKER,
				},
			})
			CreateBusiness([]map[string]interface{}{
				{
					"_id":   "**********",
					"email": "<EMAIL>",
				}})
			Convey("When service handle request if conflict email", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"name":   "Asker 01",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_EXIST.ErrorCode)
				})
			})
		})
	})
	// user is member of another business
	t.Run("1.3.1", func(t *testing.T) {
		log.Println("==================================== user is member of another business")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			CreateUser([]map[string]interface{}{
				{
					"phone": "**********",
					"name":  "Asker 01",
					"type":  globalConstant.USER_TYPE_ASKER,
				},
			})
			CreateBusinessMember([]map[string]interface{}{
				{
					"_id":    "**********",
					"userId": "**********",
					"status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				},
			})
			Convey("When service handle request if conflict email", func() {
				body := map[string]interface{}{
					"userId": "**********",
					"name":   "Asker 01",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_IS_MEMBER_OF_ANOTHER_BUSINESS.ErrorCode)
				})
			})
		})
	})
	// conflict email
	t.Run("1.4", func(t *testing.T) {
		log.Println("==================================== Conflict email")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			CreateUser([]map[string]interface{}{
				{
					"phone": "0834567891",
					"name":  "Asker 01",
					"type":  globalConstant.USER_TYPE_ASKER,
				},
			})
			CreateBusiness([]map[string]interface{}{
				{
					"_id":   "**********",
					"email": "<EMAIL>",
				}})
			Convey("When service handle request if conflict email", func() {
				body := map[string]interface{}{
					"userId": "0834567891",
					"name":   "Asker 01",
					"email":  "<EMAIL>",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_EMAIL_EXIST.ErrorCode)
				})
			})
		})
	})
	// conflict tax code
	t.Run("1.5", func(t *testing.T) {
		log.Println("==================================== Conflict tax code")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			CreateUser([]map[string]interface{}{
				{
					"phone": "0834567891",
					"name":  "Asker 01",
					"type":  globalConstant.USER_TYPE_ASKER,
				},
			})
			CreateBusiness([]map[string]interface{}{
				{
					"_id":     "**********",
					"taxCode": "123456",
				}})
			Convey("When service handle request if conflict tax code", func() {
				body := map[string]interface{}{
					"userId":  "0834567891",
					"name":    "Asker 01",
					"email":   "<EMAIL>",
					"taxCode": "123456",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TAX_CODE_EXIST.ErrorCode)
				})
			})
		})
	})
	// insert business success
	t.Run("1.6", func(t *testing.T) {
		log.Println("==================================== insert business success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessUser), t, func() {
			CreateUser([]map[string]interface{}{
				{
					"phone": "**********",
					"name":  "Asker 01",
					"type":  globalConstant.USER_TYPE_ASKER,
				},
			})
			body := map[string]interface{}{
				"userId":       "**********",
				"name":         "Asker 01",
				"email":        "<EMAIL>",
				"taxCode":      "123456",
				"sector":       "IT",
				"businessSize": "LARGE",
				"address":      "Số 9 Trần Não, Phường Bình An, Quận 2",
				"businessLicense": []map[string]interface{}{
					{
						"name": "test",
						"url":  "testURL",
					}, {
						"name": "test2",
						"url":  "testURL2",
					},
				},
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessUser, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)

					// check db
					var businessUser *business.Business
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], "**********", bson.M{}, &businessUser)

					So(businessUser.XId, ShouldEqual, "**********")
					So(businessUser.Name, ShouldEqual, "Asker 01")
					So(businessUser.Email, ShouldEqual, "<EMAIL>")
					So(businessUser.TaxCode, ShouldEqual, "123456")
					So(businessUser.Sector, ShouldEqual, "IT")
					So(businessUser.BusinessSize, ShouldEqual, "LARGE")
					So(businessUser.Address, ShouldEqual, "Số 9 Trần Não, Phường Bình An, Quận 2")
					So(len(businessUser.BusinessLicense), ShouldEqual, 2)
					So(businessUser.BusinessLicense[0].Name, ShouldEqual, "test")
					So(businessUser.BusinessLicense[0].Url, ShouldEqual, "testURL")
					So(businessUser.BusinessLicense[1].Name, ShouldEqual, "test2")
					So(businessUser.BusinessLicense[1].Url, ShouldEqual, "testURL2")
					So(businessUser.Status, ShouldEqual, globalConstant.BUSINESS_STATUS_REGISTERED)
					So(businessUser.CreatedAt, ShouldNotBeNil)
				})
			})
		})
	})

	// ======================== 2. create business level ===================
	apiCreateBusinessLevel := "/api/v3/api-asker-vn/create-business-level"
	// validate data
	t.Run("2.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessLevel), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing name", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_NAME_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params amount invalid", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"name":       "xxx",
					"amount":     -500,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_AMOUNT_INVALID.ErrorCode)
				})
			})
		})
	})
	// business not found
	t.Run("2.2", func(t *testing.T) {
		log.Println("==================================== business not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessLevel), t, func() {
			Convey("When service handle request if business not found", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"name":       "xxx",
					"amount":     300_000,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// insert business level success
	t.Run("2.3", func(t *testing.T) {
		log.Println("==================================== insert business level success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiCreateBusinessLevel), t, func() {
			CreateBusiness([]map[string]interface{}{
				{
					"_id":    "businessTest",
					"name":   "bTaskee",
					"status": globalConstant.BUSINESS_STATUS_ACTIVE,
				},
			})
			body := map[string]interface{}{
				"businessId": "businessTest",
				"name":       "Bronze",
				"amount":     300_000,
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiCreateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)

					So(result["detail"]["_id"], ShouldNotBeEmpty)
					So(result["detail"]["name"], ShouldEqual, "Bronze")
					So(result["detail"]["amount"], ShouldEqual, 300_000)
					var businessUser *businessLevel.BusinessLevel
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], result["detail"]["_id"].(string), bson.M{}, &businessUser)

					So(businessUser.Name, ShouldEqual, "Bronze")
					So(businessUser.BusinessId, ShouldEqual, "businessTest")
					So(businessUser.Amount, ShouldEqual, 300_000)
					So(businessUser.Status, ShouldEqual, globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE)
					So(businessUser.CreatedAt, ShouldNotBeNil)
				})
			})
		})
	})

	// ======================== 3. list members by level ===================
	apiListMembersByLevel := "/api/v3/api-asker-vn/list-members-by-level"
	// validate data
	t.Run("3.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMembersByLevel), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMembersByLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// get user balance failed
	t.Run("3.2", func(t *testing.T) {
		log.Println("==================================== get user balance failed")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMembersByLevel), t, func() {
			Convey("When service handle request if user not found", func() {
				body := map[string]interface{}{
					"businessId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMembersByLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// list members by level success
	t.Run("3.3", func(t *testing.T) {
		log.Println("==================================== list members by level success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMembersByLevel), t, func() {
			// Create User
			CreateUser([]map[string]interface{}{
				{
					"phone": "**********",
					"name":  "Asker 00",
					"type":  globalConstant.USER_TYPE_ASKER,
				}, {
					"phone":  "0834567891",
					"name":   "Asker 01",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-01",
					"emails": []map[string]interface{}{
						{
							"address": "email-01",
						},
					},
				}, {
					"phone":  "0834567892",
					"name":   "Asker 02",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-02",
				}, {
					"phone":  "0834567893",
					"name":   "Asker 03",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-03",
				}, {
					"phone":  "0834567894",
					"name":   "Asker 04",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-04",
				}, {
					"phone":  "0834567895",
					"name":   "Asker 05",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-05",
				}, {
					"phone":  "0834567896",
					"name":   "Asker 06",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-06",
				}, {
					"phone":  "0834567897",
					"name":   "Asker 07",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-07",
				}, {
					"phone":  "0834567898",
					"name":   "Asker 08",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-08",
				},
			})
			CreateBusiness([]map[string]interface{}{
				{
					"_id":    "**********",
					"name":   "bTaskee",
					"status": globalConstant.BUSINESS_STATUS_ACTIVE,
					"bPay":   13_240_000.0,
				},
			})
			CreateBusinessLevel([]map[string]interface{}{
				{
					"_id":        "levelBronzeId",
					"name":       "Bronze",
					"businessId": "**********",
					"amount":     300_000,
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
				{
					"_id":        "levelSilverId",
					"name":       "Silver",
					"businessId": "**********",
					"amount":     600_000,
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
				{
					"_id":        "levelGoldId",
					"name":       "Gold",
					"businessId": "**********",
					"amount":     1_200_000,
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
			})
			CreateBusinessMember([]map[string]interface{}{
				{
					"_id":        "businessMember_1",
					"businessId": "**********",
					"userId":     "0834567891",
					"levelId":    "levelBronzeId",
					"bPay":       300_000,
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				}, {
					"_id":        "businessMember_2",
					"businessId": "**********",
					"userId":     "0834567892",
					"levelId":    "levelBronzeId",
					"bPay":       300_000,
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				}, {
					"_id":        "businessMember_3",
					"businessId": "**********",
					"userId":     "0834567893",
					"levelId":    "levelBronzeId",
					"bPay":       300_000,
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				}, {
					"_id":        "businessMember_4",
					"businessId": "**********",
					"userId":     "0834567894",
					"levelId":    "levelSilverId",
					"bPay":       600_000,
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				}, {
					"_id":        "businessMember_5",
					"businessId": "**********",
					"userId":     "0834567895",
					"levelId":    "levelSilverId",
					"bPay":       600_000,
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				}, {
					"_id":        "businessMember_7",
					"businessId": "**********",
					"userId":     "0834567897",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				}, {
					"_id":        "businessMember_8",
					"businessId": "**********",
					"userId":     "0834567898",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
				},
			})
			body := map[string]interface{}{
				"businessId": "**********",
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMembersByLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(result["balance"], ShouldEqual, 13_240_000)

					membersByLevel := []*listMembersByLevel.BusinessLevelDetail{}
					b, _ = json.Marshal(result["levels"])
					json.Unmarshal(b, &membersByLevel)

					testedMember := map[string]bool{}
					testedLevel := map[string]bool{}

					So(len(membersByLevel), ShouldEqual, 4)
					So(len(membersByLevel), ShouldEqual, 4)
					So(membersByLevel[0].NameText.En, ShouldEqual, "No group yet")
					So(membersByLevel[0].TotalMember, ShouldEqual, 2)
					So(len(membersByLevel[0].Members), ShouldEqual, 2)
					for _, member := range membersByLevel[0].Members {
						switch member.XId {
						case "businessMember_7":
							So(member.Avatar, ShouldEqual, "avatar-07")
							So(member.Name, ShouldEqual, "Asker 07")
							So(member.Phone, ShouldEqual, "0834567897")
							testedMember[member.XId] = true
						case "businessMember_8":
							So(member.Avatar, ShouldEqual, "avatar-08")
							So(member.Name, ShouldEqual, "Asker 08")
							So(member.Phone, ShouldEqual, "0834567898")
							testedMember[member.XId] = true
						}
					}

					for i := 1; i < len(membersByLevel); i++ {
						level := membersByLevel[i]
						switch level.XId {
						case "levelBronzeId":
							So(level.Name, ShouldEqual, "Bronze")
							So(level.NameText.En, ShouldEqual, "Bronze")
							So(level.Amount, ShouldEqual, 300_000)
							So(level.TotalMember, ShouldEqual, 3)
							So(len(level.Members), ShouldEqual, 3)
							for _, member := range level.Members {
								switch member.XId {
								case "businessMember_1":
									So(member.Avatar, ShouldEqual, "avatar-01")
									So(member.Name, ShouldEqual, "Asker 01")
									So(member.Phone, ShouldEqual, "0834567891")
									So(member.BPay, ShouldEqual, 300_000)
									So(member.Email, ShouldEqual, "email-01")
									testedMember[member.XId] = true
								case "businessMember_2":
									So(member.Avatar, ShouldEqual, "avatar-02")
									So(member.Name, ShouldEqual, "Asker 02")
									So(member.Phone, ShouldEqual, "0834567892")
									So(member.BPay, ShouldEqual, 300_000)
									testedMember[member.XId] = true
								case "businessMember_3":
									So(member.Avatar, ShouldEqual, "avatar-03")
									So(member.Name, ShouldEqual, "Asker 03")
									So(member.Phone, ShouldEqual, "0834567893")
									So(member.BPay, ShouldEqual, 300_000)
									testedMember[member.XId] = true
								}
							}
							testedLevel[level.XId] = true
						case "levelSilverId":
							So(level.Name, ShouldEqual, "Silver")
							So(level.NameText.En, ShouldEqual, "Silver")
							So(level.Amount, ShouldEqual, 600_000)
							So(level.TotalMember, ShouldEqual, 2)
							So(len(level.Members), ShouldEqual, 2)
							for _, member := range level.Members {
								switch member.XId {
								case "businessMember_4":
									So(member.Avatar, ShouldEqual, "avatar-04")
									So(member.Name, ShouldEqual, "Asker 04")
									So(member.Phone, ShouldEqual, "0834567894")
									So(member.BPay, ShouldEqual, 600_000)
									testedMember[member.XId] = true
								case "businessMember_5":
									So(member.Avatar, ShouldEqual, "avatar-05")
									So(member.Name, ShouldEqual, "Asker 05")
									So(member.Phone, ShouldEqual, "0834567895")
									So(member.BPay, ShouldEqual, 600_000)
									testedMember[member.XId] = true
								}
							}
							testedLevel[level.XId] = true
						case "levelGoldId":
							So(level.Name, ShouldEqual, "Gold")
							So(level.NameText.En, ShouldEqual, "Gold")
							So(level.Amount, ShouldEqual, 1_200_000)
							So(level.TotalMember, ShouldEqual, 0)
							So(len(level.Members), ShouldEqual, 0)
							testedLevel[level.XId] = true
						}
					}

					So(len(testedMember), ShouldEqual, 7)
					So(len(testedLevel), ShouldEqual, 3)
				})
			})
		})
	})

	// ======================== 4. list member transactions ===================
	apiListMemberTransactions := "/api/v3/api-asker-vn/list-business-member-transactions"

	positiveType := globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT
	negativeType := globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT
	memberId := "businessMember_1"
	createTestListMemberTransactionFlow := func() (taskIds []string, currentDate time.Time) {
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			}, {
				"phone": "0834567810",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567820",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567830",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		taskIds = CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "**********",
				"taskerPhone": "0834567810",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			}, {
				"askerPhone":  "**********",
				"taskerPhone": "0834567820",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			}, {
				"askerPhone":  "**********",
				"taskerPhone": "0834567830",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
			},
		})

		// Create transactions
		now := globalLib.GetCurrentTime(local.TimeZone)
		date := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, local.TimeZone)
		CreateBusinessMemberTransactions([]map[string]interface{}{
			{
				"_id":       "transaction_1",
				"type":      positiveType,
				"memberId":  memberId,
				"amount":    600_000,
				"name":      globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TOP_UP_BPAY_BY_BUSINESS,
				"createdAt": date,
			}, {
				"_id":       "transaction_2",
				"type":      negativeType,
				"memberId":  memberId,
				"amount":    100_000,
				"name":      globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TASK,
				"createdAt": date.AddDate(0, 0, 1),
				"taskId":    taskIds[0],
			}, { // last month
				"_id":       "transaction_3",
				"type":      negativeType,
				"memberId":  memberId,
				"amount":    100_000,
				"name":      globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TASK,
				"reason":    "Use service",
				"createdAt": date.AddDate(0, 0, -7),
				"taskId":    taskIds[1],
			}, {
				"_id":       "transaction_4",
				"type":      negativeType,
				"memberId":  memberId,
				"amount":    100_000,
				"name":      globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_RATING_TIP,
				"reason":    "Tip",
				"createdAt": date.AddDate(0, 0, 2),
			}, {
				"_id":       "transaction_5",
				"type":      negativeType,
				"memberId":  memberId,
				"amount":    100_000,
				"name":      globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TASK,
				"reason":    "Use service",
				"createdAt": date.AddDate(0, 0, 3),
				"taskId":    taskIds[2],
			}, {
				"_id":       "transaction_6",
				"type":      negativeType,
				"memberId":  "wrong_member_id",
				"amount":    100_000,
				"name":      globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TASK,
				"reason":    "Use service",
				"createdAt": date.AddDate(0, 0, 3),
				"taskId":    "wrong_task_id",
			},
		})
		return taskIds, date
	}
	// validate data
	t.Run("4.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMemberTransactions), t, func() {
			Convey("When service handle request if request params missing memberId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMemberTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_MEMBER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing month", func() {
				body := map[string]interface{}{
					"memberId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMemberTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MONTH_AND_YEAR_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing year", func() {
				body := map[string]interface{}{
					"memberId": "**********",
					"month":    7,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMemberTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MONTH_AND_YEAR_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// member not found
	t.Run("4.2", func(t *testing.T) {
		log.Println("==================================== member not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMemberTransactions), t, func() {

			// Create
			body := map[string]interface{}{
				"memberId": "**********",
				"month":    7,
				"year":     2024,
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMemberTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					memberTransactions := []*getListMemberTransaction.BusinessMemberTransactionDetail{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &memberTransactions)

					So(res.Code, ShouldEqual, 200)
					So(len(memberTransactions), ShouldEqual, 0)
				})
			})
		})
	})
	// list transaction success: case current month
	t.Run("4.3", func(t *testing.T) {
		log.Println("==================================== list transaction success: case current month")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMemberTransactions), t, func() {
			taskIds, date := createTestListMemberTransactionFlow()
			CreateReportTransaction([]map[string]interface{}{
				{"transactionId": "transaction_1"},
				{"transactionId": "transaction_4"},
			})
			body := map[string]interface{}{
				"memberId": memberId,
				"month":    date.Month(),
				"year":     date.Year(),
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMemberTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					memberTransactions := []*getListMemberTransaction.BusinessMemberTransactionDetail{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &memberTransactions)

					So(res.Code, ShouldEqual, 200)
					So(len(memberTransactions), ShouldEqual, 4)
					testedItem := map[string]bool{}
					for _, transaction := range memberTransactions {
						So(transaction.Currency.Code, ShouldEqual, "VND")
						So(transaction.Currency.Sign, ShouldEqual, "₫")
						switch transaction.XId {
						case "transaction_1":
							So(transaction.Amount, ShouldEqual, 600_000)
							So(transaction.Type, ShouldEqual, positiveType)
							So(transaction.Name, ShouldEqual, "TOP_UP_BPAY_BY_BUSINESS")
							So(transaction.NameText.Vi, ShouldEqual, "Nạp tiền bởi doanh nghiệp")
							So(transaction.CreatedAt, ShouldNotBeNil)
							So(transaction.TaskId, ShouldEqual, "")
							So(transaction.IsReport, ShouldEqual, true)
							testedItem[transaction.XId] = true
						case "transaction_2":
							So(transaction.Amount, ShouldEqual, 100_000)
							So(transaction.Type, ShouldEqual, negativeType)
							So(transaction.Name, ShouldEqual, "TASK")
							So(transaction.NameText.Vi, ShouldEqual, "Thanh toán công việc")
							So(transaction.CreatedAt, ShouldNotBeNil)
							So(transaction.TaskId, ShouldEqual, taskIds[0])
							So(transaction.ServiceText.En, ShouldEqual, globalConstant.SERVICE_NAME_HOME_CLEANING)
							So(transaction.IsReport, ShouldEqual, false)
							testedItem[transaction.XId] = true
						case "transaction_4":
							So(transaction.Amount, ShouldEqual, 100_000)
							So(transaction.Type, ShouldEqual, negativeType)
							So(transaction.Name, ShouldEqual, "RATING_TIP")
							So(transaction.NameText.Vi, ShouldEqual, "Tiền tips")
							So(transaction.Reason, ShouldEqual, "Tip")
							So(transaction.CreatedAt, ShouldNotBeNil)
							So(transaction.TaskId, ShouldEqual, "")
							So(transaction.IsReport, ShouldEqual, true)
							testedItem[transaction.XId] = true
						case "transaction_5":
							So(transaction.Amount, ShouldEqual, 100_000)
							So(transaction.Type, ShouldEqual, negativeType)
							So(transaction.Name, ShouldEqual, "TASK")
							So(transaction.NameText.Vi, ShouldEqual, "Thanh toán công việc")
							So(transaction.Reason, ShouldEqual, "Use service")
							So(transaction.CreatedAt, ShouldNotBeNil)
							So(transaction.TaskId, ShouldEqual, taskIds[2])
							So(transaction.ServiceText.En, ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
							So(transaction.IsReport, ShouldEqual, false)
							testedItem[transaction.XId] = true
						}
					}

					So(len(testedItem), ShouldEqual, 4)
				})
			})
		})
	})
	// list transaction success: case previous month
	t.Run("4.4", func(t *testing.T) {
		log.Println("==================================== list transaction success: case previous month")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListMemberTransactions), t, func() {
			taskIds, date := createTestListMemberTransactionFlow()
			body := map[string]interface{}{
				"memberId": memberId,
				"month":    date.AddDate(0, -1, 0).Month(),
				"year":     date.AddDate(0, -1, 0).Year(),
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListMemberTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					memberTransactions := []*getListMemberTransaction.BusinessMemberTransactionDetail{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &memberTransactions)

					So(res.Code, ShouldEqual, 200)
					So(len(memberTransactions), ShouldEqual, 1)
					testedItem := map[string]bool{}
					for _, transaction := range memberTransactions {
						So(transaction.Currency.Code, ShouldEqual, "VND")
						So(transaction.Currency.Sign, ShouldEqual, "₫")
						switch transaction.XId {
						case "transaction_3":
							So(transaction.Amount, ShouldEqual, 100_000)
							So(transaction.Type, ShouldEqual, negativeType)
							So(transaction.Name, ShouldEqual, "TASK")
							So(transaction.NameText.Vi, ShouldEqual, "Thanh toán công việc")
							So(transaction.Reason, ShouldEqual, "Use service")
							So(transaction.CreatedAt, ShouldNotBeNil)
							So(transaction.TaskId, ShouldEqual, taskIds[1])
							So(transaction.ServiceText.En, ShouldEqual, globalConstant.SERVICE_NAME_HOME_CLEANING)
							testedItem[transaction.XId] = true
						}
					}

					So(len(testedItem), ShouldEqual, 1)
				})
			})
		})
	})
	// ======================== 5. list business transactions ===================
	apiListBusinessTransactions := "/api/v3/api-asker-vn/list-business-transactions"
	// validate data
	t.Run("5.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListBusinessTransactions), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListBusinessTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing month", func() {
				body := map[string]interface{}{
					"businessId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListBusinessTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MONTH_AND_YEAR_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing year", func() {
				body := map[string]interface{}{
					"businessId": "**********",
					"month":      7,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListBusinessTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MONTH_AND_YEAR_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// empty transactions
	t.Run("5.2", func(t *testing.T) {
		log.Println("==================================== empty transactions")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListBusinessTransactions), t, func() {
			body := map[string]interface{}{
				"businessId": "**********",
				"month":      7,
				"year":       2024,
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListBusinessTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					memberTransactions := []*getListBusinessTransaction.BusinessTransactionsByKey{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &memberTransactions)

					So(res.Code, ShouldEqual, 200)
					So(len(memberTransactions), ShouldEqual, 4)
					So(memberTransactions[0].Key, ShouldEqual, getListBusinessTransaction.BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY)
					So(memberTransactions[0].TotalAmount, ShouldEqual, 0)
					So(len(memberTransactions[0].Transactions), ShouldEqual, 0)
					So(memberTransactions[1].Key, ShouldEqual, getListBusinessTransaction.BUSINESS_TRANSACTION_KEY_TOP_UP_MEMBER)
					So(memberTransactions[1].TotalAmount, ShouldEqual, 0)
					So(len(memberTransactions[1].Transactions), ShouldEqual, 0)
					So(memberTransactions[2].Key, ShouldEqual, getListBusinessTransaction.BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER)
					So(memberTransactions[2].TotalAmount, ShouldEqual, 0)
					So(len(memberTransactions[2].Transactions), ShouldEqual, 0)
					So(memberTransactions[3].Key, ShouldEqual, getListBusinessTransaction.BUSINESS_TRANSACTION_KEY_REVOKE_BPAY_MEMBER)
					So(memberTransactions[3].TotalAmount, ShouldEqual, 0)
					So(len(memberTransactions[3].Transactions), ShouldEqual, 0)
				})
			})
		})
	})
	// list business transactions
	t.Run("5.3", func(t *testing.T) {
		log.Println("==================================== list business transactions")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiListBusinessTransactions), t, func() {
			now := globalLib.GetCurrentTime(local.TimeZone)
			date := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, local.TimeZone)
			CreateUser([]map[string]interface{}{
				{
					"phone": "**********",
					"name":  "Asker 00",
					"type":  globalConstant.USER_TYPE_ASKER,
				}, {
					"phone":  "0834567891",
					"name":   "Asker 01",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-01",
				}, {
					"phone":  "0834567892",
					"name":   "Asker 02",
					"type":   globalConstant.USER_TYPE_ASKER,
					"avatar": "avatar-02",
				},
			})
			// Create Business
			CreateBusiness([]map[string]interface{}{
				{
					"_id":    "**********",
					"name":   "bTaskee",
					"status": globalConstant.BUSINESS_STATUS_ACTIVE,
					"bPay":   13_240_000.0,
				},
			})
			CreateBusinessTransactions([]map[string]interface{}{
				{
					"_id":        "businessTransaction_1",
					"businessId": "**********",
					"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
					"amount":     300_000.0,
					"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE,
					"reason":     "Nap tien cho doanh nghiep",
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 1),
				}, {
					"_id":        "businessTransaction_2",
					"businessId": "**********",
					"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
					"amount":     400_000.0,
					"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE,
					"reason":     "Nap tien cho doanh nghiep",
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 2),
				}, {
					"_id":        "businessTransaction_3",
					"businessId": "**********",
					"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
					"amount":     300_000.0,
					"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 2),
					"levelInfo": map[string]interface{}{
						"levelId":   "levelBronze",
						"levelName": "Bronze",
					},
				}, {
					"_id":        "businessTransaction_4",
					"businessId": "**********",
					"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
					"amount":     600_000.0,
					"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 2),
					"levelInfo": map[string]interface{}{
						"levelId":   "levelSilver",
						"levelName": "Silver",
					},
				}, {
					"_id":        "businessTransaction_5",
					"businessId": "**********",
					"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
					"amount":     200_000.0,
					"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 3),
					"levelInfo": map[string]interface{}{
						"levelId":   "levelBronze",
						"levelName": "Bronze",
					},
				},
			})
			CreateBusinessMemberTransactions([]map[string]interface{}{
				{
					"_id":        "transaction_1",
					"type":       negativeType,
					"userId":     "0834567891",
					"memberId":   "0834567891",
					"businessId": "**********",
					"amount":     600_000,
					"name":       globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TASK,
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 1),
				}, {
					"_id":        "transaction_2",
					"type":       negativeType,
					"userId":     "0834567891",
					"memberId":   "0834567891",
					"businessId": "**********",
					"amount":     100_000,
					"name":       globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_RATING_TIP,
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 1),
				}, {
					"_id":        "transaction_3",
					"type":       negativeType,
					"userId":     "0834567892",
					"memberId":   "0834567892",
					"businessId": "**********",
					"amount":     300_000,
					"name":       globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_TASK,
					"currency": map[string]interface{}{
						"code": "VND",
						"sign": "₫",
					},
					"createdAt": date.AddDate(0, 0, 7),
				},
			})
			CreateReportTransaction([]map[string]interface{}{
				{"transactionId": "businessTransaction_1"},
			})

			body := map[string]interface{}{
				"businessId": "**********",
				"month":      date.Month(),
				"year":       date.Year(),
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiListBusinessTransactions, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					memberTransactions := []*getListBusinessTransaction.BusinessTransactionsByKey{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &memberTransactions)

					So(res.Code, ShouldEqual, 200)
					So(len(memberTransactions), ShouldEqual, 4)
					So(memberTransactions[0].Key, ShouldEqual, "TOP_UP_BPAY")
					So(memberTransactions[0].KeyText.Vi, ShouldEqual, "Đã nạp vào tài khoản")
					So(memberTransactions[0].TotalAmount, ShouldEqual, 700_000.0)
					So(len(memberTransactions[0].Transactions), ShouldEqual, 2)
					So(memberTransactions[0].Transactions[0].Type, ShouldEqual, globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT)
					So(memberTransactions[0].Transactions[0].Name.Vi, ShouldEqual, "Nạp tiền bởi bTaskee")
					So(memberTransactions[0].Transactions[0].Currency.Code, ShouldEqual, "VND")
					So(memberTransactions[0].Transactions[0].Currency.Sign, ShouldEqual, "₫")
					So(memberTransactions[0].Transactions[0].Amount, ShouldEqual, 400_000.0)
					So(memberTransactions[0].Transactions[0].IsReport, ShouldEqual, false)
					So(memberTransactions[0].Transactions[1].Type, ShouldEqual, globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT)
					So(memberTransactions[0].Transactions[1].Name.Vi, ShouldEqual, "Nạp tiền bởi bTaskee")
					So(memberTransactions[0].Transactions[1].Currency.Code, ShouldEqual, "VND")
					So(memberTransactions[0].Transactions[1].Currency.Sign, ShouldEqual, "₫")
					So(memberTransactions[0].Transactions[1].Amount, ShouldEqual, 300_000.0)
					So(memberTransactions[0].Transactions[1].IsReport, ShouldEqual, true)

					So(memberTransactions[1].Key, ShouldEqual, "TOP_UP_MEMBER")
					So(memberTransactions[1].KeyText.Vi, ShouldEqual, "Đã nạp cho nhân viên")
					So(memberTransactions[1].TotalAmount, ShouldEqual, 1_100_000.0)
					So(len(memberTransactions[1].Transactions), ShouldEqual, 2)
					tested := map[string]bool{}
					for _, transaction := range memberTransactions[1].Transactions {
						So(transaction.Type, ShouldEqual, globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT)
						So(transaction.Currency.Code, ShouldEqual, "VND")
						So(transaction.Currency.Sign, ShouldEqual, "₫")
						switch transaction.Name.Vi {
						case "Bronze":
							So(transaction.Amount, ShouldEqual, 500_000.0)
							tested["Bronze"] = true
						case "Silver":
							So(transaction.Amount, ShouldEqual, 600_000.0)
							tested["Silver"] = true
						}
					}
					So(len(tested), ShouldEqual, 2)

					So(memberTransactions[2].Key, ShouldEqual, "USED_BPAY_MEMBER")
					So(memberTransactions[2].KeyText.Vi, ShouldEqual, "Nhân viên đã sử dụng")
					So(memberTransactions[2].TotalAmount, ShouldEqual, 1_000_000.0)
					So(len(memberTransactions[2].Transactions), ShouldEqual, 2)
					tested = map[string]bool{}
					for _, transaction := range memberTransactions[2].Transactions {
						So(transaction.Type, ShouldEqual, globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT)
						So(transaction.Currency.Code, ShouldEqual, "VND")
						switch transaction.Name.Vi {
						case "Asker 01":
							So(transaction.Amount, ShouldEqual, 700_000.0)
							So(transaction.Avatar, ShouldEqual, "avatar-01")
							tested["Asker 01"] = true
						case "Asker 02":
							So(transaction.Amount, ShouldEqual, 300_000.0)
							So(transaction.Avatar, ShouldEqual, "avatar-02")
							tested["Asker 02"] = true
						}
					}
					So(len(tested), ShouldEqual, 2)

					So(memberTransactions[3].Key, ShouldEqual, "REVOKE_BPAY_MEMBER")
					So(memberTransactions[3].KeyText.Vi, ShouldEqual, "Đã thu hồi từ nhân viên")
					So(memberTransactions[3].TotalAmount, ShouldEqual, 0)
					So(len(memberTransactions[3].Transactions), ShouldEqual, 0)
				})
			})
		})
	})
	// ======================== 6. remove member from business ===================
	apiRemoveMemberFromBusiness := "/api/v3/api-asker-vn/remove-member-from-business"
	// validate data
	t.Run("6.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveMemberFromBusiness), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveMemberFromBusiness, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing memberId", func() {
				body := map[string]interface{}{
					"businessId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveMemberFromBusiness, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_MEMBER_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// business member not found
	t.Run("6.2", func(t *testing.T) {
		log.Println("==================================== business member not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveMemberFromBusiness), t, func() {
			Convey("When service handle request if business member not found", func() {
				body := map[string]interface{}{
					"businessId": "**********",
					"memberId":   "memberId_1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveMemberFromBusiness, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_MEMBER_NOT_FOUND.ErrorCode)
				})
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveMemberFromBusiness), t, func() {
			CreateBusinessMember([]map[string]interface{}{
				{
					"_id":        "memberId_1",
					"businessId": "**********",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_INACTIVE,
				},
			})
			Convey("When service handle request if business member not found", func() {
				body := map[string]interface{}{
					"businessId": "**********",
					"memberId":   "memberId_1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveMemberFromBusiness, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_MEMBER_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// not have permission
	t.Run("6.3", func(t *testing.T) {
		log.Println("==================================== not have permission")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveMemberFromBusiness), t, func() {
			Convey("When service handle request if business member not found", func() {
				CreateBusiness([]map[string]interface{}{
					{
						"_id":  "**********",
						"bPay": 300_000.0,
					},
				})
				CreateBusinessLevel([]map[string]interface{}{
					{
						"_id":        "level_1",
						"businessId": "**********",
						"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
					},
				})
				CreateBusinessMember([]map[string]interface{}{
					{
						"_id":        "memberId_1",
						"businessId": "0834567892",
						"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
						"bPay":       100_000.0,
					},
				})

				body := map[string]interface{}{
					"businessId": "**********",
					"memberId":   "memberId_1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveMemberFromBusiness, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS.ErrorCode)
				})
			})
		})
	})
	// remove business member success
	t.Run("6.4", func(t *testing.T) {
		log.Println("==================================== remove business member success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveMemberFromBusiness), t, func() {
			CreateBusiness([]map[string]interface{}{
				{
					"_id":    "**********",
					"bPay":   300_000.0,
					"status": globalConstant.BUSINESS_STATUS_ACTIVE,
				},
			})
			CreateBusinessLevel([]map[string]interface{}{
				{
					"_id":        "level_1",
					"businessId": "**********",
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
			})
			CreateBusinessMember([]map[string]interface{}{
				{
					"_id":        "memberId_1",
					"businessId": "**********",
					"levelId":    "level_1",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
					"bPay":       100_000.0,
				},
			})
			body := map[string]interface{}{
				"businessId": "**********",
				"memberId":   "memberId_1",
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveMemberFromBusiness, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					var member *businessMember.BusinessMember
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], "memberId_1", bson.M{"status": 1, "bBay": 1, "businessId": 1, "levelId": 1}, &member)
					So(member.BPay, ShouldEqual, 0.0)
					So(member.Status, ShouldEqual, globalConstant.BUSINESS_MEMBER_STATUS_INACTIVE)
					So(member.LevelId, ShouldBeEmpty)

					var business *business.Business
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], "**********", bson.M{"bPay": 1}, &business)
					So(business.BPay, ShouldEqual, 400_000.0)

					memberMap := map[string]interface{}{}
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], "memberId_1", bson.M{"changeHistories": 1}, &memberMap)
					changeHistories := []map[string]interface{}{}
					b, _ := json.Marshal(memberMap["changeHistories"])
					json.Unmarshal(b, &changeHistories)
					So(len(changeHistories), ShouldEqual, 1)

					So(changeHistories[0]["key"], ShouldEqual, "REMOVE_MEMBER_FROM_BUSINESS")
					So(changeHistories[0]["createdBy"], ShouldEqual, "**********")
					So(changeHistories[0]["createdAt"], ShouldNotBeNil)

					content := cast.ToStringMap(changeHistories[0]["content"])
					So(content["oldStatus"], ShouldEqual, globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE)
					So(content["newStatus"], ShouldEqual, globalConstant.BUSINESS_MEMBER_STATUS_INACTIVE)
				})
			})
		})
	})

	// ================= 7. remove business level ==================
	apiRemoveBusinessLevel := "/api/v3/api-asker-vn/remove-business-level"
	// validate data
	t.Run("7.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveBusinessLevel), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing levelId", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// level not found
	t.Run("7.2", func(t *testing.T) {
		log.Println("==================================== level not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveBusinessLevel), t, func() {
			Convey("When service handle request if user not found", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"levelId":    "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// not have permission
	t.Run("7.3", func(t *testing.T) {
		log.Println("==================================== not have permission")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveBusinessLevel), t, func() {
			Convey("When service handle request if business member not found", func() {
				CreateBusiness([]map[string]interface{}{
					{
						"_id":  "**********",
						"bPay": 300_000.0,
					},
				})
				CreateBusinessLevel([]map[string]interface{}{
					{
						"_id":        "level_1",
						"businessId": "0834567892",
						"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
					},
				})
				body := map[string]interface{}{
					"businessId": "**********",
					"levelId":    "level_1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS.ErrorCode)
				})
			})
		})
	})
	// remove level success
	t.Run("7.4", func(t *testing.T) {
		log.Println("==================================== remove level success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveBusinessLevel), t, func() {
			CreateBusiness([]map[string]interface{}{
				{
					"_id":  "**********",
					"bPay": 300_000.0,
				},
			})
			CreateBusinessLevel([]map[string]interface{}{
				{
					"_id":        "level_1",
					"businessId": "**********",
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
			})
			CreateBusinessMember([]map[string]interface{}{
				{
					"_id":        "memberId_1",
					"businessId": "**********",
					"levelId":    "level_1",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
					"bPay":       100_000.0,
				},
				{
					"_id":        "memberId_2",
					"businessId": "**********",
					"levelId":    "level_1",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
					"bPay":       100_000.0,
				},
				{
					"_id":        "memberId_3",
					"businessId": "**********",
					"levelId":    "level_1",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
					"bPay":       100_000.0,
				},
			})
			body := map[string]interface{}{
				"businessId": "**********",
				"levelId":    "level_1",
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					var businessLevel *businessLevel.BusinessLevel
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], "level_1", bson.M{"status": 1}, &businessLevel)
					So(businessLevel.Status, ShouldEqual, globalConstant.BUSINESS_LEVEL_STATUS_DELETED)

					var members []*businessMember.BusinessMember
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"businessId": "**********"}, bson.M{"levelId": 1}, &members)
					So(len(members), ShouldEqual, 3)
					for _, member := range members {
						So(member.LevelId, ShouldBeEmpty)
					}

					levelMap := map[string]interface{}{}
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], "level_1", bson.M{"changeHistories": 1}, &levelMap)
					changeHistories := []map[string]interface{}{}
					b, _ := json.Marshal(levelMap["changeHistories"])
					json.Unmarshal(b, &changeHistories)
					So(len(changeHistories), ShouldEqual, 1)

					So(changeHistories[0]["key"], ShouldEqual, "REMOVE_LEVEL")
					So(changeHistories[0]["createdBy"], ShouldEqual, "**********")
					So(changeHistories[0]["createdAt"], ShouldNotBeNil)

					content := cast.ToStringMap(changeHistories[0]["content"])
					So(content["oldStatus"], ShouldEqual, globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE)
					So(content["newStatus"], ShouldEqual, globalConstant.BUSINESS_LEVEL_STATUS_DELETED)

					membersMap := []map[string]interface{}{}
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"businessId": "**********"}, bson.M{"changeHistories": 1}, &membersMap)
					So(len(membersMap), ShouldEqual, 3)
					for _, member := range membersMap {
						changeHistories := []map[string]interface{}{}
						b, _ := json.Marshal(member["changeHistories"])
						json.Unmarshal(b, &changeHistories)
						So(len(changeHistories), ShouldEqual, 1)

						So(changeHistories[0]["key"], ShouldEqual, "REMOVE_LEVEL")
						So(changeHistories[0]["createdBy"], ShouldEqual, "**********")
						So(changeHistories[0]["createdAt"], ShouldNotBeNil)

						content := cast.ToStringMap(changeHistories[0]["content"])
						So(content["oldLevelId"], ShouldEqual, "level_1")
						So(content["newLevelId"], ShouldBeNil)
					}
				})
			})
		})
	})

	// ================= 8. update business level ==================
	apiUpdateBusinessLevel := "/api/v3/api-asker-vn/update-business-level"
	// validate data
	t.Run("8.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateBusinessLevel), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing levelId", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing data update", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"levelId":    "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params invalid amount update", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"levelId":    "xxx",
					"amount":     -300_000,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_AMOUNT_INVALID.ErrorCode)
				})
			})
		})
	})
	// level not found
	t.Run("8.2", func(t *testing.T) {
		log.Println("==================================== level not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateBusinessLevel), t, func() {
			Convey("When service handle request if user not found", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"levelId":    "xxx",
					"name":       "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// not have permission
	t.Run("8.3", func(t *testing.T) {
		log.Println("==================================== not have permission")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateBusinessLevel), t, func() {
			Convey("When service handle request if business member not found", func() {
				CreateBusiness([]map[string]interface{}{
					{
						"_id":  "**********",
						"bPay": 300_000.0,
					},
				})
				CreateBusinessLevel([]map[string]interface{}{
					{
						"_id":        "level_1",
						"name":       "level 1",
						"amount":     400_000.0,
						"businessId": "0834567891",
						"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
					},
				})
				body := map[string]interface{}{
					"businessId": "**********",
					"levelId":    "level_1",
					"name":       "level 1 update",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS.ErrorCode)
				})
			})
		})
	})
	// update level name success
	t.Run("8.4", func(t *testing.T) {
		log.Println("==================================== update level name success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateBusinessLevel), t, func() {
			CreateBusiness([]map[string]interface{}{
				{
					"_id":  "**********",
					"bPay": 300_000.0,
				},
			})
			CreateBusinessLevel([]map[string]interface{}{
				{
					"_id":        "level_1",
					"name":       "level 1",
					"amount":     400_000.0,
					"businessId": "**********",
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
			})
			body := map[string]interface{}{
				"businessId": "**********",
				"levelId":    "level_1",
				"name":       "level 1 update",
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					var businessLevel *businessLevel.BusinessLevel
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], "level_1", bson.M{"name": 1, "amount": 1}, &businessLevel)
					So(businessLevel.Name, ShouldEqual, "level 1 update")
					So(businessLevel.Amount, ShouldEqual, 400_000.0)

					levelMap := map[string]interface{}{}
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], "level_1", bson.M{"changeHistories": 1}, &levelMap)
					changeHistories := []map[string]interface{}{}
					b, _ := json.Marshal(levelMap["changeHistories"])
					json.Unmarshal(b, &changeHistories)
					So(len(changeHistories), ShouldEqual, 1)

					So(changeHistories[0]["key"], ShouldEqual, "UPDATE_LEVEL_INFO")
					So(changeHistories[0]["createdBy"], ShouldEqual, "**********")
					So(changeHistories[0]["createdAt"], ShouldNotBeNil)

					content := cast.ToStringMap(changeHistories[0]["content"])
					So(content["oldName"], ShouldEqual, "level 1")
					So(content["newName"], ShouldEqual, "level 1 update")
				})
			})
		})
	})
	// update level amount success
	t.Run("8.5", func(t *testing.T) {
		log.Println("==================================== update level amount success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateBusinessLevel), t, func() {
			CreateBusiness([]map[string]interface{}{
				{
					"_id":  "**********",
					"bPay": 300_000.0,
				},
			})
			CreateBusinessLevel([]map[string]interface{}{
				{
					"_id":        "level_1",
					"name":       "level 1",
					"amount":     400_000.0,
					"businessId": "**********",
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
			})
			body := map[string]interface{}{
				"businessId": "**********",
				"levelId":    "level_1",
				"amount":     500_000.0,
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateBusinessLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					var businessLevel *businessLevel.BusinessLevel
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], "level_1", bson.M{"name": 1, "amount": 1}, &businessLevel)
					So(businessLevel.Name, ShouldEqual, "level 1")
					So(businessLevel.Amount, ShouldEqual, 500_000.0)

					levelMap := map[string]interface{}{}
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], "level_1", bson.M{"changeHistories": 1}, &levelMap)
					changeHistories := []map[string]interface{}{}
					b, _ := json.Marshal(levelMap["changeHistories"])
					json.Unmarshal(b, &changeHistories)
					So(len(changeHistories), ShouldEqual, 1)

					So(changeHistories[0]["key"], ShouldEqual, "UPDATE_LEVEL_INFO")
					So(changeHistories[0]["createdBy"], ShouldEqual, "**********")
					So(changeHistories[0]["createdAt"], ShouldNotBeNil)

					content := cast.ToStringMap(changeHistories[0]["content"])
					So(content["oldAmount"], ShouldEqual, 400_000.0)
					So(content["newAmount"], ShouldEqual, 500_000.0)
				})
			})
		})
	})

	// ================= 9. update member level ==================
	apiUpdateMemberLevel := "/api/v3/api-asker-vn/update-member-level"
	// validate data
	t.Run("9.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateMemberLevel), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing memberId", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_MEMBER_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing levelIds", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"memberId":   "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// member not found, level not found
	t.Run("9.2", func(t *testing.T) {
		log.Println("==================================== member not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateMemberLevel), t, func() {
			Convey("When service handle request if member not found", func() {
				body := map[string]interface{}{
					"businessId": "xxx",
					"memberId":   "xxx",
					"levelId":    "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_MEMBER_NOT_FOUND.ErrorCode)
				})
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateMemberLevel), t, func() {
			Convey("When service handle request if user not found", func() {
				CreateBusiness([]map[string]interface{}{
					{
						"_id":  "**********",
						"bPay": 300_000.0,
					},
				})
				CreateBusinessMember([]map[string]interface{}{
					{
						"_id":        "memberId_1",
						"businessId": "**********",
						"levelId":    "level_2",
						"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
						"bPay":       100_000.0,
					},
				})
				body := map[string]interface{}{
					"businessId": "**********",
					"memberId":   "memberId_1",
					"levelId":    "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_LEVEL_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// not have permission
	t.Run("9.3", func(t *testing.T) {
		log.Println("==================================== not have permission")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateMemberLevel), t, func() {
			Convey("When service handle request if business member not found", func() {
				CreateBusiness([]map[string]interface{}{
					{
						"_id":  "**********",
						"bPay": 300_000.0,
					},
				})
				CreateBusinessLevel([]map[string]interface{}{
					{
						"_id":        "level_1",
						"name":       "level 1",
						"amount":     400_000.0,
						"businessId": "0834567891",
						"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
					},
				})
				CreateBusinessMember([]map[string]interface{}{
					{
						"_id":        "memberId_1",
						"businessId": "0834567891",
						"levelId":    "level_2",
						"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
						"bPay":       100_000.0,
					},
				})
				body := map[string]interface{}{
					"businessId": "**********",
					"memberId":   "memberId_1",
					"levelId":    "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS.ErrorCode)
				})
			})
		})
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateMemberLevel), t, func() {
			Convey("When service handle request if business level not found", func() {
				CreateBusiness([]map[string]interface{}{
					{
						"_id":  "**********",
						"bPay": 300_000.0,
					},
				})
				CreateBusinessLevel([]map[string]interface{}{
					{
						"_id":        "level_1",
						"name":       "level 1",
						"amount":     400_000.0,
						"businessId": "0834567891",
						"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
					},
				})
				CreateBusinessMember([]map[string]interface{}{
					{
						"_id":        "memberId_1",
						"businessId": "**********",
						"levelId":    "level_2",
						"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
						"bPay":       100_000.0,
					},
				})
				body := map[string]interface{}{
					"businessId": "**********",
					"memberId":   "memberId_1",
					"levelId":    "level_1",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS.ErrorCode)
				})
			})
		})
	})
	// update member level success
	t.Run("9.4", func(t *testing.T) {
		log.Println("==================================== update member level success")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateMemberLevel), t, func() {
			CreateBusiness([]map[string]interface{}{
				{
					"_id":  "**********",
					"bPay": 300_000.0,
					"name": "ABC",
				},
			})
			CreateBusinessLevel([]map[string]interface{}{
				{
					"_id":        "level_1",
					"name":       "level 1",
					"amount":     400_000.0,
					"businessId": "**********",
					"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
				},
			})
			CreateBusinessMember([]map[string]interface{}{
				{
					"_id":        "memberId_1",
					"businessId": "**********",
					"levelId":    "level_2",
					"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
					"bPay":       100_000.0,
				},
			})
			body := map[string]interface{}{
				"businessId": "**********",
				"memberId":   "memberId_1",
				"levelId":    "level_1",
			}
			Convey("When service handle request", func() {
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateMemberLevel, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					var businessMember *businessMember.BusinessMember
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], "memberId_1", bson.M{"levelId": 1}, &businessMember)
					So(businessMember.LevelId, ShouldEqual, "level_1")

					levelMap := map[string]interface{}{}
					globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], "memberId_1", bson.M{"changeHistories": 1}, &levelMap)
					changeHistories := []map[string]interface{}{}
					b, _ := json.Marshal(levelMap["changeHistories"])
					json.Unmarshal(b, &changeHistories)
					So(len(changeHistories), ShouldEqual, 1)

					So(changeHistories[0]["key"], ShouldEqual, "UPDATE_MEMBER_LEVEL")
					So(changeHistories[0]["createdBy"], ShouldEqual, "**********")
					So(changeHistories[0]["createdAt"], ShouldNotBeNil)

					content := cast.ToStringMap(changeHistories[0]["content"])
					So(content["oldLevelId"], ShouldEqual, "level_2")
					So(content["newLevelId"], ShouldEqual, "level_1")
				})
			})
		})
	})

	// ======================== 10. send business report ===================
	apiSendBusinessReport := "/api/v3/api-asker-vn/send-business-report"
	// validate data
	t.Run("10.1", func(t *testing.T) {
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendBusinessReport), t, func() {
			Convey("When service handle request if request params missing businessId", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendBusinessReport, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing month", func() {
				body := map[string]interface{}{
					"businessId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendBusinessReport, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MONTH_AND_YEAR_REQUIRED.ErrorCode)
				})
			})
			Convey("When service handle request if request params missing year", func() {
				body := map[string]interface{}{
					"businessId": "**********",
					"month":      7,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiSendBusinessReport, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_MONTH_AND_YEAR_REQUIRED.ErrorCode)
				})
			})
		})
	})
	// business not found
	t.Run("10.2", func(t *testing.T) {
		log.Println("==================================== Business not found")
		ResetData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendBusinessReport), t, func() {
			body := map[string]interface{}{
				"businessId": "**********",
				"month":      11,
				"year":       2024,
			}
			reqBody, _ := json.Marshal(body)
			Convey("When service handle request", func() {
				req := httptest.NewRequest("POST", apiSendBusinessReport, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// business email not found
	t.Run("10.3", func(t *testing.T) {
		log.Println("==================================== business email not found")
		ResetData()
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "**********",
				"name":   "bTaskee",
				"status": globalConstant.BUSINESS_STATUS_ACTIVE,
				"bPay":   13_240_000.0,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendBusinessReport), t, func() {
			body := map[string]interface{}{
				"businessId": "**********",
				"month":      11,
				"year":       2024,
			}
			reqBody, _ := json.Marshal(body)
			Convey("When service handle request", func() {
				req := httptest.NewRequest("POST", apiSendBusinessReport, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 404)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_BUSINESS_EMAIL_NOT_FOUND.ErrorCode)
				})
			})
		})
	})
	// send report success, uncomment to test manually
	// t.Run("10.4", func(t *testing.T) {
	// 	log.Println("==================================== send report success")
	// 	ResetData()
	// 	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	// 	date := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, local.TimeZone)
	// 	// Create User
	// 	CreateUser([]map[string]interface{}{
	// 		{
	// 			"phone": "**********",
	// 			"name":  "Van",
	// 			"type":  globalConstant.USER_TYPE_ASKER,
	// 		}, {
	// 			"phone":  "0834567891",
	// 			"name":   "Asker 01",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-01",
	// 			"emails": []map[string]interface{}{
	// 				{
	// 					"address": "email-01",
	// 				},
	// 			},
	// 		}, {
	// 			"phone":  "0834567892",
	// 			"name":   "Asker 02",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-02",
	// 		}, {
	// 			"phone":  "0834567893",
	// 			"name":   "Asker 03",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-03",
	// 		}, {
	// 			"phone":  "0834567894",
	// 			"name":   "Asker 04",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-04",
	// 		}, {
	// 			"phone":  "0834567895",
	// 			"name":   "Asker 05",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-05",
	// 		}, {
	// 			"phone":  "0834567896",
	// 			"name":   "Asker 06",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-06",
	// 		}, {
	// 			"phone":  "0834567897",
	// 			"name":   "Asker 07",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-07",
	// 		}, {
	// 			"phone":  "0834567898",
	// 			"name":   "Asker 08",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-08",
	// 		},
	// 	})
	// 	CreateBusiness([]map[string]interface{}{
	// 		{
	// 			"_id":    "**********",
	// 			"name":   "bTaskee",
	// 			"status": globalConstant.BUSINESS_STATUS_ACTIVE,
	// 			"bPay":   13_240_000.0,
	// 			"email":  "<EMAIL>",
	// 		},
	// 	})
	// 	CreateBusinessLevel([]map[string]interface{}{
	// 		{
	// 			"_id":        "levelBronzeId",
	// 			"name":       "Bronze",
	// 			"businessId": "**********",
	// 			"amount":     300_000,
	// 			"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	// 		},
	// 		{
	// 			"_id":        "levelSilverId",
	// 			"name":       "Silver",
	// 			"businessId": "**********",
	// 			"amount":     600_000,
	// 			"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	// 		},
	// 		{
	// 			"_id":        "levelGoldId",
	// 			"name":       "Gold",
	// 			"businessId": "**********",
	// 			"amount":     1_200_000,
	// 			"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	// 		},
	// 	})
	// 	CreateBusinessMember([]map[string]interface{}{
	// 		{
	// 			"_id":        "businessMember_1",
	// 			"businessId": "**********",
	// 			"userId":     "0834567891",
	// 			"levelId":    "levelBronzeId",
	// 			"bPay":       300_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "businessMember_3",
	// 			"businessId": "**********",
	// 			"userId":     "0834567893",
	// 			"levelId":    "levelBronzeId",
	// 			"bPay":       300_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "businessMember_2",
	// 			"businessId": "**********",
	// 			"userId":     "0834567892",
	// 			"levelId":    "levelBronzeId",
	// 			"bPay":       300_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "businessMember_4",
	// 			"businessId": "**********",
	// 			"userId":     "0834567894",
	// 			"levelId":    "levelSilverId",
	// 			"bPay":       600_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		}, {
	// 			"_id":        "businessMember_6",
	// 			"businessId": "**********",
	// 			"userId":     "0834567896",
	// 			"levelId":    "levelGoldId",
	// 			"bPay":       1_200_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 3),
	// 		}, {
	// 			"_id":        "businessMember_5",
	// 			"businessId": "**********",
	// 			"userId":     "0834567895",
	// 			"levelId":    "levelSilverId",
	// 			"bPay":       600_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		}, {
	// 			"_id":        "businessMember_7",
	// 			"businessId": "**********",
	// 			"userId":     "0834567897",
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		}, {
	// 			"_id":        "businessMember_8",
	// 			"businessId": "**********",
	// 			"userId":     "0834567898",
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		},
	// 	})

	// 	CreateBusinessTransactions([]map[string]interface{}{
	// 		{
	// 			"_id":        "transaction_1",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER,
	// 			"memberId":   "businessMember_1",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 		}, {
	// 			"_id":        "transaction_2",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     12_000_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE,
	// 			"businessId": "**********",
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "transaction_3",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER,
	// 			"memberId":   "businessMember_2",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 		}, {
	// 			"_id":        "transaction_4",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER,
	// 			"memberId":   "businessMember_3",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 		}, {
	// 			"_id":        "transaction_5",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_3",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelBronzeId",
	// 				"levelName": "Bronze",
	// 			},
	// 		}, {
	// 			"_id":        "transaction_6",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_2",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelBronzeId",
	// 				"levelName": "Bronze",
	// 			},
	// 		}, {
	// 			"_id":        "transaction_7",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     600_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_5",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 4),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelSilverId",
	// 				"levelName": "Silver",
	// 			},
	// 		}, {
	// 			"_id":        "transaction_8",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     1_200_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_6",
	// 			"businessId": "**********",
	// 			"createdAt":  date.AddDate(0, 0, 10),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelGoldId",
	// 				"levelName": "Gold",
	// 			},
	// 		},
	// 	})
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiSendBusinessReport), t, func() {
	// 		body := map[string]interface{}{
	// 			"businessId": "**********",
	// 			"month":      date.Month(),
	// 			"year":       date.Year(),
	// 		}
	// 		reqBody, _ := json.Marshal(body)
	// 		Convey("When service handle request", func() {
	// 			req := httptest.NewRequest("POST", apiSendBusinessReport, bytes.NewBuffer(reqBody))
	// 			res := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(res, req)

	// 			Convey("Check the response", func() {
	// 				result := make(map[string]map[string]interface{})
	// 				b, _ := io.ReadAll(res.Body)
	// 				json.Unmarshal(b, &result)

	// 				So(res.Code, ShouldEqual, 200)
	// 			})
	// 		})
	// 	})
	// })

}
