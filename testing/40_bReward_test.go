package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTaskerGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerGift"
)

func Test_bReward(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Get list reward")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 2",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2021,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 3",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"brandInfo": map[string]interface{}{
					"image": "http://image2",
					"text": map[string]interface{}{
						"vi": "Grab",
					},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 4",
				},
				"isoCode":     local.ISO_CODE,
				"startDate":   "2022,11,02,15,15",
				"rankRequire": 2,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 100,
						"point":         70,
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				var respResultM []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				So(respResult[0]["title"]["vi"], ShouldEqual, "Incentive 1")
				So(respResult[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
				So(respResultM[0]["originalPoint"], ShouldEqual, 100)
				So(respResultM[0]["point"], ShouldEqual, 70)
				So(respResultM[0]["discountPercent"], ShouldEqual, 30)
				So(respResult[1]["title"]["vi"], ShouldEqual, "Incentive 2")
				So(respResult[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
				So(respResult[2]["title"]["vi"], ShouldEqual, "Incentive 3")
				So(respResult[2]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Get list reward")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 2",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2021,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 3",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
		})

		body := map[string]interface{}{
			"userId": "0834567890",
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		res := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(res, req)
			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
		})
	})
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== Get reward detail")
		apiUrl := "/api/v3/api-asker-vn/get-reward-detail"

		Convey("Check the response", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
		})
		Convey("Check the response", t, func() {
			ResetData()
			ids := CreateIncentive([]map[string]interface{}{
				{
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"office": []map[string]interface{}{
						{
							"name": "Hà Nội",
							"text": map[string]interface{}{
								"vi": "HN-1-VI",
							},
						},
					},
					"social": map[string]interface{}{
						"hotline":   "0000-0000-0001",
						"email":     "<EMAIL>",
						"facebook":  "http://facebook",
						"instagram": "http://instagram",
						"website":   "http://website",
						"ios":       "http://ios",
						"android":   "http://android",
					},
					"brandInfo": map[string]interface{}{
						"image": "http://image2",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode": local.ISO_CODE,
				},
			})
			currentTime := globalLib.GetCurrentTime(local.TimeZone)
			CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
				{
					"_id":       "xxx",
					"startDate": currentTime.AddDate(0, 0, -1),
					"endDate":   currentTime.AddDate(0, 0, 1),
					"createdAt": currentTime.AddDate(0, 0, -1),
					"status":    "ACTIVE",
					"isTesting": false,
					"incentiveInfos": []map[string]interface{}{
						{
							"_id":           "incentiveFS1",
							"originalPoint": 500,
							"point":         250,
						},
						{
							"_id":           "incentiveFS2",
							"originalPoint": 250,
							"point":         200,
						},
					},
				},
			})
			body := map[string]interface{}{
				"id": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			result := &modelTaskerGift.TaskerGift{}
			json.Unmarshal(b, &result)
			So(result.Title.Vi, ShouldEqual, "Incentive 1")
			So(len(result.Office), ShouldEqual, 1)
			So(result.Office[0].Name, ShouldEqual, "Hà Nội")
			So(result.Office[0].Text.Vi, ShouldEqual, "HN-1-VI")
			So(result.BrandInfo.Image, ShouldEqual, "http://image2")
			So(result.BrandInfo.Text.Vi, ShouldEqual, "Grab")
			So(result.Social.Hotline, ShouldEqual, "0000-0000-0001")
			So(result.Social.Email, ShouldEqual, "<EMAIL>")
			So(result.Social.Facebook, ShouldEqual, "http://facebook")
			So(result.Social.Instagram, ShouldEqual, "http://instagram")
			So(result.Social.Website, ShouldEqual, "http://website")
			So(result.Social.Ios, ShouldEqual, "http://ios")
			So(result.Social.Android, ShouldEqual, "http://android")
		})
	})
	t.Run("3.1", func(t *testing.T) {
		log.Println("==================================== Get reward detail")
		apiUrl := "/api/v3/api-asker-vn/get-reward-detail"
		Convey("Check the response", t, func() {
			ResetData()
			ids := CreateIncentive([]map[string]interface{}{
				{
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"office": []map[string]interface{}{
						{
							"name": "Hà Nội",
							"text": map[string]interface{}{
								"vi": "HN-1-VI",
							},
						},
					},
					"isoCode": local.ISO_CODE,
					"exchange": map[string]interface{}{
						"by":    "POINT",
						"point": 100,
					},
				},
			})
			currentTime := globalLib.GetCurrentTime(local.TimeZone)
			CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
				{
					"startDate": currentTime.AddDate(0, 0, -1),
					"endDate":   currentTime.AddDate(0, 0, 1),
					"createdAt": currentTime.AddDate(0, 0, -1),
					"status":    "ACTIVE",
					"isTesting": false,
					"incentiveInfos": []map[string]interface{}{
						{
							"_id":           ids[0],
							"originalPoint": 100,
							"point":         80,
						},
					},
				},
			})
			body := map[string]interface{}{
				"id": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			result := map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["originalPoint"], ShouldEqual, 100)
			So(result["point"], ShouldEqual, 80)
			So(result["discountPercent"], ShouldEqual, 20)
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Get my reward")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards"

		Convey("Check the response", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})
		Convey("Check the response", t, func() {
			body := map[string]interface{}{
				"userId": "xxx",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
		})
		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2022,11,02,15,15",
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2021,11,02,15,15",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
				},
			})
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			result := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(len(result), ShouldEqual, 2)
			So(result[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			So(result[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			So(result[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			So(result[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			So(result[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
		})
	})

	t.Run("4.1", func(t *testing.T) {
		log.Println("==================================== Get my reward v2 with marketing campaign type promotion can be use")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})
		Convey("Check the response", t, func() {
			body := map[string]interface{}{
				"userId": "xxx",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
		})
		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2022,11,02,15,15",
					"promotionCode": "gift01",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2021,11,02,15,15",
					"promotionCode": "gift02",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift03",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 4",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift04",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 5",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift05",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
			})

			// Create marketing campaign
			createdAt := globalLib.GetCurrentTime(local.TimeZone)
			CreateMarketingCampaign([]map[string]interface{}{
				{
					"img": "Img Test 1",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC123",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{
					"img": "Img Test 2",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 2",
						"en": "Happy Vietnam's Women Day 2",
						"ko": "Happy Vietnam's Women Day 2",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ) 3",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_INFO,
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // has limit
					"img": "Img Test 3",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC124",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // has limit but not enough quantity
					"img": "Img Test 4",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC125",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // enough quantity but user used it before
					"img": "Img Test 5",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC126",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
			})

			CreatePromotionCode([]map[string]interface{}{
				{
					"code": "ABC123",
				},
				{
					"code":  "ABC124",
					"limit": 100,
				},
				{
					"code":  "ABC125",
					"limit": 2,
				},
				{
					"code":  "ABC126",
					"limit": 200,
				}, {
					"code": "gift01",
				}, {
					"code": "gift02",
				}, {
					"code": "gift03",
				}, {
					"code":   "gift04",
					"locked": true,
				},
			})

			CreatePromotionHistory([]map[string]interface{}{
				{
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC126",
					"userId":        "0834567890",
				},
			})

			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 4)
			So(len(resultMM), ShouldEqual, 4)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")

			So(resultMM[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			So(resultMM[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")

			// Check marketing campaign
			So(resultMM[2]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
			So(resultMM[2]["brandInfo"]["image"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/sEFHixKrmjNcp9jaw")
			So(resultMM[2]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")

			So(resultMM[3]["title"]["vi"], ShouldEqual, "Chúc mừng ngày Phụ Nữ Việt Nam")
			So(resultMM[3]["brandInfo"]["image"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/sEFHixKrmjNcp9jaw")
			So(resultMM[3]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
		})
	})

	t.Run("4.1.1", func(t *testing.T) {
		log.Println("==================================== Get my reward v2. Gift from system not require to check promotion code block")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":    "0834567890",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"language": globalConstant.LANG_VI,
			},
		})
		CreateGift([]map[string]interface{}{
			{
				"userId": "0834567890",
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"brandInfo": map[string]interface{}{
					"image": "brand.image",
					"text": map[string]interface{}{
						"vi": "Grab",
					},
				},
				"isoCode":       local.ISO_CODE,
				"createdAt":     "2022,11,02,15,15",
				"promotionCode": "partnerCode",
				"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
			},
		})

		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}

		Convey("Given the request is handled by the Router", t, func() {
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			Convey("Then check the response", func() {
				So(res.Code, ShouldEqual, 200)
				b, _ := io.ReadAll(res.Body)

				resultM := []map[string]interface{}{}
				resultMM := []map[string]map[string]interface{}{}
				json.Unmarshal(b, &resultM)
				json.Unmarshal(b, &resultMM)
				So(len(resultM), ShouldEqual, 1)
				So(len(resultMM), ShouldEqual, 1)
				So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
				So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
				So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			})
		})
	})

	// Filter incentiveType LIXI_TASKER only
	t.Run("4.3", func(t *testing.T) {
		log.Println("==================================== Get my reward v2 only filterby incentiveType")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			now := globalLib.GetCurrentTime(local.TimeZone)
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2022,11,02,15,15",
					"promotionCode": "gift01",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2021,11,02,15,15",
					"promotionCode": "gift02",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift03",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 4",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift04",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 5",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift05",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId":  "0834567890",
					"image":   "test1",
					"expired": now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
					"title": map[string]interface{}{
						"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
						"en": "eGift Cash - Kichi Kichi - 100.000VND",
						"ko": "현금 eGift - Kichi Kichi - 100.000VND",
					},
					"applyFor": map[string]interface{}{
						"city": []string{
							"Hồ Chí Minh",
							"Đồng Nai",
							"Hà Nội",
						},
						"isSharePublic": true,
					},
					"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
					"promotionCode": "oMcom5M",
					"shareGiftInfo": map[string]interface{}{
						"type":  "MONEY",
						"value": 50000.0,
					},
					"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
					"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
					"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				},
			})

			// Create marketing campaign
			createdAt := globalLib.GetCurrentTime(local.TimeZone)
			CreateMarketingCampaign([]map[string]interface{}{
				{
					"img": "Img Test 1",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC123",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{
					"img": "Img Test 2",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 2",
						"en": "Happy Vietnam's Women Day 2",
						"ko": "Happy Vietnam's Women Day 2",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ) 3",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_INFO,
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // has limit
					"img": "Img Test 3",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC124",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // has limit but not enough quantity
					"img": "Img Test 4",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC125",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // enough quantity but user used it before
					"img": "Img Test 5",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC126",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
			})

			CreatePromotionCode([]map[string]interface{}{
				{
					"code": "ABC123",
				},
				{
					"code":  "ABC124",
					"limit": 100,
				},
				{
					"code":  "ABC125",
					"limit": 2,
				},
				{
					"code":  "ABC126",
					"limit": 200,
				}, {
					"code": "gift01",
				}, {
					"code": "gift02",
				}, {
					"code": "gift03",
				}, {
					"code":   "gift04",
					"locked": true,
				},
			})

			CreatePromotionHistory([]map[string]interface{}{
				{
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC126",
					"userId":        "0834567890",
				},
			})

			body := map[string]interface{}{
				"userId": "0834567890",
				"filterBy": map[string]interface{}{
					"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				},
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 1)
			So(len(resultMM), ShouldEqual, 1)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "eGift Tiền mặt - Kichi Kichi - 100.000đ")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
		})
	})

	// Filter incentiveType LIXI_TASKER only
	t.Run("4.3", func(t *testing.T) {
		log.Println("==================================== Get my reward v2 only filterby incentiveType")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			now := globalLib.GetCurrentTime(local.TimeZone)
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2022,11,02,15,15",
					"promotionCode": "gift01",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2021,11,02,15,15",
					"promotionCode": "gift02",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift03",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 4",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift04",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 5",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2020,11,02,15,15",
					"promotionCode": "gift05",
					"source":        globalConstant.GIFT_SOURCE_SYSTEM,
				},
				{
					"userId":  "0834567890",
					"image":   "test1",
					"expired": now.AddDate(0, 0, 2).Format("2006,1,2,15,4"),
					"title": map[string]interface{}{
						"vi": "eGift Tiền mặt - Kichi Kichi - 100.000đ",
						"en": "eGift Cash - Kichi Kichi - 100.000VND",
						"ko": "현금 eGift - Kichi Kichi - 100.000VND",
					},
					"applyFor": map[string]interface{}{
						"city": []string{
							"Hồ Chí Minh",
							"Đồng Nai",
							"Hà Nội",
						},
						"isSharePublic": true,
					},
					"incentiveId":   "xa9eb7a495a1bef713d754077fb611bc7",
					"promotionCode": "oMcom5M",
					"shareGiftInfo": map[string]interface{}{
						"type":  "MONEY",
						"value": 50000.0,
					},
					"source":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
					"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
					"promotionId":   "x0e6869cbce6b8a0e0e8fc0187bbcb14f",
				},
			})

			// Create marketing campaign
			createdAt := globalLib.GetCurrentTime(local.TimeZone)
			CreateMarketingCampaign([]map[string]interface{}{
				{
					"img": "Img Test 1",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC123",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{
					"img": "Img Test 2",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam 2",
						"en": "Happy Vietnam's Women Day 2",
						"ko": "Happy Vietnam's Women Day 2",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ) 3",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ) 3",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_INFO,
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // has limit
					"img": "Img Test 3",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC124",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // has limit but not enough quantity
					"img": "Img Test 4",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC125",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
				{ // enough quantity but user used it before
					"img": "Img Test 5",
					"title": map[string]interface{}{
						"vi": "Chúc mừng ngày Phụ Nữ Việt Nam",
						"en": "Happy Vietnam's Women Day",
						"ko": "Happy Vietnam's Women Day",
					},
					"description": map[string]interface{}{
						"vi": "Giảm 50% cho công việc đăng trong ngày hôm nay 20/10/2018 (Tối đa 30,000đ)",
						"en": "Save 50% when you booking on the app today (Maximum 30,000đ)",
						"ko": "Save 50% when you booking on the app today (Maximum 30,000đ)",
					},
					"action": "now",
					"actionText": map[string]interface{}{
						"vi": " Đặt ngay",
						"en": "Book Now",
						"ko": "Book Now",
					},
					"startDate":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAt.Year(), int(createdAt.Month()), createdAt.Day(), createdAt.Hour(), createdAt.Minute()),
					"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
					"status":      globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
					"isoCode":     local.ISO_CODE,
					"type":        globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION,
					"promotion": map[string]interface{}{
						"code": "ABC126",
					},
					"applyForUser": map[string]interface{}{
						"target": "BOTH",
					},
				},
			})

			CreatePromotionCode([]map[string]interface{}{
				{
					"code": "ABC123",
				},
				{
					"code":  "ABC124",
					"limit": 100,
				},
				{
					"code":  "ABC125",
					"limit": 2,
				},
				{
					"code":  "ABC126",
					"limit": 200,
				}, {
					"code": "gift01",
				}, {
					"code": "gift02",
				}, {
					"code": "gift03",
				}, {
					"code":   "gift04",
					"locked": true,
				},
			})

			CreatePromotionHistory([]map[string]interface{}{
				{
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC126",
					"userId":        "0834567890",
				},
			})

			body := map[string]interface{}{
				"userId": "0834567890",
				"filterBy": map[string]interface{}{
					"incentiveType": globalConstant.INCENTIVE_TYPE_LIXI_TASKER,
				},
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 1)
			So(len(resultMM), ShouldEqual, 1)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "eGift Tiền mặt - Kichi Kichi - 100.000đ")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
		})
	})

	// gift return has same from voucherId
	t.Run("4.4", func(t *testing.T) {
		log.Println("==================================== gift return has same from voucherId")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2022,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER,
						"userVoucherId": "voucher1",
					},
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2021,11,02,15,15",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
				},
			})

			// Create marketing campaign
			CreatePromotionCode([]map[string]interface{}{
				{
					"code": "ABC123",
				},
				{
					"code":  "ABC124",
					"limit": 100,
				},
				{
					"code":  "ABC125",
					"limit": 2,
				},
				{
					"code":  "ABC126",
					"limit": 200,
				},
			})

			CreatePromotionHistory([]map[string]interface{}{
				{
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC126",
					"userId":        "0834567890",
				},
			})

			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    1,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 2)
			So(len(resultMM), ShouldEqual, 2)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			So(resultM[0]["quantity"], ShouldEqual, 1)

			So(resultMM[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			So(resultMM[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
			So(resultM[1]["quantity"], ShouldEqual, 3)
		})
	})

	// gift return has same from voucherId: case isUsed
	t.Run("4.5", func(t *testing.T) {
		log.Println("==================================== gift return has same from voucherId")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2022,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher1",
					},
					"used": true,
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2021,11,02,15,15",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"used": true,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"used": true,
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
				},
			})

			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"data": map[string]interface{}{
					"isUsed": true,
				},
				"page": 1,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 2)
			So(len(resultMM), ShouldEqual, 2)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			So(resultM[0]["quantity"], ShouldEqual, 1)

			So(resultMM[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			So(resultMM[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
			So(resultM[1]["quantity"], ShouldEqual, 2)
		})
	})
	// gift return has same from voucherId
	t.Run("4.2", func(t *testing.T) {
		log.Println("==================================== gift return has same from voucherId")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2022,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher1",
					},
					"promotionCode": "gift01",
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2021,11,02,15,15",
					"promotionCode": "gift02",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"promotionCode": "gift03",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"promotionCode": "gift04",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"promotionCode": "gift05",
				},
			})

			// Create marketing campaign
			CreatePromotionCode([]map[string]interface{}{
				{
					"code": "ABC123",
				},
				{
					"code":  "ABC124",
					"limit": 100,
				},
				{
					"code":  "ABC125",
					"limit": 2,
				},
				{
					"code":  "ABC126",
					"limit": 200,
				}, {
					"code": "gift01",
				}, {
					"code": "gift02",
				}, {
					"code": "gift03",
				}, {
					"code": "gift04",
				}, {
					"code": "gift05",
				},
			})

			CreatePromotionHistory([]map[string]interface{}{
				{
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC124",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC125",
				}, {
					"promotionCode": "ABC126",
					"userId":        "0834567890",
				},
			})

			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"page":    1,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 2)
			So(len(resultMM), ShouldEqual, 2)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			So(resultM[0]["quantity"], ShouldEqual, 1)

			So(resultMM[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			So(resultMM[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
			So(resultM[1]["quantity"], ShouldEqual, 3)
		})
	})

	// gift return has same from voucherId: case isUsed
	t.Run("4.3", func(t *testing.T) {
		log.Println("==================================== gift return has same from voucherId")
		apiUrl := "/api/v3/api-asker-vn/get-my-rewards-v2"

		Convey("Check the response", t, func() {
			ResetData()
			CreateUser([]map[string]interface{}{
				{
					"phone":    "0834567890",
					"name":     "Asker 01",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				}, {
					"phone":    "0834567891",
					"name":     "Asker 02",
					"type":     globalConstant.USER_TYPE_ASKER,
					"language": globalConstant.LANG_VI,
				},
			})
			CreateGift([]map[string]interface{}{
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 1",
					},
					"brandInfo": map[string]interface{}{
						"image": "brand.image",
						"text": map[string]interface{}{
							"vi": "Grab",
						},
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2022,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher1",
					},
					"used":          true,
					"promotionCode": "gift01",
				},
				{
					"userId": "0834567891",
					"title": map[string]interface{}{
						"vi": "Incentive 2",
					},
					"isoCode":       local.ISO_CODE,
					"createdAt":     "2021,11,02,15,15",
					"promotionCode": "gift02",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"used":          true,
					"promotionCode": "gift03",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"used":          true,
					"promotionCode": "gift04",
				},
				{
					"userId": "0834567890",
					"title": map[string]interface{}{
						"vi": "Incentive 3",
					},
					"isoCode":   local.ISO_CODE,
					"createdAt": "2020,11,02,15,15",
					"from": map[string]interface{}{
						"from":          globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION,
						"userVoucherId": "voucher2",
					},
					"promotionCode": "gift05",
				},
			})
			// Create marketing campaign
			CreatePromotionCode([]map[string]interface{}{
				{
					"code": "gift01",
				}, {
					"code": "gift02",
				}, {
					"code": "gift03",
				}, {
					"code": "gift04",
				}, {
					"code": "gift05",
				},
			})

			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
				"data": map[string]interface{}{
					"isUsed": true,
				},
				"page": 1,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)

			resultM := []map[string]interface{}{}
			resultMM := []map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultM)
			json.Unmarshal(b, &resultMM)
			So(len(resultM), ShouldEqual, 2)
			So(len(resultMM), ShouldEqual, 2)
			So(resultMM[0]["title"]["vi"], ShouldEqual, "Incentive 1")
			So(resultMM[0]["brandInfo"]["image"], ShouldEqual, "brand.image")
			So(resultMM[0]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
			So(resultM[0]["quantity"], ShouldEqual, 1)

			So(resultMM[1]["title"]["vi"], ShouldEqual, "Incentive 3")
			So(resultMM[1]["brandInfo"]["text"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
			So(resultM[1]["quantity"], ShouldEqual, 2)
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== Get list reward with searchText")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":    "0834567890",
				"name":     "Asker 01",
				"type":     globalConstant.USER_TYPE_ASKER,
				"language": globalConstant.LANG_VI,
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
			},
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2021,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"isoCode":     local.ISO_CODE,
				"startDate":   "2021,11,02,15,15",
				"rankRequire": 2,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 100,
						"point":         70,
					},
				},
			},
		})

		body := map[string]interface{}{
			"filterBy": map[string]interface{}{
				"searchText": "Con cá",
			},
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				var respResultM []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2) // Incentive type != TASKER = not get data
				So(respResult[0]["title"]["vi"], ShouldEqual, "Con cá dàng")
				So(respResultM[0]["originalPoint"], ShouldEqual, 100)
				So(respResultM[0]["point"], ShouldEqual, 70)
				So(respResultM[0]["discountPercent"], ShouldEqual, 30)
				So(respResult[1]["title"]["vi"], ShouldEqual, "Con cá khô")
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== Get list reward filter type")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"type":        "EXCLUSIVE_DEAL",
				"isoCode":     local.ISO_CODE,
				"startDate":   "2021,11,02,15,15",
				"rankRequire": 2,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"type":      "EXCLUSIVE_DEAL",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"type":      "TOP_DEAL",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 4",
				},
				"type":        "EXCLUSIVE_DEAL",
				"isoCode":     local.ISO_CODE,
				"startDate":   "2021,11,02,15,15",
				"rankRequire": 4,
			},
		})

		body := map[string]interface{}{
			"filterBy": map[string]interface{}{
				"type": "EXCLUSIVE_DEAL",
			},
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 2) // Incentive type != TASKER = not get data
				So(respResult[0]["title"]["vi"], ShouldEqual, "Incentive 1")
				So(respResult[1]["title"]["vi"], ShouldEqual, "Con cá dàng")
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Get list reward filter type")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})

		body := map[string]interface{}{
			"userId": "0834567890",
			"filterBy": map[string]interface{}{
				"type": "RECOMMEND_FOR_YOU",
			},
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				So(respResult[0]["title"]["vi"], ShouldEqual, "Incentive 1")
				So(respResult[1]["title"]["vi"], ShouldEqual, "Con cá dàng")
				So(respResult[2]["title"]["vi"], ShouldEqual, "Con cá khô")
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		log.Println("==================================== Get list reward filter type")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
				"language": "vi",
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Con cá chuối",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"applyFor": map[string]interface{}{
					"service": []string{
						"Hồ Chí Minh",
					},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})

		body := map[string]interface{}{
			"userId": "0834567890",
			"filterBy": map[string]interface{}{
				"type":       "RECOMMEND_FOR_YOU",
				"searchText": "Con",
				"serviceId":  "pcZRQ6PqmjrAPe5gt",
			},
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				for _, v := range respResult {
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[1], ids[2]})
				}
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("==================================== Get reward home page")
		apiUrl := "/api/v3/api-asker-vn/get-reward-home-page"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			}, {
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"type": "EXCLUSIVE_DEAL",
				"exchange": map[string]interface{}{
					"point": 100,
				},
				"isoCode": local.ISO_CODE,
			}, {
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"type": "EXCLUSIVE_DEAL",
				"exchange": map[string]interface{}{
					"point": 100,
				},
				"originalPoint": 150,
				"isoCode":       local.ISO_CODE,
			}, {
				"_id": "incentiveTopDeal",
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode": local.ISO_CODE,
			}, {
				"title": map[string]interface{}{
					"vi": "Con cá dồ",
				},
				"isoCode": local.ISO_CODE,
				"type":    "",
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"from": "SYSTEM",
			}, {
				"title": map[string]interface{}{
					"vi": "Con cá hôa",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"from":    "SYSTEM",
				"isoCode": local.ISO_CODE,
			}, {
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"exchange": map[string]interface{}{
					"point": 100,
				},
				"from":    "SYSTEM",
				"isoCode": local.ISO_CODE,
			}, {
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"exchange": map[string]interface{}{
					"point": 100,
				},
				"from":    "SYSTEM",
				"isoCode": local.ISO_CODE,
			}, {
				"_id": "incentiveTopDeal1",
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode":     local.ISO_CODE,
				"rankRequire": 4,
			},
		})
		var incentives []map[string]interface{}
		for i := 0; i < 20; i++ {
			incentives = append(incentives, []map[string]interface{}{
				{
					"title": map[string]interface{}{
						"vi": fmt.Sprintf("%s %d", "Incentive", i),
					},
					"type": "EXCLUSIVE_DEAL",
					"exchange": map[string]interface{}{
						"point": 100,
					},
					"isoCode": local.ISO_CODE,
				},
				{
					"title": map[string]interface{}{
						"vi": fmt.Sprintf("%s %d", "Incentive", i),
					},
					"type": "",
					"exchange": map[string]interface{}{
						"point": 100,
					},
					"from":    "SYSTEM",
					"isoCode": local.ISO_CODE,
				},
			}...)
		}
		CreateGift([]map[string]interface{}{
			{
				"incentiveId":   "incentiveTopDeal",
				"askerPhone":    "0834567891",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"source":        "SYSTEM_WITH_PARTNER",
			}, {
				"incentiveId":   "incentiveTopDeal1",
				"askerPhone":    "0834567891",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv2",
				"isoCode":       local.ISO_CODE,
				"source":        "SYSTEM_WITH_PARTNER",
			},
		})
		CreateIncentive(incentives)
		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				listReward := respResult["listReward"].([]interface{})
				So(len(listReward), ShouldEqual, 3)
				So(listReward[0].(map[string]interface{})["type"], ShouldEqual, "RECOMMEND_FOR_YOU")
				So(listReward[0].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Đề xuất cho bạn")
				So(listReward[1].(map[string]interface{})["type"], ShouldEqual, "EXCLUSIVE_DEAL")
				So(listReward[1].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Ưu đãi độc quyền")
				So(listReward[2].(map[string]interface{})["type"], ShouldEqual, "TOP_DEAL")
				So(listReward[2].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Ưu đãi hàng đầu")
				for _, v := range listReward {
					v := v.(map[string]interface{})
					So(v["type"], ShouldBeIn, []string{"RECOMMEND_FOR_YOU", "EXCLUSIVE_DEAL", "TOP_DEAL"})
					switch v["type"].(string) {
					case "RECOMMEND_FOR_YOU":
						So(v["text"].(map[string]interface{})["vi"], ShouldEqual, "Đề xuất cho bạn")
						So(len(v["rewards"].([]interface{})), ShouldEqual, 10)
					case "TOP_DEAL":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 1)
					case "EXCLUSIVE_DEAL":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 10)
					}
				}
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		log.Println("==================================== Get reward home page")
		apiUrl := "/api/v3/api-asker-vn/get-reward-home-page"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
			},
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			},
		})
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
				},
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"type":      "EXCLUSIVE_DEAL",
				"isoCode":   local.ISO_CODE,
				"startDate": "2021,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"type":      "EXCLUSIVE_DEAL",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"type":      "ABC",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"_id": "incentiveTopDeal",
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode": local.ISO_CODE,
				"brandInfo": map[string]interface{}{
					"image": "http://image2",
					"text": map[string]interface{}{
						"vi": "Grab",
					},
				},
			},
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "flash sale 1",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode": local.ISO_CODE,
				"brandInfo": map[string]interface{}{
					"image": "http://image2",
					"text": map[string]interface{}{
						"vi": "BEAMIN",
					},
				},
			},
			{
				"_id": "incentiveFS2",
				"title": map[string]interface{}{
					"vi": "flash sale 2",
				},
				"exchange": map[string]interface{}{
					"point": 250,
				},
				"isoCode": local.ISO_CODE,
				"brandInfo": map[string]interface{}{
					"image": "http://image2",
					"text": map[string]interface{}{
						"vi": "BEAMIN",
					},
				},
			},
			{
				"_id": "incentiveFS3",
				"title": map[string]interface{}{
					"vi": "flash sale 3",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode":     local.ISO_CODE,
				"rankRequire": 4,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"type":        "EXCLUSIVE_DEAL",
				"isoCode":     local.ISO_CODE,
				"startDate":   "2020,11,02,15,15",
				"rankRequire": 4,
			},
		})

		CreateGift([]map[string]interface{}{
			{
				"incentiveId": "incentiveTopDeal",
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567891",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"source":        "SYSTEM_WITH_PARTNER",
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv7",
				"isoCode":       local.ISO_CODE,
				"source":        "SYSTEM_WITH_PARTNER",
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 10% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 10% discount for Cleaning Service",
					"ko": "청소 서비스 10% 할인 즐기기",
				},
				"askerPhone":    "0834567890",
				"serviceName":   globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"promotionCode": "hsztksv6",
				"isoCode":       local.ISO_CODE,
				"source":        "SYSTEM_WITH_PARTNER",
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 500,
						"point":         250,
					},
					{
						"_id":           "incentiveFS2",
						"originalPoint": 250,
						"point":         200,
					},
					{
						"_id":           "incentiveTopDeal",
						"originalPoint": 100,
						"point":         70,
					},
					{
						"_id":           "incentiveFS3",
						"originalPoint": 500,
						"point":         250,
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["numberRewardOfUser"], ShouldEqual, 2)
				listReward := respResult["listReward"].([]interface{})
				So(len(listReward), ShouldEqual, 4)
				So(listReward[0].(map[string]interface{})["type"], ShouldEqual, "RECOMMEND_FOR_YOU")
				So(listReward[0].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Đề xuất cho bạn")
				So(listReward[1].(map[string]interface{})["type"], ShouldEqual, "FLASH_SALE")
				So(listReward[1].(map[string]interface{})["endDate"], ShouldNotBeNil)
				So(listReward[1].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Flash Sale")
				So(listReward[2].(map[string]interface{})["type"], ShouldEqual, "EXCLUSIVE_DEAL")
				So(listReward[2].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Ưu đãi độc quyền")
				So(listReward[3].(map[string]interface{})["type"], ShouldEqual, "TOP_DEAL")
				So(listReward[3].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Ưu đãi hàng đầu")
				for _, v := range listReward {
					v := v.(map[string]interface{})
					So(v["type"], ShouldBeIn, []string{"RECOMMEND_FOR_YOU", "EXCLUSIVE_DEAL", "TOP_DEAL", "FLASH_SALE"})
					switch v["type"].(string) {
					case "RECOMMEND_FOR_YOU":
						So(v["text"].(map[string]interface{})["vi"], ShouldEqual, "Đề xuất cho bạn")
						So(len(v["rewards"].([]interface{})), ShouldEqual, 3)
						for _, data := range v["rewards"].([]interface{}) {
							So(data.(map[string]interface{})["brandText"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
						}
						So(v["rewards"].([]interface{})[1].(map[string]interface{})["brandText"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
					case "TOP_DEAL":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 1)
						for _, data := range v["rewards"].([]interface{}) {
							So(data.(map[string]interface{})["brandText"].(map[string]interface{})["vi"], ShouldEqual, "Grab")
							So(data.(map[string]interface{})["originalPoint"], ShouldEqual, 100)
							So(data.(map[string]interface{})["point"], ShouldEqual, 70)
							So(data.(map[string]interface{})["discountPercent"], ShouldEqual, 30)
						}
					case "EXCLUSIVE_DEAL":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 2)
						for _, data := range v["rewards"].([]interface{}) {
							So(data.(map[string]interface{})["brandText"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
						}
					case "FLASH_SALE":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 3)
						for _, data := range v["rewards"].([]interface{}) {
							incentvice := data.(map[string]interface{})
							So(incentvice["brandText"].(map[string]interface{})["vi"], ShouldBeIn, []string{"BEAMIN", "Grab"})
							if incentvice["_id"] == "incentiveFS1" {
								So(incentvice["originalPoint"], ShouldEqual, 500)
								So(incentvice["point"], ShouldEqual, 250)
								So(incentvice["discountPercent"], ShouldEqual, 50)
							} else if incentvice["_id"] == "incentiveFS2" {
								So(incentvice["originalPoint"], ShouldEqual, 250)
								So(incentvice["point"], ShouldEqual, 200)
								So(incentvice["discountPercent"], ShouldEqual, 20)
							}
						}
					}
				}
			})
		})
	})
	// Get Reward For U
	t.Run("11", func(t *testing.T) {
		log.Println("==================================== Get rewards for u validate")
		apiUrl := "/api/v3/api-asker-vn/get-rewards-for-you"

		Convey("Check the response", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
		})
		Convey("Check the response", t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 200)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result, ShouldBeNil)
		})
	})
	t.Run("12", func(t *testing.T) {
		log.Println("==================================== Get rewards for U")
		apiUrl := "/api/v3/api-asker-vn/get-rewards-for-you"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
				"language": "vi",
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "Con cá chuối",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"applyFor": map[string]interface{}{
					"service": []string{
						"Hồ Chí Minh",
					},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 100,
						"point":         70,
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				for _, v := range respResult {
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[1], ids[2]})
					if v["_id"].(string) == ids[0] {
						So(v["originalPoint"], ShouldEqual, 100)
						So(v["point"], ShouldEqual, 70)
						So(v["discountPercent"], ShouldEqual, 30)
					}
				}
			})
		})
	})
	// Get Rewards by Task
	t.Run("13", func(t *testing.T) {
		log.Println("==================================== Get rewards by Task")
		apiUrl := "/api/v3/api-asker-vn/get-rewards-for-book-task"

		Convey("Check the response isoCode require", t, func() {
			body := map[string]interface{}{}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
		})
		Convey("Check the response userId require", t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})
		Convey("Check the response serviceId require", t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
				"userId":  "123",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			So(res.Code, ShouldEqual, 400)
			b, _ := io.ReadAll(res.Body)
			result := map[string]map[string]interface{}{}
			json.Unmarshal(b, &result)
			So(result["error"]["code"], ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
		})
	})
	t.Run("14", func(t *testing.T) {
		log.Println("==================================== Get rewards by Task")
		apiUrl := "/api/v3/api-asker-vn/get-rewards-for-book-task"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
				"language": "vi",
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "Con cá chuối",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
					},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá đù",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"applyFor": map[string]interface{}{
					"service": []string{"pcZRQ6PqmjrAPe5gt", "pcZRQ6PqmjrAPe5gt123"},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá đù",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"applyFor": map[string]interface{}{
					"service": []string{"pcZRQ6PqmjrAPe5gt123"},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})

		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 100,
						"point":         70,
					},
				},
			},
		})
		body := map[string]interface{}{
			"userId":    "0834567890",
			"isoCode":   local.ISO_CODE,
			"serviceId": "pcZRQ6PqmjrAPe5gt",
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
				for _, v := range respResult {
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[2], ids[3]})
					if v["_id"].(string) == ids[0] {
						So(v["originalPoint"], ShouldEqual, 100)
						So(v["point"], ShouldEqual, 70)
						So(v["discountPercent"], ShouldEqual, 30)
					}
				}
			})
		})
	})
	t.Run("15", func(t *testing.T) {
		log.Println("==================================== Get reward home page")
		apiUrl := "/api/v3/api-asker-vn/get-reward-home-page"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
				},
			},
			{
				"phone": "0834567891",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
				},
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":    "SYSTEM_WITH_PARTNER",
				"isoCode": local.ISO_CODE,
				"endDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"type":      "EXCLUSIVE_DEAL",
				"isoCode":   local.ISO_CODE,
				"startDate": "2021,11,02,15,15",
			}, {
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"type":      "EXCLUSIVE_DEAL",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			}, {
				"_id": "incentiveTopDeal2",
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"type":      "ABC",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			}, {
				"_id": "incentiveTopDeal",
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode": local.ISO_CODE,
			}, {
				"_id": "incentiveTopDeal3",
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"exchange": map[string]interface{}{
					"point": 500,
				},
				"isoCode": local.ISO_CODE,
			},
		})

		CreateGift([]map[string]interface{}{
			{
				"incentiveId": "incentiveTopDeal",
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":  "0834567891",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"isoCode":     local.ISO_CODE,
				"source":      "SYSTEM_WITH_PARTNER",
			}, {
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"isoCode":     local.ISO_CODE,
				"source":      "SYSTEM_WITH_PARTNER",
			}, {
				"incentiveId": "incentiveTopDeal3",
				"title": map[string]interface{}{
					"vi": "Ưu đãi 10% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 10% discount for Cleaning Service",
					"ko": "청소 서비스 10% 할인 즐기기",
				},
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"isoCode":     local.ISO_CODE,
				"source":      "SYSTEM_WITH_PARTNER",
			},
		})

		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["numberRewardOfUser"], ShouldEqual, 2)
				listReward := respResult["listReward"].([]interface{})
				So(len(listReward), ShouldEqual, 3)
				So(listReward[0].(map[string]interface{})["type"], ShouldEqual, "RECOMMEND_FOR_YOU")
				So(listReward[0].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Đề xuất cho bạn")
				So(listReward[1].(map[string]interface{})["type"], ShouldEqual, "EXCLUSIVE_DEAL")
				So(listReward[1].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Ưu đãi độc quyền")
				So(listReward[2].(map[string]interface{})["type"], ShouldEqual, "TOP_DEAL")
				So(listReward[2].(map[string]interface{})["text"].(map[string]interface{})["vi"], ShouldEqual, "Ưu đãi hàng đầu")
				for _, v := range listReward {
					v := v.(map[string]interface{})
					So(v["type"], ShouldBeIn, []string{"RECOMMEND_FOR_YOU", "EXCLUSIVE_DEAL", "TOP_DEAL"})
					switch v["type"].(string) {
					case "RECOMMEND_FOR_YOU":
						So(v["text"].(map[string]interface{})["vi"], ShouldEqual, "Đề xuất cho bạn")
						So(len(v["rewards"].([]interface{})), ShouldEqual, 5)
					case "TOP_DEAL":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 2)
					case "EXCLUSIVE_DEAL":
						So(len(v["rewards"].([]interface{})), ShouldEqual, 2)
					}
				}
			})
		})
	})
	// Get rewards for U case asker without point
	t.Run("16", func(t *testing.T) {
		log.Println("==================================== Get rewards for U")
		apiUrl := "/api/v3/api-asker-vn/get-rewards-for-you"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
				"language": "vi",
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Con cá chuối",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"applyFor": map[string]interface{}{
					"service": []string{
						"Hồ Chí Minh",
					},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})

		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 5)
				for _, v := range respResult {
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[1], ids[2], ids[3], ids[4]})
					So(v["brandText"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
				}
			})
		})
	})
	// Get list reward filter type rewards for U case asker without point
	t.Run("17", func(t *testing.T) {
		log.Println("==================================== Get list reward filter type")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá khô",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
				"rankRequire": 1,
			},
			{
				"title": map[string]interface{}{
					"vi": "Con cá dàng",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 150,
				},
				"rankRequire": 4,
			},
		})

		body := map[string]interface{}{
			"userId": "0834567890",
			"filterBy": map[string]interface{}{
				"type": "RECOMMEND_FOR_YOU",
			},
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 5)
				So(respResult[0]["title"]["vi"], ShouldEqual, "Incentive 1")
				So(respResult[1]["title"]["vi"], ShouldEqual, "Con cá dàng")
				So(respResult[2]["title"]["vi"], ShouldEqual, "Con cá khô")
				var respResultM []map[string]interface{}
				json.Unmarshal(bytes, &respResultM)
				for _, v := range respResultM {
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[1], ids[2], ids[3], ids[4]})
				}
			})
		})
	})
	t.Run("18", func(t *testing.T) {
		log.Println("==================================== Get list reward filter type TOP_DEAL")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"point": 200,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentive1",
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"_id": "incentive2",
				"title": map[string]interface{}{
					"vi": "Incentive 2",
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"_id": "incentive3",
				"title": map[string]interface{}{
					"vi": "Incentive 3",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
			{
				"_id": "incentive4",
				"title": map[string]interface{}{
					"vi": "Incentive 4",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			},
		})
		listIncentive := []map[string]interface{}{}
		for i := 5; i < 20; i++ {
			incentive := map[string]interface{}{
				"_id": fmt.Sprintf("incentive%d", i),
				"title": map[string]interface{}{
					"vi": fmt.Sprintf("incentive%d", i),
				},
				"from":      "SYSTEM_WITH_PARTNER",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
			}
			listIncentive = append(listIncentive, incentive)
		}
		CreateIncentive(listIncentive)
		listGift := []map[string]interface{}{}
		listIncentiveId := []string{}
		for i := 10; i < 20; i++ {
			incentiveId := fmt.Sprintf("incentive%d", i)
			listIncentiveId = append(listIncentiveId, incentiveId)
			gift := map[string]interface{}{
				"incentiveId": incentiveId,
				"title": map[string]interface{}{
					"vi": "Ưu đãi 10% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 10% discount for Cleaning Service",
					"ko": "청소 서비스 10% 할인 즐기기",
				},
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"isoCode":     local.ISO_CODE,
				"source":      "SYSTEM_WITH_PARTNER",
			}
			listGift = append(listGift, gift)
		}
		for i := 10; i < 20; i++ {
			incentiveId := fmt.Sprintf("incentive%d", i)
			listIncentiveId = append(listIncentiveId, incentiveId)
			gift := map[string]interface{}{
				"incentiveId": incentiveId,
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"isoCode":     local.ISO_CODE,
				"source":      "SYSTEM_WITH_PARTNER",
			}
			listGift = append(listGift, gift)
		}
		for i := 10; i < 19; i++ {
			incentiveId := fmt.Sprintf("incentive%d", i)
			listIncentiveId = append(listIncentiveId, incentiveId)
			gift := map[string]interface{}{
				"incentiveId": incentiveId,
				"title": map[string]interface{}{
					"vi": "Ưu đãi 20% khi đặt lịch Dọn dẹp nhà",
					"en": "Enjoy 20% discount for Cleaning Service",
					"ko": "청소 서비스 20% 할인 즐기기",
				},
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"isoCode":     local.ISO_CODE,
				"source":      "SYSTEM_WITH_PARTNER",
			}
			listGift = append(listGift, gift)
		}
		listGift = append(listGift, map[string]interface{}{
			"incentiveId": "incentive15",
			"title": map[string]interface{}{
				"vi": "Ưu đãi 10% khi đặt lịch Dọn dẹp nhà",
				"en": "Enjoy 10% discount for Cleaning Service",
				"ko": "청소 서비스 10% 할인 즐기기",
			},
			"askerPhone":  "0834567890",
			"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			"isoCode":     local.ISO_CODE,
			"source":      "SYSTEM_WITH_PARTNER",
		})
		listGift = append(listGift, map[string]interface{}{
			"incentiveId": "incentive6",
			"title": map[string]interface{}{
				"vi": "Ưu đãi 10% khi đặt lịch Dọn dẹp nhà",
				"en": "Enjoy 10% discount for Cleaning Service",
				"ko": "청소 서비스 10% 할인 즐기기",
			},
			"askerPhone":  "0834567890",
			"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			"isoCode":     local.ISO_CODE,
			"source":      "SYSTEM_WITH_PARTNER",
		})
		listGift = append(listGift, map[string]interface{}{
			"incentiveId": "incentive7",
			"title": map[string]interface{}{
				"vi": "Ưu đãi 10% khi đặt lịch Dọn dẹp nhà",
				"en": "Enjoy 10% discount for Cleaning Service",
				"ko": "청소 서비스 10% 할인 즐기기",
			},
			"askerPhone":  "0834567890",
			"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			"isoCode":     local.ISO_CODE,
			"source":      "SYSTEM_WITH_PARTNER",
		})
		CreateGift(listGift)

		body := map[string]interface{}{
			"userId": "0834567890",
			"filterBy": map[string]interface{}{
				"type": "TOP_DEAL",
			},
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 10)
				So(respResult[0]["_id"].(string), ShouldEqual, "incentive15")
				So(respResult[9]["_id"].(string), ShouldEqual, "incentive19")
				for _, v := range respResult {
					So(v["_id"].(string), ShouldBeIn, listIncentiveId)
					So(v["_id"].(string), ShouldNotBeIn, []string{"incentive7", "incentive6"})
				}
			})
		})
	})
	// Get list reward flash sale
	t.Run("19", func(t *testing.T) {
		log.Println("==================================== Get list reward filter type flash sale")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"rankName": "GOLD",
					"point":    200,
				},
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "incentiveFS 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,12,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 500,
				},
			}, {
				"_id": "incentiveFS2",
				"title": map[string]interface{}{
					"vi": "incentiveFS 2",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			}, {
				"_id": "incentiveFS3",
				"title": map[string]interface{}{
					"vi": "incentiveFS 3",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,10,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 100,
				},
			}, {
				"_id": "incentiveFS4",
				"title": map[string]interface{}{
					"vi": "incentiveFS 4",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,9,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 50,
				},
			}, {
				"_id": "incentiveFS5",
				"title": map[string]interface{}{
					"vi": "incentiveFS 5",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,8,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 50,
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"_id":       "xxx",
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"isTesting": false,
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 500,
						"point":         250,
					},
					{
						"_id":           "incentiveFS2",
						"originalPoint": 250,
						"point":         200,
					},
					{
						"_id":           "incentiveFS3",
						"originalPoint": 100,
						"point":         50,
					},
					{
						"_id":           "incentiveFS4",
						"originalPoint": 50,
						"point":         40,
					},
				},
			},
		})
		body := map[string]interface{}{
			"userId": "0834567890",
			"filterBy": map[string]interface{}{
				"type": "FLASH_SALE",
			},
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 4)
				So(respResult[0]["title"]["vi"], ShouldEqual, "incentiveFS 1")
				So(respResult[1]["title"]["vi"], ShouldEqual, "incentiveFS 2")
				So(respResult[2]["title"]["vi"], ShouldEqual, "incentiveFS 3")
				So(respResult[3]["title"]["vi"], ShouldEqual, "incentiveFS 4")
				var respResultM []map[string]interface{}
				json.Unmarshal(bytes, &respResultM)
				for _, v := range respResultM {
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[1], ids[2], ids[3]})
					switch v["_id"].(string) {
					case ids[0]:
						So(v["point"].(float64), ShouldEqual, 250)
						So(v["originalPoint"].(float64), ShouldEqual, 500)
						So(v["discountPercent"].(float64), ShouldEqual, 50)
					case ids[1]:
						So(v["point"].(float64), ShouldEqual, 200)
						So(v["originalPoint"].(float64), ShouldEqual, 250)
						So(v["discountPercent"].(float64), ShouldEqual, 20)
					case ids[2]:
						So(v["point"].(float64), ShouldEqual, 50)
						So(v["originalPoint"].(float64), ShouldEqual, 100)
						So(v["discountPercent"].(float64), ShouldEqual, 50)
					case ids[3]:
						So(v["point"].(float64), ShouldEqual, 40)
						So(v["originalPoint"].(float64), ShouldEqual, 50)
						So(v["discountPercent"].(float64), ShouldEqual, 20)
					}
				}
			})
		})
	})
	// Get reward flash sale
	t.Run("20", func(t *testing.T) {
		log.Println("==================================== Get reward flash sale")
		apiUrl := "/api/v3/api-asker-vn/get-rewards-flash-sale"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		ids := CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentiveFS1",
				"title": map[string]interface{}{
					"vi": "incentiveFS 1",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,12,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 500,
				},
			}, {
				"_id": "incentiveFS2",
				"title": map[string]interface{}{
					"vi": "incentiveFS 2",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 250,
				},
			}, {
				"_id": "incentiveFS3",
				"title": map[string]interface{}{
					"vi": "incentiveFS 3",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,10,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 100,
				},
			}, {
				"_id": "incentiveFS4",
				"title": map[string]interface{}{
					"vi": "incentiveFS 4",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,9,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 50,
				},
			}, {
				"_id": "incentiveFS5",
				"title": map[string]interface{}{
					"vi": "incentiveFS 5",
				},
				"from":      "SYSTEM",
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,8,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 50,
				},
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		CreateVNAskerFlashSaleIncentive([]map[string]interface{}{
			{
				"_id":       "xxx",
				"startDate": currentTime.AddDate(0, 0, -1),
				"endDate":   currentTime.AddDate(0, 0, 1),
				"createdAt": currentTime.AddDate(0, 0, -1),
				"status":    "ACTIVE",
				"incentiveInfos": []map[string]interface{}{
					{
						"_id":           "incentiveFS1",
						"originalPoint": 500,
						"point":         250,
					},
					{
						"_id":           "incentiveFS2",
						"originalPoint": 250,
						"point":         200,
					},
					{
						"_id":           "incentiveFS3",
						"originalPoint": 100,
						"point":         50,
					},
					{
						"_id":           "incentiveFS4",
						"originalPoint": 50,
						"point":         40,
					},
				},
			},
		})
		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["text"].(map[string]interface{})["vi"], ShouldEqual, "Flash Sale")
				So(len(respResult["rewards"].([]interface{})), ShouldEqual, 4)
				So(respResult["rewards"].([]interface{})[0].(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldEqual, "incentiveFS 1")
				So(respResult["rewards"].([]interface{})[1].(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldEqual, "incentiveFS 2")
				So(respResult["rewards"].([]interface{})[2].(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldEqual, "incentiveFS 3")
				So(respResult["rewards"].([]interface{})[3].(map[string]interface{})["title"].(map[string]interface{})["vi"], ShouldEqual, "incentiveFS 4")
				for _, data := range respResult["rewards"].([]interface{}) {
					v := data.(map[string]interface{})

					So(v["brandText"].(map[string]interface{})["vi"], ShouldEqual, "bTaskee")
					So(v["_id"].(string), ShouldBeIn, []string{ids[0], ids[1], ids[2], ids[3]})

					switch v["_id"].(string) {
					case ids[0]:
						So(v["point"].(float64), ShouldEqual, 250)
						So(v["originalPoint"].(float64), ShouldEqual, 500)
						So(v["discountPercent"].(float64), ShouldEqual, 50)
					case ids[1]:
						So(v["point"].(float64), ShouldEqual, 200)
						So(v["originalPoint"].(float64), ShouldEqual, 250)
						So(v["discountPercent"].(float64), ShouldEqual, 20)
					case ids[2]:
						So(v["point"].(float64), ShouldEqual, 50)
						So(v["originalPoint"].(float64), ShouldEqual, 100)
						So(v["discountPercent"].(float64), ShouldEqual, 50)
					case ids[3]:
						So(v["point"].(float64), ShouldEqual, 40)
						So(v["originalPoint"].(float64), ShouldEqual, 50)
						So(v["discountPercent"].(float64), ShouldEqual, 20)
					}
				}
			})
		})
	})

	// Not seen breward already redeem
	t.Run("21", func(t *testing.T) {
		log.Println("==================================== Not seen breward already redeem")
		apiUrl := "/api/v3/api-asker-vn/get-list-reward"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateIncentive([]map[string]interface{}{
			{
				"_id": "incentive1",
				"title": map[string]interface{}{
					"vi": "Incentive 1",
				},
				"isoCode":         local.ISO_CODE,
				"startDate":       "2022,11,02,15,15",
				"isRedeemOneTime": true,
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 2",
				},
				"isoCode":         local.ISO_CODE,
				"startDate":       "2021,11,02,15,15",
				"isRedeemOneTime": true,
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 3",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2020,11,02,15,15",
				"brandInfo": map[string]interface{}{
					"image": "http://image2",
					"text": map[string]interface{}{
						"vi": "Grab",
					},
				},
			},
			{
				"title": map[string]interface{}{
					"vi": "Incentive 4",
				},
				"isoCode":   local.ISO_CODE,
				"startDate": "2022,11,02,15,15",
			},
		})
		CreateRedeemGiftTransaction([]map[string]interface{}{
			{
				"userId": "0834567890",
				"incentiveInfo": map[string]interface{}{
					"incentiveId": "incentive1",
				},
			},
		})
		body := map[string]interface{}{
			"userId":  "0834567890",
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)
			Convey("Then check the response", func() {
				var respResult []map[string]map[string]interface{}
				var respResultM []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(len(respResult), ShouldEqual, 3)
			})
		})
	})
}
