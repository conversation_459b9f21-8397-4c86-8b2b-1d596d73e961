/*
* @File: 16_service_test.go
* @Description: Handler function, case test
* @CreatedAt: 13/11/2020
* @Author: vinhnt
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"go.mongodb.org/mongo-driver/bson"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
)

func TestService(t *testing.T) {

	// t.Run("1", func(t *testing.T) {
	// 	log.Println("==================================== Validate GetRegisterNewService")
	// 	// GetRegisterNewService
	// 	apiUrl := "/api/v3/api-asker-vn/register-new-service"
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
	// 		Convey("Check request when isoCode incorrect", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": "HA",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 500)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
	// 		})
	// 		Convey("Check request when params invalid", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": 123,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
	// 		})
	// 	})
	// })
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== GetRegisterNewService")
		// GetRegisterNewService
		apiUrl := "/api/v3/api-asker-vn/register-new-service"
		ResetData()

		//Create User
		CreateRegisterService([]map[string]interface{}{
			{
				"registerId": "registerId01",
				"info": map[string]interface{}{
					"vi": "Tài xế riêng",
					"en": "Private Driver",
					"ko": "요리",
					"th": "คนขับส่วนตัว",
				},
			}, {
				"registerId": "registerId02",
				"info": map[string]interface{}{
					"vi": "Tài xế riêng",
					"en": "Private Driver",
					"ko": "요리",
					"th": "คนขับส่วนตัว",
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s by userId", apiUrl), t, func() {
			body := map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Reviews", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)

					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["registerId"], ShouldEqual, "registerId01")
					// So(respResultM[0]["info"]["en"], ShouldEqual, "Private Driver")
					So(respResult[1]["registerId"], ShouldEqual, "registerId02")
					// So(respResultM[1]["info"]["en"], ShouldEqual, "Private Driver")
					for _, v := range respResult {
						So(v["isoCode"], ShouldEqual, local.ISO_CODE)
					}
				})
			})
		})
	})
	// t.Run("3", func(t *testing.T) {
	// 	apiURL := subPath + "/get-services"
	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
	// 		Convey("Check request when isoCode blank", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": "",
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
	// 		})
	// 		Convey("Check request when params invalid", func() {
	// 			body := map[string]interface{}{
	// 				"isoCode": 123,
	// 			}
	// 			reqBody, _ := json.Marshal(body)
	// 			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 			resp := httptest.NewRecorder()
	// 			service.NewRouter().ServeHTTP(resp, req)
	// 			So(resp.Code, ShouldEqual, 400)

	// 			var respResult map[string]map[string]interface{}
	// 			bytes, _ := io.ReadAll(resp.Body)
	// 			json.Unmarshal(bytes, &respResult)
	// 			So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
	// 			So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
	// 		})
	// 	})
	// })
	t.Run("4", func(t *testing.T) {
		apiURL := subPath + "/get-services"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check request when isoCode is VN", func() {
				body := map[string]interface{}{
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				b, _ := io.ReadAll(resp.Body)
				var services []*modelService.Service
				json.Unmarshal(b, &services)
				So(services, ShouldNotBeNil)
				So(len(services), ShouldNotEqual, 0)
			})
		})
	})
	// get tasker service with task patient care
	t.Run("6", func(t *testing.T) {
		apiURL := subPath + "/get-tasker-services"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		var svPatientCare *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_PATIENT_CARE}, bson.M{"_id": 1}, &svPatientCare)
		var svCleaning *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &svCleaning)
		// Update service channel
		globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": svPatientCare.XId}, bson.M{"$set": bson.M{"taskerList": []string{"**********"}}})
		defer globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": svPatientCare.XId}, bson.M{"$unset": bson.M{"taskerList": 1}})
		Convey(fmt.Sprintf("Given a HTTP request for %s by taskerId", apiURL), t, func() {
			body := map[string]interface{}{
				"taskerId":   "**********",
				"appVersion": "3.28.0",
				"userId":     "**********",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Services", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)

					var serviceIds []string
					js, _ := json.Marshal(respResult["serviceIds"])
					json.Unmarshal(js, &serviceIds)

					So(resp.Code, ShouldEqual, 200)
					So(len(serviceIds), ShouldEqual, 2)
					tested := map[string]bool{}
					for _, v := range serviceIds {
						So(v, ShouldBeIn, []string{svPatientCare.XId, svCleaning.XId})
						tested[v] = true
					}
					So(len(tested), ShouldEqual, 2)
				})
			})
		})
	})

	// get tasker service with task home cooking
	t.Run("7", func(t *testing.T) {
		apiURL := subPath + "/get-tasker-services"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		var svHomeCooking *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_COOKING}, bson.M{"_id": 1}, &svHomeCooking)
		var svCleaning *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &svCleaning)
		// Update service channel
		globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": svHomeCooking.XId}, bson.M{"$set": bson.M{"taskerList": []string{"**********"}}})
		defer globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": svHomeCooking.XId}, bson.M{"$unset": bson.M{"taskerList": 1}})
		Convey(fmt.Sprintf("Given a HTTP request for %s by taskerId", apiURL), t, func() {
			body := map[string]interface{}{
				"taskerId":   "**********",
				"appVersion": "3.29.0",
				"userId":     "**********",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Services", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)

					var serviceIds []string
					js, _ := json.Marshal(respResult["serviceIds"])
					json.Unmarshal(js, &serviceIds)

					So(resp.Code, ShouldEqual, 200)
					So(len(serviceIds), ShouldEqual, 2)
					tested := map[string]bool{}
					for _, v := range serviceIds {
						So(v, ShouldBeIn, []string{svHomeCooking.XId, svCleaning.XId})
						tested[v] = true
					}
					So(len(tested), ShouldEqual, 2)
				})
			})
		})
	})
	// get tasker service with task elderly care
	t.Run("8", func(t *testing.T) {
		apiURL := subPath + "/get-tasker-services"
		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})
		var svElderlyCare *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_ELDERLY_CARE}, bson.M{"_id": 1}, &svElderlyCare)
		var svCleaning *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &svCleaning)
		// Update service channel
		globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": svElderlyCare.XId}, bson.M{"$set": bson.M{"taskerList": []string{"**********"}}})
		defer globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": svElderlyCare.XId}, bson.M{"$unset": bson.M{"taskerList": 1}})
		Convey(fmt.Sprintf("Given a HTTP request for %s by taskerId", apiURL), t, func() {
			body := map[string]interface{}{
				"taskerId":   "**********",
				"appVersion": "3.29.0",
				"userId":     "**********",
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Tasker Services", func() {
					var respResult map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)

					var serviceIds []string
					js, _ := json.Marshal(respResult["serviceIds"])
					json.Unmarshal(js, &serviceIds)

					So(resp.Code, ShouldEqual, 200)
					So(len(serviceIds), ShouldEqual, 2)
					tested := map[string]bool{}
					for _, v := range serviceIds {
						So(v, ShouldBeIn, []string{svElderlyCare.XId, svCleaning.XId})
						tested[v] = true
					}
					So(len(tested), ShouldEqual, 2)
				})
			})
		})
	})
}
