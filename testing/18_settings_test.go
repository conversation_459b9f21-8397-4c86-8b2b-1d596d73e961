/*
* @File: 18_settings_test.go
* @Description: Handler function, case test
* @CreatedAt: 02/03/2020
* @Author: linhnh
* @UpdatedAt: 11/12/2020
* @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSettings(t *testing.T) {

	var disinfectionDetailVN = map[string]interface{}{
		"city": []map[string]interface{}{
			{
				"name": "Hồ Chí Minh",
				"area": []map[string]interface{}{
					{
						"name":             "option1",
						"from":             1,
						"to":               70,
						"price":            550000,
						"isShowInAppAsker": true,
					},
					{
						"name":             "option2",
						"from":             71,
						"to":               100,
						"price":            700000,
						"isShowInAppAsker": true,
					},
					{
						"name":             "option3",
						"from":             101,
						"to":               200,
						"price":            900000,
						"isShowInAppAsker": true,
					},
					{
						"name":                "option4",
						"from":                201,
						"to":                  500,
						"pricePerSquareMeter": 5500,
					},
					{
						"name":                "option5",
						"from":                501,
						"to":                  1000,
						"pricePerSquareMeter": 4500,
					},
					{
						"name":                "option6",
						"from":                1001,
						"to":                  5000,
						"pricePerSquareMeter": 3500,
					},
					{
						"name":                "option7",
						"from":                5001,
						"to":                  10000,
						"pricePerSquareMeter": 3000,
					},
				},
			},
		},
	}

	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_DISINFECTION}, bson.M{"$set": bson.M{"disinfectionDetail": disinfectionDetailVN}})

	// GetAllSettings
	// t.Run("1", func(t *testing.T) {
	// 	log.Println("==================================== GetAllSettings")

	t.Run("2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if body invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if appVersion is empty", func() {
				body := map[string]interface{}{
					"userId":     "123123",
					"appVersion": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_APP_VERSION_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check if user not found", func() {
				body := map[string]interface{}{
					"userId":     "xxxxx",
					"appVersion": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if body invalid", func() {
				body := map[string]interface{}{
					"userId": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
			})
			Convey("Check the response if appVersion is empty", func() {
				body := map[string]interface{}{
					"userId":     "123123",
					"appVersion": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_APP_VERSION_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check if user not found", func() {
				body := map[string]interface{}{
					"userId":     "xxxxx",
					"appVersion": "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 404)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		query := bson.M{
			"$or": []bson.M{
				{"status": globalConstant.SERVICE_STATUS_ACTIVE},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isTesting": true},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
			},
			"onlyShowTasker": bson.M{"$ne": true},
		}
		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateService(query, bson.M{"$set": bson.M{
			"tetBookingDates": bson.M{
				"fromDate": now.AddDate(0, 0, -1),
				"toDate":   now.AddDate(0, 0, 1),
				"eventDescription": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"en": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"ko": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
				},
				"title": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "Title Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"subTitle": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "SubTitle Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"banner": bson.M{
					"vi": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"en": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"ko": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"th": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
				},
				"bookTaskTime": bson.M{},
				"note": bson.M{
					"title": bson.M{
						"vi": "Dịch vụ Dọn Dẹp Nhà dịp Tết 2020",
						"en": "House Cleaning Service on Tet Holiday 2020",
						"ko": "2020년 설 기간 가사도우미 서비스 안내",
					},
					"content": bson.A{
						bson.M{
							"vi": "- Hỗ trợ đặt lịch trước cho dịp Tết ngay từ bây giờ.",
							"en": "- Advance booking for Tet Holiday is available now.",
							"ko": "- 설 기간 미리 예약 지원.",
						},
						bson.M{
							"vi": "- Có thể đặt lịch trước từ 10/01 – 10/02/2020.",
							"en": "- Support for advance booking service on January 10th to February 10th 2020.",
							"ko": "- 2020/01/10일 부터 2020/02/10 까지 가사도우미 서비스 미리 예약 가능.",
						},
						bson.M{
							"vi": "- Giá dịch vụ tăng từ ngày 23 Tết đến mùng 10 Tết (17/01 – 03/02/2020), dao động từ 30% - 120%.",
							"en": "- The price will increase from the 23rd to 10th Tet’s Holiday (from January 17th to February 3rd, 2020), ranging from 30% to 120%.",
							"ko": "- 2020/1/17일 부터 2020/2/3일 까지 청소 서비스 가격 30~120% 상승.",
						},
					},
					"detailNote": bson.M{
						"vi": "Lưu ý: Dịch vụ Dọn Dẹp Nhà chỉ bao gồm các công việc nhà cơ bản. Nếu bạn có nhu cầu dọn dẹp kỹ, tổng vệ sinh nhà cửa, vui lòng chọn dịch vụ Tổng Vệ Sinh.",
						"en": "Note: House Cleaning services only includes basic household chores. If you need a thorough cleaning, overall house cleaning, please choose Deep Cleaning service.",
						"ko": "참고: 가사도우미 서비스는 기본적인 가사일에 한하여 진행 가능합니다. 집안을 구석구석 꼼꼼하게 청소 진행하는 서비스를 원하실 경우 ’대청소’항목으로 예약 부탁드립니다.",
					},
				},
				"cusTomLunarDay": bson.A{
					bson.M{
						"calendarDay": "10/01",
						"lunarDay":    "16/12",
					},
					bson.M{
						"calendarDay": "11/01",
						"lunarDay":    "17/12",
					},
				},
			},
		}})

		categories := []map[string]interface{}{
			{
				"name":   "Entertainment",
				"status": "ACTIVE",
				"weight": 0,
			},
			{
				"name":   "Services",
				"status": "ACTIVE",
				"weight": 1,
			},
			{
				"name":   "Food & Beverage",
				"status": "INACTIVE",
				"weight": 2,
			},
		}
		UpdateSettings(bson.M{"$set": bson.M{"giftSetting.category": categories}})

		UpdateAskerSetting(local.ISO_CODE, bson.M{"$set": bson.M{"minDebtPaymentAmount": 1000}})

		CreateEventConfig([]map[string]interface{}{
			{
				"name":      "TET",
				"status":    globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 7),
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					location, _ := respResult["location"].(map[string]interface{})
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						So(v["tetBookingDates"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["cusTomLunarDay"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["eventDescription"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["title"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["title"].(map[string]interface{})["en"], ShouldEqual, "Title Đặt lịch trước tết Canh Tý 2020")
						So(v["tetBookingDates"].(map[string]interface{})["subTitle"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["subTitle"].(map[string]interface{})["en"], ShouldEqual, "SubTitle Đặt lịch trước tết Canh Tý 2020")
						So(v["tetBookingDates"].(map[string]interface{})["note"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["banner"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["banner"].(map[string]interface{})["en"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o")
					}
					So(respResult["googleMapWebServiceKey"], ShouldNotBeNil)
					So(respResult["subscriptionSetting"], ShouldNotBeNil)
					So(respResult["settingRating"], ShouldNotBeNil)
					So(respResult["settingSystem"], ShouldNotBeNil)

					b, _ := json.Marshal(respResult["settingSystem"])
					settingSystem := make(map[string]interface{})
					json.Unmarshal(b, &settingSystem)

					b, _ = json.Marshal(settingSystem["giftSetting"])
					giftSetting := make(map[string][]map[string]interface{})
					json.Unmarshal(b, &giftSetting)

					So(len(giftSetting["category"]), ShouldEqual, 2)
					for i, v := range giftSetting["category"] {
						if i == 0 {
							So(v["name"], ShouldEqual, "Entertainment")
						} else if i == 1 {
							So(v["name"], ShouldEqual, "Services")
						}
					}

					So(respResult["isTester"], ShouldNotBeNil)
					So(respResult["cardPaymentConfig"], ShouldNotBeNil)
					So(respResult["currency"], ShouldNotBeNil)
					So(location, ShouldNotBeNil)
					So(location["countryCode"], ShouldNotBeNil)
					So(location["isoCode"], ShouldNotBeNil)
					So(respResult["AWS3Config"], ShouldNotBeNil)
					So(respResult["isHaveEventConfig"], ShouldBeTrue)

					b, _ = json.Marshal(respResult["askerSetting"])
					askerSetting := make(map[string]interface{})
					json.Unmarshal(b, &askerSetting)
					So(askerSetting["minDebtPaymentAmount"], ShouldEqual, 1000)
				})
			})
		})

		UpdateService(query, bson.M{"$unset": bson.M{"tetBookingDates": 1}})
		UpdateSettings(bson.M{"$unset": bson.M{"giftSetting": 1}})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate Check Compare Version App")
		apiGetNotifications := "/api/v3/api-asker-vn/check-compare-version-app"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetNotifications), t, func() {
			Convey("Check request when params is invalid", func() {
				body := map[string]interface{}{
					"version": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when version blank", func() {
				body := map[string]interface{}{
					"version": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_VERSION_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_VERSION_REQUIRED.Message)
			})
			Convey("Check request when platform blank", func() {
				body := map[string]interface{}{
					"version":  "2.30.1",
					"platform": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PLATFORM_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_PLATFORM_REQUIRED.Message)
			})
			Convey("Check request when platform invalid", func() {
				body := map[string]interface{}{
					"version":  "2.30.1",
					"platform": "window",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetNotifications, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_PLATFORM_INVALID.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_PLATFORM_INVALID.Message)
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/check-compare-version-app"
		log.Println("==================================== UT Check Compare Version App")
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"versionAsker": map[string]interface{}{
					"ios": map[string]interface{}{
						"version":       "2.0.0",
						"link":          "https://itunes.apple.com/vn/app/id1054302942?mt=8",
						"description":   "",
						"isShow":        true,
						"isForce":       false,
						"forcedVersion": "2.0.0",
					},
					"android": map[string]interface{}{
						"version":       "2.0.0",
						"link":          "https://play.google.com/store/apps/details?id=com.lanterns.btaskee",
						"description":   "",
						"isShow":        true,
						"isForce":       false,
						"forcedVersion": "2.0.0",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"version":  "2.0.0",
				"platform": "android",
			}
			resp := httptest.NewRecorder()
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				service.NewRouter().ServeHTTP(resp, req)
				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult, ShouldBeNil)
			})
		})
		UpdateSettings(bson.M{
			"$unset": bson.M{
				"versionAsker": 1,
			},
		})
	})
	t.Run("9", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/check-compare-version-app"
		log.Println("==================================== UT Check Compare Version App Case 2")
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"versionAsker": map[string]interface{}{
					"ios": map[string]interface{}{
						"version":     "2.30.0",
						"link":        "https://itunes.apple.com/vn/app/id1054302942?mt=8",
						"description": "",
						"isShow":      true,
						"isForce":     false,
					},
					"android": map[string]interface{}{
						"version":     "2.30.0",
						"link":        "https://play.google.com/store/apps/details?id=com.lanterns.btaskee",
						"description": "",
						"isShow":      true,
						"isForce":     false,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"version":  "2.20.1",
				"platform": "android",
			}
			resp := httptest.NewRecorder()
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["version"], ShouldEqual, "2.30.0")
				So(respResult["isShow"], ShouldBeTrue)
			})
		})

		UpdateSettings(bson.M{
			"$unset": bson.M{
				"versionAsker": 1,
			},
		})
	})

	t.Run("9.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/check-compare-version-app"
		log.Println("==================================== UT Check Compare Version App Case 3")
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"versionAsker": map[string]interface{}{
					"ios": map[string]interface{}{
						"version":       "2.40.0",
						"link":          "https://itunes.apple.com/vn/app/id1054302942?mt=8",
						"description":   "",
						"isShow":        true,
						"isForce":       false,
						"forcedVersion": "2.30.0",
					},
					"android": map[string]interface{}{
						"version":       "2.40.0",
						"link":          "https://play.google.com/store/apps/details?id=com.lanterns.btaskee",
						"description":   "",
						"isShow":        true,
						"isForce":       false,
						"forcedVersion": "2.30.0",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"version":  "2.20.1",
				"platform": "android",
			}
			resp := httptest.NewRecorder()
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["version"], ShouldEqual, "2.40.0")
				So(respResult["isShow"], ShouldBeTrue)
				So(respResult["isForce"], ShouldBeTrue)
			})
		})

		UpdateSettings(bson.M{
			"$unset": bson.M{
				"versionAsker": 1,
			},
		})
	})
	t.Run("9.2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/check-compare-version-app"
		log.Println("==================================== UT Check Compare Version App Case 4")
		UpdateSettings(map[string]interface{}{
			"$set": map[string]interface{}{
				"versionAsker": map[string]interface{}{
					"ios": map[string]interface{}{
						"version":       "2.40.0",
						"link":          "https://itunes.apple.com/vn/app/id1054302942?mt=8",
						"description":   "",
						"isShow":        true,
						"isForce":       false,
						"forcedVersion": "2.30.0",
					},
					"android": map[string]interface{}{
						"version":       "2.40.0",
						"link":          "https://play.google.com/store/apps/details?id=com.lanterns.btaskee",
						"description":   "",
						"isShow":        true,
						"isForce":       false,
						"forcedVersion": "2.30.0",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"version":  "2.30.1",
				"platform": "android",
			}
			resp := httptest.NewRecorder()
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			Convey("Check the response", func() {
				service.NewRouter().ServeHTTP(resp, req)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["version"], ShouldEqual, "2.40.0")
				So(respResult["isShow"], ShouldBeTrue)
				So(respResult["isForce"], ShouldBeFalse)
			})
		})

		UpdateSettings(bson.M{
			"$unset": bson.M{
				"versionAsker": 1,
			},
		})
	})
	t.Run("13.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 for user blacklist, pay task by cash only")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":            "0834567890",
				"name":             "Asker 02",
				"type":             globalConstant.USER_TYPE_ASKER,
				"isBlacklistByOps": true,
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}

		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$set": bson.M{
			"isoCode": local.ISO_CODE,
			"paymentMethods": map[string]interface{}{
				"topUp": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "3.0.5", "isDefault": true},
				},
				"subscription": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
				},
				"bookTask": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "MOMO", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "ZALO_PAY", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
				},
				"recharge": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "4.0.6"},
				},
			},
		}})

		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResultM := make(map[string]map[string]interface{})
					respResultMM := make(map[string]map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultMM)
					So(resp.Code, ShouldEqual, 200)

					// PAYMENT TOPUP
					So(len(respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["isDefault"], ShouldBeFalse)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["isDefault"], ShouldBeTrue)
						}
					}
					// PAYMENT SUBSCRIPTION
					So(len(respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					//PAYMENT BOOKTASK
					So(len(respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{})), ShouldEqual, 1)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["name"].(string), ShouldEqual, "CASH")
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
					}
					//PAYMENT RECHARGE
					So(len(respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
				})
			})
		})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$unset": bson.M{"paymentMethods": 1}})
	})

	t.Run("14", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get SettingSystem communityGroup")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT}, bson.M{"$set": bson.M{"isOpenGoMarketDefault": true,
			"goMarketWithStore": map[string]interface{}{
				"status":    globalConstant.GO_MARKET_WITH_STORE_STATUS_INACTIVE,
				"isTesting": true,
			},
			"premiumOptions": map[string]interface{}{
				"status": globalConstant.SERVICE_PREMIUM_OPTIONS_STATUS_ACTIVE,
				"applyForCities": []string{
					"Hồ Chí Minh",
					"Hà Nội",
				},
				"isTesting": true,
			},
		}})
		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}
		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateSettings(bson.M{"$set": bson.M{
			"communityGroup": map[string]interface{}{
				"facebook":  "https://www.facebook.com/groups/312115383263451",
				"youtube":   "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ",
				"instagram": "https://www.instagram.com/btaskee",
			},
			"tester":                         []string{"0834567890"},
			"numberOfTaskCanSeeRatingTasker": 20,
			"isShowCovidInfo":                true,
			"isShowCovidOption":              false,
			"loginWith": []map[string]interface{}{
				{"name": "phoneNumber", "status": "ACTIVE"},
				{"name": "facebook", "status": "INACTIVE"},
			},
			"headerSetting": map[string]interface{}{
				"image": "http://...",
				"headerText": map[string]interface{}{
					"vi": "Chúc giáng sinh vui vẻ",
				},
				"images": []map[string]interface{}{
					{
						"date": map[string]interface{}{
							"from": now.AddDate(0, 0, -10),
							"to":   now.AddDate(0, 0, -1),
						},
						"image": map[string]interface{}{
							"vi": "http://link1",
							"en": "http://link1",
							"ko": "http://link1",
							"th": "http://link1",
						},
					},
					{
						"date": map[string]interface{}{
							"from": now.AddDate(0, 0, -1),
							"to":   now.AddDate(0, 0, 1),
						},
						"image": map[string]interface{}{
							"vi": "http://link2",
							"en": "http://link2",
							"ko": "http://link2",
							"th": "http://link2",
						},
					},
					{
						"date": map[string]interface{}{
							"from": now.AddDate(0, 0, 1),
							"to":   now.AddDate(0, 0, 10),
						},
						"image": map[string]interface{}{
							"vi": "http://link3",
							"en": "http://link3",
							"ko": "http://link3",
							"th": "http://link3",
						},
					},
				},
			},
		}})

		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$set": bson.M{"minTopUp": 100,
			"isoCode": local.ISO_CODE,
			"paymentMethods": map[string]interface{}{
				"topUp": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "INACTIVE", "minVersion": "4.0.5", "isTesting": true},
				},
				"subscription": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5", "isTesting": true},
					{"name": "CASH", "status": "PENDING", "minVersion": "4.0.5", "isTesting": true},
				},
				"bookTask": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "4.0.5"},
					{"name": "CASH", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "KREDIVO", "status": "ACTIVE", "minVersion": "3.0.5", "notApplyForServices": []interface{}{"CLEANING"}},
				},
				"recharge": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "4.0.6"},
				},
				"comboVoucher": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "MOMO", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "ZALO_PAY", "status": "ACTIVE", "minVersion": "4.0.5"},
				},
			},
		}})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		UpdateSubscriptionSetting(
			bson.M{
				"$set": bson.M{
					"renewBefore": 7,
					"discount": []map[string]interface{}{
						{
							"city":     "Hồ Chí Minh",
							"discount": 0,
							"discountByMonth": []map[string]interface{}{
								{
									"month":              1,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           100000,
								},
								{
									"month":              2,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           200000,
								},
								{
									"month":              3,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           300000,
								},
							},
							"startDate": currentTime.AddDate(0, -1, 0),
							"endDate":   currentTime.AddDate(0, 4, 0),
						}, {
							"city":     "Hà Nội",
							"discount": 0,
							"discountByMonth": []map[string]interface{}{
								{
									"month":              1,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           100000,
								},
								{
									"month":              2,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           200000,
								},
								{
									"month":              3,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           300000,
								},
							},
							"startDate": currentTime.AddDate(0, -3, 0),
							"endDate":   currentTime.AddDate(0, -1, 0),
						}, {
							"city":     "Đà Nẵng",
							"discount": 0,
							"discountByMonth": []map[string]interface{}{
								{
									"month":              1,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           100000,
								},
								{
									"month":              2,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           200000,
								},
								{
									"month":              3,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           300000,
								},
							},
							"startDate": currentTime.AddDate(0, 1, 0),
							"endDate":   currentTime.AddDate(0, 4, 0),
						},
					},
				},
			})
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResultM := make(map[string]map[string]interface{})
					respResultMM := make(map[string]map[string]map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					respResultMMArray := make(map[string]map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultArrayM)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultMM)
					json.Unmarshal(bytes, &respResultMMArray)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["subscriptionSetting"]["renewBefore"], ShouldEqual, 7)
					So(len(respResultMMArray["subscriptionSetting"]["discount"]), ShouldEqual, 1)
					So(respResultMMArray["subscriptionSetting"]["discount"][0]["city"], ShouldEqual, "Hồ Chí Minh")
					So(respResultMM["settingSystem"]["communityGroup"]["facebook"], ShouldEqual, "https://www.facebook.com/groups/312115383263451")
					So(respResultMM["settingSystem"]["communityGroup"]["youtube"], ShouldEqual, "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ")
					So(respResultMM["settingSystem"]["communityGroup"]["instagram"], ShouldEqual, "https://www.instagram.com/btaskee")
					So(respResultM["settingSystem"]["numberOfTaskCanSeeRatingTasker"], ShouldEqual, 20)
					So(respResultM["settingSystem"]["isShowCovidInfo"], ShouldBeTrue)
					So(respResultM["settingSystem"]["isShowCovidOption"], ShouldBeNil)
					So(respResultM["settingSystem"]["loginWith"], ShouldResemble, []interface{}{"phoneNumber"})

					So(respResultM["settingSystem"]["headerSetting"], ShouldNotBeNil)
					headerSettings := respResultM["settingSystem"]["headerSetting"].(map[string]interface{})
					imageHeader := headerSettings["imageHeader"].(map[string]interface{})
					So(imageHeader["en"], ShouldEqual, "http://link2")
					for _, v := range respResultArrayM["services"] {
						if v["text"].(map[string]interface{})["en"] == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT {
							So(v["isOpenGoMarketDefault"].(bool), ShouldBeTrue)
							So(v["goMarketWithStore"], ShouldNotBeNil)
							So(v["goMarketWithStore"].(map[string]interface{})["status"], ShouldEqual, globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE)
							So(v["premiumOptions"], ShouldNotBeNil)
							So(v["premiumOptions"].(map[string]interface{})["status"], ShouldEqual, globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE)
						}
					}
					// PAYMENT TOPUP
					So(len(respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{})), ShouldEqual, 3)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					// PAYMENT SUBSCRIPTION
					So(len(respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
					}
					//PAYMENT BOOKTASK
					So(len(respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})

						if vMap["name"].(string) == "CASH" {
							So(vMap["name"].(string), ShouldEqual, "CASH")
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else {
							So(vMap["name"].(string), ShouldEqual, "KREDIVO")
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["notApplyForServices"], ShouldResemble, []interface{}{"CLEANING"})
						}
					}
					//PAYMENT RECHARGE
					So(len(respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					//PAYMENT COMBOVOUCHER
					So(len(respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{})), ShouldEqual, 3)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "MOMO" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
				})
			})
		})
		UpdateSettings(bson.M{"$unset": bson.M{"communityGroup": 1, "tester": 1, "numberOfTaskCanSeeRatingTasker": 1, "loginWith": 1}})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$unset": bson.M{"paymentMethods": 1}})
		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT}, bson.M{"$unset": bson.M{"isOpenGoMarketDefault": 1, "goMarketWithStore": 1}})
	})

	// Check campaign for payment method
	t.Run("14.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 Check campaign for payment method")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    "VN",
			"appVersion": "4.0.0",
		}
		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateSettings(bson.M{"$set": bson.M{"tester": []string{"0834567890"}}})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$set": bson.M{"minTopUp": 100,
			"isoCode": globalConstant.ISO_CODE_VN,
			"paymentMethods": map[string]interface{}{
				"topUp": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "INACTIVE", "minVersion": "4.0.5", "isTesting": true},
				},
				"subscription": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5", "isTesting": true},
					{"name": "CASH", "status": "PENDING", "minVersion": "4.0.5", "isTesting": true},
				},
				"bookTask": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "4.0.5"},
					{"name": "CASH", "status": "PENDING", "minVersion": "3.0.5"},
				},
				"recharge": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "4.0.6"},
				},
				"comboVoucher": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "MOMO", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "ZALO_PAY", "status": "ACTIVE", "minVersion": "4.0.5"},
				},
			},
		}})

		CreatePaymentMethodCampaign([]map[string]interface{}{
			{ // Success because in time
				"paymentMethod": globalConstant.PAYMENT_METHOD_MOMO,
				"startDate":     now.AddDate(0, 0, -1),
				"endDate":       now.AddDate(0, 0, 1),
				"status":        globalConstant.PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE,
				"isTesting":     false,
				"description": map[string]interface{}{
					"vi": "Momo campaign",
				},
			},
			{ // Fail because NOT in time
				"paymentMethod": globalConstant.PAYMENT_METHOD_ZALO_PAY,
				"startDate":     now.AddDate(0, 0, 1),
				"endDate":       now.AddDate(0, 0, 2),
				"status":        globalConstant.PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE,
				"isTesting":     false,
				"description": map[string]interface{}{
					"vi": "Zalopay campaign",
				},
			},
			{ // Success because in time and for tester
				"paymentMethod": globalConstant.PAYMENT_METHOD_CASH,
				"startDate":     now.AddDate(0, 0, -1),
				"endDate":       now.AddDate(0, 0, 2),
				"status":        globalConstant.PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE,
				"isTesting":     true,
				"description": map[string]interface{}{
					"vi": "Cash campaign",
				},
			},
		})
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v2/api-service/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {

					testCampaignSuccess := func(campaign interface{}, descriptionVi string) {
						// Parse campaign to map
						campaignMap := make(map[string]interface{})
						campaignData, _ := json.Marshal(campaign)
						json.Unmarshal(campaignData, &campaignMap)

						// Check campaign with description
						campaignDescription := make(map[string]interface{})
						campaignDescriptionData, _ := json.Marshal(campaignMap["description"])
						json.Unmarshal(campaignDescriptionData, &campaignDescription)
						So(campaignDescription["vi"], ShouldEqual, descriptionVi)
					}

					respResultMM := make(map[string]map[string]map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultMM)
					So(resp.Code, ShouldEqual, 200)
					// PAYMENT TOPUP
					So(len(respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{})), ShouldEqual, 3)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							testCampaignSuccess(vMap["campaign"], "Cash campaign")
						}
					}
					// PAYMENT SUBSCRIPTION
					So(len(respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
					}
					//PAYMENT BOOKTASK
					So(len(respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{})), ShouldEqual, 1)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["name"].(string), ShouldEqual, "CASH")
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
					}
					//PAYMENT RECHARGE
					So(len(respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					//PAYMENT COMBOVOUCHER
					So(len(respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{})), ShouldEqual, 3)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "MOMO" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							testCampaignSuccess(vMap["campaign"], "Momo campaign")
						}
					}
				})
			})
		})
		UpdateSettings(bson.M{"$unset": bson.M{"tester": 1}})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$unset": bson.M{"paymentMethods": 1}})
	})

	t.Run("15.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		var city = []map[string]interface{}{
			{
				"name": "Hồ Chí Minh",
				"numberOfChildInPercentage": []map[string]interface{}{
					{
						"number":     2,
						"percentage": 0.3,
					},
				},
				"younger6YearsBaseCost": []map[string]interface{}{
					{
						"duration": 3.0,
						"cost":     250000.0,
					},
					{
						"duration": 4.0,
						"cost":     300000.0,
					},
					{
						"duration": 8.0,
						"cost":     500000.0,
					},
				},
				"older6YearsBaseCost": []map[string]interface{}{
					{
						"duration": 3.0,
						"cost":     250000.0,
					},
					{
						"duration": 4.0,
						"cost":     400000.0,
					},
					{
						"duration": 8.0,
						"cost":     500000.0,
					},
				},
				"limitBookTaskDate": 20,
			},
		}
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_CHILD_CARE}, bson.M{"$set": bson.M{"city": city}})
		UpdateService(bson.M{}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}, "$unset": bson.M{"isTesting": 1}})
		// Update ecoDiscountPercentage subs homeCleaning + monthlyOptions
		UpdateService(
			bson.M{
				"name":      globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION,
				"city.name": "Hồ Chí Minh",
			},
			bson.M{
				"$set": bson.M{
					"ecoOptions": bson.M{
						"status": "ACTIVE",
					},
					"city.$.ecoDiscountPercentage": 0.1,
					"monthlyOptions": []map[string]interface{}{
						{
							"name": "1month",
							"text": map[string]interface{}{
								"vi": "1 tháng",
							},
							"duration": 1,
						},
						{
							"name": "2month",
							"text": map[string]interface{}{
								"vi": "2 tháng",
							},
							"duration": 2,
						},
					},
				},
			},
		)
		defer UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}, bson.M{"$unset": bson.M{"city.ecoDiscountPercentage": 1, "monthlyOptions": 1}})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 24)
					tested := map[string]bool{}
					for _, v := range respResultArrayM["services"] {
						if v["name"] == globalConstant.SERVICE_KEY_NAME_DISINFECTION {
							disinfectionServiceData, _ := json.Marshal(v)
							var disinfectionService *modelService.Service
							json.Unmarshal(disinfectionServiceData, &disinfectionService)

							So(len(disinfectionService.DisinfectionDetail.City), ShouldEqual, 1)
							So(disinfectionService.DisinfectionDetail.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(disinfectionService.DisinfectionDetail.City[0].Area), ShouldEqual, 3)
							for _, area := range disinfectionService.DisinfectionDetail.City[0].Area {
								So(area.IsShowInAppAsker, ShouldBeTrue)
							}
							tested[globalConstant.SERVICE_KEY_NAME_DISINFECTION] = true
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_CHILD_CARE {
							childCareServiceData, _ := json.Marshal(v)
							var childCareService *modelService.Service
							json.Unmarshal(childCareServiceData, &childCareService)
							So(len(childCareService.City), ShouldEqual, 1)
							So(childCareService.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(childCareService.City[0].NumberOfChildInPercentage), ShouldEqual, 1)
							So(len(childCareService.City[0].Younger6YearsBaseCost), ShouldEqual, 3)
							So(len(childCareService.City[0].Older6YearsBaseCost), ShouldEqual, 3)

							// Check limit book task date
							limitBookTaskDates := []map[string]interface{}{}
							limitBookTaskDatesData, _ := json.Marshal(v["limitBookTaskDates"])
							json.Unmarshal(limitBookTaskDatesData, &limitBookTaskDates)
							So(len(limitBookTaskDates), ShouldEqual, 1)
							So(limitBookTaskDates[0]["city"], ShouldEqual, "Hồ Chí Minh")
							So(limitBookTaskDates[0]["limitBookTaskDate"], ShouldEqual, 20)

							tested[globalConstant.SERVICE_KEY_NAME_CHILD_CARE] = true
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION {
							subsServiceData, _ := json.Marshal(v)
							var subsService *modelService.Service
							json.Unmarshal(subsServiceData, &subsService)
							So(len(subsService.City), ShouldEqual, 1)
							So(subsService.City[0].EcoDiscountPercentage, ShouldEqual, 0.1)
							So(subsService.EcoOptions.Status, ShouldEqual, "ACTIVE")
							So(len(subsService.MonthlyOptions), ShouldEqual, 2)
							So(subsService.MonthlyOptions[0].Name, ShouldEqual, "1month")
							So(subsService.MonthlyOptions[0].Text.Vi, ShouldEqual, "1 tháng")
							So(subsService.MonthlyOptions[0].Duration, ShouldEqual, 1)
							So(subsService.MonthlyOptions[1].Name, ShouldEqual, "2month")
							So(subsService.MonthlyOptions[1].Text.Vi, ShouldEqual, "2 tháng")
							So(subsService.MonthlyOptions[1].Duration, ShouldEqual, 2)
							tested[globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION] = true
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING {
							industrialCleaningServiceData, _ := json.Marshal(v)
							var industrialCleaningService *modelService.Service
							json.Unmarshal(industrialCleaningServiceData, &industrialCleaningService)
							So(len(industrialCleaningService.City), ShouldEqual, 1)
							So(industrialCleaningService.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(industrialCleaningService.DetailService, ShouldNotBeNil)
							So(industrialCleaningService.DetailService.IndustrialCleaning, ShouldNotBeNil)
							So(len(industrialCleaningService.DetailService.IndustrialCleaning.City), ShouldEqual, 1)
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(industrialCleaningService.DetailService.IndustrialCleaning.City[0].HomeType), ShouldEqual, 2)
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].HomeType[0].Name, ShouldEqual, "HOME")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].HomeType[1].Name, ShouldEqual, "OFFICE")
							So(len(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services), ShouldEqual, 3)
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[0].Name, ShouldEqual, "glassesCleaning")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[1].Name, ShouldEqual, "floorBurnishing")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[2].Name, ShouldEqual, "sofaCleaning")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[2].SofaType[0].Options[0].Description.Vi, ShouldEqual, "Cleaning sofa 1 ghế")
							tested[globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING] = true
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE {
							beautyCareServiceData, _ := json.Marshal(v)
							var beautyCareService *modelService.Service
							json.Unmarshal(beautyCareServiceData, &beautyCareService)
							So(len(beautyCareService.City), ShouldEqual, 1)
							So(beautyCareService.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(beautyCareService.DetailService, ShouldNotBeNil)
							So(beautyCareService.DetailService.BeautyCare, ShouldNotBeNil)
							So(len(beautyCareService.DetailService.BeautyCare.City), ShouldEqual, 1)
							So(beautyCareService.DetailService.BeautyCare.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(beautyCareService.DetailService.BeautyCare.City[0].IsDefault, ShouldEqual, true)
							So(len(beautyCareService.DetailService.BeautyCare.City[0].Packages), ShouldEqual, 2)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].Name, ShouldEqual, "nail")
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].MainServices, ShouldHaveLength, 1)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].MainServices[0].Name, ShouldEqual, "gel")
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices, ShouldHaveLength, 3)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices[0].Name, ShouldEqual, "toe")
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices[0].Options, ShouldHaveLength, 1)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices[0].Options[0].Name, ShouldEqual, "regular")
							tested[globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE] = true
						}
					}
					So(len(tested), ShouldEqual, 5)
					UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_CHILD_CARE}, bson.M{"$unset": bson.M{"city": true}})
				})
			})
		})
	})
	t.Run("16.2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get new referral voucher value")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		body := map[string]interface{}{
			"userId":     "0834567890",
			"appVersion": "3.0.0",
		}
		UpdateSettings(bson.M{"$set": bson.M{
			"referralValue": 50000,
			"referralSetting": map[string]interface{}{
				"type": "voucher",
				"voucher": map[string]interface{}{
					"image": map[string]interface{}{
						"vi": "http://...",
					},
					"value": 30000,
				},
			},
		}})
		reqBody, _ := json.Marshal(body)
		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["settingSystem"], ShouldNotBeNil)
					settingSystem := respResult["settingSystem"].(map[string]interface{})
					So(settingSystem["referralValue"], ShouldEqual, 30000)
					So(settingSystem["referralSetting"], ShouldNotBeNil)
				})
			})
		})
	})

	// Test for new login flow
	t.Run("17", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if body invalid", func() {
				body := map[string]interface{}{
					"isoCode": 123123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
			Convey("Check the response if isoCode is empty", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
			})
			Convey("Check the response if appVersion is empty", func() {
				body := map[string]interface{}{
					"isoCode":    local.ISO_CODE,
					"appVersion": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				m := map[string]map[string]interface{}{}
				json.Unmarshal(b, &m)
				So(res.Code, ShouldEqual, 400)
				So(m["error"]["code"], ShouldEqual, lib.ERROR_APP_VERSION_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("18", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()

		var city = []map[string]interface{}{
			{
				"name":              "Hồ Chí Minh",
				"limitBookTaskDate": 30,
				"numberOfChildInPercentage": []map[string]interface{}{
					{
						"number":     2,
						"percentage": 0.3,
					},
				},
				"younger6YearsBaseCost": []map[string]interface{}{
					{
						"duration": 3.0,
						"cost":     250000.0,
					},
					{
						"duration": 4.0,
						"cost":     300000.0,
					},
					{
						"duration": 8.0,
						"cost":     500000.0,
					},
				},
				"older6YearsBaseCost": []map[string]interface{}{
					{
						"duration": 3.0,
						"cost":     250000.0,
					},
					{
						"duration": 4.0,
						"cost":     400000.0,
					},
					{
						"duration": 8.0,
						"cost":     500000.0,
					},
				},
			},
		}
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_CHILD_CARE}, bson.M{"$set": bson.M{"city": city}})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{
			"optional": map[string]interface{}{
				"isAutoChooseTaskerEnabled": 1,
			},
			"priceSetting.costForChooseTasker": 10000,
		}})

		UpdateService(
			bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION},
			bson.M{
				"$set": bson.M{
					"monthlyOptions": []map[string]interface{}{
						{
							"name": "1month",
							"text": map[string]interface{}{
								"vi": "1 tháng",
							},
							"duration": 1,
						},
						{
							"name": "2month",
							"text": map[string]interface{}{
								"vi": "2 tháng",
							},
							"duration": 2,
						},
					},
				},
			},
		)
		defer UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}, bson.M{"$unset": bson.M{"monthlyOptions": true}})

		UpdateService(bson.M{}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}, "$unset": bson.M{"isTesting": 1}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				UpdateService(bson.M{}, bson.M{"$unset": bson.M{"optional": 1}})

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 24)

					var isVerifiedHomeCleaningSubscription bool
					tested := map[string]bool{}
					for _, v := range respResultArrayM["services"] {
						// check optional
						if v["name"] == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING {
							So(v["optional"], ShouldNotBeNil)
							optional := v["optional"].(map[string]interface{})
							So(optional["isAutoChooseTaskerEnabled"], ShouldBeTrue)
							So(v["priceSetting"].(map[string]interface{})["costForChooseTasker"], ShouldEqual, 10000)
							tested[globalConstant.SERVICE_KEY_NAME_HOME_CLEANING] = true
						} else {
							So(v["optional"], ShouldBeNil)
						}

						if v["name"] == globalConstant.SERVICE_KEY_NAME_DISINFECTION {
							disinfectionServiceData, _ := json.Marshal(v)
							var disinfectionService *modelService.Service
							json.Unmarshal(disinfectionServiceData, &disinfectionService)

							So(len(disinfectionService.DisinfectionDetail.City), ShouldEqual, 1)
							So(disinfectionService.DisinfectionDetail.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(disinfectionService.DisinfectionDetail.City[0].Area), ShouldEqual, 3)
							for _, area := range disinfectionService.DisinfectionDetail.City[0].Area {
								So(area.IsShowInAppAsker, ShouldBeTrue)
							}
							tested[globalConstant.SERVICE_KEY_NAME_DISINFECTION] = true
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_CHILD_CARE {
							childCareServiceData, _ := json.Marshal(v)
							var childCareService *modelService.Service
							json.Unmarshal(childCareServiceData, &childCareService)
							So(len(childCareService.City), ShouldEqual, 1)
							So(childCareService.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(childCareService.City[0].NumberOfChildInPercentage), ShouldEqual, 1)
							So(len(childCareService.City[0].Younger6YearsBaseCost), ShouldEqual, 3)
							So(len(childCareService.City[0].Older6YearsBaseCost), ShouldEqual, 3)

							// Check limit book task date
							limitBookTaskDates := []map[string]interface{}{}
							limitBookTaskDatesData, _ := json.Marshal(v["limitBookTaskDates"])
							json.Unmarshal(limitBookTaskDatesData, &limitBookTaskDates)
							So(len(limitBookTaskDates), ShouldEqual, 1)
							So(limitBookTaskDates[0]["city"], ShouldEqual, "Hồ Chí Minh")
							So(limitBookTaskDates[0]["limitBookTaskDate"], ShouldEqual, 30)

							tested[globalConstant.SERVICE_KEY_NAME_CHILD_CARE] = true
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING {
							industrialCleaningServiceData, _ := json.Marshal(v)
							var industrialCleaningService *modelService.Service
							json.Unmarshal(industrialCleaningServiceData, &industrialCleaningService)
							So(len(industrialCleaningService.City), ShouldEqual, 1)
							So(industrialCleaningService.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(industrialCleaningService.DetailService, ShouldNotBeNil)
							So(industrialCleaningService.DetailService.IndustrialCleaning, ShouldNotBeNil)
							So(len(industrialCleaningService.DetailService.IndustrialCleaning.City), ShouldEqual, 1)
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(industrialCleaningService.DetailService.IndustrialCleaning.City[0].HomeType), ShouldEqual, 2)
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].HomeType[0].Name, ShouldEqual, "HOME")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].HomeType[1].Name, ShouldEqual, "OFFICE")
							So(len(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services), ShouldEqual, 3)
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[0].Name, ShouldEqual, "glassesCleaning")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[1].Name, ShouldEqual, "floorBurnishing")
							So(industrialCleaningService.DetailService.IndustrialCleaning.City[0].Services[2].Name, ShouldEqual, "sofaCleaning")
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION {
							isVerifiedHomeCleaningSubscription = true
							subsServiceData, _ := json.Marshal(v)
							var subsService *modelService.Service
							json.Unmarshal(subsServiceData, &subsService)
							So(len(subsService.MonthlyOptions), ShouldEqual, 2)
							So(subsService.MonthlyOptions[0].Name, ShouldEqual, "1month")
							So(subsService.MonthlyOptions[0].Text.Vi, ShouldEqual, "1 tháng")
							So(subsService.MonthlyOptions[0].Duration, ShouldEqual, 1)
							So(subsService.MonthlyOptions[1].Name, ShouldEqual, "2month")
							So(subsService.MonthlyOptions[1].Text.Vi, ShouldEqual, "2 tháng")
							So(subsService.MonthlyOptions[1].Duration, ShouldEqual, 2)
						} else if v["name"] == globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE {
							beautyCareServiceData, _ := json.Marshal(v)
							var beautyCareService *modelService.Service
							json.Unmarshal(beautyCareServiceData, &beautyCareService)
							So(len(beautyCareService.City), ShouldEqual, 1)
							So(beautyCareService.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(beautyCareService.DetailService, ShouldNotBeNil)
							So(beautyCareService.DetailService.BeautyCare, ShouldNotBeNil)
							So(len(beautyCareService.DetailService.BeautyCare.City), ShouldEqual, 1)
							So(beautyCareService.DetailService.BeautyCare.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(beautyCareService.DetailService.BeautyCare.City[0].IsDefault, ShouldEqual, true)
							So(len(beautyCareService.DetailService.BeautyCare.City[0].Packages), ShouldEqual, 2)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].Name, ShouldEqual, "nail")
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].MainServices, ShouldHaveLength, 1)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].MainServices[0].Name, ShouldEqual, "gel")
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices, ShouldHaveLength, 3)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices[0].Name, ShouldEqual, "toe")
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices[0].Options, ShouldHaveLength, 1)
							So(beautyCareService.DetailService.BeautyCare.City[0].Packages[0].ExtraServices[0].Options[0].Name, ShouldEqual, "regular")
							tested[globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE] = true
						}
					}
					So(isVerifiedHomeCleaningSubscription, ShouldBeTrue)
					So(len(tested), ShouldEqual, 4)
					UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_CHILD_CARE}, bson.M{"$unset": bson.M{"city": true, "priceSetting.costForChooseTasker": 1}})
				})
			})
		})
	})
	t.Run("20", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2 get SettingSystem communityGroup")
		ResetData()

		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}
		UpdateSettings(bson.M{"$set": bson.M{
			"communityGroup": map[string]interface{}{
				"facebook":  "https://www.facebook.com/groups/312115383263451",
				"youtube":   "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ",
				"instagram": "https://www.instagram.com/btaskee",
			},
			"numberOfTaskCanSeeRatingTasker": 20,
			"isShowCovidInfo":                true,
			"isShowCovidOption":              false,
			"loginWith": []map[string]interface{}{
				{"name": "phoneNumber", "status": "ACTIVE"},
				{"name": "facebook", "status": "INACTIVE"},
			},
		}})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$set": bson.M{"minTopUp": 100,
			"isoCode": local.ISO_CODE,
			"paymentMethods": map[string]interface{}{
				"topUp": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "INACTIVE", "minVersion": "4.0.5", "isTesting": true},
				},
				"subscription": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5", "isTesting": true},
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "PENDING", "minVersion": "4.0.5", "isTesting": true},
				},
				"bookTask": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "4.0.5"},
					{"name": "CASH", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "KREDIVO", "status": "ACTIVE", "minVersion": "3.0.5", "notApplyForServices": []interface{}{"CLEANING"}},
				},
				"recharge": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "4.0.6"},
				},
				"comboVoucher": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "MOMO", "status": "ACTIVE", "minVersion": "3.0.5", "isDefault": true},
					{"name": "ZALO_PAY", "status": "ACTIVE", "minVersion": "4.0.5"},
				},
			},
		}})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		UpdateSubscriptionSetting(
			bson.M{
				"$set": bson.M{
					"renewBefore": 7,
					"discount": []map[string]interface{}{
						{
							"city":     "Hồ Chí Minh",
							"discount": 0,
							"discountByMonth": []map[string]interface{}{
								{
									"month":              1,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           100000,
								},
								{
									"month":              2,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           200000,
								},
								{
									"month":              3,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           300000,
								},
							},
							"startDate": currentTime.AddDate(0, -1, 0),
							"endDate":   currentTime.AddDate(0, 4, 0),
						}, {
							"city":     "Hà Nội",
							"discount": 0,
							"discountByMonth": []map[string]interface{}{
								{
									"month":              1,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           100000,
								},
								{
									"month":              2,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           200000,
								},
								{
									"month":              3,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           300000,
								},
							},
							"startDate": currentTime.AddDate(0, -3, 0),
							"endDate":   currentTime.AddDate(0, -1, 0),
						}, {
							"city":     "Đà Nẵng",
							"discount": 0,
							"discountByMonth": []map[string]interface{}{
								{
									"month":              1,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           100000,
								},
								{
									"month":              2,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           200000,
								},
								{
									"month":              3,
									"percentage":         0,
									"discountPercentage": 0.1,
									"maxValue":           300000,
								},
							},
							"startDate": currentTime.AddDate(0, 1, 0),
							"endDate":   currentTime.AddDate(0, 4, 0),
						},
					},
				},
			})
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResultM := make(map[string]map[string]interface{})
					respResultMM := make(map[string]map[string]map[string]interface{})
					respResultMMArray := make(map[string]map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultMM)
					json.Unmarshal(bytes, &respResultMMArray)
					So(resp.Code, ShouldEqual, 200)
					So(respResultM["subscriptionSetting"]["renewBefore"], ShouldEqual, 7)
					So(len(respResultMMArray["subscriptionSetting"]["discount"]), ShouldEqual, 1)
					So(respResultMMArray["subscriptionSetting"]["discount"][0]["city"], ShouldEqual, "Hồ Chí Minh")
					So(respResultMM["settingSystem"]["communityGroup"]["facebook"], ShouldEqual, "https://www.facebook.com/groups/312115383263451")
					So(respResultMM["settingSystem"]["communityGroup"]["youtube"], ShouldEqual, "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ")
					So(respResultMM["settingSystem"]["communityGroup"]["instagram"], ShouldEqual, "https://www.instagram.com/btaskee")
					So(respResultM["settingSystem"]["numberOfTaskCanSeeRatingTasker"], ShouldEqual, 20)
					So(respResultM["settingSystem"]["isShowCovidInfo"], ShouldBeTrue)
					So(respResultM["settingSystem"]["isShowCovidOption"], ShouldBeNil)
					So(respResultM["settingSystem"]["loginWith"], ShouldResemble, []interface{}{"phoneNumber"})
					// PAYMENT TOPUP
					So(len(respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					// PAYMENT SUBSCRIPTION
					So(len(respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{})), ShouldEqual, 1) // Don"t get tester
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
					}
					//PAYMENT BOOKTASK
					So(len(respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{})), ShouldEqual, 2) // Min app version
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CASH" {
							So(vMap["name"].(string), ShouldEqual, "CASH")
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else {
							So(vMap["name"].(string), ShouldEqual, "KREDIVO")
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["notApplyForServices"], ShouldResemble, []interface{}{"CLEANING"})
						}
					}
					//PAYMENT RECHARGE
					So(len(respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					//PAYMENT COMBOVOUCHER
					So(len(respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{})), ShouldEqual, 3)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
							So(vMap["isDefault"], ShouldBeFalse)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["isDefault"], ShouldBeFalse)
						} else if vMap["name"].(string) == "MOMO" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["isDefault"], ShouldBeTrue)
						}
					}
				})
			})
		})
		UpdateSettings(bson.M{"$unset": bson.M{"communityGroup": 1, "tester": 1, "numberOfTaskCanSeeRatingTasker": 1, "loginWith": 1}})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$unset": bson.M{"paymentMethods": 1}})
	})
	// Check campaign of payment method
	t.Run("20.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2 Check campaign of payment method")
		ResetData()

		body := map[string]interface{}{
			"isoCode":    globalConstant.ISO_CODE_VN,
			"appVersion": "4.0.0",
		}
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$set": bson.M{"minTopUp": 100,
			"isoCode": globalConstant.ISO_CODE_VN,
			"paymentMethods": map[string]interface{}{
				"topUp": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "INACTIVE", "minVersion": "4.0.5", "isTesting": true},
				},
				"subscription": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5", "isTesting": true},
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "PENDING", "minVersion": "4.0.5", "isTesting": true},
				},
				"bookTask": []map[string]interface{}{
					{"name": "CARD", "status": "ACTIVE", "minVersion": "4.0.5"},
					{"name": "CASH", "status": "PENDING", "minVersion": "3.0.5"},
				},
				"recharge": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "CASH", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "4.0.6"},
				},
				"comboVoucher": []map[string]interface{}{
					{"name": "CARD", "status": "PENDING", "minVersion": "3.0.5"},
					{"name": "BPAY", "status": "ACTIVE", "minVersion": "3.0.5"},
					{"name": "MOMO", "status": "ACTIVE", "minVersion": "3.0.5", "isDefault": true},
					{"name": "ZALO_PAY", "status": "ACTIVE", "minVersion": "4.0.5"},
				},
			},
		}})

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreatePaymentMethodCampaign([]map[string]interface{}{
			{ // Success because in time
				"paymentMethod": globalConstant.PAYMENT_METHOD_MOMO,
				"startDate":     now.AddDate(0, 0, -1),
				"endDate":       now.AddDate(0, 0, 1),
				"status":        globalConstant.PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE,
				"isTesting":     false,
				"description": map[string]interface{}{
					"vi": "Momo campaign",
				},
			},
			{ // Fail because NOT in time
				"paymentMethod": globalConstant.PAYMENT_METHOD_ZALO_PAY,
				"startDate":     now.AddDate(0, 0, 1),
				"endDate":       now.AddDate(0, 0, 2),
				"status":        globalConstant.PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE,
				"isTesting":     false,
				"description": map[string]interface{}{
					"vi": "Zalopay campaign",
				},
			},
		})
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v2/api-service/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResultM := make(map[string]map[string]interface{})
					respResultMM := make(map[string]map[string]map[string]interface{})
					respResultMMArray := make(map[string]map[string][]map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultMM)
					json.Unmarshal(bytes, &respResultMMArray)
					So(resp.Code, ShouldEqual, 200)

					testCampaignSuccess := func(campaign interface{}, descriptionVi string) {
						// Parse campaign to map
						campaignMap := make(map[string]interface{})
						campaignData, _ := json.Marshal(campaign)
						json.Unmarshal(campaignData, &campaignMap)

						// Check campaign with description
						campaignDescription := make(map[string]interface{})
						campaignDescriptionData, _ := json.Marshal(campaignMap["description"])
						json.Unmarshal(campaignDescriptionData, &campaignDescription)
						So(campaignDescription["vi"], ShouldEqual, descriptionVi)
					}

					// PAYMENT TOPUP
					So(len(respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["topUp"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					// PAYMENT SUBSCRIPTION
					So(len(respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{})), ShouldEqual, 1) // Don"t get tester
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["subscription"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
					}
					//PAYMENT BOOKTASK
					So(len(respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{})), ShouldEqual, 1) // Min app version
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["bookTask"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						So(vMap["name"].(string), ShouldEqual, "CASH")
						So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
					}
					//PAYMENT RECHARGE
					So(len(respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{})), ShouldEqual, 2)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["recharge"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
						} else if vMap["name"].(string) == "CASH" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
						}
					}
					//PAYMENT COMBOVOUCHER
					So(len(respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{})), ShouldEqual, 3)
					for _, v := range respResultMM["settingSystem"]["paymentMethods"]["comboVoucher"].([]interface{}) {
						vMap, _ := v.(map[string]interface{})
						if vMap["name"].(string) == "CARD" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_PENDING)
							So(vMap["isDefault"], ShouldBeFalse)
						} else if vMap["name"].(string) == "BPAY" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["isDefault"], ShouldBeFalse)
						} else if vMap["name"].(string) == "MOMO" {
							So(vMap["status"].(string), ShouldEqual, globalConstant.PAYMENT_METHOD_STATUS_ACTIVE)
							So(vMap["isDefault"], ShouldBeTrue)
							testCampaignSuccess(vMap["campaign"], "Momo campaign")
						}
					}
				})
			})
		})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$unset": bson.M{"paymentMethods": 1}})
	})

	t.Run("21", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2")
		ResetData()

		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{
			"durationByArea": []map[string]interface{}{
				{
					"duration":      2,
					"area":          55,
					"numberOfRooms": 2,
				},
				{
					"duration":      3,
					"area":          65,
					"numberOfRooms": 2,
				},
			},
			"tip": map[string]interface{}{
				"tip": 10000,
				"requirements": []bson.M{
					{
						"type": 1,
						"text": map[string]interface{}{
							"vi": "Nấu ăn",
							"en": "Cooking",
							"ko": "요리",
							"th": "ทำอาหาร",
						},
						"cost":     0,
						"icon":     "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/jEN88SzAtMrCa3k72",
						"duration": 1,
					},
					{
						"type": 2,
						"text": map[string]interface{}{
							"vi": "Ủi đồ",
							"en": "Ironing",
							"ko": "다림질",
							"th": "รีดผ้า",
						},
						"cost":     0,
						"icon":     "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/SgnpFbXJ4tpJpe8N7",
						"duration": 1,
					},
					{
						"type": 3,
						"text": map[string]interface{}{
							"vi": "Mang theo dụng cụ",
							"en": "Bring cleaning supplies",
							"ko": "청소도구 준비",
							"th": "นำอุปกรณ์ทำความสะอาดมา",
						},
						"cost": 30000,
						"icon": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/WE66bnf684258Q9Lr",
					},
				},
			},
			"priceSetting.costForChooseTasker": 10000,
		}})
		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT}, bson.M{"$set": bson.M{"isOpenGoMarketDefault": true,
			"goMarketWithStore": map[string]interface{}{
				"status":    globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE,
				"isTesting": true,
			},
			"premiumOptions": map[string]interface{}{
				"status": globalConstant.SERVICE_PREMIUM_OPTIONS_STATUS_ACTIVE,
				"applyForCities": []string{
					"Hồ Chí Minh",
					"Hà Nội",
				},
				"isTesting": true,
			}}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						if v["text"].(map[string]interface{})["en"] == globalConstant.SERVICE_NAME_HOME_CLEANING {
							durationByAreaData, _ := json.Marshal(v["durationByArea"])
							durationByArea := []*modelService.ServiceDurationByArea{}
							json.Unmarshal(durationByAreaData, &durationByArea)
							So(len(durationByArea), ShouldEqual, 2)
							So(durationByArea[0].Duration, ShouldEqual, 2)
							So(durationByArea[0].Area, ShouldEqual, 55)
							So(durationByArea[0].NumberOfRooms, ShouldEqual, 2)
							So(durationByArea[1].Duration, ShouldEqual, 3)
							So(durationByArea[1].Area, ShouldEqual, 65)
							So(durationByArea[1].NumberOfRooms, ShouldEqual, 2)
							requirements := v["requirements"].([]interface{})
							So(len(requirements), ShouldEqual, 3)
							So(v["priceSetting"].(map[string]interface{})["costForChooseTasker"], ShouldEqual, 10000)
						} else if v["text"].(map[string]interface{})["en"] == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT {
							So(v["isOpenGoMarketDefault"].(bool), ShouldBeTrue)
							So(v["goMarketWithStore"], ShouldNotBeNil)
							So(v["goMarketWithStore"].(map[string]interface{})["status"], ShouldEqual, globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE)
							So(v["premiumOptions"], ShouldNotBeNil)
							So(v["premiumOptions"].(map[string]interface{})["status"], ShouldEqual, globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE)
						}
					}
				})
			})
		})
		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_HOME_CLEANING}, bson.M{"$unset": bson.M{"tip": 1, "priceSetting.costForChooseTasker": 1}})
		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT}, bson.M{"$unset": bson.M{"isOpenGoMarketDefault": 1, "goMarketWithStore": 1}})
	})
	t.Run("23", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2")
		ResetData()

		query := bson.M{
			"$or": []bson.M{
				{"status": globalConstant.SERVICE_STATUS_ACTIVE},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isTesting": true},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
			},
			"onlyShowTasker": bson.M{"$ne": true},
		}
		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateService(query, bson.M{"$set": bson.M{
			"tetBookingDates": bson.M{
				"fromDate": now.AddDate(0, 0, -1),
				"toDate":   now.AddDate(0, 0, 1),
				"eventDescription": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"en": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"ko": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
				},
				"title": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "Title Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"subTitle": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "SubTitle Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"banner": bson.M{
					"vi": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"en": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"ko": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"th": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
				},
				"bookTaskTime": bson.M{},
				"note": bson.M{
					"title": bson.M{
						"vi": "Dịch vụ Dọn Dẹp Nhà dịp Tết 2020",
						"en": "House Cleaning Service on Tet Holiday 2020",
						"ko": "2020년 설 기간 가사도우미 서비스 안내",
					},
					"content": bson.A{
						bson.M{
							"vi": "- Hỗ trợ đặt lịch trước cho dịp Tết ngay từ bây giờ.",
							"en": "- Advance booking for Tet Holiday is available now.",
							"ko": "- 설 기간 미리 예약 지원.",
						},
						bson.M{
							"vi": "- Có thể đặt lịch trước từ 10/01 – 10/02/2020.",
							"en": "- Support for advance booking service on January 10th to February 10th 2020.",
							"ko": "- 2020/01/10일 부터 2020/02/10 까지 가사도우미 서비스 미리 예약 가능.",
						},
						bson.M{
							"vi": "- Giá dịch vụ tăng từ ngày 23 Tết đến mùng 10 Tết (17/01 – 03/02/2020), dao động từ 30% - 120%.",
							"en": "- The price will increase from the 23rd to 10th Tet’s Holiday (from January 17th to February 3rd, 2020), ranging from 30% to 120%.",
							"ko": "- 2020/1/17일 부터 2020/2/3일 까지 청소 서비스 가격 30~120% 상승.",
						},
					},
					"detailNote": bson.M{
						"vi": "Lưu ý: Dịch vụ Dọn Dẹp Nhà chỉ bao gồm các công việc nhà cơ bản. Nếu bạn có nhu cầu dọn dẹp kỹ, tổng vệ sinh nhà cửa, vui lòng chọn dịch vụ Tổng Vệ Sinh.",
						"en": "Note: House Cleaning services only includes basic household chores. If you need a thorough cleaning, overall house cleaning, please choose Deep Cleaning service.",
						"ko": "참고: 가사도우미 서비스는 기본적인 가사일에 한하여 진행 가능합니다. 집안을 구석구석 꼼꼼하게 청소 진행하는 서비스를 원하실 경우 ’대청소’항목으로 예약 부탁드립니다.",
					},
				},
				"cusTomLunarDay": bson.A{
					bson.M{
						"calendarDay": "10/01",
						"lunarDay":    "16/12",
					},
					bson.M{
						"calendarDay": "11/01",
						"lunarDay":    "17/12",
					},
				},
			},
		}})

		UpdateAskerSetting(local.ISO_CODE,
			bson.M{
				"$set": bson.M{
					"supports": map[string]interface{}{
						"email":     "<EMAIL>",
						"hotline":   "1900636736",
						"blog":      "https://blog.btaskee.com/",
						"twitter":   "https://twitter.com/btaskee",
						"facebook":  "https://www.facebook.com/btaskee",
						"instagram": "https://www.instagram.com/btaskee",
						"youtube":   "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ",
						"zalo":      "https://zalo.me/887359237910439961",
					},
					"helpCenter": map[string]interface{}{
						"benefit": map[string]interface{}{
							"vi": "https://www.btaskee.com/kinh-nghiem-hay/loi-ich-khi-su-dung-btaskee/",
							"en": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
							"ko": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
							"th": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
							"id": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
						},
						"question": map[string]interface{}{
							"vi": "https://www.btaskee.com/cau-hoi-thuong-gap/",
							"en": "https://www.btaskee.com/en/faqs/",
							"ko": "https://www.btaskee.com/en/faqs/",
							"th": "https://www.btaskee.com/en/faqs/",
							"id": "https://www.btaskee.com/en/faqs/",
						},
						"term": map[string]interface{}{
							"vi": "https://www.btaskee.com/dieu-khoan-su-dung/",
							"en": "https://www.btaskee.com/en/terms/",
							"ko": "https://www.btaskee.com/en/terms/",
							"th": "https://www.btaskee.com/en/terms/",
							"id": "https://www.btaskee.com/en/terms/",
						},
						"policy": map[string]interface{}{
							"vi": "https://www.btaskee.com/chinh-sach-bao-mat/",
							"en": "https://www.btaskee.com/en/privacy/",
							"ko": "https://www.btaskee.com/en/privacy/",
							"th": "https://www.btaskee.com/en/privacy/",
							"id": "https://www.btaskee.com/en/privacy/",
						},
					},
				},
			},
		)
		categories := []map[string]interface{}{
			{
				"name":   "Entertainment",
				"status": "ACTIVE",
				"weight": 0,
			},
			{
				"name":   "Services",
				"status": "ACTIVE",
				"weight": 1,
			},
			{
				"name":   "Food & Beverage",
				"status": "INACTIVE",
				"weight": 2,
			},
		}
		UpdateSettings(bson.M{"$set": bson.M{"giftSetting.category": categories}})

		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					location, _ := respResult["location"].(map[string]interface{})
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						So(v["tetBookingDates"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["cusTomLunarDay"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["eventDescription"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["title"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["title"].(map[string]interface{})["en"], ShouldEqual, "Title Đặt lịch trước tết Canh Tý 2020")
						So(v["tetBookingDates"].(map[string]interface{})["subTitle"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["subTitle"].(map[string]interface{})["en"], ShouldEqual, "SubTitle Đặt lịch trước tết Canh Tý 2020")
						So(v["tetBookingDates"].(map[string]interface{})["note"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["banner"], ShouldNotBeNil)
						So(v["tetBookingDates"].(map[string]interface{})["banner"].(map[string]interface{})["en"], ShouldEqual, "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o")
					}
					So(respResult["googleMapWebServiceKey"], ShouldNotBeNil)
					So(respResult["subscriptionSetting"], ShouldNotBeNil)
					So(respResult["settingRating"], ShouldNotBeNil)
					So(respResult["settingSystem"], ShouldNotBeNil)

					b, _ := json.Marshal(respResult["settingSystem"])
					settingSystem := make(map[string]interface{})
					json.Unmarshal(b, &settingSystem)

					b, _ = json.Marshal(settingSystem["giftSetting"])
					giftSetting := make(map[string][]map[string]interface{})
					json.Unmarshal(b, &giftSetting)

					So(len(giftSetting["category"]), ShouldEqual, 2)
					for i, v := range giftSetting["category"] {
						if i == 0 {
							So(v["name"], ShouldEqual, "Entertainment")
						} else if i == 1 {
							So(v["name"], ShouldEqual, "Services")
						}
					}

					So(respResult["cardPaymentConfig"], ShouldNotBeNil)
					So(respResult["currency"], ShouldNotBeNil)
					So(location, ShouldNotBeNil)
					So(location["isoCode"], ShouldNotBeNil)
					So(respResult["AWS3Config"], ShouldNotBeNil)
					So(respResult["isHaveEventConfig"], ShouldBeFalse)

					b, _ = json.Marshal(respResult["askerSetting"])
					askerSetting := make(map[string]interface{})
					json.Unmarshal(b, &askerSetting)
					So(askerSetting["supports"], ShouldResemble, map[string]interface{}{
						"email":     "<EMAIL>",
						"hotline":   "1900636736",
						"blog":      "https://blog.btaskee.com/",
						"twitter":   "https://twitter.com/btaskee",
						"facebook":  "https://www.facebook.com/btaskee",
						"instagram": "https://www.instagram.com/btaskee",
						"youtube":   "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ",
						"zalo":      "https://zalo.me/887359237910439961",
					})
					So(askerSetting["helpCenter"], ShouldResemble, map[string]interface{}{
						"benefit": map[string]interface{}{
							"vi": "https://www.btaskee.com/kinh-nghiem-hay/loi-ich-khi-su-dung-btaskee/",
							"en": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
							"ko": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
							"th": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
							"id": "https://www.btaskee.com/en/for-you/enjoy-great-benefits-with-btaskee/",
						},
						"question": map[string]interface{}{
							"vi": "https://www.btaskee.com/cau-hoi-thuong-gap/",
							"en": "https://www.btaskee.com/en/faqs/",
							"ko": "https://www.btaskee.com/en/faqs/",
							"th": "https://www.btaskee.com/en/faqs/",
							"id": "https://www.btaskee.com/en/faqs/",
						},
						"term": map[string]interface{}{
							"vi": "https://www.btaskee.com/dieu-khoan-su-dung/",
							"en": "https://www.btaskee.com/en/terms/",
							"ko": "https://www.btaskee.com/en/terms/",
							"th": "https://www.btaskee.com/en/terms/",
							"id": "https://www.btaskee.com/en/terms/",
						},
						"policy": map[string]interface{}{
							"vi": "https://www.btaskee.com/chinh-sach-bao-mat/",
							"en": "https://www.btaskee.com/en/privacy/",
							"ko": "https://www.btaskee.com/en/privacy/",
							"th": "https://www.btaskee.com/en/privacy/",
							"id": "https://www.btaskee.com/en/privacy/",
						},
					})
				})
			})
		})

		UpdateService(query, bson.M{"$unset": bson.M{"tetBookingDates": 1}})
		UpdateSettings(bson.M{"$unset": bson.M{"giftSetting": 1}})
		UpdateAskerSetting(local.ISO_CODE, bson.M{"$unset": bson.M{"supports": 1, "helpCenter": 1}})
	})

	// Get tetBookingDate because of minVersion check
	t.Run("23.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2")
		ResetData()

		query := bson.M{
			"$or": []bson.M{
				{"status": globalConstant.SERVICE_STATUS_ACTIVE},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isTesting": true},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
			},
			"onlyShowTasker": bson.M{"$ne": true},
		}
		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateService(query, bson.M{"$set": bson.M{
			"tetBookingDates": bson.M{
				"fromDate":   now.AddDate(0, 0, -1),
				"toDate":     now.AddDate(0, 0, 1),
				"minVersion": "3.0.0",
				"eventDescription": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"en": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"ko": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
				},
				"title": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "Title Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"subTitle": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "SubTitle Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"banner": bson.M{
					"vi": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"en": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"ko": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"th": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
				},
				"bookTaskTime": bson.M{},
				"note": bson.M{
					"title": bson.M{
						"vi": "Dịch vụ Dọn Dẹp Nhà dịp Tết 2020",
						"en": "House Cleaning Service on Tet Holiday 2020",
						"ko": "2020년 설 기간 가사도우미 서비스 안내",
					},
					"content": bson.A{
						bson.M{
							"vi": "- Hỗ trợ đặt lịch trước cho dịp Tết ngay từ bây giờ.",
							"en": "- Advance booking for Tet Holiday is available now.",
							"ko": "- 설 기간 미리 예약 지원.",
						},
						bson.M{
							"vi": "- Có thể đặt lịch trước từ 10/01 – 10/02/2020.",
							"en": "- Support for advance booking service on January 10th to February 10th 2020.",
							"ko": "- 2020/01/10일 부터 2020/02/10 까지 가사도우미 서비스 미리 예약 가능.",
						},
						bson.M{
							"vi": "- Giá dịch vụ tăng từ ngày 23 Tết đến mùng 10 Tết (17/01 – 03/02/2020), dao động từ 30% - 120%.",
							"en": "- The price will increase from the 23rd to 10th Tet’s Holiday (from January 17th to February 3rd, 2020), ranging from 30% to 120%.",
							"ko": "- 2020/1/17일 부터 2020/2/3일 까지 청소 서비스 가격 30~120% 상승.",
						},
					},
					"detailNote": bson.M{
						"vi": "Lưu ý: Dịch vụ Dọn Dẹp Nhà chỉ bao gồm các công việc nhà cơ bản. Nếu bạn có nhu cầu dọn dẹp kỹ, tổng vệ sinh nhà cửa, vui lòng chọn dịch vụ Tổng Vệ Sinh.",
						"en": "Note: House Cleaning services only includes basic household chores. If you need a thorough cleaning, overall house cleaning, please choose Deep Cleaning service.",
						"ko": "참고: 가사도우미 서비스는 기본적인 가사일에 한하여 진행 가능합니다. 집안을 구석구석 꼼꼼하게 청소 진행하는 서비스를 원하실 경우 ’대청소’항목으로 예약 부탁드립니다.",
					},
				},
				"cusTomLunarDay": bson.A{
					bson.M{
						"calendarDay": "10/01",
						"lunarDay":    "16/12",
					},
					bson.M{
						"calendarDay": "11/01",
						"lunarDay":    "17/12",
					},
				},
			},
		}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						So(v["tetBookingDates"], ShouldNotBeNil)
					}
				})
			})
		})

		UpdateService(query, bson.M{"$unset": bson.M{"tetBookingDates": 1}})
	})

	// Not get tetBookingDate because of minVersion check
	t.Run("23.2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2")
		ResetData()

		query := bson.M{
			"$or": []bson.M{
				{"status": globalConstant.SERVICE_STATUS_ACTIVE},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isTesting": true},
				{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
			},
			"onlyShowTasker": bson.M{"$ne": true},
		}
		UpdateService(query, bson.M{"$set": bson.M{
			"tetBookingDates": bson.M{
				"minVersion": "3.0.0",
				"eventDescription": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"en": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
					"ko": "Đặt lịch trước tết Canh Tý 2020 chuẩn bị đón tết khang trang, nhà cửa sạch sẽ. Bạn có để đặt lịch trước 30 ngày kể từ bây giờ.",
				},
				"title": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "Title Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"subTitle": bson.M{
					"vi": "Đặt lịch trước tết Canh Tý 2020",
					"en": "SubTitle Đặt lịch trước tết Canh Tý 2020",
					"ko": "Đặt lịch trước tết Canh Tý 2020",
				},
				"banner": bson.M{
					"vi": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"en": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"ko": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
					"th": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/nKivnc2tMwyNH2F2o",
				},
				"bookTaskTime": bson.M{},
				"note": bson.M{
					"title": bson.M{
						"vi": "Dịch vụ Dọn Dẹp Nhà dịp Tết 2020",
						"en": "House Cleaning Service on Tet Holiday 2020",
						"ko": "2020년 설 기간 가사도우미 서비스 안내",
					},
					"content": bson.A{
						bson.M{
							"vi": "- Hỗ trợ đặt lịch trước cho dịp Tết ngay từ bây giờ.",
							"en": "- Advance booking for Tet Holiday is available now.",
							"ko": "- 설 기간 미리 예약 지원.",
						},
						bson.M{
							"vi": "- Có thể đặt lịch trước từ 10/01 – 10/02/2020.",
							"en": "- Support for advance booking service on January 10th to February 10th 2020.",
							"ko": "- 2020/01/10일 부터 2020/02/10 까지 가사도우미 서비스 미리 예약 가능.",
						},
						bson.M{
							"vi": "- Giá dịch vụ tăng từ ngày 23 Tết đến mùng 10 Tết (17/01 – 03/02/2020), dao động từ 30% - 120%.",
							"en": "- The price will increase from the 23rd to 10th Tet’s Holiday (from January 17th to February 3rd, 2020), ranging from 30% to 120%.",
							"ko": "- 2020/1/17일 부터 2020/2/3일 까지 청소 서비스 가격 30~120% 상승.",
						},
					},
					"detailNote": bson.M{
						"vi": "Lưu ý: Dịch vụ Dọn Dẹp Nhà chỉ bao gồm các công việc nhà cơ bản. Nếu bạn có nhu cầu dọn dẹp kỹ, tổng vệ sinh nhà cửa, vui lòng chọn dịch vụ Tổng Vệ Sinh.",
						"en": "Note: House Cleaning services only includes basic household chores. If you need a thorough cleaning, overall house cleaning, please choose Deep Cleaning service.",
						"ko": "참고: 가사도우미 서비스는 기본적인 가사일에 한하여 진행 가능합니다. 집안을 구석구석 꼼꼼하게 청소 진행하는 서비스를 원하실 경우 ’대청소’항목으로 예약 부탁드립니다.",
					},
				},
				"cusTomLunarDay": bson.A{
					bson.M{
						"calendarDay": "10/01",
						"lunarDay":    "16/12",
					},
					bson.M{
						"calendarDay": "11/01",
						"lunarDay":    "17/12",
					},
				},
			},
		}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "2.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						So(v["tetBookingDates"], ShouldBeNil)
					}
				})
			})
		})

		UpdateService(query, bson.M{"$unset": bson.M{"tetBookingDates": 1}})
	})

	t.Run("24", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-support-city"
		log.Println("==================================== GetSupportCity")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})

		body := map[string]interface{}{
			"isoCode": local.ISO_CODE,
		}

		UpdateSettingCountry(bson.M{"countryCode": "+84"},
			bson.M{"$set": bson.M{
				"isoCode": local.ISO_CODE,
				"city": []map[string]interface{}{
					{
						"district": []map[string]interface{}{
							{
								"key":  "Phra Nakhon|เขตพระนคร|พระนคร",
								"name": "Phra Nakhon",
							},
						},
						"status": "ACTIVE",
						"key":    "Bangkok|Krung Thep Maha Nakhon|Krung Thep|กรุงเทพมหานคร",
						"name":   "Bangkok",
					}, {
						"district": []map[string]interface{}{
							{
								"key":  "District 1|Quận 1",
								"name": "Quận 1",
							},
						},
						"status": "LOCKED",
						"key":    "Hồ Chí Minh|HCM|Sài Gòn",
						"name":   "Hồ Chí Minh",
					}, {
						"district": []map[string]interface{}{
							{
								"key":  "Hoàn Kiếm District|Quận Hoàn Kiếm|Hoan Kiem District",
								"name": "Quận Hoàn Kiếm",
							},
						},
						"status": "INACTIVE",
						"key":    "Hà Nội|HaNoi|Ha Noi",
						"name":   "Hà Nội",
					},
				},
			}})
		UpdateSubscriptionSetting(bson.M{"$set": bson.M{"renewBefore": 7}})
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					var respResultM []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultM)
					So(len(respResultM), ShouldEqual, 2)
					So(respResultM[0]["name"], ShouldEqual, "Bangkok")
					So(respResultM[0]["key"], ShouldEqual, "Bangkok|Krung Thep Maha Nakhon|Krung Thep|กรุงเทพมหานคร")
					So(respResultM[0]["district"].([]interface{})[0].(map[string]interface{})["name"], ShouldEqual, "Phra Nakhon")
					So(respResultM[0]["district"].([]interface{})[0].(map[string]interface{})["key"], ShouldEqual, "Phra Nakhon|เขตพระนคร|พระนคร")
					So(respResultM[0]["status"], ShouldEqual, "ACTIVE")

					So(respResultM[1]["name"], ShouldEqual, "Hồ Chí Minh")
					So(respResultM[1]["key"], ShouldEqual, "Hồ Chí Minh|HCM|Sài Gòn")
					So(respResultM[1]["district"].([]interface{})[0].(map[string]interface{})["name"], ShouldEqual, "Quận 1")
					So(respResultM[1]["district"].([]interface{})[0].(map[string]interface{})["key"], ShouldEqual, "District 1|Quận 1")
					So(respResultM[1]["status"], ShouldEqual, "LOCKED")
				})
			})
		})
		UpdateSettingCountry(bson.M{"countryCode": "+84"}, bson.M{"$unset": bson.M{"paymentMethods": 1}})
	})

	t.Run("25", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2")
		ResetData()

		UpdateService(bson.M{"text.en": globalConstant.SERVICE_NAME_GROCERY_ASSISTANT}, bson.M{"$set": bson.M{
			"estimatedAmountConfig": map[string]interface{}{
				"estimatedAmount": []float64{
					100,
					200,
					300,
				},
				"min": 100,
				"max": 500,
			},
		}})

		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						if v["text"].(map[string]interface{})["en"] == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT {
							estimatedAmountConfig := v["estimatedAmountConfig"].(map[string]interface{})
							So(len(estimatedAmountConfig["estimatedAmount"].([]interface{})), ShouldEqual, 3)
							for _, ea := range estimatedAmountConfig["estimatedAmount"].([]interface{}) {
								So(ea.(float64), ShouldBeIn, []float64{100, 200, 300})
							}
							So(estimatedAmountConfig["min"], ShouldEqual, 100)
							So(estimatedAmountConfig["max"], ShouldEqual, 500)
						}
					}
				})
			})
		})
	})

	t.Run("26", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2 new referral setting")
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateSettings(bson.M{"$set": bson.M{
			"referralValue": 50000,
			"referralSetting": map[string]interface{}{
				"type": "voucher",
				"voucher": map[string]interface{}{
					"image": map[string]interface{}{
						"vi": "http://...",
					},
					"value": 30000,
				},
			},
			"headerSetting": map[string]interface{}{
				"image": "http://...",
				"headerText": map[string]interface{}{
					"vi": "Chúc giáng sinh vui vẻ",
				},
				"images": []map[string]interface{}{
					{
						"date": map[string]interface{}{
							"from": now.AddDate(0, 0, -10),
							"to":   now.AddDate(0, 0, -1),
						},
						"image": map[string]interface{}{
							"vi": "http://link1",
							"en": "http://link1",
							"ko": "http://link1",
							"th": "http://link1",
						},
					},
					{
						"date": map[string]interface{}{
							"from": now.AddDate(0, 0, -1),
							"to":   now.AddDate(0, 0, 1),
						},
						"image": map[string]interface{}{
							"vi": "http://link2",
							"en": "http://link2",
							"ko": "http://link2",
							"th": "http://link2",
						},
					},
					{
						"date": map[string]interface{}{
							"from": now.AddDate(0, 0, 1),
							"to":   now.AddDate(0, 0, 10),
						},
						"image": map[string]interface{}{
							"vi": "http://link3",
							"en": "http://link3",
							"ko": "http://link3",
							"th": "http://link3",
						},
					},
				},
			},
			"campaignSetting": map[string]interface{}{
				"duration": 300,
				"autoPlay": true,
				"loop":     true,
			},
		}})

		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				UpdateSettings(bson.M{"$unset": bson.M{"headerSetting": 1}})

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["settingSystem"], ShouldNotBeNil)
					settingSystem := respResult["settingSystem"].(map[string]interface{})
					So(settingSystem["referralValue"], ShouldEqual, 30000)
					So(settingSystem["referralSetting"], ShouldNotBeNil)
					So(settingSystem["headerSetting"], ShouldNotBeNil)
					headerSettings := settingSystem["headerSetting"].(map[string]interface{})
					imageHeader := headerSettings["imageHeader"].(map[string]interface{})
					So(imageHeader["en"], ShouldEqual, "http://link2")
					So(settingSystem["campaignSetting"], ShouldNotBeNil)
					campaignSetting := settingSystem["campaignSetting"].(map[string]interface{})
					So(campaignSetting["duration"], ShouldEqual, 300)
					So(campaignSetting["autoPlay"], ShouldEqual, true)
					So(campaignSetting["loop"], ShouldEqual, true)
				})
			})
		})
	})
	// VN
	// Not get service status INACTIVE
	t.Run("30", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_INACTIVE}})
		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 23)
				})
			})
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}})
	})
	// Not get service status ACTIVE with isTesting = true
	t.Run("31", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE, "isTesting": true}})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 23)
				})
			})
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"isTesting": false}})
	})
	// Get service status INACTIVE with isTesting = true for userTester
	t.Run("32", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE, "isTesting": true}})
		UpdateSettings(bson.M{"$set": bson.M{"tester": []string{"0834567890"}}})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 24)
				})
			})
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"isTesting": false}})
		UpdateSettings(bson.M{"$unset": bson.M{"tester": 1}})
	})
	// Not get service status ACTIVE with isTesting = true
	t.Run("33", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE, "isTesting": true}})

		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 23)
				})
			})
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"isTesting": false}})
	})
	t.Run("34", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING}, bson.M{"$set": bson.M{
			"tip": map[string]interface{}{
				"tip": 10000,
				"requirements": []bson.M{
					{
						"type": 6,
						"text": map[string]interface{}{
							"vi": "Lau kính",
							"en": "Cleaning glasses",
							"ko": "Cleaning glasses",
							"th": "Cleaning glasses",
							"id": "Cleaning glasses",
						},
						"duration": 1,
						"icon":     "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/WE66bnf684258Q9Lr",
					}, {
						"type": 7,
						"text": map[string]interface{}{
							"vi": "Hút bụi thảm văn phòng",
							"en": "Vacuuming office carpets",
							"ko": "Vacuuming office carpets",
							"th": "Vacuuming office carpets",
							"id": "Vacuuming office carpets",
						},
						"costByDuration": []map[string]interface{}{
							{
								"duration": 2,
								"cost":     70000,
							},
							{
								"duration": 3,
								"cost":     85000,
							},
							{
								"duration": 4,
								"cost":     100000,
							},
						},
						"icon": "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/WE66bnf684258Q9Lr",
					},
				},
			},
		}})
		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["services"], ShouldNotBeNil)
					for _, v := range respResultArrayM["services"] {
						if v["text"].(map[string]interface{})["en"] == globalConstant.SERVICE_NAME_OFFICE_CLEANING {
							requirements := v["requirements"].([]interface{})
							So(len(requirements), ShouldEqual, 2)
							for _, requirement := range requirements {
								requirementMap := requirement.(map[string]interface{})
								So(requirementMap["type"].(float64), ShouldBeIn, []float64{6, 7})
							}
						}
						if v["text"].(map[string]interface{})["en"] == globalConstant.SERVICE_NAME_WASHING_MACHINE {
							var detailService *modelService.ServiceDetailService
							detailServiceData, _ := json.Marshal(v["detailService"])
							json.Unmarshal(detailServiceData, &detailService)
							So(detailService, ShouldNotBeNil)
							So(detailService.WashingMachine, ShouldNotBeNil)
							So(len(detailService.WashingMachine.City), ShouldEqual, 1)
							So(detailService.WashingMachine.City[0].Name, ShouldEqual, "Hồ Chí Minh")
							So(len(detailService.WashingMachine.City[0].Type), ShouldEqual, 2)
							So(detailService.WashingMachine.City[0].Type[0].Name, ShouldEqual, "TOP_LOADING")
							So(detailService.WashingMachine.City[0].Type[0].Text.En, ShouldEqual, "Top loading")
							So(len(detailService.WashingMachine.City[0].Type[0].Type), ShouldEqual, 2)
							So(detailService.WashingMachine.City[0].Type[0].Type[0].Name, ShouldEqual, "LV1")
							So(detailService.WashingMachine.City[0].Type[0].Type[0].Text.Vi, ShouldEqual, "Dưới 9kg")
							So(len(detailService.WashingMachine.City[0].Type[0].Type[0].Options), ShouldEqual, 1)
							So(detailService.WashingMachine.City[0].Type[0].Type[0].Options[0].Name, ShouldEqual, "REMOVE_TUB")
							So(detailService.WashingMachine.City[0].Type[0].Type[0].Options[0].Text.Vi, ShouldEqual, "Có tháo lồng giặt")
							So(len(detailService.WashingMachine.City[0].Type[0].Type[0].Prices), ShouldEqual, 2)
							So(detailService.WashingMachine.City[0].Type[0].Type[1].Name, ShouldEqual, "LV2")
							So(len(detailService.WashingMachine.City[0].Type[0].Type[1].Options), ShouldEqual, 1)
							So(len(detailService.WashingMachine.City[0].Type[0].Type[1].Prices), ShouldEqual, 2)

							So(detailService.WashingMachine.City[0].Type[1].Name, ShouldEqual, "FRONT_LOADING")
							So(len(detailService.WashingMachine.City[0].Type[1].Type), ShouldEqual, 2)
							So(detailService.WashingMachine.City[0].Type[1].Type[0].Name, ShouldEqual, "LV1")
							So(len(detailService.WashingMachine.City[0].Type[1].Type[0].Options), ShouldEqual, 1)
							So(len(detailService.WashingMachine.City[0].Type[1].Type[0].Prices), ShouldEqual, 2)
							So(detailService.WashingMachine.City[0].Type[1].Type[1].Name, ShouldEqual, "LV2")
							So(len(detailService.WashingMachine.City[0].Type[1].Type[1].Options), ShouldEqual, 1)
							So(len(detailService.WashingMachine.City[0].Type[1].Type[1].Prices), ShouldEqual, 2)
						}
					}
				})
			})
		})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING}, bson.M{"$unset": bson.M{"tip": 1}})
	})
	// Not get service not start
	t.Run("35", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"serviceBeginAt": currentTime.AddDate(0, 1, 0)}})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}, bson.M{"$set": bson.M{"serviceBeginAt": currentTime.AddDate(0, -1, 0)}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
			"userId":     "0834567890",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 23)
					for _, v := range respResultArrayM["services"] {
						So(v["name"].(string), ShouldNotEqual, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING)
					}
				})
			})
		})
		UpdateService(bson.M{"name": bson.M{"$in": []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING, globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}}}, bson.M{"$unset": bson.M{"serviceBeginAt": 1}})
	})
	t.Run("36", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get VN service for user")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"serviceBeginAt": currentTime.AddDate(0, 1, 0)}})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}, bson.M{"$set": bson.M{"serviceBeginAt": currentTime.AddDate(0, -1, 0)}})
		UpdateSettings(bson.M{"$set": bson.M{"tester": []string{"0834567890"}}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
			"userId":     "0834567890",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 24)
				})
			})
		})
		UpdateService(bson.M{"name": bson.M{"$in": []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING, globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}}}, bson.M{"$unset": bson.M{"serviceBeginAt": 1}})
		UpdateSettings(bson.M{"$unset": bson.M{"tester": 1}})
	})
	t.Run("37", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettingsWithoutLogin v2 get VN service for user")
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"serviceBeginAt": currentTime.AddDate(0, 1, 0)}})
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}, bson.M{"$set": bson.M{"serviceBeginAt": currentTime.AddDate(0, -1, 0)}})
		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResultArrayM["services"]), ShouldEqual, 23)
					for _, v := range respResultArrayM["services"] {
						So(v["name"].(string), ShouldNotEqual, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING)
					}
				})
			})
		})
		UpdateService(bson.M{"name": bson.M{"$in": []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING, globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}}}, bson.M{"$unset": bson.M{"serviceBeginAt": 1}})
	})

	// ================= GET PARTNER_MINIAPP_SETTINGS ===============
	// t.Run("27", func(t *testing.T) {
	// 	apiURL := "/api/v3/api-asker-vn/get-partner-mini-app-settings"
	// 	log.Println("==================================== GetPartnerMiniAppSettings")
	// 	ResetData()

	// 	partnerCode := "PARTNER_TEST"
	// 	language := globalConstant.LANG_TH
	// 	countryCode := globalConstant.COUNTRY_CODE_TH
	// 	isoCode := globalConstant.ISO_CODE_TH

	// 	homeCleaningService := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
	// 	ACService := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER}, bson.M{"_id": 1})
	// 	serviceIds := []string{homeCleaningService.XId, ACService.XId}
	// 	CreatePartnerMiniAppSettings([]map[string]interface{}{
	// 		{
	// 			"partnerCode": partnerCode,
	// 			"language":    language,
	// 			"countryCode": countryCode,
	// 			"isoCode":     isoCode,
	// 			"serviceIds":  serviceIds,
	// 			"name":        "Partner name",
	// 		},
	// 	})

	// 	settingCountryCity := []map[string]interface{}{
	// 		{
	// 			"name":   "Bangkok",
	// 			"key":    "Bangkok|Krung Thep Maha Nakhon|Krung Thep|กรุงเทพมหานคร",
	// 			"status": "ACTIVE",
	// 			"district": []map[string]interface{}{
	// 				{
	// 					"key":  "Phra Nakhon|เขตพระนคร|พระนคร",
	// 					"name": "Phra Nakhon",
	// 					"text": "พระนคร",
	// 				},
	// 			},
	// 		},
	// 	}
	// 	UpdateSettingCountry(bson.M{"isoCode": globalConstant.ISO_CODE_TH}, bson.M{"$set": bson.M{"city": settingCountryCity}})

	// 	body := map[string]interface{}{
	// 		"partnerCode": partnerCode,
	// 	}
	// 	reqBody, _ := json.Marshal(body)

	// 	Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
	// 		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
	// 		resp := httptest.NewRecorder()

	// 		Convey("When the request is handled by the Router", func() {
	// 			service.NewRouter().ServeHTTP(resp, req)

	// 			Convey("Then check the response to test Get All Settings v2", func() {
	// 				result := make(map[string]interface{})
	// 				bytes, _ := io.ReadAll(resp.Body)
	// 				json.Unmarshal(bytes, &result)
	// 				So(resp.Code, ShouldEqual, 200)
	// 				So(result["isoCode"], ShouldEqual, isoCode)
	// 				So(result["countryCode"], ShouldEqual, countryCode)
	// 				So(result["language"], ShouldEqual, language)
	// 				So(result["settingCountry"], ShouldNotBeNil)
	// 				So(result["services"], ShouldNotBeNil)

	// 				// check result.services
	// 				var services []*modelService.Service
	// 				servicesData, _ := json.Marshal(result["services"])
	// 				json.Unmarshal(servicesData, &services)

	// 				So(len(services), ShouldEqual, 2)
	// 				So(result["services"], ShouldNotBeNil)
	// 				for _, v := range services {
	// 					So(v.XId, ShouldBeIn, serviceIds)
	// 					So(v.Icon, ShouldNotBeEmpty)
	// 					if globalLib.IsHomeCleaningService(v.Text.En) {
	// 						So(v.Name, ShouldEqual, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING)
	// 						So(v.Detail, ShouldBeNil)
	// 						So(len(v.City), ShouldNotEqual, 0)
	// 					} else {
	// 						So(v.Name, ShouldEqual, globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER)
	// 						So(v.Detail, ShouldNotBeNil)
	// 						So(len(v.Detail.City), ShouldEqual, 1)
	// 						So(v.Detail.City[0].Name, ShouldEqual, "Hồ Chí Minh")
	// 						So(v.Detail.City[0].Type[0].Name, ShouldEqual, "Split")
	// 						So(v.Detail.City[0].Type[1].Name, ShouldEqual, "Portable")
	// 						So(v.Detail.City[0].Type[2].Name, ShouldEqual, "Ceilling")
	// 					}
	// 				}

	// 				var settingCountry *settingCountry.SettingCountry
	// 				settingCountryData, _ := json.Marshal(result["settingCountry"])
	// 				json.Unmarshal(settingCountryData, &settingCountry)
	// 				So(settingCountry, ShouldNotBeNil)
	// 				So(len(settingCountry.City), ShouldEqual, 1)
	// 				So(settingCountry.City[0].Name, ShouldEqual, "Bangkok")
	// 				So(settingCountry.City[0].Status, ShouldEqual, globalConstant.SERVICE_STATUS_ACTIVE)
	// 				So(settingCountry.City[0].Key, ShouldEqual, "Bangkok|Krung Thep Maha Nakhon|Krung Thep|กรุงเทพมหานคร")
	// 				So(len(settingCountry.City[0].District), ShouldEqual, 1)
	// 				So(settingCountry.City[0].District[0].Name, ShouldEqual, "Phra Nakhon")
	// 				So(settingCountry.City[0].District[0].Key, ShouldEqual, "Phra Nakhon|เขตพระนคร|พระนคร")
	// 			})
	// 		})
	// 	})
	// })

	// Get tasker setting avatar frame success
	t.Run("38", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== Get tasker setting avatar frame success")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		body := map[string]interface{}{
			"userId":     "0834567890",
			"appVersion": "3.0.0",
		}

		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateTaskerSettings(bson.M{"$set": bson.M{
			"avatarFrame": map[string]interface{}{
				"default": "xxx",
				"premium": "yyy",
				"from":    now.AddDate(0, 0, -1),
				"to":      now.AddDate(0, 0, 1),
			},
		}})
		reqBody, _ := json.Marshal(body)
		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["taskerSettings"], ShouldResemble, map[string]interface{}{
						"avatarFrame": map[string]interface{}{
							"default": "xxx",
							"premium": "yyy",
						},
					})
				})
			})
		})
	})

	// Get tasker setting avatar frame fail because of date
	t.Run("39", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== Get tasker setting avatar frame success")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
		})
		body := map[string]interface{}{
			"userId":     "0834567890",
			"appVersion": "3.0.0",
		}

		now := globalLib.GetCurrentTime(local.TimeZone)
		UpdateTaskerSettings(bson.M{"$set": bson.M{
			"avatarFrame": map[string]interface{}{
				"default": "xxx",
				"premium": "yyy",
				"from":    now.AddDate(0, 0, 1),
				"to":      now.AddDate(0, 0, 2),
			},
		}})
		reqBody, _ := json.Marshal(body)
		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["taskerSettings"], ShouldBeNil)
				})
			})
		})
	})

	t.Run("40", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"favouriteServiceByCountry": map[string]interface{}{
					local.ISO_CODE: []string{"pcZRQ6PqmjrAPe5gt", "3T8NhtMJo8mkARqLH", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d", "6ykggf82yuLtkT8GC"},
				},
			},
		})
		UpdateService(bson.M{}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}, "$unset": bson.M{"isTesting": 1}})
		UpdateService(
			bson.M{"_id": "6ykggf82yuLtkT8GC"},
			bson.M{"$set": bson.M{
				"shortText": bson.M{
					"vi": "Nấu ăn",
					"en": "Cooking",
					"ko": "Cooking",
					"th": "Cooking",
				}}},
		)
		defer UpdateService(bson.M{"name": "HOME_COOKING"}, bson.M{"$unset": bson.M{"shortText": 1}})
		CreateVNServiceGroup([]map[string]interface{}{
			{
				"_id":  "123",
				"name": "CLEANING",
				"text": bson.M{
					"en": "Cleaning",
					"vn": "Dọn dẹp",
				},
				"serviceIds": []string{"pcZRQ6PqmjrAPe5gt", "xTgw3s7tdpJa4JNJjOffice", "QGvwmm8Kp45rywZCw"},
			}, {
				"_id":  "234",
				"name": "MAINTENANCE",
				"text": bson.M{
					"en": "Maintenance",
					"vn": "Vệ sinh bảo dưỡng",
				},
				"serviceIds": []string{"3T8NhtMJo8mkARqLH", "74hdtGY2hD6f1gat9", "3T8NhtMJo8mkARqLH123"},
			}, {
				"_id":  "345",
				"name": "FAMILY",
				"text": bson.M{
					"en": "Family",
					"vn": "Dịch vụ gia đình",
				},
				"serviceIds": []string{"622430f1ff8736ae812233c2", "6ykggf82yuLtkT8GC", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d"},
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["serviceGroup"], ShouldNotBeNil)
					So(len(respResultArrayM["serviceGroup"]), ShouldEqual, 3)
					So(len(respResultArrayM["favouriteServices"]), ShouldEqual, 5)
					for i, s := range respResultArrayM["serviceGroup"] {
						So(s["_id"], ShouldNotBeNil)
						So(s["name"], ShouldNotBeNil)
						So(s["text"], ShouldNotBeNil)
						So(s["services"], ShouldNotBeNil)
						switch i {
						case 0:
							So(s["_id"], ShouldEqual, "123")
							So(s["name"], ShouldEqual, "CLEANING")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Cleaning")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"pcZRQ6PqmjrAPe5gt", "xTgw3s7tdpJa4JNJjOffice", "QGvwmm8Kp45rywZCw"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						case 1:
							So(s["_id"], ShouldEqual, "234")
							So(s["name"], ShouldEqual, "MAINTENANCE")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Maintenance")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"3T8NhtMJo8mkARqLH", "74hdtGY2hD6f1gat9", "3T8NhtMJo8mkARqLH123"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						case 2:
							So(s["_id"], ShouldEqual, "345")
							So(s["name"], ShouldEqual, "FAMILY")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Family")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"622430f1ff8736ae812233c2", "6ykggf82yuLtkT8GC", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
								if service["_id"] == "6ykggf82yuLtkT8GC" {
									So(service["text"].(map[string]interface{})["vi"].(string), ShouldEqual, "Nấu ăn")
								}
							}
						}
					}
					for _, s := range respResultArrayM["favouriteServices"] {
						So(s["_id"], ShouldBeIn, []string{"pcZRQ6PqmjrAPe5gt", "3T8NhtMJo8mkARqLH", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d", "6ykggf82yuLtkT8GC"})
						So(s["name"], ShouldNotBeNil)
						So(s["text"], ShouldNotBeNil)
						So(s["icon"], ShouldNotBeNil)
						if s["_id"] == "6ykggf82yuLtkT8GC" {
							So(s["text"].(map[string]interface{})["vi"].(string), ShouldEqual, "Nấu ăn")
						}
					}
				})
			})
		})
	})

	t.Run("40.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 with default favourite services")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		UpdateService(bson.M{}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}, "$unset": bson.M{"isTesting": 1}})

		CreateVNServiceGroup([]map[string]interface{}{
			{
				"_id":  "123",
				"name": "CLEANING",
				"text": bson.M{
					"en": "Cleaning",
					"vn": "Dọn dẹp",
				},
				"serviceIds": []string{"pcZRQ6PqmjrAPe5gt", "xTgw3s7tdpJa4JNJjOffice", "QGvwmm8Kp45rywZCw"},
				"weight":     1,
			}, {
				"_id":  "345",
				"name": "FAMILY",
				"text": bson.M{
					"en": "Family",
					"vn": "Dịch vụ gia đình",
				},
				"serviceIds": []string{"622430f1ff8736ae812233c2", "6ykggf82yuLtkT8GC", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d"},
				"weight":     3,
			}, {
				"_id":  "234",
				"name": "MAINTENANCE",
				"text": bson.M{
					"en": "Maintenance",
					"vn": "Vệ sinh bảo dưỡng",
				},
				"serviceIds": []string{"3T8NhtMJo8mkARqLH", "74hdtGY2hD6f1gat9", "3T8NhtMJo8mkARqLH123"},
				"weight":     2,
			},
		})
		var services []*modelService.Service
		globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"isServicePackage": bson.M{"$ne": true}}, bson.M{"_id": 1, "isSubscription": 1, "name": 1}, bson.M{"weight": 1}, &services)
		var listService []string
		for _, s := range services {
			if len(listService) == 8 {
				break
			}
			if s.IsSubscription && s.Name != globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION {
				continue
			}
			listService = append(listService, s.XId)
		}
		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["serviceGroup"], ShouldNotBeNil)
					So(len(respResultArrayM["serviceGroup"]), ShouldEqual, 3)
					So(len(respResultArrayM["favouriteServices"]), ShouldEqual, 8)
					for _, s := range respResultArrayM["favouriteServices"] {
						So(s["_id"], ShouldBeIn, listService)
					}
					for i, s := range respResultArrayM["serviceGroup"] {
						So(s["_id"], ShouldNotBeNil)
						So(s["name"], ShouldNotBeNil)
						So(s["text"], ShouldNotBeNil)
						So(s["services"], ShouldNotBeNil)
						switch i {
						case 0:
							So(s["_id"], ShouldEqual, "123")
							So(s["name"], ShouldEqual, "CLEANING")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Cleaning")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"pcZRQ6PqmjrAPe5gt", "xTgw3s7tdpJa4JNJjOffice", "QGvwmm8Kp45rywZCw"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						case 1:
							So(s["_id"], ShouldEqual, "234")
							So(s["name"], ShouldEqual, "MAINTENANCE")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Maintenance")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"3T8NhtMJo8mkARqLH", "74hdtGY2hD6f1gat9", "3T8NhtMJo8mkARqLH123"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						case 2:
							So(s["_id"], ShouldEqual, "345")
							So(s["name"], ShouldEqual, "FAMILY")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Family")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"622430f1ff8736ae812233c2", "6ykggf82yuLtkT8GC", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						}
					}
				})
			})
		})
	})
	t.Run("40.2", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2")
		ResetData()
		UpdateService(bson.M{}, bson.M{"$set": bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}, "$unset": bson.M{"isTesting": 1}})

		CreateVNServiceGroup([]map[string]interface{}{
			{
				"_id":  "123",
				"name": "CLEANING",
				"text": bson.M{
					"en": "Cleaning",
					"vn": "Dọn dẹp",
				},
				"serviceIds": []string{"pcZRQ6PqmjrAPe5gt", "xTgw3s7tdpJa4JNJjOffice", "QGvwmm8Kp45rywZCw"},
			}, {
				"_id":  "234",
				"name": "MAINTENANCE",
				"text": bson.M{
					"en": "Maintenance",
					"vn": "Vệ sinh bảo dưỡng",
				},
				"serviceIds": []string{"3T8NhtMJo8mkARqLH", "74hdtGY2hD6f1gat9", "3T8NhtMJo8mkARqLH123"},
			}, {
				"_id":  "345",
				"name": "FAMILY",
				"text": bson.M{
					"en": "Family",
					"vn": "Dịch vụ gia đình",
				},
				"serviceIds": []string{"622430f1ff8736ae812233c2", "6ykggf82yuLtkT8GC", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d"},
			},
		})

		body := map[string]interface{}{
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/et-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["serviceGroup"], ShouldNotBeNil)
					So(respResult["favouriteServices"], ShouldBeNil)
					So(len(respResultArrayM["serviceGroup"]), ShouldEqual, 3)
					for i, s := range respResultArrayM["serviceGroup"] {
						So(s["_id"], ShouldNotBeNil)
						So(s["name"], ShouldNotBeNil)
						So(s["text"], ShouldNotBeNil)
						So(s["services"], ShouldNotBeNil)
						switch i {
						case 0:
							So(s["_id"], ShouldEqual, "123")
							So(s["name"], ShouldEqual, "CLEANING")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Cleaning")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"pcZRQ6PqmjrAPe5gt", "xTgw3s7tdpJa4JNJjOffice", "QGvwmm8Kp45rywZCw"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						case 1:
							So(s["_id"], ShouldEqual, "234")
							So(s["name"], ShouldEqual, "MAINTENANCE")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Maintenance")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"3T8NhtMJo8mkARqLH", "74hdtGY2hD6f1gat9", "3T8NhtMJo8mkARqLH123"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						case 2:
							So(s["_id"], ShouldEqual, "345")
							So(s["name"], ShouldEqual, "FAMILY")
							So(s["text"].(map[string]interface{})["en"].(string), ShouldEqual, "Family")
							services := s["services"].([]interface{})
							for _, s := range services {
								service := s.(map[string]interface{})
								So(service["_id"], ShouldBeIn, []string{"622430f1ff8736ae812233c2", "6ykggf82yuLtkT8GC", "60fa4e169390ee2facacb87c", "60fa4e169390ee2facacb87d"})
								So(service["name"], ShouldNotBeNil)
								So(service["text"], ShouldNotBeNil)
								So(service["icon"], ShouldNotBeNil)
							}
						}
					}
				})
			})
		})
	})
	// get all setting vn home moving for moving within same building
	t.Run("42", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get all setting vn home moving for moving within same building")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}
		reqBody, _ := json.Marshal(body)

		UpdateService(bson.M{
			"name": globalConstant.SERVICE_KEY_NAME_HOME_MOVING,
		}, bson.M{
			"$unset": bson.M{
				"isTesting": 1,
			},
		})
		Convey("Given a HTTP request for /api/v2/api-service/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)

					for _, v := range respResultArrayM["services"] {
						if v["name"] == globalConstant.SERVICE_KEY_NAME_HOME_MOVING {
							serviceData, _ := json.Marshal(v)
							var service *modelService.Service
							json.Unmarshal(serviceData, &service)

							So(len(service.DetailService.HomeMoving.City), ShouldEqual, 1)
							So(service.DetailService.HomeMoving.City[0].HomeType[0].Name, ShouldEqual, "APARTMENT")
							So(service.DetailService.HomeMoving.City[0].HomeType[0].IsCanMoveInBuilding, ShouldEqual, true)
							So(service.DetailService.HomeMoving.City[0].HomeType[0].Type[0].PriceInBuildingByType, ShouldEqual, 0)
							So(service.DetailService.HomeMoving.City[0].HomeType[1].Name, ShouldEqual, "HOME")
							So(service.DetailService.HomeMoving.City[0].HomeType[1].IsCanMoveInBuilding, ShouldEqual, false)
						}
					}
					UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_MOVING}, bson.M{
						"$set": bson.M{
							"isTesting": true,
						},
					})
				})
			})
		})
	})

	// get all setting check community setting
	t.Run("43", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get all setting check community setting")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		CreateCommunitySetting([]map[string]interface{}{
			{
				"isTesting": true,
				"tester":    []string{"0834567890"},
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["communitySetting"], ShouldNotBeNil)
				})
			})
		})
	})

	// get all setting without-login check community setting
	t.Run("44", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings-without-login"
		log.Println("==================================== GetAllSettings v2 get all setting without-login check community setting")
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		CreateCommunitySetting([]map[string]interface{}{
			{
				"isTesting": true,
				"tester":    []string{"0834567890"},
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings-without-login", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get All Settings v2", func() {
					respResult := make(map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["communitySetting"], ShouldNotBeNil)
				})
			})
		})
	})

	// ========================== 43. event config ==========================
	t.Run("45", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-event-config"
		log.Println("==================================== get running event config")
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)

		CreateEventConfig([]map[string]interface{}{
			{ // not get because sort by createdAt -1
				"name":      "NOEL",
				"status":    globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 7),
				"createdAt": now.AddDate(0, 0, -1),
			},
			{ // not get because lower appVersion
				"name":       "SPECIAL",
				"status":     globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate":  now.AddDate(0, 0, -1),
				"endDate":    now.AddDate(0, 0, 7),
				"createdAt":  now.AddDate(0, 0, 1),
				"appVersion": "4.0.1",
			},
			{ // get this
				"name":       "TET",
				"status":     globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate":  now.AddDate(0, 0, -1),
				"endDate":    now.AddDate(0, 0, 7),
				"createdAt":  now,
				"appVersion": "4.0.0",
				"activeServices": []string{
					globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
					globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
					globalConstant.SERVICE_KEY_NAME_UPHOLSTERY,
				},
				"appConfig": map[string]interface{}{
					"button": "abc",
					"banner": "abc",
				},
				"paymentMethods": []string{
					"CASH",
					"MOMO",
				},
			},
		})

		body := map[string]interface{}{
			"appVersion": "4.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)

					So(respResult["name"], ShouldEqual, "TET")
					So(respResult["activeServices"], ShouldResemble, []any{
						globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
						globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
						globalConstant.SERVICE_KEY_NAME_UPHOLSTERY,
					})
					So(respResult["appConfig"], ShouldResemble, map[string]interface{}{
						"button": "abc",
						"banner": "abc",
					})
					So(respResult["paymentMethods"], ShouldResemble, []any{
						"CASH",
						"MOMO",
					})
				})
			})
		})
	})
	// get by id
	t.Run("43.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-event-config"
		log.Println("==================================== get running event config")
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)

		ids := CreateEventConfig([]map[string]interface{}{
			{ // not get because sort by createdAt -1
				"name":      "NOEL",
				"status":    globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate": now.AddDate(0, 0, -1),
				"endDate":   now.AddDate(0, 0, 7),
				"createdAt": now.AddDate(0, 0, -1),
			},
			{ // not get because lower appVersion
				"name":       "SPECIAL",
				"status":     globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate":  now.AddDate(0, 0, -1),
				"endDate":    now.AddDate(0, 0, 7),
				"createdAt":  now.AddDate(0, 0, 1),
				"appVersion": "4.0.1",
			},
			{ // get this
				"name":       "TET",
				"status":     globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
				"startDate":  now.AddDate(0, -1, -1),
				"endDate":    now.AddDate(0, -1, 7),
				"createdAt":  now,
				"appVersion": "4.0.0",
				"activeServices": []string{
					globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
					globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
					globalConstant.SERVICE_KEY_NAME_UPHOLSTERY,
				},
				"appConfig": map[string]interface{}{
					"button": "abc",
					"banner": "abc",
				},
				"paymentMethods": []string{
					"CASH",
					"MOMO",
				},
			},
		})

		body := map[string]interface{}{
			"appVersion": "4.0.0",
			"eventId":    ids[2],
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := ioutil.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)

					So(respResult["name"], ShouldEqual, "TET")
					So(respResult["activeServices"], ShouldResemble, []any{
						globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
						globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
						globalConstant.SERVICE_KEY_NAME_UPHOLSTERY,
					})
					So(respResult["appConfig"], ShouldResemble, map[string]interface{}{
						"button": "abc",
						"banner": "abc",
					})
					So(respResult["paymentMethods"], ShouldResemble, []any{
						"CASH",
						"MOMO",
					})
				})
			})
		})
	})
	t.Run("47", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 - Test Community Setting")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		// Create test data for community settings
		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateCommunitySetting([]map[string]interface{}{
			{
				"isTesting": true,
				"postThemes": []map[string]interface{}{
					{
						"_id":           "theme1",
						"themeImageUrl": "https://example.com/theme1.jpg",
						// No date range - should always be included
					},
					{
						"_id":           "theme2",
						"themeImageUrl": "https://example.com/theme2.jpg",
						"startDate":     now.AddDate(0, 0, -1),
						"endDate":       now.AddDate(0, 0, 1),
						// Current date is within range - should be included
					},
					{
						"_id":           "theme3",
						"themeImageUrl": "https://example.com/theme3.jpg",
						"startDate":     now.AddDate(0, 0, 2),
						"endDate":       now.AddDate(0, 0, 3),
						// Future date range - should not be included
					},
					{
						"_id":           "theme4",
						"themeImageUrl": "https://example.com/theme4.jpg",
						"startDate":     now.AddDate(0, 0, -3),
						"endDate":       now.AddDate(0, 0, -1),
						// Past date range - should not be included
					},
				},
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "3.0.0",
		}
		reqBody, _ := json.Marshal(body)

		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Community Setting", func() {
					respResult := make(map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResult)

					So(resp.Code, ShouldEqual, 200)
					So(respResult["communitySetting"], ShouldNotBeNil)

					// Parse community setting response
					b, _ := json.Marshal(respResult["communitySetting"])
					communitySetting := make(map[string]interface{})
					json.Unmarshal(b, &communitySetting)

					// Check isTesting field
					So(communitySetting["isTesting"], ShouldBeTrue)

					// Check postThemes
					postThemes, ok := communitySetting["postThemes"].([]interface{})
					So(ok, ShouldBeTrue)
					So(len(postThemes), ShouldEqual, 2) // Only theme1 and theme2 should be included

					// Verify theme1 (no date range)
					theme1 := postThemes[0].(map[string]interface{})
					So(theme1["_id"], ShouldEqual, "theme1")
					So(theme1["themeImageUrl"], ShouldEqual, "https://example.com/theme1.jpg")

					// Verify theme2 (current date within range)
					theme2 := postThemes[1].(map[string]interface{})
					So(theme2["_id"], ShouldEqual, "theme2")
					So(theme2["themeImageUrl"], ShouldEqual, "https://example.com/theme2.jpg")
				})
			})
		})
	})

	t.Run("48", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-all-settings"
		log.Println("==================================== GetAllSettings v2 get list service with service package beautyCare")
		ResetData()
		serviceBeauty := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE}, bson.M{"_id": 1})
		servicePackageIds := CreateServicePackage([]map[string]interface{}{
			{
				"icon": "https://img.upanh.tv/2025/04/24/icon-beauty-care.png",
				"text": map[string]interface{}{
					"vi": "Làm Đẹp",
					"en": "Beauty Care",
					"ko": "뷰티 케어",
					"th": "บริการเสริมสวย",
					"id": "Perawatan Kecantikan",
					"ms": "Penjagaan kecantikan",
				},
				"status":              "ACTIVE",
				"name":                "BEAUTY_CARE_PACKAGE",
				"group":               "ADVANCED_UTILITY_SERVICES",
				"isNewService":        true,
				"requireAskerVersion": "3.38.0",
				"introService": map[string]interface{}{
					"title": map[string]interface{}{
						"vi": "Làm Đẹp",
						"id": "Perawatan Kecantikan",
						"en": "Beauty Care",
						"th": "บริการเสริมสวย",
						"ko": "뷰티 케어",
						"ms": "Penjagaan kecantikan",
					},
				},
				"services": []map[string]interface{}{
					{
						"_id":   "HairServices",
						"image": "https://img.upanh.tv/2025/04/17/image08ad09f66ccf9dcc.png",
						"title": map[string]interface{}{
							"vi": "Làm tóc",
							"en": "Hair Services",
							"th": "บริการทำผม",
							"ko": "헤어 서비스",
							"id": "Layanan Rambut",
							"ms": "Perkhidmatan rambut",
						},
						"description": map[string]interface{}{
							"vi": "Chăm sóc mái tóc với gội đầu thư giãn, hoặc nhuộm dặm lại chân tóc đã mọc dài.",
							"en": "Haircare options like relaxing shampoo or root touch-up.",
							"ko": "마사지 샴푸 또는 머리카락 뿌리 염색과 같은 헤어 케어 옵션.",
							"th": "บริการดูแลเส้นผม เลือกสระผมเพื่อความผ่อนคลาย หรือเติมสีโคน ให้ผมดูสวยเป๊ะก่อนออกงาน",
							"id": "Pilihan perawatan rambut seperti shampoo dengan relaksasi atau sentuhan ulang pada akar rambut",
							"ms": "Pilihan penjagaan rambut seperti syampu santai atau sentuhan akar.",
						},
					},
				},
			},
		})

		CreateUser([]map[string]interface{}{
			{
				"phone":   "0834567890",
				"name":    "Asker 02",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
				"favouriteServiceByCountry": map[string]interface{}{
					"VN": []string{servicePackageIds[0]},
				}},
		})

		UpdateService(bson.M{"_id": serviceBeauty.GetXId()}, bson.M{"$set": bson.M{"isServicePackage": true}})

		CreateVNServiceGroup([]map[string]interface{}{
			{
				"name":              "ADVANCED_UTILITY_SERVICES",
				"servicePackageIds": []string{servicePackageIds[0]},
			},
		})

		body := map[string]interface{}{
			"userId":     "0834567890",
			"isoCode":    local.ISO_CODE,
			"appVersion": "4.0.0",
		}
		reqBody, _ := json.Marshal(body)
		Convey("Given a HTTP request for /api/v3/api-asker-vn/get-all-settings", t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()
			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				Convey("Then check the response to test Get All Settings v2", func() {
					respResultArrayM := make(map[string][]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)
					json.Unmarshal(bytes, &respResultArrayM)
					So(resp.Code, ShouldEqual, 200)
					services := respResultArrayM["services"]
					isHavePackgaeBeauty := false
					for _, s := range services {
						if cast.ToString(s["name"]) == "BEAUTY_CARE_PACKAGE" {
							isHavePackgaeBeauty = true
						}
					}
					So(isHavePackgaeBeauty, ShouldBeTrue)
					serviceGroup := respResultArrayM["serviceGroup"]
					for _, sg := range serviceGroup {
						listServiceGroup := make([]map[string]interface{}, 0)
						b, _ := json.Marshal(sg["services"])
						json.Unmarshal(b, &listServiceGroup)
						So(len(listServiceGroup), ShouldEqual, 1)
					}

					favService := respResultArrayM["favouriteServices"]
					So(len(favService), ShouldEqual, 1)
				})
			})
		})
	})
}
