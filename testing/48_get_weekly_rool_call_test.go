package testing

import (
	"bytes"
	"encoding/json"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func TestGetWeeklyRollCall(t *testing.T) {
	apiURL := "/api/v3/api-asker-vn/get-weekly-roll-call"
	// Validate
	t.Run("1", func(t *testing.T) {
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 400)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})
	})

	// User not found
	t.Run("2", func(t *testing.T) {
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "xx",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 404)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
		})
	})

	// get weekly roll call
	t.Run("3", func(t *testing.T) {
		log.Println("==================================== mark roll")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentDate := globalLib.GetCurrentTime(local.TimeZone)
		dayOfNoelMonth := time.Date(currentDate.Year(), lib.NOEL_MONTH, 15, 0, 0, 0, 0, local.TimeZone)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"startDate": currentDate.AddDate(0, 0, -7),
			"endDate":   currentDate.AddDate(0, 0, 7),
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "ROLL_CALL",
					"numberOfSpin": 1,
					"applyFor":     "BOTH",
				},
				{
					"action":       "ROLL_CALL_AT_NOEL",
					"numberOfSpin": 2,
					"applyFor":     "BOTH",
				},
			},
			"config": map[string]interface{}{
				"presentUnboxing": map[string]interface{}{
					"checkIn": map[string]interface{}{
						"titleImage": map[string]interface{}{
							"en": "https://i.imgur.com/mRwUL1Q.png",
							"id": "https://i.imgur.com/mRwUL1Q.png",
							"ko": "https://i.imgur.com/mRwUL1Q.png",
							"th": "https://i.imgur.com/mRwUL1Q.png",
							"vi": "https://i.imgur.com/mRwUL1Q.png",
						},
						"background":               "https://i.imgur.com/x4BzkVn.png",
						"backgroundNormalDayFail":  "https://i.imgur.com/pgB2ueE.png",
						"backgroundNormalDay":      "https://i.imgur.com/x5oSSvY.png",
						"backgroundSpecialDayFail": "https://i.imgur.com/Kpwm97s.png",
						"backgroundSpecialDay":     "https://i.imgur.com/3c4YLpR.png",
					},
				},
			},
			"isTesting":  false,
			"minVersion": "3.12.1",
		})
		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
				"accumulateSpinHistory": []map[string]interface{}{
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": dayOfNoelMonth.AddDate(0, 0, -1),
					},
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": dayOfNoelMonth.AddDate(0, 0, -2),
					},
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": dayOfNoelMonth.AddDate(0, 0, -3),
					},
				},
				"createdAt": dayOfNoelMonth.AddDate(0, 0, -7),
			},
		})
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			var respM []map[string]map[string]interface{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &respM)
			So(res.Code, ShouldEqual, 200)
			So(len(respM), ShouldBeGreaterThan, 0)

		})
	})

	// get weekly roll call but start date > current date
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== mark roll")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		currentDate := globalLib.GetCurrentTime(local.TimeZone)
		dayOfNoelMonth := time.Date(currentDate.Year(), lib.NOEL_MONTH, 15, 0, 0, 0, 0, local.TimeZone)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"startDate": time.Date(currentDate.Year()+1, 1, 1, 0, 0, 0, 0, local.TimeZone),
			"endDate":   time.Date(currentDate.Year()+1, 1, 31, 0, 0, 0, 0, local.TimeZone),
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "ROLL_CALL",
					"numberOfSpin": 1,
					"applyFor":     "BOTH",
				},
				{
					"action":       "ROLL_CALL_AT_NOEL",
					"numberOfSpin": 2,
					"applyFor":     "BOTH",
				},
			},
			"config": map[string]interface{}{
				"presentUnboxing": map[string]interface{}{
					"checkIn": map[string]interface{}{
						"titleImage": map[string]interface{}{
							"en": "https://i.imgur.com/mRwUL1Q.png",
							"id": "https://i.imgur.com/mRwUL1Q.png",
							"ko": "https://i.imgur.com/mRwUL1Q.png",
							"th": "https://i.imgur.com/mRwUL1Q.png",
							"vi": "https://i.imgur.com/mRwUL1Q.png",
						},
						"background":               "https://i.imgur.com/x4BzkVn.png",
						"backgroundNormalDayFail":  "https://i.imgur.com/pgB2ueE.png",
						"backgroundNormalDay":      "https://i.imgur.com/x5oSSvY.png",
						"backgroundSpecialDayFail": "https://i.imgur.com/Kpwm97s.png",
						"backgroundSpecialDay":     "https://i.imgur.com/3c4YLpR.png",
					},
				},
			},
			"isTesting":  false,
			"minVersion": "3.12.1",
		})
		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
				"accumulateSpinHistory": []map[string]interface{}{
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": dayOfNoelMonth.AddDate(0, 0, -1),
					},
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": dayOfNoelMonth.AddDate(0, 0, -2),
					},
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": dayOfNoelMonth.AddDate(0, 0, -3),
					},
				},
				"createdAt": dayOfNoelMonth.AddDate(0, 0, -7),
			},
		})
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			resp := map[string]map[string]interface{}{}
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &resp)

			So(res.Code, ShouldEqual, 500)
			So(resp["error"]["code"], ShouldEqual, lib.ERROR_GAME_CAMPAIGN_NOT_FOUND.ErrorCode)

		})
	})

	// get weekly roll call but start date > current date
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== mark roll")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		currentDate := globalLib.GetCurrentTime(local.TimeZone)
		var startAWeek time.Time
		week := currentDate.Day() / 7 //cal week by divide by 7
		if currentDate.Day()%7 == 0 {
			week-- //if currentDate is 7,14,21,28, week number decrease by 1
		}
		startAWeek = time.Date(currentDate.Year(), currentDate.Month(), 7*week+1, 0, 0, 0, 0, local.TimeZone)
		gameCampaignIds := CreateVNGameCampaign(map[string]interface{}{
			"startDate": currentDate.AddDate(0, 0, -7),
			"endDate":   currentDate.AddDate(0, 0, 7),
			"accumulatedSpinMissions": []map[string]interface{}{
				{
					"action":       "ROLL_CALL",
					"numberOfSpin": 1,
					"applyFor":     "BOTH",
				},
				{
					"action":       "ROLL_CALL_AT_NOEL",
					"numberOfSpin": 2,
					"applyFor":     "BOTH",
				},
			},
			"config": map[string]interface{}{
				"presentUnboxing": map[string]interface{}{
					"checkIn": map[string]interface{}{
						"titleImage": map[string]interface{}{
							"en": "https://i.imgur.com/mRwUL1Q.png",
							"id": "https://i.imgur.com/mRwUL1Q.png",
							"ko": "https://i.imgur.com/mRwUL1Q.png",
							"th": "https://i.imgur.com/mRwUL1Q.png",
							"vi": "https://i.imgur.com/mRwUL1Q.png",
						},
						"background":               "https://i.imgur.com/x4BzkVn.png",
						"backgroundNormalDayFail":  "https://i.imgur.com/pgB2ueE.png",
						"backgroundNormalDay":      "https://i.imgur.com/x5oSSvY.png",
						"backgroundSpecialDayFail": "https://i.imgur.com/Kpwm97s.png",
						"backgroundSpecialDay":     "https://i.imgur.com/3c4YLpR.png",
					},
				},
			},
			"isTesting":  false,
			"minVersion": "3.12.1",
		})
		CreateVNUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567890",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   10,
				"accumulateSpinHistory": []map[string]interface{}{
					{
						"action":    "ROLL_CALL",
						"spin":      1,
						"createdAt": time.Date(startAWeek.Year(), startAWeek.Month(), startAWeek.Day(), 1, 0, 0, 0, local.TimeZone),
					},
				},
				"createdAt": time.Date(startAWeek.Year(), startAWeek.Month(), startAWeek.Day(), 1, 0, 0, 0, local.TimeZone),
			},
		})
		Convey("Check the response if req userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
			}
			reqData, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqData))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			var respM []model.DailyRollCallResponse
			b, _ := ioutil.ReadAll(res.Body)
			json.Unmarshal(b, &respM)

			So(res.Code, ShouldEqual, 200)
			So(len(respM), ShouldBeGreaterThan, 0)
			So(respM[0].IsChecked, ShouldEqual, true)
		})
	})
}
