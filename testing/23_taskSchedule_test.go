/*
* @File: 23_taskSchedule_test.go
* @Description: Handler function, case test
* @CreatedAt: 29/10/2020
* @Author: vinhnt
* @UpdatedAt: 16/12/2020
* @UpdatedBy: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelTaskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSchedule(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiGetScheduleTasks := "/api/v3/api-asker-vn/get-schedule-tasks"
		log.Println("==================================== GetScheduleTasks")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetScheduleTasks), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":  "",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})

			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"userId":  "0834567890",
					"isoCode": "HH",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})

			Convey("Check request when data invalid", func() {
				body := map[string]interface{}{
					"userId":  2131231,
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		apiGetScheduleTasks := "/api/v3/api-asker-vn/get-schedule-tasks"
		log.Println("==================================== GetScheduleTasks")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create TaskSchedule
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"viewedTaskers": []interface{}{
					"0834567890",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
		})
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"createdAt":        "2020,11,03,15,30",
				"taskId":           taskIds[0],
				"scheduleDuration": 4,
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"createdAt":        "2020,11,02,15,20",
				"scheduleDuration": 4,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetScheduleTasks), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Schedule Tasks", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					for _, v := range respResult[0]["weeklyRepeater"].([]interface{}) {
						So(v.(float64), ShouldBeIn, []float64{2, 4, 6})
					}
					for _, v := range respResult {
						So(v["beginAt"], ShouldNotBeNil)
						So(v["scheduleTime"], ShouldNotBeNil)
						So(v["scheduleDuration"], ShouldEqual, 4)
					}
					So(respResult[0]["status"], ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
					So(respResult[0]["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult[0]["address"], ShouldEqual, "Quận 1, Hồ Chí Minh, Việt Nam")
					So(respResult[0]["createdAt"], ShouldNotBeNil)
					So(respResult[0]["task"], ShouldNotBeNil)
					So(respResultM[0]["task"]["_id"], ShouldEqual, taskIds[0])
					So(respResultM[0]["service"]["text"].(map[string]interface{})["en"], ShouldEqual, globalConstant.SERVICE_NAME_AIR_CONDITIONER)
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResult[1]["_id"], ShouldEqual, ids[1])
				})
			})
		})
	})
	t.Run("2.2", func(t *testing.T) {
		apiGetScheduleTasks := "/api/v3/api-asker-vn/get-schedule-tasks"
		log.Println("==================================== GetScheduleTasks")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
			}, {
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create TaskSchedule
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"viewedTaskers": []interface{}{
					"0834567890",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
		})
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":          "0834567890",
				"serviceName":         globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"serviceId":           "",
				"createdAt":           "2020,11,03,15,30",
				"scheduleDuration":    4,
				"forceAcceptTaskerId": "0834567890",
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"serviceId":        "",
				"createdAt":        "2020,11,02,15,20",
				"scheduleDuration": 4,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetScheduleTasks), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Schedule Tasks", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					for _, v := range respResult[0]["weeklyRepeater"].([]interface{}) {
						So(v.(float64), ShouldBeIn, []float64{2, 4, 6})
					}
					for _, v := range respResult {
						So(v["beginAt"], ShouldNotBeNil)
						So(v["scheduleTime"], ShouldNotBeNil)
						So(v["scheduleDuration"], ShouldEqual, 4)
					}
					So(respResult[0]["status"], ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
					So(respResult[0]["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult[0]["forceAcceptTasker"], ShouldNotBeNil)
					So(respResultM[0]["forceAcceptTasker"]["_id"], ShouldEqual, "0834567890")
					So(respResultM[0]["forceAcceptTasker"]["name"], ShouldEqual, "Tasker 01")
					So(respResultM[0]["forceAcceptTasker"]["isPremiumTasker"], ShouldEqual, true)
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResult[1]["_id"], ShouldEqual, ids[1])
				})
			})
		})
	})
	t.Run("2.3", func(t *testing.T) {
		apiGetScheduleTasks := "/api/v3/api-asker-vn/get-schedule-tasks"
		log.Println("==================================== GetScheduleTasks Child Care")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			}, {
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create TaskSchedule
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_CHILD_CARE,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"viewedTaskers": []interface{}{
					"0834567890",
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
				"detailChildCare": map[string]interface{}{
					"numberOfChildren": 1,
					"detailChildren": []map[string]interface{}{
						{
							"weight": 1,
							"age": map[string]interface{}{
								"type": 1,
								"text": map[string]interface{}{
									"ko": "24 tháng - 6 tuổi",
									"vi": "24 tháng - 6 tuổi",
									"en": "24 tháng - 6 tuổi",
								},
							},
						},
					},
				},
			},
		})
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_CHILD_CARE,
				"serviceId":        "",
				"createdAt":        "2020,11,03,15,30",
				"scheduleDuration": 4,
				"taskId":           taskIds[0],
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_CHILD_CARE,
				"serviceId":        "",
				"createdAt":        "2020,11,04,15,20",
				"status":           "INACTIVE",
				"scheduleDuration": 4,
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_CHILD_CARE,
				"serviceId":        "",
				"createdAt":        "2020,11,02,15,20",
				"scheduleDuration": 4,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetScheduleTasks), t, func() {
			body := map[string]interface{}{
				"userId":  "0834567890",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Schedule Tasks", func() {
					var respResult []map[string]interface{}
					var respResultM []map[string]map[string]interface{}
					var respResultMM []map[string]map[string]map[string]interface{}

					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					json.Unmarshal(bytes, &respResultMM)

					So(resp.Code, ShouldEqual, 200)
					for _, v := range respResult[0]["weeklyRepeater"].([]interface{}) {
						So(v.(float64), ShouldBeIn, []float64{2, 4, 6})
					}
					for _, v := range respResult {
						So(v["beginAt"], ShouldNotBeNil)
						So(v["scheduleTime"], ShouldNotBeNil)
						So(v["scheduleDuration"], ShouldEqual, 4)
					}
					So(respResult[0]["status"], ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
					So(respResult[0]["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResultM[0]["task"]["detailChildCare"], ShouldNotBeNil)
					So(respResult[1]["_id"], ShouldEqual, ids[2])
					So(respResult[2]["_id"], ShouldEqual, ids[1])
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		apiRemoveScheduleTask := "/api/v3/api-asker-vn/remove-task-schedule"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveScheduleTask), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":     "",
					"scheduleId": "xxxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when scheduleId blank", func() {
				body := map[string]interface{}{
					"userId":     "0834567890",
					"scheduleId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.Message)
			})

			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":     12312231,
					"scheduleId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiRemoveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		apiRemoveScheduleTask := "/api/v3/api-asker-vn/remove-task-schedule"
		log.Println("==================================== RemoveScheduleTask")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiRemoveScheduleTask), t, func() {
			body := map[string]interface{}{
				"userId":     "0834567890",
				"scheduleId": ids[0],
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiRemoveScheduleTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Remove Schedule Task", func() {
					So(resp.Code, ShouldEqual, 200)
				})

				Convey("Then check database Remove Schedule Task", func() {
					var result *modelTaskSchedule.TaskSchedule
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"status": 1}, &result)
					So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_CANCELED)
				})
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		apiActiveScheduleTask := "/api/v3/api-asker-vn/active-task-schedule"
		log.Println("==================================== ActiveScheduleTask")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiActiveScheduleTask), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiActiveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":     "",
					"scheduleId": "xxxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiActiveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when scheduleId blank", func() {
				body := map[string]interface{}{
					"userId":     "0834567890",
					"scheduleId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiActiveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.Message)
			})

			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":     1223131,
					"scheduleId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiActiveScheduleTask, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

		})
	})
	t.Run("6", func(t *testing.T) {
		apiActiveScheduleTask := "/api/v3/api-asker-vn/active-task-schedule"
		log.Println("==================================== ActiveScheduleTask")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s Case Active", apiActiveScheduleTask), t, func() {
			body := map[string]interface{}{
				"userId":     "0834567890",
				"scheduleId": ids[0],
				"isActive":   true,
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiActiveScheduleTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Active Schedule Task Case ACTIVE", func() {
					So(resp.Code, ShouldEqual, 200)
				})

				Convey("Then check database Active Schedule Task Case ACTIVE", func() {
					var result *modelTaskSchedule.TaskSchedule
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"status": 1}, &result)
					So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
				})
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		apiActiveScheduleTask := "/api/v3/api-asker-vn/active-task-schedule"
		log.Println("==================================== ActiveScheduleTask")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s Case INACTIVE", apiActiveScheduleTask), t, func() {
			body := map[string]interface{}{
				"userId":     "0834567890",
				"scheduleId": ids[0],
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiActiveScheduleTask, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Active Schedule Task Case INACTIVE", func() {
					So(resp.Code, ShouldEqual, 200)
				})

				Convey("Then check database Active Schedule Task Case INACTIVE", func() {
					var result *modelTaskSchedule.TaskSchedule
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"status": 1}, &result)
					So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_INACTIVE)
				})
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		apiUpdateTaskScheduleTime := "/api/v3/api-asker-vn/update-schedule-time"
		log.Println("==================================== UpdateTaskScheduleTime")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateTaskScheduleTime), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId":           "",
					"scheduleId":       "xxxxxx",
					"weekday":          []int32{3, 5},
					"scheduleTime":     "2020-11-03T12:33:08.135Z",
					"scheduleDuration": 4,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when scheduleId blank", func() {
				body := map[string]interface{}{
					"userId":           "0834567890",
					"scheduleId":       "",
					"weekday":          []int32{3, 5},
					"scheduleTime":     "2020-11-03T12:33:08.135Z",
					"scheduleDuration": 4,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.Message)
			})

			Convey("Check request when scheduleTime is nil", func() {
				body := map[string]interface{}{
					"userId":           "0834567890",
					"scheduleId":       "xxxxx",
					"weekday":          []int32{3, 5},
					"scheduleTime":     nil,
					"scheduleDuration": 4,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SCHEDULE_TIME_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SCHEDULE_TIME_REQUIRED.Message)
			})

			Convey("Check request when scheduleDuration is 0", func() {
				body := map[string]interface{}{
					"userId":           "0834567890",
					"scheduleId":       "xxxxx",
					"weekday":          []int32{3, 5},
					"scheduleTime":     "2020-11-03T12:33:08.135Z",
					"scheduleDuration": 0,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SCHEDULE_DURATION_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SCHEDULE_DURATION_REQUIRED.Message)
			})

			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":           123123123,
					"scheduleId":       123213213,
					"weekday":          []int32{3, 5},
					"scheduleTime":     "2020-11-03T12:33:08.135Z",
					"scheduleDuration": 0,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		apiUpdateTaskScheduleTime := "/api/v3/api-asker-vn/update-schedule-time"
		log.Println("==================================== UpdateTaskScheduleTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_HOME_COOKING,
				"status":           globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"scheduleDuration": 2,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUpdateTaskScheduleTime), t, func() {
			body := map[string]interface{}{
				"userId":           "0834567890",
				"scheduleId":       ids[0],
				"weekday":          []int32{3, 5, 7},
				"scheduleTime":     "2020-11-04T12:33:08.135Z",
				"scheduleDuration": 4,
			}

			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Schedule Task Time", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Update Schedule Task Time", func() {
				var result *modelTaskSchedule.TaskSchedule
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"weeklyRepeater": 1, "scheduleDuration": 1, "status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
				So(result.ScheduleDuration, ShouldEqual, 4)
				for _, v := range result.WeeklyRepeater {
					So(v, ShouldBeIn, []int32{3, 5, 7})
				}
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		apiUpdateTaskScheduleTime := "/api/v3/api-asker-vn/update-schedule-time"
		log.Println("==================================== UpdateTaskScheduleTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_HOME_COOKING,
				"status":           globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"scheduleDuration": 2,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case Service is %s", apiUpdateTaskScheduleTime, globalConstant.SERVICE_NAME_HOME_COOKING), t, func() {
			body := map[string]interface{}{
				"userId":           "0834567890",
				"scheduleId":       ids[1],
				"weekday":          []int32{3, 5},
				"scheduleTime":     "2020-11-03T12:33:08.135Z",
				"scheduleDuration": 4,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Schedule Task Time", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Update Schedule Task Time", func() {
				var result *modelTaskSchedule.TaskSchedule
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[1], bson.M{"weeklyRepeater": 1, "scheduleDuration": 1, "status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
				So(result.ScheduleDuration, ShouldEqual, 2)
				for _, v := range result.WeeklyRepeater {
					So(v, ShouldBeIn, []int32{3, 5})
				}
			})
		})

	})
	t.Run("11", func(t *testing.T) {
		apiUpdateTaskScheduleTime := "/api/v3/api-asker-vn/update-schedule-time"
		log.Println("==================================== UpdateTaskScheduleTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_HOME_COOKING,
				"status":           globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"scheduleDuration": 2,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case len weekday = 0", apiUpdateTaskScheduleTime), t, func() {
			body := map[string]interface{}{
				"userId":           "0834567890",
				"scheduleId":       ids[0],
				"weekday":          []int32{},
				"scheduleTime":     "2020-11-03T12:33:08.135Z",
				"scheduleDuration": 4,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Schedule Task Time", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Update Schedule Task Time", func() {
				var result *modelTaskSchedule.TaskSchedule
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"weeklyRepeater": 1, "scheduleDuration": 1, "status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_INACTIVE)
				So(result.ScheduleDuration, ShouldEqual, 4)
				So(len(result.WeeklyRepeater), ShouldEqual, 0)
			})
		})
	})
	t.Run("11.1", func(t *testing.T) {
		apiUpdateTaskScheduleTime := "/api/v3/api-asker-vn/update-schedule-time"
		log.Println("==================================== UpdateTaskScheduleTime")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case len weekday = 0", apiUpdateTaskScheduleTime), t, func() {
			body := map[string]interface{}{
				"userId":           "0834567890",
				"scheduleId":       "abc",
				"weekday":          []int32{},
				"scheduleTime":     "2020-11-03T12:33:08.135Z",
				"scheduleDuration": 4,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUpdateTaskScheduleTime, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Schedule Task Time", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_NOT_FOUND.Message)
			})
		})
	})
	t.Run("12", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/active-task-schedule-by-task-id"
		log.Println("==================================== ActiveTaskScheduleByTaskId")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when params nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
					"taskId": "xxxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when taskId blank", func() {
				body := map[string]interface{}{
					"userId": "0834567890",
					"taskId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.Message)
			})

			Convey("Check request when params invalid", func() {
				body := map[string]interface{}{
					"userId":     1223131,
					"scheduleId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

		})
	})
	t.Run("13", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/active-task-schedule-by-task-id"
		log.Println("==================================== ActiveTaskScheduleByTaskId")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
						"name":     "Kaliser",
						"avatar":   "/avatars/avatarDefault.png",
						"taskDone": 3,
					},
				},
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"askerId":     "0834567890",
				"taskId":      taskIds[0],
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_HOME_COOKING,
				"status":           globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"scheduleDuration": 2,
				"askerId":          "0834567890",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case len weekday = 0", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId": "0834567890",
				"taskId": taskIds[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Schedule Task Time", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Update Schedule Task Time", func() {
				var result *modelTaskSchedule.TaskSchedule
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_INACTIVE)
			})
		})
	})
	t.Run("14", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/active-task-schedule-by-task-id"
		log.Println("==================================== ActiveTaskScheduleByTaskId")

		ResetData()

		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		//Create Task
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test 01",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"createdAt":   "2020,11,02,15,15",
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
						"name":     "Kaliser",
						"avatar":   "/avatars/avatarDefault.png",
						"taskDone": 3,
					},
				},
			},
		})

		//Create TaskSchedule
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"status":      globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"askerId":     "0834567890",
				"taskId":      taskIds[0],
			}, {
				"askerPhone":       "0834567890",
				"serviceName":      globalConstant.SERVICE_NAME_HOME_COOKING,
				"status":           globalConstant.TASK_SCHEDULE_STATUS_CANCELED,
				"scheduleDuration": 2,
				"askerId":          "0834567890",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s Case len weekday = 0", apiUrl), t, func() {
			body := map[string]interface{}{
				"userId":   "0834567890",
				"taskId":   taskIds[0],
				"isActive": true,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("Then check the response to test Update Schedule Task Time", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
			})

			Convey("Then check database Update Schedule Task Time", func() {
				var result *modelTaskSchedule.TaskSchedule
				globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], ids[0], bson.M{"status": 1}, &result)
				So(result.Status, ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
			})
		})
	})
	// GetTaskScheduleDetail
	t.Run("15", func(t *testing.T) {
		log.Println("========================= Validate GetTaskScheduleDetail")
		apiURL := "/api/v3/api-asker-vn/get-task-schedule-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			Convey("Check the response if params invalid", func() {
				body := map[string]interface{}{
					"scheduleId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check the response if userId is empty", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
			Convey("Check the response if scheduleId is empty", func() {
				body := map[string]interface{}{
					"userId":     "123",
					"scheduleId": "",
					"isoCode":    "VN",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 400)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_SCHEDULE_ID_REQUIRED.Message)
			})
			Convey("Check the response if schedule not found", func() {
				body := map[string]interface{}{
					"userId":     "123",
					"scheduleId": "123",
					"isoCode":    "VN",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)
				b, _ := io.ReadAll(res.Body)
				result := map[string]map[string]interface{}{}
				json.Unmarshal(b, &result)
				So(res.Code, ShouldEqual, 404)
				So(result["error"]["code"], ShouldEqual, lib.ERROR_TASK_SCHEDULE_NOT_FOUND.ErrorCode)
				So(result["error"]["message"], ShouldEqual, lib.ERROR_TASK_SCHEDULE_NOT_FOUND.Message)
			})
		})
	})
	t.Run("16", func(t *testing.T) {
		apiGetScheduleTasks := "/api/v3/api-asker-vn/get-task-schedule-detail"
		log.Println("==================================== GetTaskScheduleDetail")
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567890",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"isPremiumTasker": true,
			}, {
				"phone": "0834567896",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		//Create TaskSchedule
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567896",
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567890",
					},
				},
				"viewedTaskers": []interface{}{
					"0834567890",
				},
				"collectionDate": time.Now().Add(4 * time.Hour),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"district": "Quận 1",
					"city":     "HCM",
				},
				"date": "2020,11,20,10,00",
			},
		})
		ids := CreateTaskSchedule([]map[string]interface{}{
			{
				"askerPhone":          "0834567890",
				"serviceName":         globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"serviceId":           "pcZRQ6PqmjrAPe5gt",
				"createdAt":           "2020,11,03,15,30",
				"scheduleDuration":    4,
				"forceAcceptTaskerId": "0834567890",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetScheduleTasks), t, func() {
			body := map[string]interface{}{
				"userId":     "0834567890",
				"isoCode":    local.ISO_CODE,
				"scheduleId": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetScheduleTasks, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Schedule Tasks", func() {
					var respResult map[string]interface{}
					var respResultM map[string]map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					for _, v := range respResult["weeklyRepeater"].([]interface{}) {
						So(v.(float64), ShouldBeIn, []float64{2, 4, 6})
					}
					So(respResult["beginAt"], ShouldNotBeNil)
					So(respResult["scheduleTime"], ShouldNotBeNil)
					So(respResult["scheduleDuration"], ShouldEqual, 4)
					So(respResult["status"], ShouldEqual, globalConstant.TASK_SCHEDULE_STATUS_ACTIVE)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResult["forceAcceptTasker"], ShouldBeNil)
					So(respResult["_id"], ShouldEqual, ids[0])
				})
			})
		})
	})
}
