/*
 * @File: 13_raixPushAppTokens_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 13/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelRaixPushAppTokens "gitlab.com/btaskee/go-services-model-v2/grpcmodel/raixPushAppTokens"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestRaixPushAppTokens(t *testing.T) {

	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validation InitToken")
		// InitToken
		apiUrl := "/api/v3/api-asker-vn/init-raix-push-token"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"appName": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when token nil", func() {
				body := map[string]interface{}{
					"appName": "bTaskeePartner",
					"metadata": map[string]interface{}{
						"uuid":         "8f571379b537941",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "2.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TOKEN_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TOKEN_REQUIRED.Message)
			})

			Convey("Check request when appName blank", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": ":aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					},
					"appName": "",
					"metadata": map[string]interface{}{
						"uuid":         "8f571379b537941",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "2.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_APP_NAME_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_APP_NAME_REQUIRED.Message)
			})

			Convey("Check request when metadata nil", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": ":aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					},
					"appName": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		log.Println("==================================== InitToken")
		// InitToken
		apiUrl := "/api/v3/api-asker-vn/init-raix-push-token"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":                 "0834567890",
				"name":                  "Tasker 01",
				"type":                  globalConstant.USER_TYPE_TASKER,
				"noReceiveNotification": false,
			},
		})
		CreateRaixPushAppTokens([]map[string]interface{}{
			{
				"metadata": map[string]interface{}{
					"uuid":         "8f571379b537941",
					"platform":     "android",
					"version":      "4.4.4",
					"model":        "MSM8226",
					"manufacturer": "samsung",
					"appVersion":   "2.10.2",
					"FCMToken":     "cAzRBctMsl8:APA91bHIeWVwtchgFMv9HFHIx",
				},
				"token": map[string]interface{}{
					"gcm": "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					"apn": "dzuOBZNHP5g:APA91bECW6u9rkkPLqHWK6DEuDALmOVdiAxvwLM",
				},
				"appName":     "bTaskeePartner",
				"userPhone":   "0834567890",
				"buildNumber": "101",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					},
					"userId":  "012321122",
					"appName": "bTaskeePartner",
					"metadata": map[string]interface{}{
						"uuid":         "123",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "3.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"buildNumber": "111",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
						"history": []map[string]interface{}{
							{
								"time": time.Date(2020, 2, 28, 9, 14, 9, 84, local.TimeZone).Format(time.RFC3339),
								// "time": globalLib.GetCurrentTimestamp(local.TimeZone),
								"location": map[string]interface{}{
									"lat": 10.7394393,
									"lng": 106.6962847,
								},
							},
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test InitToken", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database InitToken", func() {
						var raix *modelRaixPushAppTokens.RaixPushAppTokens
						globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, bson.M{"appName": "bTaskeePartner"}, bson.M{}, &raix)
						So(raix.AppName, ShouldEqual, "bTaskeePartner")
						So(raix.Token.Gcm, ShouldEqual, "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1")
						So(raix.Metadata.Uuid, ShouldEqual, "123")
						So(raix.Metadata.Platform, ShouldEqual, "android")
						So(raix.Metadata.Manufacturer, ShouldEqual, "samsung")
					})
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		log.Println("==================================== InitToken")
		// InitToken
		apiUrl := "/api/v3/api-asker-vn/init-raix-push-token"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":                 "0834567890",
				"name":                  "Tasker 01",
				"type":                  globalConstant.USER_TYPE_TASKER,
				"noReceiveNotification": false,
			}, {
				"phone":                 "123156464",
				"name":                  "Tasker 01",
				"type":                  globalConstant.USER_TYPE_TASKER,
				"noReceiveNotification": false,
			},
		})
		CreateRaixPushAppTokens([]map[string]interface{}{
			{
				"metadata": map[string]interface{}{
					"uuid":         "8f571379b537941",
					"platform":     "android",
					"version":      "4.4.4",
					"model":        "MSM8226",
					"manufacturer": "samsung",
					"appVersion":   "2.10.2",
					"FCMToken":     "cAzRBctMsl8:APA91bHIeWVwtchgFMv9HFHIx",
					"buildNumber":  "101",
				},
				"token": map[string]interface{}{
					"gcm": "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					"apn": "dzuOBZNHP5g:APA91bECW6u9rkkPLqHWK6DEuDALmOVdiAxvwLM",
				},
				"appName":   "bTaskeePartner",
				"userPhone": "0834567890",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
						"apn": "dzuOBZNHP5g:APA91bECW6u9rkkPLqHWK6DEuDALmOVdiAxvwLM",
					},
					"appName": "bTaskeePartner",
					"metadata": map[string]interface{}{
						"uuid":         "8f571379b537941",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "3.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"buildNumber": "111",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
						"history": []map[string]interface{}{
							{
								"time": "2020-02-28T09:14:09.847Z",
								"location": map[string]interface{}{
									"lat": 10.7394393,
									"lng": 106.6962847,
								},
							},
						},
					},
					"userId": "123156464",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test InitToken", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database InitToken", func() {
						var raix *modelRaixPushAppTokens.RaixPushAppTokens
						globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, bson.M{"appName": "bTaskeePartner"}, bson.M{}, &raix)
						So(raix.AppName, ShouldEqual, "bTaskeePartner")
						So(raix.Token.Gcm, ShouldEqual, "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1")
						So(raix.Metadata.Uuid, ShouldEqual, "8f571379b537941")
						So(raix.Metadata.Platform, ShouldEqual, "android")
						So(raix.Metadata.AppVersion, ShouldEqual, "3.10.2")
						So(raix.Metadata.BuildNumber, ShouldEqual, "111")
						So(raix.Metadata.Manufacturer, ShouldEqual, "samsung")
						user, _ := modelUser.GetOneById(local.ISO_CODE, "123156464", bson.M{})
						So(user.BuildNumber, ShouldEqual, "111")
						So(user.AppVersion, ShouldEqual, "3.10.2")
						So(user.Devices, ShouldNotBeNil)
					})
				})
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Validation UpdateHistoryToken")
		// InitToken
		apiUrl := "/api/v3/api-asker-vn/update-history-token"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"userId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when token nil", func() {
				body := map[string]interface{}{
					"userId": "123156464",
					"metadata": map[string]interface{}{
						"uuid":         "8f571379b537941",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "2.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
						"history": []map[string]interface{}{
							{
								"time": "2020-02-28T09:14:09.847Z",
								"location": map[string]interface{}{
									"lat": 10.7394393,
									"lng": 106.6962847,
								},
							},
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TOKEN_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TOKEN_REQUIRED.Message)
			})

			Convey("Check request when UserId blank", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": ":aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					},
					"userId": "",
					"metadata": map[string]interface{}{
						"uuid":         "8f571379b537941",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "2.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
						"history": []map[string]interface{}{
							{
								"time": "2020-02-28T09:14:09.847Z",
								"location": map[string]interface{}{
									"lat": 10.7394393,
									"lng": 106.6962847,
								},
							},
						},
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})

			Convey("Check request when metadata nil", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": ":aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					},
					"userId": "123156464",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_DATA_UPDATE_REQUIRED.Message)
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		log.Println("==================================== UpdateHistoryToken")
		// InitToken
		apiUrl := "/api/v3/api-asker-vn/update-history-token"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":                 "0834567890",
				"name":                  "Tasker 01",
				"type":                  globalConstant.USER_TYPE_TASKER,
				"noReceiveNotification": false,
			}, {
				"phone":                 "123156464",
				"name":                  "Tasker 01",
				"type":                  globalConstant.USER_TYPE_TASKER,
				"noReceiveNotification": false,
			},
		})
		CreateRaixPushAppTokens([]map[string]interface{}{
			{
				"metadata": map[string]interface{}{
					"uuid":         "8f571379b537941",
					"platform":     "android",
					"version":      "4.4.4",
					"model":        "MSM8226",
					"manufacturer": "samsung",
					"appVersion":   "2.10.2",
					"FCMToken":     "cAzRBctMsl8:APA91bHIeWVwtchgFMv9HFHIx",
					"buildNumber":  "101",
				},
				"token": map[string]interface{}{
					"gcm": "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
					"apn": "dzuOBZNHP5g:APA91bECW6u9rkkPLqHWK6DEuDALmOVdiAxvwLM",
				},
				"appName":   "bTaskeePartner",
				"userPhone": "0834567890",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Remove Notification", func() {
				body := map[string]interface{}{
					"token": map[string]interface{}{
						"gcm": "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1",
						"apn": "dzuOBZNHP5g:APA91bECW6u9rkkPLqHWK6DEuDALmOVdiAxvwLM",
					},
					"appName": "bTaskeePartner",
					"metadata": map[string]interface{}{
						"uuid":         "8f571379b537941",
						"platform":     "android",
						"version":      "4.4.4",
						"model":        "MSM8226",
						"manufacturer": "samsung",
						"appVersion":   "3.10.2",
						// "systemName":   "Android",
						// "deviceName":   "Unknown",
						// "timezone":     "Asia/Ho_Chi_Minh",
						"buildNumber": "111",
						"installedLocation": map[string]interface{}{
							"lat": 10.7394393,
							"lng": 106.6962847,
						},
						"history": []map[string]interface{}{
							{
								"time": "2020-02-28T09:14:09.847Z",
								"location": map[string]interface{}{
									"lat": 10.7394393,
									"lng": 106.6962847,
								},
							},
						},
					},
					"userId": "0834567890",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()

				Convey("When the request is handled by the Router", func() {
					service.NewRouter().ServeHTTP(resp, req)

					Convey("Then check the response to test UpdateHistoryToken", func() {
						So(resp.Code, ShouldEqual, 200)
					})

					Convey("Then check database UpdateHistoryToken", func() {
						var raix *modelRaixPushAppTokens.RaixPushAppTokens
						globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, bson.M{"appName": "bTaskeePartner"}, bson.M{}, &raix)
						So(raix.AppName, ShouldEqual, "bTaskeePartner")
						So(raix.Token.Gcm, ShouldEqual, "aMeImXrht7pf5C0im5Htbz1eC3RTTufd92k1")
						So(raix.Metadata.Uuid, ShouldEqual, "8f571379b537941")
						So(raix.Metadata.Platform, ShouldEqual, "android")
						So(raix.Metadata.AppVersion, ShouldEqual, "3.10.2")
						So(raix.Metadata.Manufacturer, ShouldEqual, "samsung")
						So(raix.Metadata.History, ShouldNotBeNil)
					})
				})
			})
		})
	})
}
