/*
 * @File: 35_refund_request_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 10/11/2021
 * @Author: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"go.mongodb.org/mongo-driver/bson"
)

func Test_Tasker(t *testing.T) {
	// Validate
	t.Run("1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-tasker-services"
		log.Println("==================================== Validate")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"taskerId": 111,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				})
			})

			Convey("When service handle request if tasker is empty", func() {
				body := map[string]interface{}{
					"taskerId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				})
			})

			Convey("When service handle request if userId is empty", func() {
				body := map[string]interface{}{
					"taskerId": "xxx",
					"userId":   "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 400)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				})
			})
		})
	})

	// TASKER IS BLOCKED
	t.Run("2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-tasker-services"
		log.Println("==================================== TASKER IS BLOCKED")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":  "0834567880",
				"name":   "Tasker 01",
				"type":   globalConstant.USER_TYPE_TASKER,
				"status": "LOCKED",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request if request params invalid", func() {
				body := map[string]interface{}{
					"taskerId": "0834567880",
					"userId":   "xxx",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string]map[string]interface{})
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)
					So(result["error"]["code"], ShouldEqual, lib.ERROR_TASKER_IS_BLOCKED.ErrorCode)
					So(result["error"]["errorText"].(map[string]interface{})["vi"], ShouldEqual, "Tasker này hiện tại đang tạm nghỉ. Bạn vui lòng đặt lịch Tasker yêu thích khác, hoặc có thể thao tác đặt lịch lại sau. Xin cảm ơn.")
				})
			})
		})
	})

	// Tasker in blacklist
	t.Run("2.1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-tasker-services"
		log.Println("==================================== Validate")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0834567880",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"blacklistAskers": []string{"0834567881"},
			},
		})

		services, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": bson.M{"$in": []string{globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}}}, bson.M{"_id": 1, "name": 1, "isSubscription": 1})

		serviceIds := []string{}
		for _, v := range services {
			serviceIds = append(serviceIds, v["_id"].(string))
		}

		globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": bson.M{"$in": serviceIds}}, bson.M{"$set": bson.M{"taskerList": []string{"0834567880"}}})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"userId":   "0834567881",
					"taskerId": "0834567880",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := map[string]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 500)

					So(result["error"]["code"], ShouldEqual, lib.ERROR_ASKER_IN_TASKER_BLACKLIST.ErrorCode)
					So(result["error"]["errorText"].(map[string]interface{})["vi"], ShouldEqual, "Rất tiếc, Tasker hiện chưa sẵn sàng nhận công việc. Cảm ơn bạn đã thông cảm.")
				})
			})
		})
	})

	// Success get tasker serviceIds
	t.Run("3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/get-tasker-services"
		log.Println("==================================== Validate")
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
		})

		services, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": bson.M{"$in": []string{globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING, globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}}}, bson.M{"_id": 1, "name": 1, "isSubscription": 1})

		serviceIds := []string{}
		var hCleaningServiceId string
		for _, v := range services {
			serviceIds = append(serviceIds, v["_id"].(string))
			isSubscription := cast.ToBool(v["isSubscription"])
			if v["name"].(string) == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING && !isSubscription {
				hCleaningServiceId = v["_id"].(string)
			}
		}

		globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": bson.M{"$in": serviceIds}}, bson.M{"$set": bson.M{"taskerList": []string{"0834567880"}}})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("When service handle request", func() {
				body := map[string]interface{}{
					"taskerId": "0834567880",
					"userId":   "0834567881",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				res := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					result := make(map[string][]string)
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					So(res.Code, ShouldEqual, 200)
					So(len(result["serviceIds"]), ShouldEqual, 1)
					So(result["serviceIds"][0], ShouldEqual, hCleaningServiceId)
				})
			})
		})
	})

}
