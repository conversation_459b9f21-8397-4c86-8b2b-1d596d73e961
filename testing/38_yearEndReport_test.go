/*
 * @File: 35_refund_request_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 10/11/2021
 * @Author: vinhnt
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerYearEndReport"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestYearEndReport(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Validate TaskerYearEndReport")
		// TaskerYearEndReport
		apiTaskerYearEndReport := "/api/v3/api-asker-vn/tasker-year-end-report"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiTaskerYearEndReport), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"taskerId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiTaskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASKER_ID_REQUIRED.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiTaskerYearEndReport), t, func() {
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"taskerId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiTaskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiTaskerYearEndReport), t, func() {
			Convey("Check request when user not found blank", func() {
				body := map[string]interface{}{
					"taskerId": "123",
					"isoCode":  local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiTaskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		// TaskerYearEndReport
		apiTaskerYearEndReport := "/api/v3/api-asker-vn/tasker-year-end-report"
		log.Println("==================================== TaskerYearEndReport")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -300)
		CreateUser([]map[string]interface{}{
			{
				"phone":         "0823456789",
				"name":          "Tasker 01",
				"type":          globalConstant.USER_TYPE_TASKER,
				"createdAt":     fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"firstVerifyAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -100),
			},
		})
		CreateTaskerYearEndReport(lastYear, local.ISO_CODE, []map[string]interface{}{
			{
				"_id":    "0823456789",
				"badges": []string{"PREMIUM", "ON_TIME"},
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskCost":     30600000,
				"totalTaskDuration": 306,
				"reviews":           []string{"Làm tốt. Lịch sự 4", "Làm tốt. Lịch sự 2", "Làm tốt. Lịch sự", "Làm tốt. Lịch sự 3", "Chị làm tốt, kỹ, nhanh nhẹn"},
				"avgRating":         4.445,
			},
		})
		body := map[string]interface{}{
			"taskerId": "0823456789",
			"isoCode":  local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiTaskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["register"], ShouldEqual, 100)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306)
				So(respResult["totalTaskCost"], ShouldEqual, 30600000)
				So(respResult["badges"], ShouldResemble, []interface{}{"PREMIUM", "ON_TIME"})
				So(respResult["reviews"], ShouldResemble, []interface{}{"Làm tốt. Lịch sự 4", "Làm tốt. Lịch sự 2", "Làm tốt. Lịch sự", "Làm tốt. Lịch sự 3", "Chị làm tốt, kỹ, nhanh nhẹn"})
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResult["year"], ShouldEqual, lastYear)
				So(respResult["isPremiumTasker"], ShouldBeFalse)
				So(respResult["avgRating"], ShouldEqual, 4.45)
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		// TaskerYearEndReport TH
		apiTaskerYearEndReport := "/api/v3/api-asker-vn/tasker-year-end-report"
		log.Println("==================================== TaskerYearEndReport")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -100)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0823456789",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"isoCode":   globalConstant.ISO_CODE_TH,
			},
		})
		CreateTaskerYearEndReport(lastYear, globalConstant.ISO_CODE_TH, []map[string]interface{}{
			{
				"_id":    "0823456789",
				"badges": []string{"PREMIUM", "ON_TIME"},
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskCost":     30600000,
				"totalTaskDuration": 306,
				"reviews":           []string{"Làm tốt. Lịch sự 4", "Làm tốt. Lịch sự 2", "Làm tốt. Lịch sự", "Làm tốt. Lịch sự 3", "Chị làm tốt, kỹ, nhanh nhẹn"},
				"isPremiumTasker":   true,
				"avgRating":         3.3333333,
			},
		})
		body := map[string]interface{}{
			"taskerId": "0823456789",
			"isoCode":  local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiTaskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["register"], ShouldEqual, 100)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306)
				So(respResult["totalTaskCost"], ShouldEqual, 30600000)
				So(respResult["badges"], ShouldResemble, []interface{}{"PREMIUM", "ON_TIME"})
				So(respResult["reviews"], ShouldResemble, []interface{}{"Làm tốt. Lịch sự 4", "Làm tốt. Lịch sự 2", "Làm tốt. Lịch sự", "Làm tốt. Lịch sự 3", "Chị làm tốt, kỹ, nhanh nhẹn"})
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResult["year"], ShouldEqual, lastYear)
				So(respResult["isPremiumTasker"], ShouldBeTrue)
				So(respResult["avgRating"], ShouldEqual, 3.33)
			})
		})
	})

	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Validate AskerYearEndReport")
		// TaskerYearEndReport
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when user not found blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		// AskerYearEndReport
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report"
		log.Println("==================================== AskerYearEndReport")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -100)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			},
		})
		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskDuration": 306,
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["register"], ShouldEqual, 100)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306)
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResult["year"], ShouldEqual, lastYear)
			})
		})
	})

	t.Run("6", func(t *testing.T) {
		// AskerYearEndReport TH
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report"
		log.Println("==================================== AskerYearEndReport TH")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -100)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			},
		})
		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskDuration": 306,
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["register"], ShouldEqual, 100)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306)
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResult["year"], ShouldEqual, lastYear)
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate AskerYearEndReport")
		// TaskerYearEndReport
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report-vn"

		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when userId blank", func() {
				body := map[string]interface{}{
					"userId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when user not found blank", func() {
				body := map[string]interface{}{
					"userId":  "123",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_NOT_FOUND.Message)
			})
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiAskerYearEndReport), t, func() {
			Convey("Check request when user report not found blank", func() {
				CreateUser([]map[string]interface{}{
					{
						"phone": "**********",
						"name":  "Asker 01",
						"type":  globalConstant.USER_TYPE_ASKER,
					},
				})
				body := map[string]interface{}{
					"userId":  "**********",
					"isoCode": local.ISO_CODE,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ASKER_REPORT_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ASKER_REPORT_NOT_FOUND.Message)
			})
		})
	})
	// have point report in report collection
	t.Run("7.1", func(t *testing.T) {
		// AskerYearEndReport
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report-vn"
		log.Println("==================================== AskerYearEndReport")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Member",
						"en": "Member",
						"ko": "Member",
						"th": "Member",
					},
					"rankName": "MEMBER",
				},
				"resetRankHistory": []map[string]interface{}{
					{
						"rankInfo": map[string]interface{}{
							"rankName": "PLATINUM",
							"text": map[string]interface{}{
								"vi": "Bạch kim",
								"en": "Platinum",
								"ko": "플래티넘",
								"th": "ระดับ Platinum",
							},
							"point": 1179,
						},
						"point":              679,
						"phase":              2,
						"isResetRank_phase1": true,
					},
					{
						"rankInfo": map[string]interface{}{
							"rankName": "MEMBER",
							"text": map[string]interface{}{
								"vi": "Member",
								"en": "Member",
								"ko": "Member",
								"th": "Member",
							},
							"point": 1179,
						},
						"point":              679,
						"phase":              2,
						"isResetRank_phase2": true,
					},
				},
			}, {
				"phone":     "0823456710",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Dọn dẹp nhà",
						"en": "Cleaning",
						"ko": "가사 도우미",
						"th": "Cleaning",
					},
					"rankName": "MEMBER",
				},
			},
		})
		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskDuration": 306,
				"listService":       []string{"SofaCleaning", "HOME_COOKING", "CLEANING", "HOUSE_KEEPING", "DEEP_CLEANING", "GO_MARKET", "LAUNDRY", "AIR_CONDITIONER_SERVICE", "ELDERLY_CARE", "PATIENT_CARE", "DISINFECTION_SERVICE", "CHILD_CARE", "OFFICE_CLEANING", "WASHING_MACHINE", "WATER_HEATER"},
				"listUnusedService": []string{"SofaCleaning", "HOME_COOKING", "HOUSE_KEEPING", "GO_MARKET", "LAUNDRY", "AIR_CONDITIONER_SERVICE", "ELDERLY_CARE", "PATIENT_CARE", "DISINFECTION_SERVICE", "CHILD_CARE", "OFFICE_CLEANING", "WASHING_MACHINE", "WATER_HEATER"},
				"lastYearRankName": map[string]interface{}{
					"rankName": "PLATINUM",
					"text": map[string]interface{}{
						"vi": "Bạch kim",
						"en": "Platinum",
						"ko": "플래티넘",
						"th": "ระดับ Platinum",
					},
				},
				"totalUsedbPoint":        90,
				"totalAccumulatedbPoint": 110,
				"numberOfReferred":       2,
				"listUsedService": []map[string]interface{}{
					{
						"serviceName":  "CLEANING",
						"numberOfUsed": 53,
					},
					{
						"serviceName":  "DEEP_CLEANING",
						"numberOfUsed": 1,
					},
				},
			},
		})
		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["bPointToMoney"], ShouldEqual, 450000)
				So(respResult["compareRank"], ShouldEqual, "DOWN")
				So(respResult["isCreatedInLastYear"], ShouldBeFalse)
				So(respResult["numberOfReferred"], ShouldEqual, 2)
				So(respResult["numberOfService"], ShouldEqual, 15)
				So(respResult["numberOfUnusedService"], ShouldEqual, 13)
				So(respResult["numberOfUsedService"], ShouldEqual, 2)
				So(respResult["totalAccumulatedbPoint"], ShouldEqual, 110)
				So(respResult["totalUsedbPoint"], ShouldEqual, 90)
				So(respResult["percentOfUsedbPoint"], ShouldEqual, 82)
				So(respResult["register"], ShouldEqual, 2)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306)
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResultM["lastRankInfo"]["rankName"], ShouldEqual, "PLATINUM")
				So(respResultM["rankInfo"]["rankName"], ShouldEqual, "MEMBER")
				So(respResult["year"], ShouldEqual, lastYear)
				listUsedService, _ := respResult["listUsedService"].([]interface{})
				So(listUsedService, ShouldResemble, []interface{}{"CLEANING", "DEEP_CLEANING"})
				listUnusedService, _ := respResult["listUnusedService"].([]interface{})
				for _, s := range listUnusedService {
					So(s, ShouldNotBeIn, []interface{}{"CLEANING", "DEEP_CLEANING"})
				}
			})
		})
	})
	// dont have point report in report collection
	t.Run("8", func(t *testing.T) {
		// AskerYearEndReport
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report-vn"
		log.Println("==================================== AskerYearEndReport VN")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
		globalDataAccess.DeleteAllByQuery(collectionName, []interface{}{})
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"rankName": "PLATINUM",
					"text": map[string]interface{}{
						"vi": "Bạch kim",
						"en": "Platinum",
						"ko": "플래티넘",
						"th": "ระดับ Platinum",
					},
				},
				"resetRankHistory": []map[string]interface{}{
					{
						"rankInfo": map[string]interface{}{
							"rankName": "GOLD",
							"text": map[string]interface{}{
								"vi": "Vàng",
								"en": "Gold",
								"ko": "금",
								"th": "Gold",
							},
						},
					},
					{
						"rankInfo": map[string]interface{}{
							"rankName": "PLATINUM",
							"text": map[string]interface{}{
								"vi": "Bạch kim",
								"en": "Platinum",
								"ko": "플래티넘",
								"th": "ระดับ Platinum",
							},
						},
					},
				},
			}, {
				"phone":     "0823456710",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Dọn dẹp nhà",
						"en": "Cleaning",
						"ko": "가사 도우미",
						"th": "Cleaning",
					},
					"rankName": "MEMBER",
				},
			},
		})
		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskDuration": 306,
				"listService":       []string{"SofaCleaning", "HOME_COOKING", "CLEANING", "HOUSE_KEEPING", "DEEP_CLEANING", "GO_MARKET", "LAUNDRY", "AIR_CONDITIONER_SERVICE", "ELDERLY_CARE", "PATIENT_CARE", "DISINFECTION_SERVICE", "CHILD_CARE", "OFFICE_CLEANING", "WASHING_MACHINE", "WATER_HEATER"},
				"listUnusedService": []string{"SofaCleaning", "HOME_COOKING", "HOUSE_KEEPING", "GO_MARKET", "AIR_CONDITIONER_SERVICE", "ELDERLY_CARE", "PATIENT_CARE", "DISINFECTION_SERVICE", "CHILD_CARE", "OFFICE_CLEANING", "WASHING_MACHINE", "WATER_HEATER"},
				"lastYearRankName": map[string]interface{}{
					"rankName": "PLATINUM",
					"text": map[string]interface{}{
						"vi": "Bạch kim",
						"en": "Platinum",
						"ko": "플래티넘",
						"th": "ระดับ Platinum",
					},
				},
				"numberOfReferred": 2,
				"listUsedService": []map[string]interface{}{
					{
						"serviceName":  "CLEANING",
						"numberOfUsed": 53,
					},
					{
						"serviceName":  "DEEP_CLEANING",
						"numberOfUsed": 1,
					},
					{
						"serviceName":  "LAUNDRY",
						"numberOfUsed": 3,
					},
				},
				"totalFavouriteTasker":      15,
				"totalVouchersReceived":     5,
				"totalVoucherValueReceived": 150000,
				"totalVouchersUsed":         3,
				"totalVoucherValueUsed":     90000,
			},
		})

		//Create Point Transaction
		createdAtPoint := globalLib.GetCurrentTime(local.TimeZone).AddDate(-1, 0, 0)
		CreatePointTransaction([]map[string]interface{}{
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd58",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "**********",
				"point":     350.0,
				"type":      "C",
				"createdAt": fmt.Sprintf("%d,11,02,15,40", createdAtPoint.Year()),
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd60",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp nhà vệ sinh",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "**********",
				"point":     50.0,
				"type":      "C",
				"createdAt": fmt.Sprintf("%d,11,02,15,40", createdAtPoint.Year()),
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd62",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "**********",
				"point":     20.0,
				"type":      "C",
				"createdAt": fmt.Sprintf("%d,11,02,15,40", createdAtPoint.Year()),
			},
			{
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd64",
					"name":   "DONE_TASK",
					"text":   "Giặt ủi",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "**********",
				"point":     500.0,
				"type":      "D",
				"createdAt": fmt.Sprintf("%d,11,02,15,40", createdAtPoint.Year()),
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd66",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp buồng phòng",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "**********",
				"point":     200.0,
				"type":      "D",
				"createdAt": fmt.Sprintf("%d,11,02,15,40", createdAtPoint.Year()),
			}, {
				"source": map[string]interface{}{
					"name":            "SYSTEM",
					"text":            "Đổi quà từ bTaskee",
					"incentiveId":     "prdQgLqLwKRgN8im50",
					"promotionCodeId": "DMB2JxEN8Njkk7DCC50",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "0823456710",
				"point":     26.0,
				"type":      "D",
				"createdAt": fmt.Sprintf("%d,11,02,15,40", createdAtPoint.Year()),
			}, {
				"source": map[string]interface{}{
					"taskId": "x5fa37ea3651af126ff4bdd60",
					"name":   "DONE_TASK",
					"text":   "Dọn dẹp nhà vệ sinh",
				},
				"isoCode":   local.ISO_CODE,
				"userId":    "**********",
				"point":     50.0,
				"type":      "C",
				"createdAt": "2020,11,02,15,40",
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["bPointToMoney"], ShouldEqual, 2100000)
				So(respResult["compareRank"], ShouldEqual, "UP")
				So(respResult["isCreatedInLastYear"], ShouldBeFalse)
				So(respResult["numberOfReferred"], ShouldEqual, 2)
				So(respResult["numberOfService"], ShouldEqual, 15)
				So(respResult["numberOfUnusedService"], ShouldEqual, 12)
				So(respResult["numberOfUsedService"], ShouldEqual, 3)
				So(respResult["totalAccumulatedbPoint"], ShouldEqual, 700)
				So(respResult["totalUsedbPoint"], ShouldEqual, 420)
				So(respResult["percentOfUsedbPoint"], ShouldEqual, 60)
				So(respResult["register"], ShouldEqual, 2)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306+3*3)
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResultM["lastRankInfo"]["rankName"], ShouldEqual, "GOLD")
				So(respResultM["rankInfo"]["rankName"], ShouldEqual, "PLATINUM")
				So(respResult["year"], ShouldEqual, lastYear)
				listUsedService, _ := respResult["listUsedService"].([]interface{})
				So(listUsedService, ShouldResemble, []interface{}{"CLEANING", "LAUNDRY", "DEEP_CLEANING"})
				listUnusedService, _ := respResult["listUnusedService"].([]interface{})
				for _, s := range listUnusedService {
					So(s, ShouldNotBeIn, []interface{}{"CLEANING", "LAUNDRY", "DEEP_CLEANING"})
				}
				So(respResult["totalFavouriteTasker"], ShouldEqual, 15)
				So(respResult["totalVouchersReceived"], ShouldEqual, 5)
				So(respResult["totalVoucherValueReceived"], ShouldEqual, 150000)
				So(respResult["totalVouchersUsed"], ShouldEqual, 3)
				So(respResult["totalVoucherValueUsed"], ShouldEqual, 90000)
				So(respResult["accumulatedbPointToMoney"], ShouldEqual, 3500000)
			})
		})
	})
	// year end report new user
	t.Run("9", func(t *testing.T) {
		// AskerYearEndReport
		apiAskerYearEndReport := "/api/v3/api-asker-vn/asker-year-end-report-vn"
		log.Println("==================================== AskerYearEndReport VN")

		ResetData()
		lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
		collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
		globalDataAccess.DeleteAllByQuery(collectionName, []interface{}{})
		//Create User
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"rankName": "MEMBER",
					"text": map[string]interface{}{
						"vi": "Member",
						"en": "Member",
						"ko": "Member",
						"th": "Member",
					},
				},
				"resetRankHistory": []map[string]interface{}{
					{
						"text": map[string]interface{}{
							"vi": "Member",
							"en": "Member",
							"ko": "Member",
							"th": "Member",
						},
					},
				},
			},
		})
		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
				"mostUsedService": map[string]interface{}{
					"vi": "Dọn dẹp nhà",
					"en": "Cleaning",
					"ko": "가사 도우미",
					"th": "Cleaning",
				},
				"totalTaskDone":     153,
				"totalTaskDuration": 306,
				"listService":       []string{"SofaCleaning", "HOME_COOKING", "CLEANING", "HOUSE_KEEPING", "DEEP_CLEANING", "GO_MARKET", "LAUNDRY", "AIR_CONDITIONER_SERVICE", "ELDERLY_CARE", "PATIENT_CARE", "DISINFECTION_SERVICE", "CHILD_CARE", "OFFICE_CLEANING", "WASHING_MACHINE", "WATER_HEATER"},
				"listUnusedService": []string{"SofaCleaning", "HOME_COOKING", "HOUSE_KEEPING", "GO_MARKET", "AIR_CONDITIONER_SERVICE", "ELDERLY_CARE", "PATIENT_CARE", "DISINFECTION_SERVICE", "CHILD_CARE", "OFFICE_CLEANING", "WASHING_MACHINE", "WATER_HEATER"},
				"lastYearRankName": map[string]interface{}{
					"rankName": "PLATINUM",
					"text": map[string]interface{}{
						"vi": "Bạch kim",
						"en": "Platinum",
						"ko": "플래티넘",
						"th": "ระดับ Platinum",
					},
				},

				"totalUsedbPoint":        90,
				"totalAccumulatedbPoint": 110,
				"numberOfReferred":       2,
				"listUsedService": []map[string]interface{}{
					{
						"serviceName":  "CLEANING",
						"numberOfUsed": 53,
					},
					{
						"serviceName":  "DEEP_CLEANING",
						"numberOfUsed": 1,
					},
					{
						"serviceName":  "LAUNDRY",
						"numberOfUsed": 3,
					},
				},
				"totalFavouriteTasker":      15,
				"totalVouchersReceived":     5,
				"totalVoucherValueReceived": 150000,
				"totalVouchersUsed":         3,
				"totalVoucherValueUsed":     90000,
			},
		})

		body := map[string]interface{}{
			"userId":  "**********",
			"isoCode": local.ISO_CODE,
		}

		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiAskerYearEndReport, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", t, func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test Get Up Comming Tasks", func() {
				var respResult map[string]interface{}
				var respResultM map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				json.Unmarshal(bytes, &respResultM)
				So(resp.Code, ShouldEqual, 200)
				So(respResult["bPointToMoney"], ShouldEqual, 450000)
				So(respResult["compareRank"], ShouldEqual, "NEW")
				So(respResult["isCreatedInLastYear"], ShouldBeFalse)
				So(respResult["numberOfReferred"], ShouldEqual, 2)
				So(respResult["numberOfService"], ShouldEqual, 15)
				So(respResult["numberOfUnusedService"], ShouldEqual, 12)
				So(respResult["numberOfUsedService"], ShouldEqual, 3)
				So(respResult["totalAccumulatedbPoint"], ShouldEqual, 110)
				So(respResult["totalUsedbPoint"], ShouldEqual, 90)
				So(respResult["percentOfUsedbPoint"], ShouldEqual, 82)
				So(respResult["register"], ShouldEqual, 2)
				So(respResult["totalTaskDone"], ShouldEqual, 153)
				So(respResult["totalTaskDuration"], ShouldEqual, 306+3*3)
				So(respResultM["mostUsedService"]["en"], ShouldEqual, "Cleaning")
				So(respResultM["lastRankInfo"]["rankName"], ShouldBeNil)
				So(respResultM["rankInfo"]["rankName"], ShouldEqual, "MEMBER")
				So(respResult["year"], ShouldEqual, lastYear)
				listUsedService, _ := respResult["listUsedService"].([]interface{})
				So(listUsedService, ShouldResemble, []interface{}{"CLEANING", "LAUNDRY", "DEEP_CLEANING"})
				listUnusedService, _ := respResult["listUnusedService"].([]interface{})
				for _, s := range listUnusedService {
					So(s, ShouldNotBeIn, []interface{}{"CLEANING", "LAUNDRY", "DEEP_CLEANING"})
				}
				So(respResult["totalFavouriteTasker"], ShouldEqual, 15)
				So(respResult["totalVouchersReceived"], ShouldEqual, 5)
				So(respResult["totalVoucherValueReceived"], ShouldEqual, 150000)
				So(respResult["totalVouchersUsed"], ShouldEqual, 3)
				So(respResult["totalVoucherValueUsed"], ShouldEqual, 90000)
				So(respResult["accumulatedbPointToMoney"], ShouldEqual, 550000)
			})
		})
	})

	// ========================= 10. Share year end report =====================

	// validate
	t.Run("10.1", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.Message)
			})
		})
	})
	// report not found
	t.Run("10.2", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		ResetData()

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 404)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ASKER_REPORT_NOT_FOUND.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ASKER_REPORT_NOT_FOUND.Message)
			})
		})
	})
	// already share
	t.Run("10.3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		ResetData()
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		lastYear := currentTime.Year() - 1
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"rankName": "MEMBER",
					"text": map[string]interface{}{
						"vi": "Member",
						"en": "Member",
						"ko": "Member",
						"th": "Member",
					},
				},
			},
		})

		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id":                         "**********",
				"isAccumulatedPointBySharing": true,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				user, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"point": 1, "rankInfo": 1})
				So(user.Point, ShouldEqual, 0)
			})
		})
	})
	// share success, +1 bPoint
	t.Run("10.4", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		ResetData()
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$addToSet": bson.M{"rankSetting": map[string]interface{}{
			"rankName": "MEMBER",
			"point":    200,
		}}})
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		lastYear := currentTime.Year() - 1
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"rankName": "MEMBER",
					"text": map[string]interface{}{
						"vi": "Member",
						"en": "Member",
						"ko": "Member",
						"th": "Member",
					},
				},
			},
		})

		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				user, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"point": 1, "rankInfo": 1})
				So(user.Point, ShouldEqual, 1)
				So(user.RankInfo.Point, ShouldEqual, 1)
			})
		})
	})
	// share success, +5 bPoint
	t.Run("10.5", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		ResetData()
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$addToSet": bson.M{"rankSetting": map[string]interface{}{
			"rankName": "MEMBER",
			"point":    200,
		}}})
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		lastYear := currentTime.Year() - 1
		collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"point":    20,
					"rankName": "SILVER",
					"text": map[string]interface{}{
						"vi": "Silver",
						"en": "Silver",
						"ko": "Silver",
						"th": "Silver",
					},
				},
				"point": 20,
			},
		})

		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id": "**********",
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["accumulatedPoint"], ShouldEqual, 5)
				user, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"point": 1, "rankInfo": 1})
				So(user.Point, ShouldEqual, 25)
				So(user.RankInfo.Point, ShouldEqual, 25)

				var askerYearEndReport *modelAskerYearEndReport.AskerYearEndReport
				globalDataAccess.GetOneById(collectionName, "**********", bson.M{}, &askerYearEndReport)
				So(askerYearEndReport.IsAccumulatedPointBySharing, ShouldBeTrue)
			})
		})
	})
	// share success, +25 bPoint
	t.Run("10.6", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		ResetData()
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$addToSet": bson.M{"rankSetting": map[string]interface{}{
			"rankName": "MEMBER",
			"point":    200,
		}}})
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		lastYear := currentTime.Year() - 1
		collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"point":    20,
					"rankName": "GOLD",
					"text": map[string]interface{}{
						"vi": "Gold",
						"en": "Gold",
						"ko": "Gold",
						"th": "Gold",
					},
				},
				"point": 20,
			},
		})

		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id":                    "**********",
				"totalAccumulatedbPoint": 4000,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["accumulatedPoint"], ShouldEqual, 25)
				user, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"point": 1, "rankInfo": 1})
				So(user.Point, ShouldEqual, 45)
				So(user.RankInfo.Point, ShouldEqual, 45)

				var askerYearEndReport *modelAskerYearEndReport.AskerYearEndReport
				globalDataAccess.GetOneById(collectionName, "**********", bson.M{}, &askerYearEndReport)
				So(askerYearEndReport.IsAccumulatedPointBySharing, ShouldBeTrue)
			})
		})
	})
	// share success, +50 bPoint
	t.Run("10.7", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/share-year-end-report"
		log.Println("==================================== ShareYearEndReport")

		ResetData()
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$addToSet": bson.M{"rankSetting": map[string]interface{}{
			"rankName": "MEMBER",
			"point":    200,
		}}})
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(-2, 0, 0)
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		lastYear := currentTime.Year() - 1
		collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
		CreateUser([]map[string]interface{}{
			{
				"phone":     "**********",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rankInfo": map[string]interface{}{
					"point":    20,
					"rankName": "PLATINUM",
					"text": map[string]interface{}{
						"vi": "Platinum",
						"en": "Platinum",
						"ko": "Platinum",
						"th": "Platinum",
					},
				},
				"point": 20,
			},
		})

		CreateAskerYearEndReport(lastYear, []map[string]interface{}{
			{
				"_id":                    "**********",
				"totalAccumulatedbPoint": 4000,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when taskerId blank", func() {
				body := map[string]interface{}{
					"userId": "**********",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				var respResult map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["accumulatedPoint"], ShouldEqual, 50)
				user, _ := modelUser.GetOneById(local.ISO_CODE, "**********", bson.M{"point": 1, "rankInfo": 1})
				So(user.Point, ShouldEqual, 70)
				So(user.RankInfo.Point, ShouldEqual, 70)

				var askerYearEndReport *modelAskerYearEndReport.AskerYearEndReport
				globalDataAccess.GetOneById(collectionName, "**********", bson.M{}, &askerYearEndReport)
				So(askerYearEndReport.IsAccumulatedPointBySharing, ShouldBeTrue)
			})
		})
	})
}
