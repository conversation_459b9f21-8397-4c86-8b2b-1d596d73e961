package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/shoppingCartGroceryAssistant"
	modelStoreGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/storeGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
)

func TestGroceryAssistant(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Get category")
		apiUrl := "/api/v3/api-asker-vn/get-category-grocery-assistant"

		// Check require filed
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"storeId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when storeId empty", func() {
				body := map[string]interface{}{
					"storeId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_STORE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_STORE_ID_REQUIRED.Message)
			})
		})

		// Get category - Default is first category
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"storeId": "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult map[string][]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(len(respResult["categories"]), ShouldEqual, 2)
				So(respResult["categories"][0]["_id"], ShouldEqual, "s1_cat1")
				So(respResult["categories"][1]["_id"], ShouldEqual, "s1_cat2")

				So(len(respResult["subCategories"]), ShouldEqual, 2)
				So(respResult["subCategories"][0]["_id"], ShouldEqual, "s1_cat1_sub1")
				So(respResult["subCategories"][1]["_id"], ShouldEqual, "s1_cat1_sub2")

				var products []map[string]interface{}
				bytes, _ = json.Marshal(respResult["subCategories"][0]["products"])
				json.Unmarshal(bytes, &products)
				So(len(products), ShouldEqual, 1)
				So(products[0]["_id"], ShouldEqual, "s1_cat1_sub1_p1")

				products = []map[string]interface{}{}
				bytes, _ = json.Marshal(respResult["subCategories"][1]["products"])
				json.Unmarshal(bytes, &products)
				So(len(products), ShouldEqual, 1)
				So(products[0]["_id"], ShouldEqual, "s1_cat1_sub1_p1")
			})
		})

		// Get category - By category id
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"storeId":    "s1",
					"categoryId": "s1_cat2",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult map[string][]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(len(respResult["categories"]), ShouldEqual, 2)
				So(respResult["categories"][0]["_id"], ShouldEqual, "s1_cat1")
				So(respResult["categories"][1]["_id"], ShouldEqual, "s1_cat2")

				So(len(respResult["subCategories"]), ShouldEqual, 1)
				So(respResult["subCategories"][0]["_id"], ShouldEqual, "s1_cat2_sub1")

				var products []map[string]interface{}
				bytes, _ = json.Marshal(respResult["subCategories"][0]["products"])
				json.Unmarshal(bytes, &products)
				So(len(products), ShouldEqual, 1)
				So(products[0]["_id"], ShouldEqual, "s1_cat2_sub1_p1")
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Find popular")
		apiUrl := "/api/v3/api-asker-vn/find-popular-grocery-assistant"

		globalDataAccess.UpdateOneById(
			globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
			"s1",
			bson.M{"$set": bson.M{
				"categories.0.subCategories.0.numberOfFindPopular": 2,
				"categories.0.subCategories.1.numberOfFindPopular": 3,
			}},
		)

		// Check require filed
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check request when PARSE_API_PARAMS ERROR", func() {
				body := map[string]interface{}{
					"storeId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when storeId empty", func() {
				body := map[string]interface{}{
					"storeId": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)
				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_STORE_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_STORE_ID_REQUIRED.Message)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"storeId": "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(len(respResult), ShouldEqual, 2)
				So(respResult[0]["_id"], ShouldEqual, "s1_cat1_sub2")
				So(respResult[1]["_id"], ShouldEqual, "s1_cat1_sub1")
			})
		})

		globalDataAccess.UpdateOneById(
			globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
			"s1",
			bson.M{"$unset": bson.M{
				"categories.0.subCategories.0.numberOfFindPopular": 1,
				"categories.0.subCategories.1.numberOfFindPopular": 1,
			}},
		)
	})
	t.Run("3", func(t *testing.T) {
		apiUrl := "/api/v3/api-asker-vn/find-product-grocery-assistant"
		globalDataAccess.UpdateAllByQuery(
			globalCollection.COLLECTION_PRODUCT_GROCERY_ASSISTANT[local.ISO_CODE],
			bson.M{},
			bson.M{"$set": bson.M{
				"storeId": "s1",
			}},
		)

		log.Println("==================================== Find product by text")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_QUERY_REQUIRED.ErrorCode)
			})
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"text": "search text",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_LANGUAGE_REQUIRED.ErrorCode)
			})
		})

		log.Println("==================================== Find product by text")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"language": "vi",
					"text":     "Sườn non",
					"storeId":  "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, "s1_cat2_sub1_p1")

				var store *modelStoreGroceryAssistant.StoreGroceryAssistant
				globalDataAccess.GetOneByQuery(
					globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
					bson.M{},
					bson.M{"categories.subCategories._id": 1, "categories.subCategories.numberOfFindPopular": 1},
					&store,
				)
				So(store.Categories[1].SubCategories[0].NumberOfFindPopular, ShouldEqual, 1)
			})
			globalDataAccess.UpdateOneByQuery(
				globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
				bson.M{},
				bson.M{"$inc": bson.M{"categories.1.subCategories.0.numberOfFindPopular": -1}},
			)
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"language": "vi",
					"text":     "suon non",
					"storeId":  "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, "s1_cat2_sub1_p1")

				var store *modelStoreGroceryAssistant.StoreGroceryAssistant
				globalDataAccess.GetOneByQuery(
					globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
					bson.M{},
					bson.M{"categories.subCategories._id": 1, "categories.subCategories.numberOfFindPopular": 1},
					&store,
				)
				So(store.Categories[1].SubCategories[0].NumberOfFindPopular, ShouldEqual, 1)
			})
			globalDataAccess.UpdateOneByQuery(
				globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
				bson.M{},
				bson.M{"$inc": bson.M{"categories.1.subCategories.0.numberOfFindPopular": -1}},
			)
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"language": "vi",
					"text":     "cai be",
					"storeId":  "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, "s1_cat1_sub1_p1")

				var store *modelStoreGroceryAssistant.StoreGroceryAssistant
				globalDataAccess.GetOneByQuery(
					globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
					bson.M{"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE},
					bson.M{"categories.subCategories._id": 1, "categories.subCategories.numberOfFindPopular": 1},
					&store,
				)
				So(store.Categories[0].SubCategories[0].NumberOfFindPopular, ShouldEqual, 1)
				So(store.Categories[0].SubCategories[1].NumberOfFindPopular, ShouldEqual, 1)
			})
			globalDataAccess.UpdateOneByQuery(
				globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
				bson.M{},
				bson.M{"$inc": bson.M{"categories.0.subCategories.0.numberOfFindPopular": -1, "categories.0.subCategories.1.numberOfFindPopular": -1}},
			)
		})

		log.Println("==================================== Find product by sub category")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"subCategoryId": "s1_cat2_sub1",
					"storeId":       "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, "s1_cat2_sub1_p1")

				var store *modelStoreGroceryAssistant.StoreGroceryAssistant
				globalDataAccess.GetOneByQuery(
					globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
					bson.M{"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE},
					bson.M{"categories.subCategories._id": 1, "categories.subCategories.numberOfFindPopular": 1},
					&store,
				)
				So(store.Categories[1].SubCategories[0].NumberOfFindPopular, ShouldEqual, 1)
			})
			globalDataAccess.UpdateOneByQuery(
				globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
				bson.M{},
				bson.M{"$inc": bson.M{"categories.1.subCategories.0.numberOfFindPopular": -1}},
			)
		})

		log.Println("==================================== Find product by product id")
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiUrl), t, func() {
			Convey("Check result", func() {
				body := map[string]interface{}{
					"productId": "s1_cat1_sub1_p1",
					"storeId":   "s1",
				}
				reqBody, _ := json.Marshal(body)

				req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)

				So(resp.Code, ShouldEqual, 200)

				var respResult []map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)

				So(len(respResult), ShouldEqual, 1)
				So(respResult[0]["_id"], ShouldEqual, "s1_cat1_sub1_p1")

				var store *modelStoreGroceryAssistant.StoreGroceryAssistant
				globalDataAccess.GetOneByQuery(
					globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
					bson.M{"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE},
					bson.M{"categories.subCategories._id": 1, "categories.subCategories.numberOfFindPopular": 1},
					&store,
				)
				So(store.Categories[0].SubCategories[0].NumberOfFindPopular, ShouldEqual, 1)
			})
			globalDataAccess.UpdateOneByQuery(
				globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
				bson.M{},
				bson.M{"$inc": bson.M{"categories.0.subCategories.0.numberOfFindPopular": -1}},
			)
		})
	})
	// Validate update shoppingCart
	t.Run("4", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"
		Convey("Check the response if request param is invalid", t, func() {
			body := map[string]interface{}{
				"userId": 123,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})

		Convey("Check the response if update action is incorrect", t, func() {
			body := map[string]interface{}{
				"userId": "123",
				"action": "2",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 500)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_UPDATE_ACTION_INCORRECT.ErrorCode)
		})

		Convey("Check the response if productId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "123",
				"action":    "INCREASE",
				"productId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_PRODUCT_ID_REQUIRED.ErrorCode)
		})

		Convey("Check the response if product user create not have text", t, func() {
			body := map[string]interface{}{
				"userId":        "123",
				"action":        "DECREASE",
				"isUserCreated": true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_PRODUCT_NAME_REQUIRED.ErrorCode)
		})

		Convey("Check the response if product user create not have price", t, func() {
			body := map[string]interface{}{
				"userId": "123",
				"newProduct": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "new product",
					},
				},
				"action":        "DECREASE",
				"isUserCreated": true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_PRODUCT_PRICE_REQUIRED.ErrorCode)
		})

		Convey("Check the response if product user create but action is REDUCE", t, func() {
			body := map[string]interface{}{
				"userId": "123",
				"newProduct": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "new product",
					},
					"price":      100000,
					"categoryId": "111",
				},
				"action":        "DECREASE",
				"isUserCreated": true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 500)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_UPDATE_ACTION_INCORRECT.ErrorCode)
		})
	})

	// Add the first lineItem when user don't have shoppingCart but action is reduce
	// User create product
	t.Run("5", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		Convey("Check the response and the database", t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"newProduct": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Ca muoi",
					},
					"price":      20000,
					"categoryId": "123",
				},
				"action":        "DECREASE",
				"isUserCreated": true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 500)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_UPDATE_ACTION_INCORRECT.ErrorCode)
		})
	})

	// Add the first lineItem when user don't have shoppingCart
	// User create product
	t.Run("6", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		Convey("Check the response and the database", t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"newProduct": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Ca muoi",
					},
					"price":      20000,
					"categoryId": "111",
				},
				"action":        "INCREASE",
				"isUserCreated": true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)
			So(resultMap["productId"], ShouldNotBeEmpty)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 1)
			So(shoppingCart.LineItems[0].Text.Vi, ShouldEqual, "Ca muoi")
			So(shoppingCart.LineItems[0].IsUserCreated, ShouldBeTrue)
			So(shoppingCart.LineItems[0].Price, ShouldEqual, 20000)
			So(shoppingCart.LineItems[0].Quantity, ShouldEqual, 1)
		})
	})

	// Add the first lineItem when user don't have shoppingCart
	// Provider product
	t.Run("7", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		Convey("Check the response and the database", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "INCREASE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 1)
			So(shoppingCart.LineItems[0].Text.Vi, ShouldEqual, "Rau cải xanh")
			So(shoppingCart.LineItems[0].IsUserCreated, ShouldBeFalse)
			So(shoppingCart.LineItems[0].Price, ShouldEqual, 100000)
			So(shoppingCart.LineItems[0].Quantity, ShouldEqual, 1)
			So(shoppingCart.LineItems[0].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(shoppingCart.LineItems[0].CategoryIds, ShouldResemble, []string{"testCategoryIds"})
		})
	})

	// Add the first lineItem when user already have shoppingCart
	// User create product
	t.Run("7.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id": "testId",
					},
				},
			},
		})

		Convey("Check the response and the database", t, func() {
			body := map[string]interface{}{
				"userId": "**********",
				"newProduct": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Ca muoi",
					},
					"price":      20000,
					"categoryId": "xxx",
				},
				"action":        "INCREASE",
				"isUserCreated": true,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)
			So(resultMap["productId"], ShouldNotBeEmpty)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 2)
			So(shoppingCart.LineItems[1].Text.Vi, ShouldEqual, "Ca muoi")
			So(shoppingCart.LineItems[1].IsUserCreated, ShouldBeTrue)
			So(shoppingCart.LineItems[1].Price, ShouldEqual, 20000)
			So(shoppingCart.LineItems[1].Quantity, ShouldEqual, 1)
		})
	})

	// User already have shoppingCart
	// Provider product
	// New product isn't in shoppingCart
	t.Run("8", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id": "testId",
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "INCREASE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 2)
			So(shoppingCart.LineItems[1].Text.Vi, ShouldEqual, "Rau cải xanh")
			So(shoppingCart.LineItems[1].IsUserCreated, ShouldBeFalse)
			So(shoppingCart.LineItems[1].Price, ShouldEqual, 100000)
			So(shoppingCart.LineItems[1].Quantity, ShouldEqual, 1)
			So(shoppingCart.LineItems[1].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(shoppingCart.LineItems[1].CategoryIds, ShouldResemble, []string{"testCategoryIds"})
		})
	})

	// User already have shoppingCart
	// New product isn't in shoppingCart
	// Product not found
	t.Run("9", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id": "testId",
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "INCREASE",
				"productId": "ids[0]",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 404)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_PRODUCT_NOT_FOUND.ErrorCode)
		})
	})

	// User already have shoppingCart
	// Product already in shoppingCart
	t.Run("10", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id":   ids[0],
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 1,
					},
					{
						"_id":   "test",
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 1,
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "INCREASE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 2)
			So(shoppingCart.LineItems[0].Text.Vi, ShouldEqual, "Rau cải xanh")
			So(shoppingCart.LineItems[0].IsUserCreated, ShouldBeFalse)
			So(shoppingCart.LineItems[0].Price, ShouldEqual, 100000)
			So(shoppingCart.LineItems[0].Quantity, ShouldEqual, 2)
			So(shoppingCart.LineItems[0].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(shoppingCart.LineItems[0].CategoryIds, ShouldResemble, []string{"testCategoryIds"})

			So(shoppingCart.LineItems[1].Quantity, ShouldEqual, 1)
		})
	})

	// User already have shoppingCart
	// Product already in shoppingCart
	// Reduce quantity
	t.Run("11", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id":   ids[0],
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 4,
					},
					{
						"_id":   "test",
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 3,
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "DECREASE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 2)
			So(shoppingCart.LineItems[0].Text.Vi, ShouldEqual, "Rau cải xanh")
			So(shoppingCart.LineItems[0].IsUserCreated, ShouldBeFalse)
			So(shoppingCart.LineItems[0].Price, ShouldEqual, 100000)
			So(shoppingCart.LineItems[0].Quantity, ShouldEqual, 3)
			So(shoppingCart.LineItems[0].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(shoppingCart.LineItems[0].CategoryIds, ShouldResemble, []string{"testCategoryIds"})

			So(shoppingCart.LineItems[1].Quantity, ShouldEqual, 3)
		})
	})

	// User already have shoppingCart
	// Product already in shoppingCart
	// Reduce quantity + Delete product has quantity = 0
	t.Run("12", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id":   ids[0],
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 1,
					},
					{
						"_id":   "test",
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 3,
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "DECREASE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 1)
			So(shoppingCart.LineItems[0].Text.Vi, ShouldEqual, "Rau cải")
			So(shoppingCart.LineItems[0].IsUserCreated, ShouldBeFalse)
			So(shoppingCart.LineItems[0].Price, ShouldEqual, 100000)
			So(shoppingCart.LineItems[0].Quantity, ShouldEqual, 3)
			So(shoppingCart.LineItems[0].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(shoppingCart.LineItems[0].CategoryIds, ShouldResemble, []string{"testCategoryIds"})
		})
	})

	// User already have shoppingCart
	// Product already in shoppingCart
	// Reduce quantity + Delete shopping cart
	t.Run("13", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id":   ids[0],
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 1,
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "DECREASE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldBeNil)
		})
	})

	// User already have shoppingCart
	// Product already in shoppingCart
	// Delete quantity
	t.Run("13.1", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/update-shopping-cart"

		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id":   ids[0],
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 1,
					},
					{
						"_id":   "2",
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 1,
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId":    "**********",
				"action":    "REMOVE",
				"productId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 200)

			// Check the database
			var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &shoppingCart)
			So(shoppingCart, ShouldNotBeNil)
			So(len(shoppingCart.LineItems), ShouldEqual, 1)
			So(shoppingCart.LineItems[0].XId, ShouldEqual, "2")
		})
	})

	// Validate get shopping cart by userId
	t.Run("14", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-shopping-cart-by-userId"
		Convey("Check the response if request param is invalid", t, func() {
			body := map[string]interface{}{
				"userId": 123,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
		})
		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			resultMap := map[string]map[string]interface{}{}
			json.Unmarshal(b, &resultMap)
			So(res.Code, ShouldEqual, 400)
			So(resultMap["error"]["code"], ShouldEqual, lib.ERROR_USER_ID_REQUIRED.ErrorCode)
		})
	})

	t.Run("15", func(t *testing.T) {
		apiURL := "/api/v3/api-asker-vn/get-shopping-cart-by-userId"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "**********",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 01",
			},
		})

		ids := CreateProduct([]map[string]interface{}{
			{
				"SKU":   "111111",
				"price": 100000,
				"text": map[string]interface{}{
					"vi": "Rau cải xanh",
				},
				"image": "imageRauCaiXanh.png",
				"categoryIds": []string{
					"testCategoryIds",
				},
			},
		})

		CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id":   ids[0],
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải xanh",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 4,
					},
					{
						"_id":   "test",
						"SKU":   "111111",
						"price": 100000,
						"text": map[string]interface{}{
							"vi": "Rau cải",
						},
						"image": "imageRauCaiXanh.png",
						"categoryIds": []string{
							"testCategoryIds",
						},
						"quantity": 3,
					},
				},
			},
		})

		Convey("Check the response if userId is empty", t, func() {
			body := map[string]interface{}{
				"userId": "**********",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			service.NewRouter().ServeHTTP(res, req)

			b, _ := io.ReadAll(res.Body)
			var result *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
			json.Unmarshal(b, &result)
			So(res.Code, ShouldEqual, 200)
			So(result, ShouldNotBeNil)
			So(len(result.LineItems), ShouldEqual, 2)
			So(result.LineItems[0].XId, ShouldEqual, ids[0])
			So(result.LineItems[0].SKU, ShouldEqual, "111111")
			So(result.LineItems[0].Price, ShouldEqual, 100000)
			So(result.LineItems[0].Text.Vi, ShouldEqual, "Rau cải xanh")
			So(result.LineItems[0].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(result.LineItems[0].CategoryIds, ShouldResemble, []string{"testCategoryIds"})
			So(result.LineItems[0].Quantity, ShouldEqual, 4)

			So(result.LineItems[1].XId, ShouldEqual, "test")
			So(result.LineItems[1].SKU, ShouldEqual, "111111")
			So(result.LineItems[1].Price, ShouldEqual, 100000)
			So(result.LineItems[1].Text.Vi, ShouldEqual, "Rau cải")
			So(result.LineItems[1].Image, ShouldEqual, "imageRauCaiXanh.png")
			So(result.LineItems[1].CategoryIds, ShouldResemble, []string{"testCategoryIds"})
			So(result.LineItems[1].Quantity, ShouldEqual, 3)
		})
	})
	t.Run("16", func(t *testing.T) {
		log.Println("==================================== Validate Get Stores")
		apiGetStores := "/api/v3/api-asker-vn/get-stores-grocery-assistant"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetStores), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"taskPlace": "123",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when taskPlace nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_PLACE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_PLACE_REQUIRED.Message)
			})
			Convey("Check request when taskPlace city blank", func() {
				body := map[string]interface{}{
					"taskPlace": map[string]interface{}{
						"country": local.ISO_CODE,
					},
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_TASK_PLACE_CITY_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_TASK_PLACE_CITY_REQUIRED.Message)
			})
		})
	})

	t.Run("17", func(t *testing.T) {
		log.Println("==================================== Get Stores")
		// GetStores
		apiGetStores := "/api/v3/api-asker-vn/get-stores-grocery-assistant"

		ResetData()

		//Create Store
		ids := CreateStore([]map[string]interface{}{
			{
				"place": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "HA NOI",
					"district": "Hoan Kiem",
				},
				"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE,
				"text": map[string]interface{}{
					"vi": "Bach Hoa Xanh",
					"en": "Bach Hoa Xanh",
				},
				"image":   "bachhoaxanh.img",
				"address": "quan Hoan Kiem, Ha Noi",
				"type": map[string]interface{}{
					"name": "Bach hoa",
					"text": map[string]interface{}{
						"vi": "Bach hoa",
						"en": "Bach hoa",
					},
				},
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetStores), t, func() {
			body := map[string]interface{}{
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "HA NOI",
					"district": "Hoan Kiem",
					"lat":      0.0,
					"lng":      0.0,
				},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					respResults := []map[string]interface{}{}
					respResultMaps := []map[string]map[string]interface{}{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResults)
					json.Unmarshal(bytes, &respResultMaps)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResults), ShouldEqual, 1)
					So(respResults[0]["_id"], ShouldEqual, ids[0])
					So(respResults[0]["image"], ShouldEqual, "bachhoaxanh.img")
					So(respResults[0]["address"], ShouldEqual, "quan Hoan Kiem, Ha Noi")
					So(respResultMaps[0]["text"]["vi"], ShouldEqual, "Bach Hoa Xanh")
					So(respResultMaps[0]["type"]["name"], ShouldEqual, "Bach hoa")
				})
			})
		})
	})

	t.Run("18", func(t *testing.T) {
		log.Println("==================================== Validate remove-shopping-card-by-id")
		apiGetStores := "/api/v3/api-asker-vn/remove-shopping-card-by-id"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetStores), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"shoppingCardId": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
			Convey("Check request when taskPlace nil", func() {
				body := map[string]interface{}{}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_SHOPPING_CARD_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_SHOPPING_CARD_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("19", func(t *testing.T) {
		log.Println("==================================== Remove ShoppingCardById")
		// GetStores
		apiGetStores := "/api/v3/api-asker-vn/remove-shopping-card-by-id"

		ResetData()

		//Create ShoppingCart
		ids := CreateShoppingCart([]map[string]interface{}{
			{
				"userId": "**********",
				"lineItems": []map[string]interface{}{
					{
						"_id": "testId",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiGetStores), t, func() {
			body := map[string]interface{}{
				"shoppingCardId": ids[0],
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiGetStores, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Notifications", func() {
					So(resp.Code, ShouldEqual, 200)
					// Check the database
					var shoppingCart *shoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
					globalDataAccess.GetOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], ids[0], bson.M{}, &shoppingCart)
					So(shoppingCart, ShouldBeNil)
				})
			})
		})
	})
}
