/*
 * @File: 7_incentive_test.go
 * @Description: Handler function, case test
 * @CreatedAt: 20/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func TestIncentive(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		//Get Incentive
		log.Println("==================================== Get Incentive")
		apiIncentive := "/api/v3/api-asker-vn/get-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
			}, {
				"image":     "test2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM TEST",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
			{
				"image":     "test3",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
				"rankRequire":  3,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s with default filter/sort", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"isoCode": local.ISO_CODE,
				"page":    1,
				"limit":   10}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router with default filter", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResult[0]["from"], ShouldEqual, "SYSTEM")
					So(respResult[0]["point"], ShouldEqual, 500)

					now := globalLib.GetCurrentTime(local.TimeZone)
					So(respResult[0]["startDate"], ShouldBeLessThan, now.Format(time.RFC3339))
					So(respResult[0]["endDate"], ShouldBeGreaterThan, now.Format(time.RFC3339))
				})
			})
		})
	})

	t.Run("1.1", func(t *testing.T) {
		log.Println("==================================== Validate GetIncentive")
		//GetIncentive
		apiIncentive := "/api/v3/api-asker-vn/get-incentive"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentive), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
			Convey("Check request when isoCode incorrect", func() {
				body := map[string]interface{}{
					"isoCode": "JA",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 500)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_INCORRECT.Message)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		//Get Incentive
		log.Println("==================================== Get Incentive")
		apiIncentive := "/api/v3/api-asker-vn/get-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 700,
				},
			}, {
				"image":     "test2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"from":         "SYSTEM",
				"categoryName": "Food & Beverage",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			}, {
				"image":     "test3",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
			}, {
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 700,
				},
				"rankRequire": 2,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s with sort POINT_DECREASE", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"isoCode": local.ISO_CODE,
				"sortBy":  "POINT_DECREASE",
				"page":    1,
				"limit":   10}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive with filter createdBy and sort status", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResult[0]["point"], ShouldEqual, 700)
					So(respResult[0]["image"], ShouldEqual, "test1")
					So(respResult[0]["point"], ShouldBeGreaterThan, respResult[1]["point"])
				})
			})
		})
	})
	t.Run("2.1", func(t *testing.T) {
		//Get Incentive
		log.Println("==================================== Get Incentive")
		apiIncentive := "/api/v3/api-asker-vn/get-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":        "test1",
				"startDate":    fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":      fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 700,
				},
				"createdAt": "2020,11,02,15,15",
			}, {
				"image":        "test2",
				"startDate":    fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":      fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"from":         "SYSTEM",
				"categoryName": "Food & Beverage",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s with sort POINT_INCREASE", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"isoCode": local.ISO_CODE,
				"sortBy":  "POINT_INCREASE",
				"page":    1,
				"limit":   10}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive with filter createdBy and sort status", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["_id"], ShouldEqual, ids[1])
					So(respResult[0]["point"], ShouldEqual, 500)
					So(respResult[0]["image"], ShouldEqual, "test2")
					So(respResult[1]["point"], ShouldBeGreaterThan, respResult[0]["point"])
				})
			})
		})
	})
	t.Run("2.2", func(t *testing.T) {
		//Get Incentive
		log.Println("==================================== Get Incentive")
		apiIncentive := "/api/v3/api-asker-vn/get-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":        "test1",
				"startDate":    fmt.Sprintf("%d,%d,%d,0,15", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":      fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 700,
				},
				"createdAt": "2020,11,02,15,15",
			}, {
				"image":        "test2",
				"startDate":    fmt.Sprintf("%d,%d,%d,0,20", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":      fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"from":         "SYSTEM",
				"categoryName": "Food & Beverage",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s with sort LASTEST", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"isoCode": local.ISO_CODE,
				"sortBy":  "LASTEST",
				"page":    1,
				"limit":   10}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive with filter createdBy and sort status", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["_id"], ShouldEqual, ids[1])
					So(respResult[0]["point"], ShouldEqual, 500)
					So(respResult[0]["image"], ShouldEqual, "test2")
					So(respResult[1]["point"], ShouldBeGreaterThan, respResult[0]["point"])
					So(globalLib.ParseDateFromString(respResult[0]["startDate"].(string), local.TimeZone), ShouldHappenAfter, globalLib.ParseDateFromString(respResult[1]["startDate"].(string), local.TimeZone))
				})
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		//Get Incentive
		log.Println("==================================== Get Incentive")
		apiIncentive := "/api/v3/api-asker-vn/get-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		CreateIncentive([]map[string]interface{}{
			{
				"image":        "test1",
				"startDate":    fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":      fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"from":         "SYSTEM",
				"categoryName": "Category Test",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,15",
				"exchange": map[string]interface{}{
					"by":    "POINT",
					"point": 300,
				},
			}, {
				"image":        "test2",
				"startDate":    fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":      fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"from":         "SYSTEM TEST",
				"categoryName": "Category Test",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s with filter options", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"isoCode": local.ISO_CODE,
				"filterBy": map[string]interface{}{
					"category":       "Category Test",
					"exchangedPoint": 400,
					"createdBy":      "testname",
					"from":           "SYSTEM",
				},
				"sortBy": "",
				"page":   1,
				"limit":  10}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive with filter options", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 1)
					So(respResult[0]["point"], ShouldEqual, 300)
					So(respResult[0]["image"], ShouldEqual, "test1")
				})
			})
		})
	})
	t.Run("4", func(t *testing.T) {
		log.Println("==================================== Validate GetIncentiveDetail")
		//GetIncentiveDetail
		apiIncentiveDetail := "/api/v3/api-asker-vn/get-incentive-detail"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentiveDetail), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"id": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentiveDetail, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when id blank", func() {
				body := map[string]interface{}{
					"id": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentiveDetail, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ID_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ID_REQUIRED.Message)
			})
		})
	})
	t.Run("5", func(t *testing.T) {
		log.Println("==================================== GetIncentiveDetail")
		apiIncentiveDetail := "/api/v3/api-asker-vn/get-incentive-detail"
		ResetData()
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image": "test1",
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"social": map[string]interface{}{
					"facebook":  "facbook.com",
					"instagram": "instagram.com",
					"ios":       "ios.com",
					"android":   "android.com",
					"website":   "website.com",
				},
			}, {
				"image": "test2",
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM TEST",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s id: %s", apiIncentiveDetail, ids[0]), t, func() {
			var body = map[string]interface{}{
				"id": ids[0],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentiveDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["codeList"], ShouldBeNil)
					So(respResult["from"], ShouldEqual, "SYSTEM")
					So(respResult["image"], ShouldEqual, "test1")
					So(respResult["point"], ShouldEqual, 500)
					So(respResultM["title"]["en"], ShouldEqual, "eGift Cash - Kichi Kichi - 100.000VND")
					So(respResult["categoryName"], ShouldEqual, "ABC")
					So(respResult["createdBy"], ShouldEqual, "vinhnt")
					So(respResult["partner"], ShouldEqual, "UR_BOX")
					So(respResult["status"], ShouldEqual, globalConstant.INCENTIVE_STATUS_ACTIVE)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
					So(respResultM["social"]["facebook"], ShouldEqual, "facbook.com")
					So(respResultM["social"]["android"], ShouldEqual, "android.com")
					So(respResultM["social"]["website"], ShouldEqual, "website.com")
				})
			})
		})
	})
	t.Run("6", func(t *testing.T) {
		log.Println("==================================== GetIncentiveDetail")
		apiIncentiveDetail := "/api/v3/api-asker-vn/get-incentive-detail"
		ResetData()
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image": "test1",
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM",
				"categoryName": "ABC",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
			}, {
				"image": "test2",
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM TEST",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s id: %s", apiIncentiveDetail, ids[1]), t, func() {
			var body = map[string]interface{}{
				"id": ids[1],
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentiveDetail, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test Get Incentive", func() {
					respResult := make(map[string]interface{})
					respResultM := make(map[string]map[string]interface{})
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					json.Unmarshal(bytes, &respResultM)
					So(resp.Code, ShouldEqual, 200)
					So(respResult["codeList"], ShouldBeNil)
					So(respResult["_id"], ShouldEqual, ids[1])
					So(respResult["from"], ShouldEqual, "SYSTEM TEST")
					So(respResult["image"], ShouldEqual, "test2")
					So(respResult["point"], ShouldEqual, 500)
					// So(respResultM["exchange"]["by"], ShouldEqual, "POINT")
					So(respResultM["title"]["en"], ShouldEqual, "eGift Cash - Kichi Kichi - 100.000VND")
					So(respResult["categoryName"], ShouldEqual, "Food & Beverage")
					So(respResult["createdBy"], ShouldEqual, "vinhnt")
					So(respResult["partner"], ShouldEqual, "UR_BOX")
					So(respResult["status"], ShouldEqual, globalConstant.INCENTIVE_STATUS_ACTIVE)
					So(respResult["isoCode"], ShouldEqual, local.ISO_CODE)
				})
			})
		})
	})
	t.Run("7", func(t *testing.T) {
		log.Println("==================================== Validate GetSpecialIncentive")
		//GetSpecialIncentive
		apiIncentive := "/api/v3/api-asker-vn/get-special-incentive"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentive), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"isoCode": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})

			Convey("Check request when isoCode blank", func() {
				body := map[string]interface{}{
					"userId":  "03125142",
					"isoCode": "",
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_ISO_CODE_REQUIRED.Message)
			})
		})
	})
	t.Run("8", func(t *testing.T) {
		//GetSpecialIncentive
		log.Println("==================================== GetSpecialIncentive")
		apiIncentive := "/api/v3/api-asker-vn/get-special-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		ids := CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,15", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
			}, {
				"image":     "test2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM TEST",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
			}, {
				"image":     "test3",
				"startDate": fmt.Sprintf("%d,%d,%d,0,10", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM_WITH_PARTNER",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
			}, {
				"image":     "test4",
				"startDate": fmt.Sprintf("%d,%d,%d,0,15", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
				"rankRequire":        2,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"userId":  "123",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test GetSpecialIncentive", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
					So(respResult[0]["_id"], ShouldEqual, ids[0])
					So(respResult[1]["_id"], ShouldEqual, ids[2])
					So(respResult[0]["from"], ShouldEqual, "SYSTEM")
					So(respResult[0]["point"], ShouldEqual, 500)

					So(globalLib.ParseDateFromString(respResult[0]["startDate"].(string), local.TimeZone), ShouldHappenAfter, globalLib.ParseDateFromString(respResult[1]["startDate"].(string), local.TimeZone))
					now := globalLib.GetCurrentTime(local.TimeZone)
					So(respResult[0]["startDate"], ShouldBeLessThan, now.Format(time.RFC3339))
					So(respResult[0]["endDate"], ShouldBeGreaterThan, now.Format(time.RFC3339))
				})
			})
		})
	})
	t.Run("9", func(t *testing.T) {
		log.Println("==================================== Validate GetIncentiveDetail")
		//GetIncentiveDetail
		apiIncentiveDetail := "/api/v3/api-asker-vn/get-incentive"
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentiveDetail), t, func() {
			Convey("Check request when can not parse params", func() {
				body := map[string]interface{}{
					"id": 123,
				}
				reqBody, _ := json.Marshal(body)
				req := httptest.NewRequest("POST", apiIncentiveDetail, bytes.NewBuffer(reqBody))
				resp := httptest.NewRecorder()
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 400)

				var respResult map[string]map[string]interface{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(respResult["error"]["code"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
				So(respResult["error"]["message"], ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.Message)
			})
		})
	})
	t.Run("10", func(t *testing.T) {
		//GetSpecialIncentive
		log.Println("==================================== GetSpecialIncentive can not get isTesting for normal user")
		apiIncentive := "/api/v3/api-asker-vn/get-special-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,15", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
				"isTesting":          true,
			}, {
				"image":     "test2",
				"startDate": fmt.Sprintf("%d,%d,%d,0,0", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Đà Nẵng",
						"Huế",
						"Hội An",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":         "SYSTEM TEST",
				"categoryName": "Food & Beverage",
				"partner":      "UR_BOX",
				"status":       globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":    "vinhnt",
				"isoCode":      local.ISO_CODE,
				"createdAt":    "2020,11,02,15,20",
				"isTesting":    true,
			}, {
				"image":     "test3",
				"startDate": fmt.Sprintf("%d,%d,%d,0,10", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM_WITH_PARTNER",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
				"isTesting":          true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"userId":  "123",
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test GetSpecialIncentive", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 0)
				})
			})
		})
	})
	t.Run("11", func(t *testing.T) {
		//GetSpecialIncentive when not login
		log.Println("==================================== GetSpecialIncentive")
		apiIncentive := "/api/v3/api-asker-vn/get-special-incentive"
		ResetData()
		before1Day := time.Now().AddDate(0, 0, -1)
		after2Days := time.Now().AddDate(0, 0, 2)
		CreateIncentive([]map[string]interface{}{
			{
				"image":     "test1",
				"startDate": fmt.Sprintf("%d,%d,%d,0,15", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
			}, {
				"image":     "test3",
				"startDate": fmt.Sprintf("%d,%d,%d,0,10", before1Day.Year(), before1Day.Month(), before1Day.Day()),
				"endDate":   fmt.Sprintf("%d,%d,%d,0,0", after2Days.Year(), after2Days.Month(), after2Days.Day()),
				"applyFor": map[string]interface{}{
					"city": []string{
						"Hồ Chí Minh",
						"Đồng Nai",
						"Hà Nội",
					},
				},
				"promotion": map[string]interface{}{
					"numberOfDayDueDate": 30,
				},
				"from":               "SYSTEM_WITH_PARTNER",
				"categoryName":       "ABC",
				"partner":            "UR_BOX",
				"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
				"createdBy":          "vinhnt",
				"isoCode":            local.ISO_CODE,
				"createdAt":          "2020,11,02,15,15",
				"isSpecialIncentive": true,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiIncentive), t, func() {
			var body = map[string]interface{}{
				"isoCode": local.ISO_CODE,
			}

			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiIncentive, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)

				Convey("Then check the response to test GetSpecialIncentive", func() {
					var respResult []map[string]interface{}
					bytes, _ := io.ReadAll(resp.Body)

					json.Unmarshal(bytes, &respResult)
					So(resp.Code, ShouldEqual, 200)
					So(len(respResult), ShouldEqual, 2)
				})
			})
		})
	})
}
