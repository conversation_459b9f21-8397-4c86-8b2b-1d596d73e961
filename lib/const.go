/*
 * @File: const.go
 * @Description: Handle, New const
 * @CreatedAt: 03/02/2020
 * @Author: linhnh
 * @UpdatedAt: 18/11/2020
 * @UpdatedBy: linhnh
 */
package lib

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

const (
	MAX_RATING_DATE            = 7
	MAX_LEN_FAVOURITE_SERVICES = 8
	PAGING_LIMIT               = 20
	SORT_BY_LATEST             = "LATEST"
	SORT_BY_LOW_TO_HIGH        = "LOW_TO_HIGH"
	SORT_BY_HIGH_TO_LOW        = "HIGH_TO_LOW"
)

var ErrorCodeListUrBox = []*model.UrBoxErrorCode{
	{Message: "1 trong số quà tặng bạn đặt mua đã hết hạn, bạn hãy chọn lại quà khác.", Code: "GIFT_EXPRIED"},
	{Message: "Hệ thống khách hàng không đủ tiền", Code: "NOT_ENOUGH_MONEY"},
	{Message: "Bạn vui lòng chọn quà muốn tặng trước.", Code: "CHOOSE_GIFT"},
	{Message: "Số lượng phải lớn hơn 0", Code: "QUANTITY_GREATER_THAN_0"},
	{Message: "Email không đúng định dạng", Code: "EMAIL_INCORRECT_FORMAT"},
	{Message: "Mã khuyến mại không đúng", Code: "CODE_ERROR"},
	{Message: "CODE_IS_USED_UP", Code: "CODE_IS_USED_UP"},
	{Message: "Số lượng sản phẩm không đủ", Code: "QUANTITY_NOT_ENOUGH"},
}

const (
	SLACK_CHANNEL_ASKER_FEEDBACK_VN = "asker-feedback"
)

var TASK_STATUS_ACCEPT_CREATE_REFUND_REQUEST = []string{globalConstant.TASK_STATUS_CANCELED, globalConstant.TASK_STATUS_EXPIRED}

var TASK_STATUS_ALLOW_ASKER_VIEW_CHAT_HISTORY = []string{globalConstant.TASK_STATUS_DONE, globalConstant.TASK_STATUS_CANCELED}

var TASK_STATUS_ALLOW_ASKER_SEND_CHAT = []string{globalConstant.TASK_STATUS_DONE, globalConstant.TASK_STATUS_CONFIRMED}
var TASK_STATUS_ALLOW_ASKER_SEND_CHAT_MOVING_TASK = []string{globalConstant.TASK_STATUS_CONFIRMED}

// noel campaign 2024
const (
	USER_CAMPAIGN_ACTION_ROLL_CALL         = "ROLL_CALL"
	USER_CAMPAIGN_ACTION_ROLL_CALL_AT_NOEL = "ROLL_CALL_AT_NOEL"

	NOEL_DATE  = 25
	NOEL_MONTH = 12

	USER_CAMPAIGN_ACTION_LOGIN              = "LOGIN"
	USER_CAMPAIGN_ACTION_DONE_TASK          = "DONE_TASK"
	USER_CAMPAIGN_ACTION_REFERRAL           = "REFERRAL"
	USER_CAMPAIGN_ACTION_DONE_TASK_REFERRAL = "DONE_TASK_REFERRAL"
	USER_CAMPAIGN_ACTION_DONE_TASK_FIRST    = "DONE_TASK_FIRST"
	USER_CAMPAIGN_ACTION_NEW_SIGN_UP        = "SIGN_UP"

	MISSION_NAME_WELLCOME                       = "WELLCOME"
	BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY        = "TOP_UP_BPAY"
	BUSINESS_TRANSACTION_KEY_TOP_UP_MEMBER      = "TOP_UP_MEMBER"
	BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER   = "USED_BPAY_MEMBER"
	BUSINESS_TRANSACTION_KEY_REVOKE_BPAY_MEMBER = "REVOKE_BPAY_MEMBER"
	FROM_GIFT                                   = "GIFT"
)

var MIN_REQUIRE_VERSION_FOR_GET_TASKER_SERVICES map[string]string = map[string]string{
	globalConstant.SERVICE_KEY_NAME_HOME_COOKING: "3.29.0",
	globalConstant.SERVICE_KEY_NAME_ELDERLY_CARE: "3.29.0",
	globalConstant.SERVICE_KEY_NAME_CHILD_CARE:   "3.28.0",
	globalConstant.SERVICE_KEY_NAME_PATIENT_CARE: "3.28.0",
}
