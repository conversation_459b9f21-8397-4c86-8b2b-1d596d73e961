/*
 * @File: helpers.go
 * @Description: Handler Func
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package lib

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"regexp"
	"strings"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcEmailVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPushNotificationVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcWebsocket"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/common"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelMarketingCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/marketingCampaign"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelPromotionHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionhistory"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelRedeemGiftTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftTransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/reportTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var cfg = config.GetConfig()

/*
 * @Description: Parse api request params
 * @CreatedAt: 21/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func ParseBodyParams(r *http.Request) (*model.ApiRequest, *globalResponse.ResponseErrorCode) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody model.ApiRequest
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		return nil, &ERROR_CAN_NOT_PARSE_API_PARAMS
	}
	return &reqBody, nil
}

func ParseBodyParamsBusiness(r *http.Request) (*model.BusinessRequest, *globalResponse.ResponseErrorCode) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody model.BusinessRequest
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		return nil, &ERROR_CAN_NOT_PARSE_API_PARAMS
	}
	return &reqBody, nil
}

/*
 * @Description: Parse api request params for BackEnd
 * @CreatedAt: 26/04/2021
 * @Author: vinhnt
 */
func ParseBodyParamsFromBackend(r *http.Request) (*model.ApiBackEndRequest, *globalResponse.ResponseErrorCode) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody model.ApiBackEndRequest
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		return nil, &ERROR_CAN_NOT_PARSE_API_PARAMS
	}
	return &reqBody, nil
}

func ParseBodyBusinessParams(r *http.Request) (*model.BusinessRequest, *globalResponse.ResponseErrorCode) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody model.BusinessRequest
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		return nil, &ERROR_CAN_NOT_PARSE_API_PARAMS
	}
	return &reqBody, nil
}

func ParseToken(r *http.Request) string {
	authToken := r.Header.Get(globalConstant.API_HEADER_AUTHORIZATION)
	array := strings.Split(authToken, " ")
	return array[1]
}

func IsConflictTask(task *modelTask.Task, confirmedTask *modelTask.Task, timeInBetweenTask int32) bool {
	taskDate := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	taskStart_1 := taskDate.Add(time.Duration(-timeInBetweenTask) * time.Minute)
	taskEnd_1 := taskDate.Add(time.Duration(task.Duration*60+float64(timeInBetweenTask)) * time.Minute)

	taskStart_2 := globalLib.ParseDateFromTimeStamp(confirmedTask.Date, local.TimeZone)
	taskEnd_2 := taskStart_2.Add(time.Duration(confirmedTask.Duration) * time.Hour)

	if (taskStart_1.After(taskStart_2) && taskStart_1.Before(taskEnd_2)) || (taskStart_2.After(taskStart_1) && taskStart_2.Before(taskEnd_1)) {
		return true
	}
	return false
}

func CalcDistance(lat1 float64, lng1 float64, lat2 float64, lng2 float64) float64 {
	pi := float64(math.Pi)
	phi1 := lat1 * pi / 180
	phi2 := lat2 * pi / 180
	deltaLambda := (lng2 - lng1) * pi / 180
	R := 6371e3
	// gives d in metres
	d := math.Acos(math.Sin(phi1)*math.Sin(phi2)+math.Cos(phi1)*math.Cos(phi2)*math.Cos(deltaLambda)) * R
	return math.Round(d)
}

/*
 * @Description: Send Notification
 * @CreatedAt: 02/02/2021
 * @Author: ngoctb3
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SendNotification(arrayNotification []interface{}, userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds, title *modelService.ServiceText, body *modelService.ServiceText, payload *modelPushNotificationRequest.PushNotificationRequestPayload, option string) {
	defer local.Logger.Sync()
	if len(arrayNotification) > 0 {
		globalDataAccess.InsertAll(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], arrayNotification)
	}
	if len(userIds) > 0 && option == "" {
		client, connect, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
		if err != nil {
			local.Logger.Warn("Connect GRPC Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
			return
		}
		defer connect.Close()
		reqNotify := &modelPushNotificationRequest.PushNotificationRequest{
			UserIds: userIds,
			Title:   title,
			Body:    body,
			Payload: payload,
		}
		_, err = client.Send(context.Background(), reqNotify)
		if err != nil {
			local.Logger.Warn("Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
		}
	}
}

/*
 * @Description: Is Phone Valid By Country Code
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
func IsPhoneValidByCountryCode(phone string) bool {
	return globalLib.IsPhoneValid(phone, local.ISO_CODE)
}

func SendEmail(option *emailSending.EmailSending) error {
	var err error
	client, connect, err := grpcEmailVN.ConnectGRPCEmailVN(cfg.GRPC_Email_Service_URL)
	if err != nil {
		return err
	}
	defer connect.Close()
	_, err = client.SendEmail(context.Background(), option)
	if err != nil {
		return err
	}

	return nil
}

func GetDaysFromDateTimestamp(date []*timestamppb.Timestamp) []int32 {
	result := []int32{}
	weekdayMap := make(map[int32]bool)
	for _, d := range date {
		weekday := globalLib.ParseDateFromTimeStamp(d, local.TimeZone).Weekday()
		weekdayMap[int32(weekday)] = true
	}

	for k := range weekdayMap {
		result = append(result, k)
	}
	return result
}

func GetIsoWeekDays(weekday []int32) []string {
	result := []string{}
	for _, w := range weekday {
		result = append(result, time.Weekday(w).String())
	}
	return result
}

func CheckIsUserTester(user *modelUser.Users) bool {
	if user == nil {
		return false
	}
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &settings)
	isUserTester := false
	if settings != nil && len(settings.Tester) > 0 && globalLib.FindStringInSlice(settings.Tester, user.Phone) > -1 {
		isUserTester = true
	}
	return isUserTester
}

func IsAskerCanViewTaskChatHistory(taskStatus string, taskDate time.Time, duration float64) bool {
	if globalLib.FindStringInSlice(TASK_STATUS_ALLOW_ASKER_VIEW_CHAT_HISTORY, taskStatus) < 0 {
		return false
	}

	// After 7 days from done task -> Return false
	now := globalLib.GetCurrentTime(local.TimeZone)

	// Done task at
	taskDoneAt := taskDate.Add(time.Duration(duration) * time.Hour)

	// End time to see chat
	taskerCanSeeChatWithIn := 7 * 24 * time.Hour
	endTime := taskDoneAt.Add(taskerCanSeeChatWithIn)
	return now.Before(endTime)
}

func IsAskerCanSendChat(taskStatus string, taskDate time.Time, taskDuration float64, taskServiceName string) bool {
	// Moving
	if globalLib.IsHomeMovingServiceByKeyName(taskServiceName) {
		return isAskerCancelSendChat_HomeMoving(taskStatus, taskDate, taskDuration)
	}

	// Normal task
	return isAskerCancelSendChat_NormalTask(taskStatus, taskDate, taskDuration)
}

func isAskerCancelSendChat_HomeMoving(taskStatus string, taskDate time.Time, duration float64) bool {
	if globalLib.FindStringInSlice(TASK_STATUS_ALLOW_ASKER_SEND_CHAT_MOVING_TASK, taskStatus) < 0 {
		return false
	}
	// After 24 hoú from done task -> Return false
	now := globalLib.GetCurrentTime(local.TimeZone)

	// Done task at
	taskDoneAt := taskDate.Add(time.Duration(duration) * time.Hour)

	// End time to see chat
	taskerCanSendChatWithIn := 24 * time.Hour
	endTime := taskDoneAt.Add(taskerCanSendChatWithIn)

	return now.Before(endTime)
}

func isAskerCancelSendChat_NormalTask(taskStatus string, taskDate time.Time, duration float64) bool {
	if globalLib.FindStringInSlice(TASK_STATUS_ALLOW_ASKER_SEND_CHAT, taskStatus) < 0 {
		return false
	}

	// After 30 minutes from done task -> Return false
	now := globalLib.GetCurrentTime(local.TimeZone)

	// Done task at
	taskDoneAt := taskDate.Add(time.Duration(duration) * time.Hour)

	// End time to see chat
	taskerCanSendChatWithIn := 30 * time.Minute
	endTime := taskDoneAt.Add(taskerCanSendChatWithIn)

	return now.Before(endTime)
}

func GetRankAsker(asker *modelUser.Users) (askerRank string) {
	askerRank = globalConstant.ASKER_RANK_NAME_MEMBER
	if asker == nil {
		return
	}
	if asker.RankInfo != nil && asker.RankInfo.RankName != "" {
		askerRank = asker.RankInfo.RankName
	}
	return
}

func GetAskerPoint(asker *modelUser.Users) (point float64) {
	if asker == nil {
		return
	}
	point = asker.Point
	return
}

func CreateReferralCode(name string) string {
	name = strings.ReplaceAll(name, " ", "")
	refferal := strings.ReplaceAll(strings.ToLower(globalLib.RemoveUnicode(name)), " ", "")
	if len(refferal) < globalConstant.NUMBER_LAST_CHARACTER_NAME {
		refferal = fmt.Sprintf("%s%s", refferal, common.RandomString(globalConstant.NUMBER_LAST_CHARACTER_NAME-len(refferal)))
	} else {
		regex := regexp.MustCompile(`.{4}$`) // Get last 4 characters
		refferal = regex.FindString(refferal)
	}
	isExist := true
	for isExist {
		refferal = fmt.Sprintf("%s%s", refferal, common.RandomString(4))
		isExist, _ = modelUser.IsExistByQuery(local.ISO_CODE, bson.M{"referralCode": refferal})
	}
	return refferal
}

func GetMarketingCampaignForUserV3(userId, from string) []*modelMarketingCampaign.MarketingCampaign {
	if userId == "" {
		return nil
	}

	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"totalTaskDone": 1, "createdAt": 1, "phone": 1, "isOldUser": 1})
	if asker == nil {
		return nil
	}

	// Get data
	currentDate := globalLib.GetCurrentTime(local.TimeZone)
	actions := [4]int{globalConstant.MARKETING_CAMPAIGN_ACTION_NEW, globalConstant.MARKETING_CAMPAIGN_ACTION_SEEN, globalConstant.MARKETING_CAMPAIGN_ACTION_SKIP, globalConstant.MARKETING_CAMPAIGN_ACTION_BOOK}
	query := bson.M{
		"status":    globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": currentDate},
		"endDate":   bson.M{"$gte": currentDate},
		"isTesting": bson.M{"$ne": true},
	}
	var query1, query2 bson.M
	query1 = bson.M{
		"$or": []bson.M{
			{fmt.Sprintf("userIds.%s", userId): bson.M{"$in": actions}},
			{fmt.Sprintf("userIds.%s", userId): bson.M{"$exists": false}},
		}}
	if asker.IsOldUser || asker.TotalTaskDone > 0 {
		query2 = bson.M{
			"$or": []bson.M{
				{"applyForUser.target": bson.M{"$in": []string{globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_CURRENT, globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH}}},
				{"applyForUser.userIds": userId},
			},
		}
	} else {
		query2 = bson.M{
			"$or": []bson.M{
				{"applyForUser.target": bson.M{"$in": []string{globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW, globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH}}},
				{"applyForUser.userIds": userId},
			},
		}
	}
	if from != FROM_GIFT {
		query["$or"] = []bson.M{
			{"isHidingOnHomePage": false},
			{"isHidingOnHomePage": bson.M{"$exists": false}},
		}
	}
	query["$and"] = []bson.M{
		query1,
		query2,
	}
	isUserTester := CheckIsUserTester(asker)
	if isUserTester {
		delete(query, "isTesting")
	}
	var result []*modelMarketingCampaign.MarketingCampaign
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE],
		query,
		bson.M{"userIds": 0, "emailTemplate": 0, "notificationTemplate": 0, "applyForUser": 0},
		bson.M{"startDate": -1},
		&result,
	)

	return result
}

func GetAvailableMarketingCampaignForUser(reqBody *model.ApiRequest, from string) ([]map[string]interface{}, map[string]*modelPromotionCode.PromotionCode) {
	// Get list marketing campaign available for user
	marketingCampaigns := GetMarketingCampaignForUserV3(reqBody.UserId, from)

	// Get promotion limit by code and promotion used count by code
	mapPromotionByCode, mapPromotionUsedCountByCode, mapPromotionCodeUsedOfUser := getPromotionsAndHistories(marketingCampaigns, reqBody.UserId, reqBody.TaskPlace)

	// Refactor marketing campaigns
	return refactorMarketingCampaigns(marketingCampaigns, mapPromotionByCode, mapPromotionUsedCountByCode, mapPromotionCodeUsedOfUser), mapPromotionByCode
}

func getPromotionsAndHistories(marketingCampaigns []*modelMarketingCampaign.MarketingCampaign, userId string, taskPlace *modelTask.TaskPlace) (map[string]*modelPromotionCode.PromotionCode, map[string]int32, map[string]bool) {
	promotionCodes := []string{}
	for _, v := range marketingCampaigns {
		if !IsMarketingCampaignTypePromotion(v) {
			continue
		}
		promotionCodes = append(promotionCodes, v.Promotion.Code)
	}

	// Get promotion limit by code
	query := bson.M{"code": bson.M{"$in": promotionCodes}}
	if taskPlace != nil {
		queryOr := []bson.M{}
		if taskPlace.City != "" {
			queryOr = append(queryOr, bson.M{
				"$or": []bson.M{
					{"taskPlace.city.0": bson.M{"$exists": false}},
					{"taskPlace.city": taskPlace.City},
				},
			})
		}
		if taskPlace.District != "" {
			queryOr = append(queryOr, bson.M{
				"$or": []bson.M{
					{"taskPlace.district.0": bson.M{"$exists": false}},
					{"taskPlace.district": taskPlace.District},
				},
			})
		}
		if len(queryOr) > 0 {
			query["$and"] = queryOr
		}
	}
	var promotions []*modelPromotionCode.PromotionCode
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE],
		query,
		bson.M{"code": 1, "limit": 1, "minOrderValue": 1, "taskStartDate": 1, "taskEndDate": 1, "serviceId": 1, "value": 1, "paymentMethods": 1, "hourRanges": 1},
		&promotions,
	)
	mapPromotionByCode := map[string]*modelPromotionCode.PromotionCode{}
	for _, v := range promotions {
		mapPromotionByCode[v.Code] = v
	}

	// Get promotion used count by code
	var promotionHistories []*modelPromotionHistory.PromotionHistory
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], bson.M{"promotionCode": bson.M{"$in": promotionCodes}}, bson.M{"promotionCode": 1, "userId": 1}, &promotionHistories)
	mapPromotionUsedCountByCode := map[string]int32{}
	mapPromotionCodeUsedOfUser := map[string]bool{}
	for _, v := range promotionHistories {
		mapPromotionUsedCountByCode[v.PromotionCode]++
		if v.UserId == userId {
			mapPromotionCodeUsedOfUser[v.PromotionCode] = true
		}
	}

	return mapPromotionByCode, mapPromotionUsedCountByCode, mapPromotionCodeUsedOfUser
}

func refactorMarketingCampaigns(marketingCampaigns []*modelMarketingCampaign.MarketingCampaign, mapPromotionByCode map[string]*modelPromotionCode.PromotionCode, mapPromotionUsedCountByCode map[string]int32, mapPromotionCodeUsedOfUser map[string]bool) []map[string]interface{} {
	result := []map[string]interface{}{}
	for _, v := range marketingCampaigns {
		// Only get mkt campaign type PROMOTION
		if !IsMarketingCampaignTypePromotion(v) {
			continue
		}
		// Only get mkt campaign that user has not used before
		if mapPromotionCodeUsedOfUser[v.Promotion.Code] {
			continue
		}
		// Init item data
		item := map[string]interface{}{
			"_id":     v.XId,
			"title":   v.Title,
			"expired": globalLib.ParseDateFromTimeStamp(v.EndDate, local.TimeZone),
			"brandInfo": &modelIncentive.IncentiveBrandInfo{
				Image: "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/sEFHixKrmjNcp9jaw",
				Text: &modelService.ServiceText{
					Vi: "bTaskee",
					En: "bTaskee",
					Ko: "bTaskee",
					Th: "bTaskee",
					Id: "bTaskee",
				},
			},
			"type":          "MARKETING_CAMPAIGN",
			"promotionCode": v.Promotion.Code,
			"createdAt":     globalLib.ParseDateFromTimeStamp(v.CreatedAt, local.TimeZone),
		}
		// get quantity left
		var quantity int32 = 0
		if promotionCode, ok := mapPromotionByCode[v.Promotion.Code]; ok && promotionCode != nil && promotionCode.Limit > 0 {
			quantity = promotionCode.Limit - mapPromotionUsedCountByCode[v.Promotion.Code]
			// Only get promotion code has quantity > 0.
			if quantity <= 0 {
				continue
			}
			// Only set quantity if promotionCode limit > 0
			// item["quantity"] = quantity
		}

		// Append item to result
		result = append(result, item)
	}
	return result
}

func IsMarketingCampaignTypePromotion(m *modelMarketingCampaign.MarketingCampaign) bool {
	return m.GetType() == globalConstant.MARKETING_CAMPAIGN_TYPE_PROMOTION && m.GetPromotion().GetCode() != ""
}

func GetUserLanguage(userId string) string {
	user, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"language": 1})
	if user != nil {
		return user.Language
	}
	return globalConstant.LANG_VI
}

func ConvertStringToServiceText(text string) *modelService.ServiceText {
	return &modelService.ServiceText{
		Vi: text,
		En: text,
		Ko: text,
		Th: text,
		Id: text,
	}
}

func GetReportTransactions(transactionIds []string) []string {
	var reportedTransactions []*reportTransaction.ReportTransaction
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE],
		bson.M{"transactionId": bson.M{"$in": transactionIds}},
		bson.M{"transactionId": 1},
		&reportedTransactions,
	)

	reportedTransactionIds := []string{}
	for _, reportedTransaction := range reportedTransactions {
		reportedTransactionIds = append(reportedTransactionIds, reportedTransaction.TransactionId)
	}

	reportedTransactionIds = globalLib.UniqString(reportedTransactionIds)
	return reportedTransactionIds
}

func SendNotificationV2(arrayNotification []interface{}, userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds, title *modelService.ServiceText, body *modelService.ServiceText, payload *modelPushNotificationRequest.PushNotificationRequestPayload, option string) {
	defer local.Logger.Sync()
	if len(arrayNotification) > 0 {
		globalDataAccess.InsertAll(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], arrayNotification)
	}
	if len(userIds) > 0 && option == "" {
		client, connect, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
		if err != nil {
			local.Logger.Warn("Connect GRPC Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
			return
		}
		defer connect.Close()
		reqNotify := &modelPushNotificationRequest.PushNotificationRequest{
			UserIds: userIds,
			Title:   title,
			Body:    body,
			Payload: payload,
		}
		_, err = client.Send(context.Background(), reqNotify)
		if err != nil {
			local.Logger.Warn("Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
		}
	}
	// Send notification in app
	grpcWebsocket.SendSocketNotification(arrayNotification, cfg.GRPC_Websocket_VN_V3_URL, userIds, payload.Type, title, body)
}

func GetIncentiveIdAlreadyRedeem(asker *modelUser.Users, incentive []*modelIncentive.Incentive) []string {
	if asker == nil {
		return []string{}
	}

	var incentiveIds []string
	for _, v := range incentive {
		incentiveIds = append(incentiveIds, v.XId)
	}
	var redeemGiftTransactions []*modelRedeemGiftTransaction.RedeemGiftTransaction
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], bson.M{"userId": asker.XId, "incentiveInfo.incentiveId": bson.M{"$in": incentiveIds}}, bson.M{"incentiveInfo.incentiveId": 1}, &redeemGiftTransactions)

	var incentiveIdAlreadyRedeem []string
	if len(redeemGiftTransactions) > 0 {
		for _, redeemGiftTransaction := range redeemGiftTransactions {
			incentiveIdAlreadyRedeem = append(incentiveIdAlreadyRedeem, redeemGiftTransaction.IncentiveInfo.IncentiveId)
		}
	}
	return incentiveIdAlreadyRedeem
}
