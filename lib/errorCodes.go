/*
 * @File: errorCodes.go
 * @Description: New Error
 * @CreatedAt: 03/02/2020
 * @Author: linhnh
 * @UpdatedAt: 18/12/2020
 * @UpdatedBy: vinhnt
 */
package lib

import (
	"net/http"

	globalResponse "gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
)

var ERROR_CODE_CLIENT = http.StatusBadRequest
var ERROR_CODE_NOT_FOUND = http.StatusNotFound
var ERROR_CODE_SERVER = http.StatusInternalServerError
var ERROR_CODE_UNAUTHORIZED = http.StatusUnauthorized

var ERROR_CUSTOM = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER}
var SYSTEM_ERROR = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SYSTEM_ERROR", Message: "System error"}

var ERROR_DATA_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "DATA_NOT_FOUND", Message: "Data is not found"}
var ERROR_HOUSEKEEPING_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "HOUSEKEEPING_NOT_FOUND", Message: "Housekeeping is not found"}
var ERROR_ROOM_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "ROOM_NOT_FOUND", Message: "Room is not found"}
var ERROR_CURRENCY_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "CURRENCY_NOT_FOUND", Message: "Currency is not found"}
var ERROR_TASK_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "TASK_NOT_FOUND", Message: "Booking is not found"}
var ERROR_USER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "USER_NOT_FOUND", Message: "User is not found"}
var ERROR_PHONE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "PHONE_NOT_FOUND", Message: "Phone number is not found"}
var ERROR_INCENTIVE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "INCENTIVE_NOT_FOUND", Message: "Incentive is not found"}
var ERROR_COMPANY_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "COMPANY_NOT_FOUND", Message: "Company is not found"}
var ERROR_MARKETING_CAMPAIGN_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "MARKETING_CAMPAIGN_NOT_FOUND", Message: "Marketing Campaign is not found"}
var ERROR_GIFT_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "GIFT_NOT_FOUND", Message: "Gift is not found"}
var ERROR_DISCOVER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "DISCOVER_NOT_FOUND", Message: "Discover is not found"}
var ERROR_CATEGORY_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "CATEGORY_NOT_FOUND", Message: "Category is not found"}
var ERROR_CHAT_MESSAGE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "CHAT_MESSAGE_NOT_FOUND", Message: "Chat message is not found"}
var ERROR_SERVICE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SERVICE_NOT_FOUND", Message: "Service is not found"}
var ERROR_SUBSCRIPTION_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SUBSCRIPTION_NOT_FOUND", Message: "Subscription is not found"}
var ERROR_PAYMENT_CARD_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "PAYMENT_CARD_NOT_FOUND", Message: "Payment card not found"}
var ERROR_SHARED_USER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SHARED_USER_NOT_FOUND", Message: "Shared not found"}
var ERROR_STORE_GROCERY_ASSISTANT_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "STORE_GROCERY_ASSISTANT_NOT_FOUND", Message: "Store not found"}
var ERROR_PRODUCT_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "PRODUCT_NOT_FOUND", Message: "Product not found"}
var ERROR_TRANSACTION_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "TRANSACTION_NOT_FOUND", Message: "Transaction not found"}
var ERROR_USER_REPORT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "USER_REPORT_NOT_FOUND", Message: "User report is not found"}
var ERROR_FINANCIAL_ACCOUNT_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "FINANCIAL_ACCOUNT_NOT_FOUND", Message: "Financial account is not found"}
var ERROR_COMBO_VOUCHER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "COMBO_VOUCHER_NOT_FOUND", Message: "Combo voucher is not found"}
var ERROR_USER_COMBO_VOUCHER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "USER_COMBO_VOUCHER_NOT_FOUND", Message: "User combo voucher is not found"}
var ERROR_SETTING_SYSTEM_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SETTING_SYSTEM_NOT_FOUND", Message: "Setting system is not found"}
var ERROR_PARTNER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "PARTNER_NOT_FOUND", Message: "Partner is not found"}
var ERROR_SETTING_COUNTRY_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SETTING_COUNTRY_NOT_FOUND", Message: "Setting country not found"}
var ERROR_ASKER_REFERRAL_SETTINGS_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "ASKER_REFERRAL_SETTINGS_NOT_FOUND", Message: "Asker referral settings not found"}
var ERROR_GAME_CAMPAIGN_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "GAME_CAMPAIGN_NOT_FOUND", Message: "Game campaign not found"}
var ERROR_USER_GAME_CAMPAIGN_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "USER_GAME_CAMPAIGN_NOT_FOUND", Message: "User game campaign not found"}
var ERROR_SURVEY_SETTING_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SURVEY_SETTING_NOT_FOUND", Message: "Survey not found"}
var ERROR_TASK_SCHEDULE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "TASK_SCHEDULE_NOT_FOUND", Message: "Task schedule not found"}
var ERROR_LEVEL_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "LEVEL_NOT_FOUND", Message: "Level not found"}
var ERROR_ASKER_REPORT_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "ASKER_REPORT_NOT_FOUND",
	Message:    "Asker report not found",
	ErrorText: &modelService.ServiceText{
		En: "Your report not found",
		Vi: "Không tìm thấy thông tin báo cáo của bạn",
		Id: "Laporan Anda tidak ditemukan",
		Ko: "귀하의 보고서를 찾을 수 없습니다",
		Th: "ไม่พบรายงานของคุณ",
	},
}

var ERROR_CAN_NOT_PARSE_API_PARAMS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CAN_NOT_PARSE_API_PARAMS", Message: "Server can not parse params from client"}
var ERROR_APP_VERSION_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "APP_VERSION_REQUIRED", Message: "App version is required"}
var ERROR_ISO_CODE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ISO_CODE_REQUIRED", Message: "ISOCode is required"}
var ERROR_NAME_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "NAME_REQUIRED",
	Message:    "Name is required",
	ErrorText: &modelService.ServiceText{
		En: "Name is required",
		Vi: "Yêu cầu nhập tên",
		Id: "Nama diperlukan",
		Ko: "이름이 필요합니다",
		Th: "ต้องการชื่อ",
	},
}
var ERROR_EMAIL_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "EMAIL_REQUIRED",
	Message:    "Email is required",
	ErrorText: &modelService.ServiceText{
		En: "Email is required",
		Vi: "Yêu cầu nhập email",
		Id: "Email diperlukan",
		Ko: "이메일이 필요합니다",
		Th: "ต้องการอีเมล",
	},
}
var ERROR_ADDRESS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ADDRESS_REQUIRED", Message: "Address is required"}
var ERROR_USER_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "USER_ID_REQUIRED", Message: "User id is required"}
var ERROR_GAME_CAMPAIGN_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "GAME_CAMPAIGN_ID_REQUIRED", Message: "Game campaign id is required"}
var ERROR_TASK_PLACE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TASK_PLACE_REQUIRED", Message: "Task place is required"}
var ERROR_HOME_TYPE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "HOME_TYPE_REQUIRED", Message: "Home type is required"}
var ERROR_BOOKING_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "BOOKING_ID_REQUIRED", Message: "Booking id is required"}
var ERROR_HOUSE_NUMBER_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "HOUSE_NUMBER_REQUIRED", Message: "House number is required"}
var ERROR_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ID_REQUIRED", Message: "Id is required"}
var ERROR_LAT_LNG_IP_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "LAT_LNG_IP_REQUIRED", Message: "Lat, lng or ip address is required"}
var ERROR_AREA_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "AREA_REQUIRED", Message: "Area is required"}
var ERROR_IMAGES_NOT_VALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "IMAGES_NOT_VALID", Message: "Images must be an array"}
var ERROR_ROOM_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ROOM_ID_REQUIRED", Message: "Room id is required"}
var ERROR_DATA_UPDATE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "DATA_UPDATE_REQUIRED", Message: "Data update is required"}
var ERROR_PHONE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PHONE_REQUIRED", Message: "Phone number is required"}
var ERROR_CODE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CODE_REQUIRED", Message: "Code is required"}
var ERROR_SERVICE_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SERVICE_ID_REQUIRED", Message: "Service id is required"}
var ERROR_PAGE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PAGE_REQUIRED", Message: "Page is required"}
var ERROR_MESSAGE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "MESSAGE_REQUIRED", Message: "Message is required"}
var ERROR_CHAT_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CHAT_ID_REQUIRED", Message: "Chat id is required"}
var ERROR_TEXT_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TEXT_REQUIRED", Message: "Text is required"}
var ERROR_CREATED_AT_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CREATED_AT_REQUIRED", Message: "Created at is required"}
var ERROR_COUNTRY_CODE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "COUNTRY_CODE_REQUIRED", Message: "Country code is required"}
var ERROR_FREE_SCHEDULE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "FREE_SCHEDULE_REQUIRED", Message: "Free schedule is required"}
var ERROR_TIME_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TIME_REQUIRED", Message: "Time is required"}
var ERROR_LAT_LNG_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "LAT_LNG_REQUIRED", Message: "Lat and lng are required"}
var ERROR_DATE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "DATE_REQUIRED", Message: "Date is required"}
var ERROR_DURATION_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "DURATION_REQUIRED", Message: "Duration is required"}
var ERROR_SUBSCRIPTION_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SUBSCRIPTION_ID_REQUIRED", Message: "Subscription id is required"}
var ERROR_CARD_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CARD_ID_REQUIRED", Message: "Card id is required"}
var ERROR_TOKEN_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TOKEN_REQUIRED", Message: "Token is required"}
var ERROR_REFERRAL_CODE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "REFERRAL_CODE_REQUIRED", Message: "Referral code is required"}
var ERROR_LANGUAGE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "LANGUAGE_REQUIRED", Message: "Language is required"}
var ERROR_APP_NAME_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "APP_NAME_REQUIRED", Message: "App name is required"}
var ERROR_SCHEDULE_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SCHEDULE_ID_REQUIRED", Message: "Schedule id is required"}
var ERROR_SCHEDULE_TIME_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SCHEDULE_TIME_REQUIRED", Message: "Schedule time is required"}
var ERROR_SCHEDULE_DURATION_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SCHEDULE_DURATION_REQUIRED", Message: "Schedule duration is required"}
var ERROR_PAYMENT_METHOD_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PAYMENT_METHOD_REQUIRED", Message: "Payment method is required"}
var ERROR_TASKER_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TASKER_ID_REQUIRED", Message: "Tasker id is required"}
var ERROR_ARRAY_TASKER_IDS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ARRAY_TASKER_IDS_REQUIRED", Message: "Array of tasker id is required"}
var ERROR_GIFT_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "GIFT_ID_REQUIRED", Message: "Gift id is required"}
var ERROR_SERVICES_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SERVICES_REQUIRED", Message: "Array of service is required"}
var ERROR_FEEDBACK_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "FEEDBACK_REQUIRED", Message: "Feedback is required"}
var ERROR_ROOM_TYPES_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ROOM_TYPES_REQUIRED", Message: "Array room type are required"}
var ERROR_ROOM_TYPE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "ROOM_TYPE_REQUIRED", Message: "Room type is required"}
var ERROR_AVATAR_URL_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "AVATAR_URL_REQUIRED", Message: "Avatar url is required"}
var ERROR_CAMPAIGN_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CAMPAIGN_ID_REQUIRED", Message: "Campaign id is required"}
var ERROR_CAMPAIGN_ACTION_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CAMPAIGN_ACTION_REQUIRED", Message: "Campaign action is required"}
var ERROR_INCENTIVE_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "INCENTIVE_ID_REQUIRED", Message: "Incentive id is required"}
var ERROR_LOCATION_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "LOCATION_ID_REQUIRED", Message: "Location id is required"}
var ERROR_VERIFICATION_DATA_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "VERIFICATION_DATA_REQUIRED", Message: "Verification Data is required"}
var ERROR_USER_TYPE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "USER_TYPE_REQUIRED", Message: "User type is required"}
var ERROR_REPORT_DATA_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "REPORT_DATA_REQUIRED", Message: "Report data is required"}
var ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SUBSCRIPTION_REQUEST_ID_REQUIRED", Message: "Subscription request id is required"}
var ERROR_TRANSACTION_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TRANSACTION_ID_REQUIRED", Message: "Transaction id is required"}
var ERROR_REASON_REPORT_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "REASON_REPORT_REQUIRED", Message: "Reason report is required"}
var ERROR_VERSION_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "VERSION_REQUIRED", Message: "Version is required"}
var ERROR_PLATFORM_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PLATFORM_REQUIRED", Message: "Platfrom is required"}
var ERROR_CHANNEL_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CHANNEL_REQUIRED", Message: "Channel slack is required"}
var ERROR_USERNAME_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "USERNAME_REQUIRED", Message: "Username is required"}
var ERROR_MESSAGE_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "MESSAGE_ID_REQUIRED", Message: "Message id is required"}
var ERROR_FROM_USER_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "FROM_USER_REQUIRED", Message: "From user is required"}
var ERROR_TO_USER_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TO_USER_REQUIRED", Message: "To user is required"}
var ERROR_SHARED_USER_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SHARED_USER_ID_REQUIRED", Message: "Shared user id required"}
var ERROR_STORE_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "STORE_ID_REQUIRED", Message: "Store id is required"}
var ERROR_CATEGORY_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CATEGORY_ID_REQUIRED", Message: "Category id is required"}
var ERROR_QUERY_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "QUERY_REQUIRED", Message: "Query is required"}
var ERROR_PRODUCT_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PRODUCT_ID_REQUIRED", Message: "Product id is required"}
var ERROR_PRODUCT_NAME_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PRODUCT_NAME_REQUIRED", Message: "Product name is required"}
var ERROR_PRODUCT_PRICE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PRODUCT_PRICE_REQUIRED", Message: "Product price is required"}
var ERROR_TASK_PLACE_CITY_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "TASK_PLACE_CITY_REQUIRED", Message: "Task place city is required"}
var ERROR_SHOPPING_CARD_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SHOPPING_CARD_ID_REQUIRED", Message: "Shopping card id is required"}
var ERROR_PLACE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PLACE_REQUIRED", Message: "Place is required"}
var ERROR_PLACE_COUNTRY_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PLACE_COUNTRY_REQUIRED", Message: "Place country is required"}
var ERROR_PLACE_CITY_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PLACE_CITY_REQUIRED", Message: "Place city is required"}
var ERROR_PLACE_DISTRICT_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PLACE_DISTRICT_REQUIRED", Message: "Place district is required"}
var ERROR_YEAR_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "YEAR_REQUIRED", Message: "Year is required"}
var ERROR_REASON_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "REASON_REQUIRED", Message: "Reason required"}
var ERROR_REFUND_REASON_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "REFUND_REASON_REQUIRED", Message: "Refund reason is required"}
var ERROR_COMBO_VOUCHER_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "COMBO_VOUCHER_ID_REQUIRED", Message: "Combo voucher ID is required"}
var ERROR_USER_COMBO_VOUCHER_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "USER_COMBO_VOUCHER_ID_REQUIRED", Message: "User combo voucher ID is required"}
var ERROR_MESSAGE_TO_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "MESSAGE_TO_REQUIRED", Message: "MessageTo is required"}
var ERROR_PARTNER_CODE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "PARTNER_CODE_REQUIRED", Message: "Partner code required"}
var ERROR_LIST_SERVICE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "LIST_SERVICE_REQUIRED", Message: "List service required"}
var ERROR_EXCEEDED_QUANTITY_OF_SERVICES = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "EXCEEDED_QUANTITY_OF_SERVICES", Message: "Exceeded quantity of services"}
var ERROR_SURVEY_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SURVEY_ID_REQUIRED", Message: "Survey id required"}
var ERROR_SURVEY_ANSWERS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SURVEY_ANSWERS_REQUIRED", Message: "Survey answers required"}
var ERROR_BOOKING_ID_OR_MEMBER_IDS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "BOOKING_ID_OR_MEMBER_IDS_REQUIRED", Message: "Booking id or member ids required"}
var ERROR_MEMBER_INFOS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "MEMBER_INFOS_REQUIRED", Message: "Member infos is required"}
var ERROR_BUSINESS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "BUSINESS_REQUIRED", Message: "Business is required"}
var ERROR_MEMBER_IDS_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "MEMBER_IDS_REQUIRED", Message: "Member ids is required"}
var ERROR_LEVEL_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "LEVEL_REQUIRED", Message: "LevelId is required"}

var ERROR_DATA_INVALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "DATA_INVALID", Message: "Data invalid"}
var ERROR_UPDATE_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "UPDATE_FAILED", Message: "Update failed"}
var ERROR_REMOVE_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "REMOVE_FAILED", Message: "Remove failed"}
var ERROR_TIME_NOT_ALLOW = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TIME_NOT_ALLOW", Message: "Time does not allow"}
var ERROR_CAN_NOT_GET_PRICE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CAN_NOT_GET_PRICE", Message: "Can not get price"}
var ERROR_USER_IS_MEMBER_OF_ANOTHER_BUSINESS = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "USER_IS_MEMBER_OF_ANOTHER_BUSINESS",
	Message:    "User is member of another business",
	ErrorText: &modelService.ServiceText{
		Vi: "Người dùng là thành viên của một doanh nghiệp khác",
		En: "User is a member of another business",
		Th: "ผู้ใช้เป็นสมาชิกของธุรกิจอื่น",
		Id: "Pengguna adalah anggota dari bisnis lain",
		Ko: "사용자는 다른 사업체의 회원입니다",
	},
}
var ERROR_BUSINESS_EXIST = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "BUSINESS_EXIST",
	Message:    "Business is already existed",
	ErrorText: &modelService.ServiceText{
		Vi: "Doanh nghiệp đã tồn tại",
		En: "Business is already existed",
		Th: "ธุรกิจมีอยู่แล้ว",
		Id: "Bisnis sudah ada",
		Ko: "사업체가 이미 존재합니다",
	},
}
var ERROR_EMAIL_EXIST = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "EMAIL_EXIST",
	Message:    "Email is already existed",
	ErrorText: &modelService.ServiceText{
		Vi: "Email đã tồn tại",
		En: "Email is already existed",
		Th: "มีอีเมลนี้อยู่แล้ว",
		Id: "Email sudah ada",
		Ko: "이메일이 이미 존재합니다",
	},
}
var ERROR_TAX_CODE_EXIST = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "TAX_CODE_EXIST",
	Message:    "Tax code is already existed",
	ErrorText: &modelService.ServiceText{
		Vi: "Mã số thuế đã tồn tại",
		En: "Tax code is already existed",
		Th: "รหัสภาษีมีอยู่แล้ว",
		Id: "Kode pajak sudah ada",
		Ko: "세금 코드가 이미 존재합니다",
	},
}
var ERROR_TASK_NOT_CONFIRMED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_NOT_CONFIRMED", Message: "Task is not confirmed"}
var ERROR_TASKER_INVALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASKER_INVALID", Message: "Tasker invalid"}
var ERROR_GOOGLE_TRANSLATION_ERROR = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "GOOGLE_TRANSLATION_ERROR", Message: "Error from translation google"}
var ERROR_SUBSCRIPTION_REQUEST_EXIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SUBSCRIPTION_REQUEST_EXIST", Message: "Subscription request is already existed"}
var ERROR_SUBSCRIPTION_CONFLICTED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SUBSCRIPTION_CONFLICTED", Message: "Subscription conflicted"}
var ERROR_TASKER_BLACKLIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASKER_BLACKLIST", Message: "Tasker in blacklist"}
var ERROR_BAD_RATING = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "BAD_RATING", Message: "Have bad rating"}
var ERROR_EMAIL_INVALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "EMAIL_INVALID", Message: "Email invalid"}
var ERROR_ID_NUMBER_EXISTS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "ID_NUMBER_EXISTS", Message: "Id number is already exist"}
var ERROR_AIQUA_CONFIG_NOT_SET = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "AIQUA_CONFIG_NOT_SET", Message: "Aiqua config not set"}
var ERROR_TRACKING_AIQUA_ERROR = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TRACKING_AIQUA_ERROR", Message: "Tracking Aiqua error"}
var ERROR_TASK_REFUSED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_REFUSED", Message: "Task refused"}
var ERROR_TASK_STARTED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_STARTED", Message: "Task started"}
var ERROR_EXISTS_PAYOUT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "EXISTS_PAYOUT", Message: "Payout exist"}
var ERROR_USER_MIGRATED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_MIGRATED", Message: "User is migrated"}
var ERROR_NUMBER_OF_QUESTIONS_INVALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "NUMBER_OF_QUESTIONS_INVALID", Message: "Number of questions is zero"}
var ERROR_ON_RESET_DATA = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "ON_RESET_DATA", Message: "On reset data"}
var ERROR_APN_TO_FCM_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "APN_TO_FCM_FAILED", Message: "Error convert APN to FCM"}
var ERROR_LOGGLY_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "LOGGLY_FAILED", Message: "Loggly failed"}
var ERROR_SERVICE_NOT_SUPPORT_COUNTRY = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SERVICE_NOT_SUPPORT_COUNTRY", Message: "Service does not support this country."}
var ERROR_INCENTIVE_HAS_STOPPED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "INCENTIVE_HAS_STOPPED", Message: "Incentive has stopped."}
var ERROR_INCENTIVE_EXPIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "INCENTIVE_EXPIRED", Message: "Incentive has expired."}
var ERROR_POINT_GREATER_THAN_ZERO = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "POINT_GREATER_THAN_ZERO", Message: "Reward points must be greater than 0."}
var ERROR_USER_NOT_ENOUGH_POINTS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_NOT_ENOUGH_POINTS", Message: "User not enough points."}
var ERROR_CREATE_POINT_TRANSACTION_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CREATE_POINT_TRANSACTION_FAILED", Message: "Create point transaction failed."}
var ERROR_CREATE_GIFT_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CREATE_GIFT_FAILED", Message: "Create gift failed."}
var ERROR_CREATE_REDEEM_GIFT_TRANSACTION_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CREATE_REDEEM_GIFT_TRANSACTION_FAILED", Message: "Create redeem gift transaction failed."}
var ERROR_INSERT_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "INSERT_FAILED", Message: "Insert data to database failed."}
var ERROR_CONVERSATION_DONE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CONVERSATION_DONE", Message: "This conversation is done"}
var ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "NOT_HAVE_PERMISSION", Message: "Not permission get list chat message"}
var ERROR_NOT_HAVE_PERMISSION_SEND_CHAT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "NOT_HAVE_PERMISSION_SEND_CHAT", Message: "Not permission send chat message"}
var ERROR_USER_TYPE_INCORRECT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_TYPE_INCORRECT", Message: "User type is incorrect"}
var ERROR_USER_STATUS_INCORRECT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_STATUS_INCORRECT", Message: "User status is incorrect"}
var ERROR_LOGIN_WITH_OLD_PHONE_NUMBER = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "LOGIN_WITH_OLD_PHONE_NUMBER", Message: "Login with old phone number"}
var ERROR_PHONE_NOT_EXIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "PHONE_NOT_EXIST", Message: "Phone does not existed"}
var ERROR_USER_INACTIVE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_INACTIVE", Message: "User status is INACTIVE"}
var ERROR_USER_BLOCKED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_LOCKED", Message: "User status is LOCKED"}
var ERROR_PLATFORM_INVALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "PLATFORM_INVALID", Message: "Platform invalid"}
var ERROR_ISO_CODE_INCORRECT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "ISO_CODE_INCORRECT", Message: "Iso code is incorrect"}
var ERROR_PHONE_INVALID = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "PHONE_INVALID", Message: "Phone is invalid"}
var ERROR_COUNTRY_CODE_INCORRECT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "COUNTRY_CODE_INCORRECT", Message: "Country code incorrect"}
var ERROR_TRANSACTION_HAS_BEEN_REPORT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TRANSACTION_HAS_BEEN_REPORT", Message: "Transaction has been report"}
var ERROR_USER_IN_BLACKLIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_IN_BLACKLIST", Message: "User in blackList"}
var ERROR_CAN_NOT_CHARGE_PAYMENT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CAN_NOT_CHARGE_PAYMENT", Message: "Can not charge payment"}
var ERROR_USER_ISO_CODE_NOT_MATCH = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_ISO_CODE_NOT_MATCH", Message: "New gift owner isoCode not match with gift isoCode"}
var ERROR_GIFT_CAN_NOT_SHARE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "GIFT_CAN_NOT_SHARE", Message: "Cannot share gift for another user"}
var ERROR_UPDATE_ACTION_INCORRECT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "UPDATE_ACTION_INCORRECT", Message: "Update action is incorrect"}
var ERROR_TASK_PAYMENT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_PAYMENT_ERROR", Message: "Task payment error"}
var ERROR_EVENT_NOT_RUNNING = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "EVENT_NOT_RUNNING", Message: "Event not running"}
var ERROR_VOUCHER_LIXI_EXIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "VOUCHER_LIXI_EXIST", Message: "Voucher lixi is exist"}
var ERROR_TASK_NOT_DONE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_NOT_DONE", Message: "Task not done"}
var ERROR_TASK_HAS_BEEN_REPORTED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_HAS_BEEN_REPORTED", Message: "Task has been reported"}
var ERROR_TASK_REFUND_REQUEST_ALREADY_EXIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_REFUND_REQUEST_ALREADY_EXIST", Message: "Task refund request already exist"}
var ERROR_TASK_STATUS_CAN_NOT_CREATE_REFUND_REQUEST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_STATUS_CAN_NOT_CREATE_REFUND_REQUEST", Message: "Task status can not create refund request"}
var ERROR_PAYMENT_METHOD_CAN_NOT_CREATE_REFUND_REQUEST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "PAYMENT_METHOD_CAN_NOT_CREATE_REFUND_REQUEST", Message: "Task payment method can not create refund request"}
var ERROR_REDEEM_GIFT_IS_PAUSE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "REDEEM_GIFT_IS_PAUSE", Message: "Redeem gift is pause"}
var ERROR_CREATE_COMBO_VOUCHER_FAILED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CREATE_COMBO_VOUCHER_FAILED", Message: "Created combo voucher failed"}
var ERROR_COMBO_VOUCHER_CAN_GET_ONCE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "COMBO_VOUCHER_CAN_GET_ONCE", Message: "Combo voucher can get once"}
var ERROR_COMBO_VOUCHER_NOT_FREE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "COMBO_VOUCHER_NOT_FREE", Message: "Combo voucher not free"}
var ERROR_SUBSCRIPTION_IS_CANCELLED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "SUBSCRIPTION_IS_CANCELLED",
	Message:    "Subscription is cancelled",
	ErrorText: &modelService.ServiceText{
		Vi: "Gói đăng ký đã bị hủy",
		En: "Subscription is cancelled",
		Th: "การสมัครสมาชิกถูกยกเลิกแล้ว",
		Id: " Langganan dibatalkan",
		Ko: "구독이 취소되었습니다",
	},
}
var ERROR_COMBO_VOUCHER_IS_NOT_SUBSCRIPTION = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "COMBO_VOUCHER_IS_NOT_SUBSCRIPTION",
	Message:    "Combo voucher is not subscription",
	ErrorText: &modelService.ServiceText{
		Vi: "Gói voucher không phải là gói đăng ký",
		En: "Combo voucher is not subscription",
		Th: "คอมโบวอเชอร์ไม่ใช่การสมัครสมาชิก",
		Id: "Voucher kombo bukan langganan",
		Ko: "콤보 바우처는 구독이 아닙니다",
	},
}
var ERROR_YOUR_RANK_CAN_NOT_REDEEM_GIFT = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "YOUR_RANK_CAN_NOT_REDEEM_GIFT", Message: "Your rank can not redeem gift"}
var ERROR_OFFER_HAS_ENDED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "OFFER_HAS_ENDED", Message: "Offer has ended"}
var ERROR_CAN_NOT_GET_GAME_CAMPAIGN = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CAN_NOT_GET_GAME_CAMPAIGN", Message: "Can not get game campaign"}
var ERROR_CAN_NOT_GET_USER_GAME_CAMPAIGN = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CAN_NOT_GET_USER_GAME_CAMPAIGN", Message: "Can not get user game campaign"}
var ERROR_ASKER_HAVE_SENT_SURVEY = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "ASKER_HAVE_SENT_SURVEY", Message: "Asker have sent survey"}
var ERROR_ASKER_VOTED_FAV_TASKER = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "ASKER_VOTED_FAV_TASKER", Message: "Asker voted fav tasker"}
var ERROR_TOKEN_EXPIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_UNAUTHORIZED, ErrorCode: "TOKEN_EXPIRED", Message: "Token expired"}
var ERROR_WEBSOCKET_ERROR = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "WEBSOCKET_ERROR", Message: "Websocket error"}
var ERROR_FORCED_UPDATE_PASSWORD = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "FORCED_UPDATE_PASSWORD",
	Message:    "Force update password",
	ErrorText: &modelService.ServiceText{
		Vi: "",
		En: "",
		Ko: "",
		Th: "",
		Id: "",
	},
}
var ERROR_TASK_STATUS_NOT_ALLOW_VIEW_TASK_LIST = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASK_STATUS_NOT_ALLOW_VIEW_TASK_LIST", Message: "Task status not allow view task list"}

var ERROR_NOT_HAVE_PERMISSION_GET_CHECKLIST = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "ERROR_NOT_HAVE_PERMISSION_GET_CHECKLIST",
	Message:    "Not permission get checklist",
	ErrorText: &modelService.ServiceText{
		Vi: "Bạn không có quyền xem danh sách công việc",
		Ko: "You do not have permission to view the task checklist",
		En: "You do not have permission to view the task checklist",
		Th: "คุณไม่ได้รับอนุญาตให้ดูรายการงาน",
		Id: "You do not have permission to view the task checklist",
	},
}

var ERROR_TASKER_CONFLICT_TIME = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "TASKER_CONFLICT_TIME",
	Message:    "Tasker conflict time",
	ErrorText: &modelService.ServiceText{
		Vi: "Nhân viên khác trình làm việc với nhân viên khác trình",
		En: "Tasker conflict time",
		Th: "Tasker conflict time",
		Id: "Tasker conflict time",
		Ko: "Tasker conflict time",
	},
}

var ERROR_LOCATION_INFO_INVALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "LOCATION_INFO_INVALID",
	Message:    "Location info invalid",
	ErrorText: &modelService.ServiceText{
		Vi: "Thông địa chỉ không hợp lệ",
		En: "Invalid address information",
		Th: "ข้อมูลที่อยู่ไม่ถูกต้อง",
		Id: "Informasi alamat tidak valid",
		Ko: "유효하지 않은 주소 정보",
	},
}

var ERROR_FAIRY_TALE_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "FAIRY_TALE_NOT_FOUND",
	Message:    "Fairy tale not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Câu truyện này không được tìm thấy",
		En: "Fairy tale not found",
		Th: "ไม่พบธนาคาร",
		Id: "Fairy tale not found",
		Ko: "Fairy tale not found",
	},
}

var ERROR_GET_LIST_FAIRY_TALE_FAILED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "GET_LIST_FAIRY_TALE_FAILED",
	Message:    "Get list fairy tale failed",
	ErrorText: &modelService.ServiceText{
		Vi: "Danh sách câu truyện không được tìm thấy",
		En: "Get list fairy tale failed",
		Th: "ไม่พบธนาคาร",
		Id: "Get list fairy tale failed",
		Ko: "Get list fairy tale failed",
	},
}

var ERROR_LOCATION_ADDRESS_IN_USE = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "ERROR_LOCATION_ADDRESS_IN_USE",
	Message:    "The location has already been used, please choose another location",
	ErrorText: &modelService.ServiceText{
		Vi: "Địa chỉ đã được sử dụng, vui lòng chọn địa chỉ khác.",
		En: "The location has already been used, please choose another location",
		Th: "สถานที่ถูกใช้งานแล้ว กรุณาเลือกสถานที่ใหม่",
		Id: "Lokasi sudah digunakan, silakan pilih lokasi lain.",
		Ko: "위치가 이미 사용되었습니다. 다른 위치를 선택해 주세요.",
	},
}

var ERROR_COMING_SOON = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "COMING_SOON",
	Message:    "Coming soon",
	ErrorText: &modelService.ServiceText{
		Vi: "Sắp ra mắt",
		Ko: "Coming soon",
		Th: "เร็วๆ นี้",
		En: "Coming soon",
		Id: "Segera hadir",
	},
}

var ERROR_USER_CHECKED_TODAY = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "ERROR_USER_CHECKED_TODAY",
	Message:    "You have checked in today. Wait until tomorrow.",
	ErrorText: &modelService.ServiceText{
		Vi: "Bạn đã điểm danh hôm nay. Hãy chờ đến ngày mai.",
		Ko: "오늘 체크인을 완료하셨습니다. 내일까지 기다려주세요.",
		En: "You have checked in today. Wait until tomorrow.",
		Th: "คุณได้เช็คอินวันนี้แล้ว กรุณารอจนถึงวันพรุ่งนี้",
		Id: "Anda telah check-in hari ini. Tunggu hingga besok.",
	},
}

var ERROR_BPAY_INSUFFICIENT = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "ERROR_BPAY_INSUFFICIENT",
	Message:    "BPay insufficient",
	ErrorText: &modelService.ServiceText{
		Vi: "bPay không đủ",
		En: "bPay insufficient",
		Th: "bPay ไม่พอ",
		Id: "bPay tidak cukup",
		Ko: "bPay 렌더",
	},
}

var ERROR_LEVEL_INVALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "LEVEL_INVALID",
	Message:    "Level invalid",
	ErrorText: &modelService.ServiceText{
		Vi: "Thông tin cấp bậc không hợp lệ",
		En: "Level invalid",
		Th: "Level ไม่ถูกต้อง",
		Id: "Level tidak valid",
		Ko: "Level 렌더",
	},
}

var ERROR_MEMBER_IN_VALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "MEMBER_IN_VALID",
	Message:    "Member in valid",
	ErrorText: &modelService.ServiceText{
		Vi: "Nhân viên không hợp lệ",
		En: "Member in valid",
		Th: "Member ไม่ถูกต้อง",
		Id: "Member tidak valid",
		Ko: "Member 렌더",
	},
}

var ERROR_BUSINESS_LEVEL_AMOUNT_INVALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "BUSINESS_LEVEL_AMOUNT_INVALID",
	Message:    "Business level amount invalid",
	ErrorText: &modelService.ServiceText{
		Vi: "Số tiền không hợp lệ",
		En: "Business level amount invalid",
		Th: "จำนวนระดับธุรกิจไม่ถูกต้อง",
		Id: "Jumlah tingkat bisnis tidak valid",
		Ko: "비즈니스 레벨 금액이 유효하지 않습니다",
	},
}

var ERROR_BUSINESS_ID_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "BUSINESS_ID_REQUIRED",
	Message:    "Business id required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu id doanh nghiệp",
		En: "Business id required",
		Th: "ต้องการรหัสธุรกิจ",
		Id: "ID bisnis diperlukan",
		Ko: "사업자 ID가 필요합니다",
	},
}

var ERROR_BUSINESS_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "BUSINESS_NOT_FOUND",
	Message:    "Business not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy doanh nghiệp",
		En: "Business not found",
		Th: "ไม่พบธุรกิจ",
		Id: "Bisnis tidak ditemukan",
		Ko: "사업체를 찾을 수 없습니다",
	},
}

var ERROR_BUSINESS_MEMBER_ID_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "BUSINESS_MEMBER_ID_REQUIRED",
	Message:    "Business member id required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu id thành viên doanh nghiệp",
		En: "Business member id required",
		Th: "ต้องการรหัสสมาชิกธุรกิจ",
		Id: "ID anggota bisnis diperlukan",
		Ko: "비즈니스 회원 ID가 필요합니다",
	},
}

var ERROR_MONTH_AND_YEAR_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "MONTH_AND_YEAR_REQUIRED",
	Message:    "Month and year required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu tháng và năm",
		En: "Month and year required",
		Th: "ต้องการเดือนและปี",
		Id: "Bulan dan tahun diperlukan",
		Ko: "월과 연도가 필요합니다",
	},
}

var ERROR_BUSINESS_MEMBER_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "BUSINESS_MEMBER_NOT_FOUND",
	Message:    "Business member not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy thành viên doanh nghiệp",
		En: "Business member not found",
		Th: "ไม่พบสมาชิกธุรกิจ",
		Id: "Anggota bisnis tidak ditemukan",
		Ko: "비즈니스 회원을 찾을 수 없습니다",
	},
}

var ERROR_BUSINESS_LEVEL_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "BUSINESS_LEVEL_NOT_FOUND",
	Message:    "Business level not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy mã doanh nghiệp",
		En: "Business level not found",
		Th: "ไม่พบระดับธุรกิจ",
		Id: "Tingkat bisnis tidak ditemukan",
		Ko: "비즈니스 레벨을 찾을 수 없습니다",
	},
}

var ERROR_BUSINESS_LEVEL_ID_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "BUSINESS_LEVEL_ID_REQUIRED",
	Message:    "Business level id required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu mã cấp doanh nghiệp",
		En: "Business level id required",
		Th: "ต้องการรหัสระดับธุรกิจ",
		Id: "Tingkat bisnis tidak ditemukan",
		Ko: "비즈니스 레벨을 찾을 수 없습니다",
	},
}

var ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "RESOURCE_NOT_BELONG_TO_BUSINESS",
	Message:    "Resource not belong to business",
	ErrorText: &modelService.ServiceText{
		Vi: "Tài nguyên không thuộc về doanh nghiệp",
		En: "Resource not belong to business",
		Th: "ทรัพยากรไม่เป็นของธุรกิจ",
		Id: "Sumber daya tidak dimiliki oleh bisnis",
		Ko: "리소스가 비즈니스에 속하지 않습니다",
	},
}

var ERROR_REVOKE_MEMBER_FAILED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "REVOKE_MEMBER_FAILED",
	Message:    "Revoke member failed",
	ErrorText: &modelService.ServiceText{
		Vi: "Thu hồi thành viên thất bại",
		En: "Revoke member failed",
		Th: "การเพิกถอนสมาชิกล้มเหลว",
		Id: "Gagal mencabut anggota",
		Ko: "회원 해제가 실패했습니다",
	},
}

var ERROR_BUSINESS_EMAIL_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "USER_EMAIL_NOT_FOUND",
	Message:    "Business email not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy email doanh nghiệp",
		En: "Business email not found",
		Th: "ไม่พบอีเมลธุรกิจ",
		Id: "Email bisnis tidak ditemukan",
		Ko: "비즈니스 이메일을 찾을 수 없습니다",
	},
}

var ERROR_USER_EMAIL_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "USER_EMAIL_NOT_FOUND",
	Message:    "Business email not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy email doanh nghiệp",
		En: "Business email not found",
		Th: "ไม่พบอีเมลธุรกิจ",
		Id: "Email bisnis tidak ditemukan",
		Ko: "비즈니스 이메일을 찾을 수 없습니다",
	},
}

var ERROR_CAN_NOT_SEND_EMAIL = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "CAN_NOT_SEND_EMAIL",
	Message:    "Can not send email",
	ErrorText: &modelService.ServiceText{
		Vi: "Không thể gửi email",
		En: "Can not send email",
		Th: "ไม่สามารถส่งอีเมลได้",
		Id: "Tidak dapat mengirim email",
		Ko: "이메일을 보낼 수 없습니다",
	},
}

var HAVE_PROCESS_TOPUP_RUNNING = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "HAVE_PROCESS_TOPUP_RUNNING",
	Message:    "Top-up process is already running",
	ErrorText: &modelService.ServiceText{
		Vi: "Đang có lệnh nạp tiền được xử lý",
		En: "A top-up process is already in progress",
		Th: "มีการประมวลผลการเติมเงินอยู่",
		Id: "Sedang menjalankan proses top-up",
		Ko: "충전 프로세스가 진행 중입니다",
	},
}

var ERROR_ADD_USER_BPOINT_FAILED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "ADD_USER_BPOINT_FAILED",
	Message:    "Add user bPoint failed",
	ErrorText: &modelService.ServiceText{
		Vi: "Thêm bPoint cho người dùng thất bại",
		En: "Add user bPoint failed",
		Th: "เพิ่ม bPoint ให้ผู้ใช้ล้มเหลว",
		Id: "Gagal menambahkan bPoint untuk pengguna",
		Ko: "사용자 b포인트 추가 실패",
	},
}
var ERROR_TASK_ID_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "TASK_ID_REQUIRED",
	Message:    "Task id is required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu mã công việc.",
		Ko: "작업 ID가 필요합니다.",
		En: "Task id is required.",
		Th: "จำเป็นต้องมีรหัสงาน.",
		Id: "ID tugas diperlukan.",
	},
}

var ERROR_MISSING_CALL_INFORMATION = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "MISSING_CALL_INFORMATION",
	Message:    "Missing call information",
	ErrorText: &modelService.ServiceText{
		Vi: "Thông tin cuộc gọi bị thiếu",
		Ko: "통화 정보 누락",
		En: "Missing call information",
		Th: "ข้อมูลการโทรที่หายไป",
		Id: "Informasi panggilan yang hilang",
	},
}

var ERROR_TASKER_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "TASKER_NOT_FOUND",
	Message:    "Tasker not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy thông tin Tasker",
		En: "Tasker not found",
		Th: "Tasker not found",
		Id: "Tasker not found",
		Ko: "Tasker not found",
	},
}

var ERROR_TASKER_IS_BLOCKED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "TASKER_IS_BLOCKED",
	Message:    "Tasker is blocked",
	ErrorText: &modelService.ServiceText{
		Vi: "Tasker này hiện tại đang tạm nghỉ. Bạn vui lòng đặt lịch Tasker yêu thích khác, hoặc có thể thao tác đặt lịch lại sau. Xin cảm ơn.",
		En: "This Tasker is currently on a break. Please schedule another preferred Tasker or try booking again later. Thank you.",
		Ko: "해당 Tasker님은 현재 휴식 중이십니다. 번거로우시겠지만 고객님께서는 다른 선호하는 Tasker를 예약하시거나, 나중에 다시 예약해 주시기 바랍니다. 감사합니다.",
		Id: "Tasker ini sedang beristirahat sementara. Silakan memesan Tasker favorit lain, atau Anda dapat melakukan pemesanan kembali nanti. Terima kasih.",
		Th: "ขออภัยค่ะ ขณะนี้ผู้ให้บริการท่านนี้กำลังหยุดพักชั่วคราว กรุณาจองผู้ให้บริการที่ท่านต้องการท่านอื่น หรือทำการจองใหม่ในภายหลัง ขอขอบคุณค่ะ",
	},
}

var ERROR_GIFT_IS_USED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "GIFT_IS_USED",
	Message:    "Gift is used",
	ErrorText: &modelService.ServiceText{
		Vi: "Quà tặng đã được sử dụng",
		En: "Gift is used",
		Th: "ของขวัญถูกใช้แล้ว",
		Id: "Hadiah sudah digunakan",
		Ko: "선물이 사용되었습니다",
	},
}

var ERROR_GIFT_IS_EXPIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "GIFT_IS_EXPIRED",
	Message:    "Gift is expired",
	ErrorText: &modelService.ServiceText{
		Vi: "Quà tặng đã hết hạn",
		En: "Gift is expired",
		Th: "ของขวัญหมดอายุแล้ว",
		Id: "Hadiah sudah kedaluwarsa",
		Ko: "선물이 만료되었습니다",
	},
}

var ERROR_CAN_NOT_LIXI_TO_COMPANY = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "CAN_NOT_LIXI_TO_COMPANY",
	Message:    "Can not lixi to company",
	ErrorText: &modelService.ServiceText{
		Vi: "Không thể lì xì cho doanh nghiệp",
		En: "Can not lixi to company",
		Th: "ไม่สามารถให้ของขวัญให้กับบริษัท",
		Id: "Tidak dapat memberikan hadiah kepada perusahaan",
		Ko: "회사에 선물을 줄 수 없습니다",
	},
}

var ERROR_GIFT_INVALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "GIFT_INVALID",
	Message:    "Gift invalid",
	ErrorText: &modelService.ServiceText{
		Vi: "Quà tặng không hợp lệ",
		En: "Gift invalid",
		Th: "ของขวัญไม่ถูกต้อง",
		Id: "Hadiah tidak valid",
		Ko: "선물이 유효하지 않습니다",
	},
}

var ERROR_NOT_HAVE_PERMISSION = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "ERROR_NOT_HAVE_PERMISSION",
	Message:    "You do not have permission to access this resource",
	ErrorText: &modelService.ServiceText{
		Vi: "Bạn không có quyền truy cập vào tài nguyên này",
		En: "You do not have permission to access this resource",
		Th: "คุณไม่มีสิทธิ์เข้าถึงทรัพยากรนี้",
		Id: "Anda tidak memiliki izin untuk mengakses sumber daya ini",
		Ko: "이 리소스에 접근할 권한이 없습니다",
	},
}

var ERROR_ASKER_IN_TASKER_BLACKLIST = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "ASKER_IN_TASKER_BLACKLIST",
	Message:    "Asker in Tasker's blacklist",
	ErrorText: &modelService.ServiceText{
		Vi: "Rất tiếc, Tasker hiện chưa sẵn sàng nhận công việc. Cảm ơn bạn đã thông cảm.",
		En: "Sorry, Tasker is not available to accept tasks right now. Thank you for your understanding.",
		Th: "ขออภัย, Tasker ขณะนี้ไม่พร้อมรับงาน ขอบคุณที่เข้าใจ",
		Id: "Maaf, Tasker saat ini tidak tersedia untuk menerima pekerjaan. Terima kasih atas pengertiannya.",
		Ko: "죄송합니다, Tasker는 현재 작업을 받을 수 없습니다. 양해해 주셔서 감사합니다.",
	},
}

var ERROR_GAME_CAMPAIGN_NOT_STARTED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "GAME_CAMPAIGN_NOT_STARTED",
	Message:    "Game campaign not started",
	ErrorText: &modelService.ServiceText{
		Vi: "Game campaign chưa bắt đầu",
		En: "Game campaign not started",
		Th: "เกมคอมเพจยังไม่เริ่มต้น",
		Id: "Game campaign belum dimulai",
		Ko: "게임 캠페인이 아직 시작되지 않았습니다",
	},
}

var ERROR_GAME_CAMPAIGN_ENDED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "GAME_CAMPAIGN_ENDED",
	Message:    "Game campaign ended",
	ErrorText: &modelService.ServiceText{
		Vi: "Game campaign đã kết thúc",
		En: "Game campaign ended",
		Th: "เกมคอมเพจเสร็จสิ้น",
		Id: "Game campaign telah berakhir",
		Ko: "게임 캠페인이 종료되었습니다",
	},
}

var ERROR_INCENTIVE_JUST_REDEEM_ONE_TIME = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "INCENTIVE_JUST_REDEEM_ONE_TIME",
	Message:    "Incentive just redeem one time",
	ErrorText: &modelService.ServiceText{
		Vi: "Bạn đã hết lượt đổi gói ưu đãi này!",
		En: "You’ve used up your redemption for this offer.",
		Th: "คุณแลกรับสิทธิ์นี้ครบแล้ว",
		Id: "enukaran untuk penawaran ini sudah kamu gunakan.",
		Ko: "이 혜택의 교환 가능 횟수를 모두 사용하셨습니다.",
	},
}

var ERROR_DETAIL_BEAUTY_CARE_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "DETAIL_BEAUTY_CARE_REQUIRED",
	Message:    "Detail beauty care is required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu chi tiết dịch vụ làm đẹp",
		En: "Detail beauty care is required",
		Th: "ต้องการรายละเอียดการดูแลสุขภาพสดใส",
		Id: "Detail beauty care is required",
		Ko: "Detail beauty care is required",
	},
}

var ERROR_BEAUTY_QUESTION_INFOS_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "BEAUTY_QUESTION_INFOS_REQUIRED",
	Message:    "Beauty question infos is required",
	ErrorText: &modelService.ServiceText{
		Vi: "Yêu cầu thông tin câu hỏi làm đẹp",
		En: "Beauty question infos is required",
		Th: "ต้องการข้อมูลคำถามการดูแลสุขภาพสดใส",
		Id: "Beauty question infos is required",
		Ko: "Beauty question infos is required",
	},
}

var ERROR_TASK_INVALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "TASK_INVALID",
	Message:    "Task invalid",
	ErrorText: &modelService.ServiceText{
		Vi: "Công việc không hợp lệ",
		En: "Task invalid",
		Th: "งานไม่ถูกต้อง",
		Id: "Tugas tidak valid",
		Ko: "작업이 유효하지 않습니다",
	},
}

var ERROR_BUNDLE_VOUCHER_ID_REQUIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_CLIENT,
	ErrorCode:  "BUNDLE_VOUCHER_ID_REQUIRED",
	Message:    "Bundle voucher id is required",
	ErrorText: &modelService.ServiceText{
		Vi: "Cần có mã phiếu giảm giá gói",
		En: "Bundle voucher id is required",
		Th: "ต้องระบุรหัสบัตรกำนัลแบบกลุ่ม",
		Id: "ID voucher bundle diperlukan",
		Ko: "번들 바우처 ID가 필요합니다",
	},
}
var ERROR_BUNDLE_VOUCHER_NOT_FOUND = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_NOT_FOUND,
	ErrorCode:  "BUNDLE_VOUCHER_NOT_FOUND",
	Message:    "Bundle voucher not found",
	ErrorText: &modelService.ServiceText{
		Vi: "Không tìm thấy phiếu giảm giá gói",
		En: "Bundle voucher not found",
		Th: "ไม่พบบัตรกำนัลแบบกลุ่ม",
		Id: "Voucher bundle tidak ditemukan",
		Ko: "번들 바우처를 찾을 수 없습니다",
	},
}
var ERROR_CREATE_REDEEM_BUNDLE_VOUCHER_TRANSACTION_FAILED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "CREATE_REDEEM_BUNDLE_VOUCHER_TRANSACTION_TRANSACTION_FAILED",
	Message:    "Create redeem bundle voucher transaction failed.",
	ErrorText: &modelService.ServiceText{
		Vi: "Tạo giao dịch đổi phiếu giảm giá gói thất bại.",
		En: "Create redeem bundle voucher transaction failed.",
		Th: "การสร้างธุรกรรมแลกบัตรกำนัลแบบกลุ่มล้มเหลว",
		Id: "Gagal membuat transaksi penukaran voucher bundle.",
		Ko: "번들 바우처 교환 거래 생성에 실패했습니다.",
	},
}
var ERROR_BUNDLE_VOUCHER_HAS_STOPPED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "BUNDLE_VOUCHER_HAS_STOPPED",
	Message:    "Bundle voucher has stopped.",
	ErrorText: &modelService.ServiceText{
		Vi: "Phiếu giảm giá gói đã dừng.",
		En: "Bundle voucher has stopped.",
		Th: "บัตรกำนัลแบบกลุ่มได้หยุดแล้ว",
		Id: "Voucher bundle telah dihentikan.",
		Ko: "번들 바우처가 중단되었습니다.",
	},
}
var ERROR_BUNDLE_VOUCHER_EXPIRED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "BUNDLE_VOUCHER_EXPIRED",
	Message:    "Bundle voucher has expired.",
	ErrorText: &modelService.ServiceText{
		Vi: "Phiếu giảm giá gói đã hết hạn.",
		En: "Bundle voucher has expired.",
		Th: "บัตรกำนัลแบบกลุ่มหมดอายุแล้ว",
		Id: "Voucher bundle telah kedaluwarsa.",
		Ko: "번들 바우처가 만료되었습니다.",
	},
}

var ERROR_YOUR_TURN_CAN_NOT_REDEEM_GIFT = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "YOUR_TURN_CAN_NOT_REDEEM_GIFT",
	Message:    "Your turn can not redeem gift",
	ErrorText: &modelService.ServiceText{
		Vi: "Bạn đã hết lượt đổi Hộp quà bí mật này!",
		En: "You’ve used up your redemption for this Secret box.",
		Th: "คุณใช้สิทธิ์แลกรับ กล่องปริศนา นี้ครบแล้ว",
		Id: "Anda telah menggunakan semua kesempatan untuk menukarkan Kotak rahasia ini.",
		Ko: "이 비밀 상자의 교환 가능 횟수를 모두 사용하셨습니다.",
		Ms: "Anda telah menggunakan semua peluang penebusan untuk Kotak rahsia ini.",
	},
}

var ERROR_BUNDLE_VOUCHER_HAS_NO_STOCK = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "BUNDLE_VOUCHER_HAS_NO_STOCK",
	Message:    "Bundle voucher has no stock",
	ErrorText: &modelService.ServiceText{
		Vi: "Hộp quà bí mật này đã hết lượt đổi. Vui lòng chọn gói khác",
		En: "This Secret box has no remaining redemptions. Please select another package.",
		Th: "กล่องปริศนานี้ไม่สามารถแลกรับได้อีก กรุณาเลือกแพ็กเกจอื่น",
		Id: "Kotak rahasia ini sudah tidak bisa ditukar lagi. Silakan pilih paket lainnya.",
		Ko: "이 비밀 상자는 더 이상 교환할 수 없습니다. 다른 상품을 선택해 주세요.",
		Ms: "Kotak rahsia ini telah mencapai had penebusan. Sila pilih pakej lain.",
	},
}

var ERROR_BUNDLE_VOUCHER_INVALID = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "ERROR_BUNDLE_VOUCHER_INVALID",
	Message:    "Bundle voucher invalid to redeem",
	ErrorText: &modelService.ServiceText{
		Vi: "Hộp quà bí mật không hợp lệ. Vui lòng thử lại sau.",
		En: "This Secret box is invalid. Please try again later.",
		Th: "กล่องปริศนาไม่สมบูรณ์ กรุณาลองใหม่อีกครั้งภายหลัง",
		Id: "PhiKotak rahasia ini tidak valid. Silakan coba lagi nanti.",
		Ko: "비밀 상자가 유효하지 않습니다. 잠시 후 다시 시도해 주세요.",
		Ms: "Kotak rahsia ini tidak sah. Sila cuba lagi kemudian.",
	},
}

var ERROR_REDEEM_BUNDLE_VOUCHER_FAILED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "REDEEM_BUNDLE_VOUCHER_FAILED",
	Message:    "redeem bundle voucher failed.",
	ErrorText: &modelService.ServiceText{
		Vi: "Đổi Hộp quà bí mật không thành công. Vui lòng thử lại sau.",
		En: "Failed to redeem the Secret box. Please try again later.",
		Th: "การแลกกล่องปริศนาไม่สำเร็จ กรุณาลองใหม่อีกครั้งภายหลัง",
		Id: "Penukaran Kotak rahasia gagal. Silakan coba lagi nanti.",
		Ko: "비밀 상자 교환에 실패했습니다. 잠시 후 다시 시도해 주세요.",
		Ms: "Penebusan Kotak rahsia tidak berjaya. Sila cuba lagi kemudian.",
	},
}

var ERROR_BUNDLE_VOUCHER_LOCKED = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER,
	ErrorCode:  "BUNDLE_VOUCHER_LOCKED",
	Message:    "This gift is currently being redeemed by another request. Please try again shortly.",
	ErrorText: &modelService.ServiceText{
		Vi: "Phiếu giảm giá gói đang được đổi bởi yêu cầu khác. Vui lòng thử lại sau.",
		En: "This gift is currently being redeemed by another request. Please try again shortly.",
		Th: "This gift is currently being redeemed by another request. Please try again shortly.",
		Id: "This gift is currently being redeemed by another request. Please try again shortly.",
		Ko: "This gift is currently being redeemed by another request. Please try again shortly.",
	},
}

var ERROR_USER_NOT_ENOUGH_POINTS_REDEEM_BUNDLE = globalResponse.ResponseErrorCode{
	StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_NOT_ENOUGH_POINTS_REDEEM_BUNDLE",
	Message: "User not enough points.",
	ErrorText: &modelService.ServiceText{
		Vi: "Bạn chưa đủ bPoint để đổi Hộp quà bí mật này. Hãy tích lũy bPoint để khám phá thêm nhiều ưu đãi hấp dẫn!",
		En: "You do not have enough bPoints to redeem this Secret Box. Please earn more bPoints to unlock more great rewards!",
		Th: "คุณไม่มีแต้ม bPoint พอที่จะแลก กล่องปริศนานี้ กรุณาสะสม bPoint เพิ่มเพื่อรับรางวัลดีๆ มากขึ้น!",
		Id: "Anda tidak punya cukup bPoint untuk menukarkan Kotak Rahasia ini. Silakan kumpulkan lebih banyak bPoint untuk membuka lebih banyak hadiah menarik!",
		Ko: "비밀 상자를 교환하기에 bPoint가 부족합니다. 더 많은 멋진 보상을 받으시려면 bPoint를 더 모아 주세요!",
		Ms: " nda tidak cukup bPoint untuk menebus Kotak Rahsia ini. Sila kumpul lebih banyak bPoint untuk buka lebih banyak ganjaran hebat!",
	},
}
