#!/bin/bash

# Hàm để tăng PATCH version
increase_patch_version() {
    local tag="$1"
    if [[ "$tag" =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)(-.+)?$ ]]; then
        local major="${BASH_REMATCH[1]}"
        local minor="${BASH_REMATCH[2]}"
        local patch="${BASH_REMATCH[3]}"
        local suffix="${BASH_REMATCH[4]}"
        patch=$((patch + 1))
        echo "v$major.$minor.$patch${suffix}"
    else
        echo "Invalid tag format"
        return 1
    fi
}

# Hàm để tăng MINOR version
increase_minor_version() {
    local tag="$1"
    if [[ "$tag" =~ ^v([0-9]+)\.([0-9]+)\.([0-9]+)(-.+)?$ ]]; then
        local major="${BASH_REMATCH[1]}"
        local minor="${BASH_REMATCH[2]}"
        local patch="${BASH_REMATCH[3]}"
        local suffix="${BASH_REMATCH[4]}"
        minor=$((minor + 1))
        patch=0
        echo "v$major.$minor.$patch${suffix}"
    else
        echo "Invalid tag format"
        return 1
    fi
}

# Cấu hình GitLab API
GITLAB_URL="https://gitlab.com/api/v4"
GITLAB_TOKEN=$MY_GITLAB_TOKEN
CURRENT_DATE=$(date +"%d/%m/%Y")
YELLOW='\033[1;33m'
NC='\033[0m' # No Color
PROJECT_ID="57828310"
DEPLOYMENT_PROD=go-api-service:
GITLAB_CI="RELEASE_VERSION: "

if [[ "$1" != "patch" && "$1" != "minor" ]]; then
  echo ""
  echo -e "${YELLOW}Tham số thứ 1 chỉ nhập 'patch' hoặc 'minor'${NC}"
  exit 1
fi

if [[ "$2" != "dev" && "$2" != "master" && "$2" != "dev_backup" ]]; then
  echo ""
  echo -e "${YELLOW}Tham số thứ 2 chỉ nhập 'dev' hoặc 'master' hoặc 'dev_backup'${NC}"
  exit 1
fi

echo -ne "${YELLOW}Feature Issue Number (cách nhau khoảng trắng nếu nhiều cái): ${NC}"
read -a FEATURE_ISSUE_IIDS

echo -ne "${YELLOW}Bug Issue Number (cách nhau khoảng trắng nếu nhiều cái): ${NC}"
read -a BUG_ISSUE_IIDS

# Nhập version mới của services-model nếu có
echo -ne "${YELLOW}Cập nhật services-model version: ${NC}"
read SERVICES_MODEL

CURRENT_BRANCH=$(git branch --show-current)

# Tạo branch release mới theo version hiện tại
echo -e "${YELLOW}Đang pull code và tạo branch release mới...${NC}"
git checkout $2
git pull

# Lấy version hiện tại từ file
CURRENT_VERSION=$(grep -o "$DEPLOYMENT_PROD[^ ]*" deploy/k8s/deployment_prod.yml | cut -d':' -f2)
if [[ "$1" =~ "patch" ]]; then
  NEW_VERSION=$(increase_patch_version "$CURRENT_VERSION")
elif [[ "$1" =~ "minor" ]]; then
  NEW_VERSION=$(increase_minor_version "$CURRENT_VERSION")
fi

git checkout -b release-$NEW_VERSION

echo ""
echo -e "${YELLOW}Đang cập nhật file...${NC}"

# Cập nhật dòng trong file
sed -i '' "s|$DEPLOYMENT_PROD$CURRENT_VERSION|$DEPLOYMENT_PROD$NEW_VERSION|" deploy/k8s/deployment_prod.yml
sed -i '' "s|$GITLAB_CI$CURRENT_VERSION|$GITLAB_CI$NEW_VERSION|" .gitlab-ci.yml

# Khởi tạo mảng để lưu danh sách issue
FEATURE_ISSUES=()
BUGS_ISSUES=()

for IID in "${FEATURE_ISSUE_IIDS[@]}"; do
  # Gọi API GitLab để lấy thông tin về issue
  API_RESPONSE=$(curl --header "PRIVATE-TOKEN: $GITLAB_TOKEN" "$GITLAB_URL/projects/$PROJECT_ID/issues/$IID")

  # Kiểm tra và xử lý kết quả từ API
  ISSUE_TITLE=$(echo "$API_RESPONSE" | jq -r '.title')
  ISSUE_TYPE=$(echo "$API_RESPONSE" | jq -r '.type')

  FEATURE_ISSUES+=("- Issue #$IID: $ISSUE_TITLE")
done

for IID in "${BUG_ISSUE_IIDS[@]}"; do
  # Gọi API GitLab để lấy thông tin về issue
  API_RESPONSE=$(curl --header "PRIVATE-TOKEN: $GITLAB_TOKEN" "$GITLAB_URL/projects/$PROJECT_ID/issues/$IID")

  # Kiểm tra và xử lý kết quả từ API
  ISSUE_TITLE=$(echo "$API_RESPONSE" | jq -r '.title')
  ISSUE_TYPE=$(echo "$API_RESPONSE" | jq -r '.type')

  BUGS_ISSUES+=("- Fix issue #$IID: $ISSUE_TITLE")
done

# Thêm danh sách issue vào phần '- Features:'
FEATURE_CONTENT="- Features:"
for issue in "${FEATURE_ISSUES[@]}"; do
  FEATURE_CONTENT+="
  $issue"
done

# Thêm danh sách issue vào phần '- Bug fixes:'
BUGS_CONTENT="- Bug fixes:"
for issue in "${BUGS_ISSUES[@]}"; do
  BUGS_CONTENT+="
  $issue"
done

# Nội dung mới
NEW_CONTENT="Version $NEW_VERSION
- Date: $CURRENT_DATE
- Improvements:
$FEATURE_CONTENT
$BUGS_CONTENT
"

# Thêm nội dung mới vào đầu file
echo "$NEW_CONTENT" | cat - Release-notes > temp && mv temp Release-notes

if [ "$SERVICES_MODEL" ]; then
  echo ""
  echo -e "${YELLOW}Đang cập nhật services-model...${NC}"
  GO111MODULE=on go get gitlab.com/btaskee/go-services-model-v2@$SERVICES_MODEL
fi

# Push code lên
echo ""
echo -ne "${YELLOW}Push code lên Gitlab ? (y/n) ${NC}"
read IS_PUSH_CODE
if [[ "$IS_PUSH_CODE" =~ "y" ]]; then
  git add -A
  git commit -m "Release $NEW_VERSION"
  git push

  echo ""
  echo -ne "${YELLOW}Trở về branch hiện tại $CURRENT_BRANCH ? (y/n) ${NC}"
  read IS_BACK_BRANCH
  if [[ "$IS_BACK_BRANCH" =~ "y" ]]; then
    git checkout $CURRENT_BRANCH
  fi
fi