package addActivatedServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func AddActivatedServices(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode
	}
	errCode = updateActivatedServices(reqBody.UserId, reqBody.ActivatedServices)
	if errCode != nil {
		return errCode
	}

	return nil
}
