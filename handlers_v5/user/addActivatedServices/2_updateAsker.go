package addActivatedServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func updateActivatedServices(askerId string, listService []string) *globalResponse.ResponseErrorCode {
	if IsExist, _ := modelUser.IsExistById(local.ISO_CODE, askerId); !IsExist {
		return &lib.ERROR_USER_NOT_FOUND
	}
	servicesToAdd := make([]bson.M, 0, len(listService))
	for _, service := range listService {
		servicesToAdd = append(servicesToAdd, bson.M{
			"serviceName": service,
		})
	}
	queryUpdate := bson.M{
		"$addToSet": bson.M{
			"activatedServices": bson.M{
				"$each": servicesToAdd,
			},
		},
	}
	_, err := modelUser.UpdateOneById(local.ISO_CODE, askerId, queryUpdate)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}
	return nil
}
