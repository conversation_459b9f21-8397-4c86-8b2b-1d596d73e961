package getBeautyAccessories

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetBeautyAccessories(req *model.ApiRequest) (any, *globalResponse.ResponseErrorCode) {
	// validate request
	err := validate(req)
	if err != nil {
		return nil, err
	}

	// get beauty accessories
	beautyAccessories, err := getAccessories(req)
	if err != nil {
		return nil, err
	}
	return beautyAccessories, nil
}
