package getBeautyAccessories

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(req *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// validate detail beauty care
	if req.DetailBeautyCare == nil && req.DetailMakeup == nil && req.DetailHairStyling == nil && req.DetailNail == nil {
		return &lib.ERROR_DETAIL_BEAUTY_CARE_REQUIRED
	}

	return nil
}
