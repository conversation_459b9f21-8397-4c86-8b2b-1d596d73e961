package getBeautyAccessories

import (
	"sort"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getAccessories(req *model.ApiRequest) (any, *globalResponse.ResponseErrorCode) {
	service, errCode := getServiceByName(req.ServiceName)
	if errCode != nil {
		return nil, errCode
	}
	return getAccessoriesBeauty(req, service)

	return nil, nil
}

func getServiceByName(serviceName string) (*modelService.Service, *globalResponse.ResponseErrorCode) {
	var service *modelService.Service
	if serviceName == "" {
		serviceName = globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE
	}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": serviceName}, bson.M{"detailService.beautyCare.accessories": 1,
		"detailService.makeup.accessories": 1, "detailService.hairStyling.accessories": 1, "detailService.nail.accessories": 1}, &service)
	if service == nil {
		return nil, &lib.ERROR_SERVICE_NOT_FOUND
	}
	return service, nil
}

func getAccessoriesBeauty(req *model.ApiRequest, service *modelService.Service) (any, *globalResponse.ResponseErrorCode) {
	beautyAccessories := []map[string]any{}
	// map task detail service
	mapTaskDetailService := map[string]bool{}
	// map task option
	mapTaskOption := map[string]bool{}
	// map task detail service and option
	var taskPackages []*modelTask.TaskDetailBeautyCarePackage
	if req.DetailMakeup != nil {
		taskPackages = req.DetailMakeup.Packages
	} else if req.DetailHairStyling != nil {
		taskPackages = req.DetailHairStyling.Packages
	} else if req.DetailNail != nil {
		taskPackages = req.DetailNail.Packages
	} else {
		taskPackages = req.DetailBeautyCare.Packages
	}
	for _, detail := range taskPackages {
		for _, service := range detail.MainServices {
			mapTaskDetailService[service.Name] = true
			for _, option := range service.Options {
				mapTaskOption[option.Name] = true
			}
			// check style
			if service.GetStyle() != nil {
				for _, styleOption := range service.GetStyle().GetOptions() {
					mapTaskOption[styleOption.Name] = true
				}
			}
		}
	}
	// get beauty accessories
	var serviceAccessories []*modelService.ServiceDetailServiceBeautyCareAccessories
	if req.DetailMakeup != nil {
		serviceAccessories = service.GetDetailService().GetMakeup().GetAccessories()
	} else if req.DetailHairStyling != nil {
		serviceAccessories = service.GetDetailService().GetHairStyling().GetAccessories()
	} else if req.DetailNail != nil {
		serviceAccessories = service.GetDetailService().GetNail().GetAccessories()
	} else {
		serviceAccessories = service.GetDetailService().GetBeautyCare().GetAccessories()
	}
	mapAccessoryDisabled := map[string]bool{}
	for _, access := range serviceAccessories {
		accessory := map[string]any{
			"name":       access.GetTitle().GetEn(),
			"title":      access.Title,
			"image":      access.Image,
			"weight":     access.Weight,
			"isDisabled": true,
		}
		if _, ok := mapAccessoryDisabled[accessory["name"].(string)]; !ok {
			mapAccessoryDisabled[accessory["name"].(string)] = true
		}
		if len(access.InServiceName) > 0 {
			for _, service := range access.InServiceName {
				if _, ok := mapTaskDetailService[service]; ok {
					if len(access.InOptionName) > 0 {
						for _, option := range access.InOptionName {
							if _, ok := mapTaskOption[option]; ok {
								accessory["isDisabled"] = false
								mapAccessoryDisabled[accessory["name"].(string)] = false
								break
							}
						}
					} else {
						accessory["isDisabled"] = false
						mapAccessoryDisabled[accessory["name"].(string)] = false
					}
					beautyAccessories = append(beautyAccessories, accessory)
					break
				}
			}
		}
	}
	// sort beautyAccessories by weight
	sort.Slice(beautyAccessories, func(i, j int) bool {
		return beautyAccessories[i]["weight"].(float64) < beautyAccessories[j]["weight"].(float64)
	})
	// remove duplication
	return removeDuplication(beautyAccessories, mapAccessoryDisabled), nil
}

func removeDuplication(beautyAccessories []map[string]any, mapAccessoryDisabled map[string]bool) []map[string]any {
	newBeautyAccessories := []map[string]any{}
	mapAccessory := map[string]bool{}
	for _, accessory := range beautyAccessories {
		accessoryName := cast.ToString(accessory["name"])
		if mapAccessory[accessoryName] {
			continue
		}
		mapAccessory[accessoryName] = true
		accessory["isDisabled"] = mapAccessoryDisabled[accessoryName]
		newBeautyAccessories = append(newBeautyAccessories, accessory)
	}
	return newBeautyAccessories
}
