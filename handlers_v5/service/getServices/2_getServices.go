package getServices

import (
	"encoding/json"

	settingV2 "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/settings/v2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

func getServiceByIds(isUserTester bool, reqBody *model.ApiRequest) []map[string]interface{} {
	now := globalLib.GetCurrentTime(local.TimeZone)
	fields := bson.M{
		"_id":                          1,
		"icon":                         1,
		"name":                         1,
		"text":                         1,
		"city":                         1,
		"status":                       1,
		"weight":                       1,
		"tip":                          1,
		"postingLimits":                1,
		"requirements":                 1,
		"detail":                       1,
		"detailAreaV3":                 1,
		"detailLaundry":                1,
		"detailCooking":                1,
		"defaultTaskTime":              1,
		"shortText":                    1,
		"workingProcessV2":             1,
		"detailSofa":                   1,
		"linkForm":                     1,
		"isNewService":                 1,
		"maximumPSI":                   1,
		"estimatedPriceDefault":        1,
		"tetBookingDates":              1,
		"minTaskOfSubscription":        1,
		"requireAskerVersion":          1,
		"disinfectionDetail":           1,
		"detailSofaTH":                 1,
		"isOpenGoMarketDefault":        1,
		"goMarketWithStore":            1,
		"estimatedAmountConfig":        1,
		"premiumOptions":               1,
		"taskServiceId":                1,
		"optional":                     1,
		"thumbnail":                    1,
		"detailService":                1,
		"relatedServiceIds":            1,
		"isSubscription":               1,
		"durationByArea":               1,
		"priceSetting.minPostTaskTime": 1,
		"ecoOptions":                   1,
		"addons":                       1,
		"monthlyOptions":               1,

		"priceSetting.costForChooseTasker": 1,
	}
	query := bson.M{
		"_id": bson.M{
			"$in": reqBody.ServiceIds,
		},
	}
	var services []*modelService.Service
	result := make([]map[string]interface{}, 0)

	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &services)
	if len(services) == 0 {
		return result
	}

	for _, service := range services {
		b, _ := json.Marshal(service)
		mapService := make(map[string]interface{})
		json.Unmarshal(b, &mapService)
		if len(service.GetCity()) > 0 {
			var arrayCity []string
			var arrayCityInfo []*modelService.ServiceCity
			var limitBookTaskDates []map[string]interface{}
			for _, c := range service.City {
				arrayCity = append(arrayCity, c.Name)
				arrayCityInfo = append(arrayCityInfo, c)
				if c.LimitBookTaskDate != 0 {
					limitBookTaskDates = append(limitBookTaskDates, map[string]interface{}{
						"city":              c.Name,
						"limitBookTaskDate": c.LimitBookTaskDate,
					})
				}
			}

			if len(limitBookTaskDates) > 0 {
				mapService["limitBookTaskDates"] = limitBookTaskDates
			}

			if service.Name == globalConstant.SERVICE_KEY_NAME_CHILD_CARE || service.Name == globalConstant.SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION ||
				service.Name == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION || service.Name == globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING ||
				service.Name == globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE {
				mapService["city"] = arrayCityInfo
			} else {
				mapService["city"] = arrayCity
			}
		}

		if service.Tip != nil {
			mapService["tip"] = service.GetTip().GetTip()
			if len(service.GetTip().GetRequirements()) > 0 {
				// Covid options stop working type 4,5
				var requirements []*modelService.ServiceTipRequirements
				for _, req := range service.Tip.Requirements {
					if req.Type != 4 && req.Type != 5 {
						requirements = append(requirements, req)
					}
				}
				if len(requirements) > 0 {
					mapService["requirements"] = requirements
				}
			}
		}

		if service.Text.En == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT {
			if service.GoMarketWithStore != nil {
				if (isUserTester && service.GoMarketWithStore.IsTesting) || service.GoMarketWithStore.Status == globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE {
					mapService["goMarketWithStore"] = &modelService.ServiceGoMarketWithStore{
						Status: globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE,
					}
				} else {
					delete(mapService, "goMarketWithStore")
				}
			}
		}

		if service.Text.En == globalConstant.SERVICE_NAME_DISINFECTION {
			if service.DisinfectionDetail != nil {
				// cities := []*modelService.ServiceDisinfectionDetailCity{}
				for _, city := range service.DisinfectionDetail.City {
					areaToShowInAppAsker := []*modelService.ServiceDisinfectionDetailCityArea{}
					for _, area := range city.Area {
						if area.IsShowInAppAsker {
							areaToShowInAppAsker = append(areaToShowInAppAsker, area)
						}
					}
					// Update new area list in each city
					// Update city.Area cũng đã update trong DisinfectionDetail. Vì city là con trỏ tới service.DisinfectionDetail.City
					city.Area = areaToShowInAppAsker
				}
			}
			mapService["disinfectionDetail"] = service.DisinfectionDetail
		}

		if service.Text.En == globalConstant.SERVICE_NAME_CARPET_CLEANING {
			if service.DetailService != nil && service.DetailService.CarpetCleaning != nil {
				// cities := []*modelService.ServiceDisinfectionDetailCity{}
				for _, city := range service.DetailService.CarpetCleaning.City {
					areaToShowInAppAsker := []*modelService.ServiceDetailServiceCarpetCleaningCityArea{}
					for _, area := range city.Area {
						if area.IsShowInAppAsker {
							areaToShowInAppAsker = append(areaToShowInAppAsker, area)
						}
					}
					// Update new area list in each city
					// Update city.Area cũng đã update trong CarpetCleaning. Vì city là con trỏ tới service.CarpetCleaning.City
					city.Area = areaToShowInAppAsker
				}
			}
			mapService["detailService"] = service.DetailService
		}

		if globalLib.IsHomeMovingServiceByKeyName(service.Name) {
			settingV2.RefactorHomeMovingService(service)
			mapService["detailService"] = service.DetailService
		}

		if globalLib.IsBeautyCareServiceByKeyName(service.Name) {
			settingV2.RefactorBeautyCareService(service, reqBody.AppVersion)
			mapService["detailService"] = service.DetailService
		}
		// Refactor makeup, hair styling, nail service by status
		if globalLib.IsMakeupServiceByKeyName(service.Name) {
			refactorMakeupService(service)
			mapService["detailService"] = service.DetailService
		}
		if globalLib.IsHairStylingServiceByKeyName(service.Name) {
			refactorHairStylingService(service)
			mapService["detailService"] = service.DetailService
		}
		if globalLib.IsNailServiceByKeyName(service.Name) {
			refactorNailService(service)
			mapService["detailService"] = service.DetailService
		}
		if service.PremiumOptions != nil && ((isUserTester && service.PremiumOptions.IsTesting) || service.PremiumOptions.Status == globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE) {
			mapService["premiumOptions"] = &modelService.ServicePremiumOptions{
				Status:         globalConstant.SERVICE_PREMIUM_OPTIONS_STATUS_ACTIVE,
				ApplyForCities: service.PremiumOptions.ApplyForCities,
			}
		} else {
			delete(mapService, "premiumOptions")
		}

		if service.TetBookingDates != nil {
			if service.TetBookingDates.MinVersion != "" && globalLib.CompareVersion(reqBody.AppVersion, service.TetBookingDates.MinVersion) < 0 {
				delete(mapService, "tetBookingDates")
			} else {
				fromDate := globalLib.ParseDateFromTimeStamp(service.TetBookingDates.FromDate, local.TimeZone)
				toDate := globalLib.ParseDateFromTimeStamp(service.TetBookingDates.ToDate, local.TimeZone)
				if !isUserTester && (service.TetBookingDates.IsTesting || now.Before(fromDate) || now.After(toDate)) {
					delete(mapService, "tetBookingDates")
				}
			}
		}
		delete(mapService, "relatedServiceIds")

		// Không xoá priceSetting nữa vì có dùng field costForChooseTasker trong field này
		// delete(item, "priceSetting")
		if service.PriceSetting != nil && service.PriceSetting.MinPostTaskTime > 0 {
			mapService["minPostTaskTime"] = service.PriceSetting.MinPostTaskTime
		}

		if globalLib.IsHomeCleaningServiceByKeyName(service.Name) {
			mapService["workingProcessV2"] = service.WorkingProcessV2
		}

		result = append(result, mapService)
	}
	return result

}

func refactorMakeupService(service *modelService.Service) {
	if service.GetDetailService().GetMakeup() == nil {
		return
	}
	for _, v := range service.GetDetailService().GetMakeup().GetCity() {
		v.MainServices = refactorBeautyGroupServicePackageServices(v.GetMainServices())
		v.ExtraServices = refactorBeautyGroupServicePackageServices(v.GetExtraServices())
	}
}

func refactorNailService(service *modelService.Service) {
	if service.GetDetailService().GetNail() == nil {
		return
	}
	for _, v := range service.GetDetailService().GetNail().GetCity() {
		v.MainServices = refactorBeautyGroupServicePackageServices(v.GetMainServices())
		v.ExtraServices = refactorBeautyGroupServicePackageServices(v.GetExtraServices())
	}
}

func refactorHairStylingService(service *modelService.Service) {
	if service.GetDetailService().GetHairStyling() == nil {
		return
	}
	for _, v := range service.GetDetailService().GetHairStyling().GetCity() {
		v.MainServices = refactorBeautyGroupServicePackageServices(v.GetMainServices())
		v.ExtraServices = refactorBeautyGroupServicePackageServices(v.GetExtraServices())
	}
}

func refactorBeautyGroupServicePackageServices(services []*modelService.ServiceDetailServiceBeautyGroupCityService) []*modelService.ServiceDetailServiceBeautyGroupCityService {
	activeServices := []*modelService.ServiceDetailServiceBeautyGroupCityService{}
	for _, v := range services {
		if v.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
			if len(v.Options) > 0 {
				v.Options = refactorBeautyServicesOptions(v.Options)
			}
			if v.GetCategories() != nil {
				activeStyles, priceRange := refactorCategoryStyle(v.GetCategories().GetListStyle())
				v.GetCategories().ListStyle = activeStyles
				if priceRange != nil && v.Price == 0 {
					v.PriceRange = priceRange
				}
			}
			if v.GetNailType() != nil {
				activeTypes, priceRange := refactorNailTypeStyle(v.GetNailType().GetListType())
				v.GetNailType().ListType = activeTypes
				if priceRange != nil && v.Price == 0 {
					v.PriceRange = priceRange
				}
			}
			activeServices = append(activeServices, v)
		}
	}

	return activeServices
}

func refactorBeautyServicesOptions(options []*modelService.ServiceDetailServiceBeautyGroupCityService) []*modelService.ServiceDetailServiceBeautyGroupCityService {
	activeOptions := []*modelService.ServiceDetailServiceBeautyGroupCityService{}
	for _, v := range options {
		if v.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
			if v.GetCategories() != nil {
				activeStyles, priceRange := refactorCategoryStyle(v.GetCategories().GetListStyle())
				v.GetCategories().ListStyle = activeStyles
				if priceRange != nil && v.Price == 0 {
					v.PriceRange = priceRange
				}
			}
			if v.GetNailType() != nil {
				activeTypes, priceRange := refactorNailTypeStyle(v.GetNailType().GetListType())
				v.GetNailType().ListType = activeTypes
				if priceRange != nil && v.Price == 0 {
					v.PriceRange = priceRange
				}
			}
			activeOptions = append(activeOptions, v)
		}
		if len(v.Options) > 0 {
			v.Options = refactorBeautyServicesOptions(v.Options)
		}
	}
	return activeOptions
}

func refactorCategoryStyle(listStyles []*modelService.ServiceDetailServiceBeautyCareCityPackageServicesCategoriesListStyle) ([]*modelService.ServiceDetailServiceBeautyCareCityPackageServicesCategoriesListStyle, *modelService.ServiceDetailServiceBeautyCareCityPackageServicesPriceRange) {
	var minPrice, maxPrice float64 = 0.0, 0.0
	activeStyles := []*modelService.ServiceDetailServiceBeautyCareCityPackageServicesCategoriesListStyle{}
	for _, style := range listStyles {
		if style.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
			if minPrice == 0 || style.GetPrice() <= minPrice {
				minPrice = style.GetPrice()
			}
			if maxPrice == 0 || style.GetPrice() >= maxPrice {
				maxPrice = style.GetPrice()
			}
			activeStyles = append(activeStyles, style)
		}
	}
	return activeStyles, &modelService.ServiceDetailServiceBeautyCareCityPackageServicesPriceRange{
		FromPrice: minPrice,
		ToPrice:   maxPrice,
	}
}

func refactorNailTypeStyle(listType []*modelService.ServiceDetailServiceBeautyGroupCityNailTypeListType) ([]*modelService.ServiceDetailServiceBeautyGroupCityNailTypeListType, *modelService.ServiceDetailServiceBeautyCareCityPackageServicesPriceRange) {
	var minPrice, maxPrice float64 = 0.0, 0.0
	activeTypes := []*modelService.ServiceDetailServiceBeautyGroupCityNailTypeListType{}
	for _, nType := range listType {
		if nType.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
			if minPrice == 0 || nType.GetPrice() <= minPrice {
				minPrice = nType.GetPrice()
			}
			if maxPrice == 0 || nType.GetPrice() >= maxPrice {
				maxPrice = nType.GetPrice()
			}
			activeStyles := []*modelService.ServiceDetailServiceBeautyGroupCityNailTypeListTypeNailStyle{}
			for _, style := range nType.GetNailStyle() {
				if style.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
					activeStyles = append(activeStyles, style)
				}
			}
			nType.NailStyle = activeStyles

			activeTypes = append(activeTypes, nType)
		}
	}
	return activeTypes, &modelService.ServiceDetailServiceBeautyCareCityPackageServicesPriceRange{
		FromPrice: minPrice,
		ToPrice:   maxPrice,
	}
}
