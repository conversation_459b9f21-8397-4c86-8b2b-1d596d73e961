package getServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetServices(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	_, isTester, err := getAsker(reqBody.UserId)
	if err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}
	return getServiceByIds(isTester, reqBody), nil
}
