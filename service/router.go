/*
 * @File: routes.go
 * @Description: Handler Func New Router
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
package service

import (
	"github.com/gorilla/mux"
	"gitlab.com/btaskee/go-services-model-v2/globalMiddleware"
)

func NewRouter() *mux.Router {
	router := mux.NewRouter().StrictSlash(true)

	apiGroup := router.PathPrefix("/api/v3/api-asker-vn").Subrouter()
	for _, route := range routes {
		apiGroup.Methods(route.Method).Path(route.Pattern).Name(route.Name).Handler(globalMiddleware.HTTPJWTMiddleware(route.HandlerFunc))
	}

	apiGroupV4 := router.PathPrefix("/api/v4/api-asker-vn").Subrouter()
	for _, route := range routes_v4 {
		apiGroupV4.Methods(route.Method).Path(route.Pattern).Name(route.Name).Handler(globalMiddleware.HTTPJWTMiddleware(route.HandlerFunc))
	}

	// routes_v5
	apiGroupV5 := router.PathPrefix("/api/v5/api-asker-vn").Subrouter()
	for _, route := range routes_v5 {
		apiGroupV5.Methods(route.Method).Path(route.Pattern).Name(route.Name).Handler(globalMiddleware.HTTPJWTMiddleware(route.HandlerFunc))
	}

	return router
}
