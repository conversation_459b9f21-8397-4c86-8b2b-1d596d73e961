package service

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getListReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getMyReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getMyRewardV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getRewardDetail"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getRewardHomePage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getRewardsFlashSale"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getRewardsForBookTask"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/getRewardsForYou"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward/lixiTasker"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bundleVoucher/getListBundleVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bundleVoucher/redeemBundleVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/addMembersToBusiness/addMembers"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/addMembersToBusiness/verifyMembers"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business/createBusiness"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business/getBusinessTopUpSettingInfo"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business/getTotalRevokeBPay"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business/getTotalTopupBPay"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business/revokeMembersLevel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business/topupMembersLevel"
	getListBusinessTransaction "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/businessTransaction/listBusinessTransaction"
	createBusinessLevel "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level/createLevel"
	removeBusinessLevel "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level/removeLevel"
	updateBusinessLevel "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level/updateLevel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member/addMembersLevel"
	listMembersByLevel "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member/listMemberByLevel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member/removeMemberFromBusiness"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member/removeMembersLevel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member/updateMemberLevel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/memberTransaction/getListMemberTransaction"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/sendBusinessReport"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/archiveChat"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/deleteChat"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/getChatHistoryByTask"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/getChatListChatMessageV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/getListChat"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/getListChatV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/loadMoreMessages"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/sendCallChatMessage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/sendChatMessageV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/setIsReadChatMessage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/setIsReadChatMessageV2"
	cancelComboVoucherSubscription "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/cancelSubscription"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getComboVoucherDetail"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getFreeComboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getListComboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getListComboVoucherV2"
	getListComboVoucherTransactionHistories "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getListTransactionHistories"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getListUserComboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher/getUserComboVoucherDetail"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/fairyTale/getFairyTaleDetail"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/fairyTale/getListFairyTale"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gameCampaign/dailyRollCall"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gameCampaign/getHistoryReceiveReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gameCampaign/getHistoryReceiveSpin"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gameCampaign/getRunningGameCampaign"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gameCampaign/getWeeklyRollCall"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/countNewGift"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/countNewGiftV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/getGiftV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/notification/getNotificationV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/others"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/rating/getTaskerReviewsV5"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/referral/getReferralFriends"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/referral/getReferralSetting"
	getEventConfig "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/settings/getEventConfig"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/settings/getPartnerMiniAppSettings"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/subscription/getDetailSubscriptionSchedule"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/survey/askerSendSurvey"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/survey/getSurveyById"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task/cancelTaskPaymentProcess"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task/checkTaskerConflictTime"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task/getTaskTodoList"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task/getTaskerFreeTime"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task/getUpComingTasks"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task/trackAskerTryToProcessPayment"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/taskSchedule/getTaskScheduleDetail"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/tasker/getTaskerServices"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/addFavouriteServices"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/addHomeMovingLocation"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/addHousekeepingLocation"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/deleteHomeMovingLocation"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/deleteHousekeepingLocation"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/getFavoriteTaskerV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/getListTaskerWorked"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/sendNotiToRemindFavouriteTasker"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/updateHomeMovingLocation"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/updateHousekeepingLocation"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/voteFavTasker/getTaskersForVote"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/voteFavTasker/voteTasker"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/yearEndReport/askerYearEndReportVN"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/yearEndReport/shareYearEndReport"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

func GetListComboVoucher(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListComboVoucher.GetListComboVoucher(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListComboVoucherV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListComboVoucherV2.GetListComboVoucherV2(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetComboVoucherDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getComboVoucherDetail.GetComboVoucherDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetFreeComboVoucher(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getFreeComboVoucher.GetFreeComboVoucher(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func CountNewGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := countNewGift.CountNewGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func CountNewGiftV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := countNewGiftV2.CountNewGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetChatHistoryByTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getChatHistoryByTask.GetChatHistoryByTask(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetRewardHomePage(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getRewardHomePage.GetRewardHomePage(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListReward(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListReward.GetListReward(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetGiftV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getGiftV2.GetGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetRewardDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getRewardDetail.GetRewardDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetMyReward(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getMyReward.GetMyReward(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetMyRewardV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getMyRewardV2.GetMyReward(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetRewardsForYou(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getRewardsForYou.GetRewardsForYou(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}
func GetPartnerMiniAppSettings(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getPartnerMiniAppSettings.GetPartnerMiniAppSettings(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Any("err", err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetRewardsForBookTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getRewardsForBookTask.GetRewardsForBookTask(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetReferralSetting(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getReferralSetting.GetReferralSetting(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetRewardsFlashSale(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getRewardsFlashSale.GetRewardsFlashSale(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetReferralFriends(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getReferralFriends.GetReferralFriends(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func ArchiveChat(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := archiveChat.ArchiveChat(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func DeleteChat(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := deleteChat.DeleteChat(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetListChat(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getListChat.GetListChat(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListChatV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getListChatV2.GetListChatV2(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func LoadMoreMessages(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := loadMoreMessages.LoadMoreMessages(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func SetIsReadChatMessage(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = setIsReadChatMessage.SetIsReadChatMessage(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func SetIsReadChatMessageV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = setIsReadChatMessageV2.SetIsReadChatMessage(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func SendChatMessageV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := sendChatMessageV2.SendChatMessageV2(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func SetReviewStore(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = others.SetReviewStore(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetRunningGameCampaign(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getRunningGameCampaign.GetRunningGameCampaign(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AskerYearEndReportVN(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := askerYearEndReportVN.AskerYearEndReport(reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AddFavouriteServices(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = addFavouriteServices.AddFavouriteServices(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetSurveyById(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getSurveyById.GetSurveyById(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AskerSendSurvey(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := askerSendSurvey.AskerSendSurvey(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetMemberInfo(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := user.GetMemberInfo(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AddHomeMovingLocation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	decoder := json.NewDecoder(r.Body)
	var reqBody map[string]interface{}
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
	}

	errCode := addHomeMovingLocation.AddHomeMovingLocation(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func UpdateHomeMovingLocation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	decoder := json.NewDecoder(r.Body)
	var reqBody map[string]interface{}
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
	}

	errCode := updateHomeMovingLocation.UpdateHomeMovingLocation(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func DeleteHomeMovingLocation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = deleteHomeMovingLocation.DeleteHomeMovingLocation(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetTaskerFreeTime(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getTaskerFreeTime.GetTaskerFreeTime(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetFavoriteTaskerV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getFavoriteTaskerV2.GetFavoriteTasker(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListTaskerWorked(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getListTaskerWorked.GetListTaskerWorked(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func CheckTaskerConflictTime(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	//1. Decode request body
	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	errCode, err := checkTaskerConflictTime.CheckTaskerConflictTime(reqBody)
	if errCode == nil {
		globalResponse.ResponseSuccess(w, nil)
	} else {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
	}
}

func GetChatListChatMessage(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Process for new app version
	result, errCode, err := chatMessage.GetChatListChatMessage(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetChatListChatMessageV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Process for new app version
	result, errCode, err := getChatListChatMessageV2.GetChatListChatMessageV2(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetTaskerServices(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getTaskerServices.GetTaskerServices(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetTaskersForVote(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getTaskersForVote.GetTaskersForVote(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func VoteTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := voteTasker.VoteTasker(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetUpComingTasks(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getUpComingTasks.GetUpComingTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetTaskTodoList(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getTaskTodoList.GetTaskTodoList(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetTaskScheduleDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getTaskScheduleDetail.GetTaskScheduleDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetDetailSubscriptionSchedule(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getDetailSubscriptionSchedule.GetDetailSubscriptionSchedule(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func TrackAskerTryToProcessPayment(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := trackAskerTryToProcessPayment.TrackAskerTryToProcessPayment(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func CancelTaskPaymentProcess(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := cancelTaskPaymentProcess.CancelTaskPaymentProcess(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func AddHousekeepingLocation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	decoder := json.NewDecoder(r.Body)
	var reqBody map[string]interface{}
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
	}

	errCode := addHousekeepingLocation.AddHousekeepingLocation(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func UpdateHousekeepingLocation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	decoder := json.NewDecoder(r.Body)
	var reqBody map[string]interface{}
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
	}

	errCode := updateHousekeepingLocation.UpdateHousekeepingLocation(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func DeleteHousekeepingLocation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = deleteHousekeepingLocation.DeleteHousekeepingLocation(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetListFairyTale(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	result, errCode, error := getListFairyTale.GetListFairyTale()
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetFairyTaleDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, error := getFairyTaleDetail.GetFairyTaleDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func DailyRollCall(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody := &model.ApiRequest{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	numberOfSpin, respCode := dailyRollCall.DailyRollCall(reqBody)
	if respCode != nil {
		local.Logger.Warn(respCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *respCode)
	} else {
		globalResponse.ResponseSuccess(w, numberOfSpin)
	}
}
func GetWeeklyRollCall(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody := &model.ApiRequest{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	result, respCode := getWeeklyRollCall.GetWeeklyRollCall(reqBody)
	if respCode != nil {
		local.Logger.Warn(respCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *respCode)
	} else {
		globalResponse.ResponseSuccess(w, result)
	}
}

func GetHistoryReceiveSpin(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getHistoryReceiveSpin.GetHistoryReceiveSpin(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetHistoryReceiveReward(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getHistoryReceiveReward.GetHistoryReceiveReward(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AddMembersToBusiness(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := addMembers.AddMembers(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func VerifyMembers(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := verifyMembers.VerifyMembers(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AddMembersLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := addMembersLevel.AddMembersLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func RemoveMembersLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := removeMembersLevel.RemoveMembersLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func TopupMembersLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = topupMembersLevel.TopupMembersLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func RevokeMembersLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = revokeMembersLevel.RevokeMembersLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetTotalTopupBPay(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getTotalTopupBPay.GetTotalTopupBPay(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetTotalRevokeBPay(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getTotalRevokeBPay.GetTotalRevokeBPay(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetBusinessTopUpSettingInfo(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParamsBusiness(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getBusinessTopUpSettingInfo.GetBusinessTopUpSettingInfo(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func CreateBusiness(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, error := createBusiness.CreateBusiness(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func CreateBusinessLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, error := createBusinessLevel.CreateLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func ListMembersByLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, error := listMembersByLevel.ListMembersByLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListMemberTransaction(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, error := getListMemberTransaction.GetListMemberTransaction(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListBusinessTransaction(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListBusinessTransaction.GetListBusinessTransaction(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func RemoveMemberFromBusiness(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, error := removeMemberFromBusiness.RemoveMemberFromBusiness(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func RemoveBusinessLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, error := removeBusinessLevel.RemoveBusinessLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func UpdateBusinessLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, error := updateBusinessLevel.UpdateBusinessLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func UpdateMemberLevel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, error := updateMemberLevel.UpdateMemberLevel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func SendBusinessReport(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyBusinessParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, error := sendBusinessReport.SendBusinessReport(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(error),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetNotificationV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getNotificationV2.GetNotificationV2(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func ShareYearEndReport(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := shareYearEndReport.ShareYearEndReport(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func SendCallChatMessage(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := sendCallChatMessage.SendCallChatMessage(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func SendNotiToRemindFavouriteTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode, err := sendNotiToRemindFavouriteTasker.SendNotiToRemindFavouriteTasker(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func LixiTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := lixiTasker.LixiTasker(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListUserComboVoucher(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListUserComboVoucher.GetListUserComboVoucher(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetUserComboVoucherDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getUserComboVoucherDetail.GetUserComboVoucherDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListComboVoucherTransactionHistories(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListComboVoucherTransactionHistories.GetListComboVoucherTransactionHistories(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func CancelComboVoucherSubscription(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = cancelComboVoucherSubscription.CancelComboVoucherSubscription(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetEventConfig(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getEventConfig.GetEventConfig(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetTaskerReviewsV5(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode, err := getTaskerReviewsV5.GetTaskerReviews(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func GetListBundleVoucher(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getListBundleVoucher.GetListBundleVoucher(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func RedeemBundleVoucher(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := redeemBundleVoucher.RedeemBundleVoucher(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}
