package service

import (
	"net/http"

	GetServicesV5 "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handlers_v5/service/getServices"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handlers_v5/task/getBeautyAccessories"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handlers_v5/user/addActivatedServices"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

// GetBeautyAccessories
func GetBeautyAccessories(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := getBeautyAccessories.GetBeautyAccessories(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func AddActivatedServices(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = addActivatedServices.AddActivatedServices(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func GetServices(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result, errCode := GetServicesV5.GetServices(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}
