/*
 * @File: index.go
 * @Description: Handler Func Start
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/09/2020
 * @UpdatedBy: linhnh
 */
package service

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	grpcApiAskerVN "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcApiAskerVN"
	"go.elastic.co/apm/module/apmhttp/v2"
	"google.golang.org/grpc"
)

var mode = os.Getenv("APPLICATION_MODE")

func NewServer(port string) *http.Server {
	r := NewRouter()
	http.Handle("/", r)
	return &http.Server{
		Addr:    fmt.Sprintf(":%s", port),
		Handler: apmhttp.Wrap(r),
	}
}

func StartHTTPServer(srv *http.Server) error {
	log.Printf("[%s] Started %s REST at port: %s", mode, local.SERVICE_NAME, strings.Trim(srv.Addr, ":"))
	return srv.ListenAndServe()
}

func StopHTTPServer(srv *http.Server) {
	timeWait := 60 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeWait)
	defer func() {
		log.Println("Close another connection")
		cancel()
	}()
	if err := srv.Shutdown(ctx); err == context.DeadlineExceeded {
		log.Print("Halted active connections")
	}
}

func NewGRPCServer() *grpc.Server {
	// create a server instance
	s := GRPCServer{}

	// create a gRPC server object
	grpcServer := grpc.NewServer()

	// attach the Ping service to the server
	grpcApiAskerVN.RegisterApiAskerVNServer(grpcServer, &s)

	return grpcServer
}

func StartGRPCServer(grpcServer *grpc.Server, gRPCPort string) error {
	log.Printf("[%s] Started %s gRPC at port: %s", mode, local.SERVICE_NAME, gRPCPort)
	// Create a listener on TCP port
	lis, err := net.Listen("tcp", fmt.Sprintf(":%s", gRPCPort))
	if err != nil {
		return err
	}
	// Start the server
	return grpcServer.Serve(lis)
}
