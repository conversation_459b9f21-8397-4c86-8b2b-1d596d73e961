package service

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/redeem/redeemGiftVN"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	grpcApiAskerVN "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcApiAskerVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftResponse"
)

type GRPCServer struct {
	grpcApiAskerVN.UnimplementedApiAskerVNServer
}

func (s *GRPCServer) RedeemGift(ctx context.Context, req *redeemGiftRequest.RedeemGiftRequest) (*redeemGiftResponse.RedeemGiftResponse, error) {
	request := &model.ApiRequest{
		UserId: req.UserId,
		GiftId: req.IncentiveId,
	}

	result, errCode := redeemGiftVN.RedeemGiftVN(request)
	if errCode != nil {
		return nil, fmt.Errorf("redeem gift error: %v. error message: %v", errCode.ErrorCode, errCode.Message)
	}

	response := &redeemGiftResponse.RedeemGiftResponse{
		PromotionCode: cast.ToString(result["code"]),
	}
	return response, nil
}
