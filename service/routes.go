/*
 * @File: routes.go
 * @Description: Varb Route
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 31/12/2020
 * @UpdatedBy: vinhnt
 */
package service

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/FATransaction"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/sendChatMessage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/feedback"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/financialAccount"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/groceryAssistant"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/housekeeping"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/incentive"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/luckyDraw"
	marketingCampaignV3 "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/marketingCampaign/v3"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/notification"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/others"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/outstandingPayment"
	paymentV3 "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/payment/v3"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/paymentCard"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/pointTransaction"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/promotionPaymentMethod"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/raixPushAppTokens"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/rating"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/reportTransaction"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/reward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/service"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/serviceChannel"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/settings"
	settingsV2 "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/settings/v2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/subscription"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/taskReportTasker"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/taskSchedule"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/tasker"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/taskerSettings"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/taskerTaskHistory"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user"
	userV4 "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/user/v4"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/workingPlaces"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/yearEndReport"
)

var routes_v5 = Routes{
	Route{
		"ApiAskerVNV5_GetTaskerReviews",
		"POST",
		"/get-tasker-reviews",
		GetTaskerReviewsV5,
	},
	Route{
		"VN_ApiAsker_V5_GetBeautyAccessories",
		"POST",
		"/get-beauty-accessories",
		GetBeautyAccessories,
	},
	Route{
		"VN_ApiAsker_V5_AddActivatedServices",
		"POST",
		"/add-activated-services",
		AddActivatedServices,
	},
	Route{
		"VN_ApiAsker_V5_GetServices",
		"POST",
		"/get-services-v2",
		GetServices,
	},
}

var routes_v4 = Routes{
	Route{
		"ApiAskerVNV4_GetAllSettingsV2",
		"POST",
		"/get-all-settings",
		settingsV2.GetAllSettings,
	},
	Route{
		"ApiAskerVNV4_GetUser",
		"POST",
		"/get-user",
		userV4.GetUser,
	},
}

var routes = Routes{
	Route{
		"ApiAskerVNV3_GetRunningEventConfig",
		"POST",
		"/get-event-config",
		GetEventConfig,
	},
	Route{
		"ApiAskerVNV3_CancelComboVoucherSubscription",
		"POST",
		"/cancel-combo-voucher-subscription",
		CancelComboVoucherSubscription,
	},
	Route{
		"ApiAskerVNV3_GetListComboVoucherTransactionHistories",
		"POST",
		"/get-list-combo-voucher-transaction-histories",
		GetListComboVoucherTransactionHistories,
	},
	Route{
		"ApiAskerVNV3_GetListComboVoucherV2",
		"POST",
		"/get-list-combo-voucher-v2",
		GetListComboVoucherV2,
	},
	Route{
		"ApiAskerVNV3_GetUserComboVoucherDetail",
		"POST",
		"/get-user-combo-voucher-detail",
		GetUserComboVoucherDetail,
	},
	Route{
		"ApiAskerVNV3_GetListUserComboVoucher",
		"POST",
		"/get-list-user-combo-voucher",
		GetListUserComboVoucher,
	},
	Route{
		"ApiAskerVNV3_ShareYearEndReport",
		"POST",
		"/share-year-end-report",
		ShareYearEndReport,
	},
	Route{
		"ApiAskerVNV3_GetBusinessTopUpSettingInfo",
		"POST",
		"/get-business-topup-setting-info",
		GetBusinessTopUpSettingInfo,
	},
	Route{
		"ApiAskerVNV3_GetTotalRevokeBPay",
		"POST",
		"/get-total-revoke-bpay",
		GetTotalRevokeBPay,
	},
	Route{
		"ApiAskerVNV3_GetTotalTopupBPay",
		"POST",
		"/get-total-topup-bpay",
		GetTotalTopupBPay,
	},
	Route{
		"ApiAskerVNV3_RevokeMembersLevel",
		"POST",
		"/revoke-members-level",
		RevokeMembersLevel,
	},
	Route{
		"ApiAskerVNV3_AddMembersLevel",
		"POST",
		"/add-members-level",
		AddMembersLevel,
	},
	Route{
		"ApiAskerVNV3_RemoveMembersLevel",
		"POST",
		"/remove-members-level",
		RemoveMembersLevel,
	},
	Route{
		"ApiAskerVNV3_TopupMembersLevel",
		"POST",
		"/topup-members-level",
		TopupMembersLevel,
	},
	Route{
		"ApiAskerVNV3_VerifyMembers",
		"POST",
		"/verify-members",
		VerifyMembers,
	},
	Route{
		"ApiAskerVNV3_AddMembersToBusiness",
		"POST",
		"/add-members-to-business",
		AddMembersToBusiness,
	},
	Route{
		"ApiAskerVNV3_SendBusinessReport",
		"POST",
		"/send-business-report",
		SendBusinessReport,
	},
	Route{
		"ApiAskerVNV3_UpdateMemberLevel",
		"POST",
		"/update-member-level",
		UpdateMemberLevel,
	},
	Route{
		"ApiAskerVNV3_UpdateBusinessLevel",
		"POST",
		"/update-business-level",
		UpdateBusinessLevel,
	},
	Route{
		"ApiAskerVNV3_RemoveBusinessLevel",
		"POST",
		"/remove-business-level",
		RemoveBusinessLevel,
	},
	Route{
		"ApiAskerVNV3_RemoveMemberFromBusiness",
		"POST",
		"/remove-member-from-business",
		RemoveMemberFromBusiness,
	},
	Route{
		"ApiAskerVNV3_GetListBusinessTransaction",
		"POST",
		"/list-business-transactions",
		GetListBusinessTransaction,
	},
	Route{
		"ApiAskerVNV3_GetListMemberTransaction",
		"POST",
		"/list-business-member-transactions",
		GetListMemberTransaction,
	},
	Route{
		"ApiAskerVNV3_ListMembersByLevel",
		"POST",
		"/list-members-by-level",
		ListMembersByLevel,
	},
	Route{
		"ApiAskerVNV3_CreateBusinessLevel",
		"POST",
		"/create-business-level",
		CreateBusinessLevel,
	},
	Route{
		"ApiAskerVNV3_CreateUserBusiness",
		"POST",
		"/create-business",
		CreateBusiness,
	},
	Route{
		"ApiAskerVNV3_GetFairyTaleDetail",
		"POST",
		"/get-fairy-tale-detail",
		GetFairyTaleDetail,
	},
	Route{
		"ApiAskerVNV3_GetListFairyTale",
		"POST",
		"/get-list-fairy-tale",
		GetListFairyTale,
	},
	// ======================
	Route{
		"ApiAskerVNV3_AddHousekeepingLocation",
		"POST",
		"/add-housekeeping-location",
		AddHousekeepingLocation,
	},
	Route{
		"ApiAskerVNV3_UpdateHousekeepingLocation",
		"POST",
		"/update-housekeeping-location",
		UpdateHousekeepingLocation,
	},
	Route{
		"ApiAskerVNV3_DeleteHousekeepingLocation",
		"POST",
		"/delete-housekeeping-location",
		DeleteHousekeepingLocation,
	},
	// ====== NEW API
	Route{
		"ApiAskerVNV3_CancelTaskPaymentProcess",
		"POST",
		"/cancel-task-payment-process",
		CancelTaskPaymentProcess,
	},
	Route{
		"ApiAskerVNV3_TrackAskerTryToProcessPayment",
		"POST",
		"/track-asker-try-to-process-payment",
		TrackAskerTryToProcessPayment,
	},
	// ======
	// GetDetailSubscriptionSchedule
	Route{
		"ApiAskerVNV3_GetDetailSubscriptionSchedule",
		"POST",
		"/get-detail-subscription-schedule",
		GetDetailSubscriptionSchedule,
	},
	// GetTaskScheduleDetail
	Route{
		"ApiAskerVNV3_GetTaskScheduleDetail",
		"POST",
		"/get-task-schedule-detail",
		GetTaskScheduleDetail,
	},
	Route{
		"ApiAskerVNV3_GetUpComingTasks",
		"POST",
		"/get-up-coming-tasks",
		GetUpComingTasks,
	},
	Route{
		"ApiAskerVNV3_GetTaskTodoList",
		"POST",
		"/get-task-todo-list",
		GetTaskTodoList,
	},
	Route{
		"ApiAskerVNV3_VoteTasker",
		"POST",
		"/vote-tasker",
		VoteTasker,
	},
	Route{
		"ApiAskerVNV3_GetTaskersForVote",
		"POST",
		"/get-taskers-for-vote",
		GetTaskersForVote,
	},
	Route{
		"ApiAskerVNV3_GetMemberInfo",
		"POST",
		"/get-member-info",
		GetMemberInfo,
	},
	Route{
		"ApiAskerVNV3_AskerYearEndReportVN",
		"POST",
		"/asker-year-end-report-vn",
		AskerYearEndReportVN,
	},
	Route{
		"ApiAskerVNV3_GetReferralFriends",
		"POST",
		"/get-referral-friends",
		GetReferralFriends,
	},
	Route{
		"ApiAskerVNV3_GetRewardsFlashSale",
		"POST",
		"/get-rewards-flash-sale",
		GetRewardsFlashSale,
	},
	Route{
		"ApiAskerVNV3_GetRewardsForBookTask",
		"POST",
		"/get-rewards-for-book-task",
		GetRewardsForBookTask,
	},
	Route{
		"ApiAskerVNV3_GetRewardsForYou",
		"POST",
		"/get-rewards-for-you",
		GetRewardsForYou,
	},
	Route{
		"ApiAskerVNV3_GetMyReward",
		"POST",
		"/get-my-rewards",
		GetMyReward,
	},
	Route{
		"ApiAskerVNV3_GetMyRewardV2",
		"POST",
		"/get-my-rewards-v2",
		GetMyRewardV2,
	},
	Route{
		"ApiAskerVNV3_GetRewardDetail",
		"POST",
		"/get-reward-detail",
		GetRewardDetail,
	},
	Route{
		"ApiAskerVNV3_GetRewardReward",
		"POST",
		"/get-list-reward",
		GetListReward,
	},
	Route{
		"ApiAskerVNV3_GetRewardHomePage",
		"POST",
		"/get-reward-home-page",
		GetRewardHomePage,
	},
	Route{
		"ApiAskerVNV3_GetListPaymentV3",
		"POST",
		"/get-list-payment",
		paymentV3.GetListPaymentV3,
	},
	Route{
		"ApiAskerVNV3_GetMarketingCampaignV3",
		"POST",
		"/get-marketing-campaign",
		marketingCampaignV3.GetMarketingCampaignV3,
	},
	Route{
		"ApiAskerVNV3_GetMarketingCampaignWithoutLoginV3",
		"POST",
		"/get-marketing-campaign-without-login",
		marketingCampaignV3.GetMarketingCampaignWithoutLoginV3,
	},
	Route{
		"ApiAskerVNV3_GetSpecialCampaignV3",
		"POST",
		"/get-special-campaign",
		marketingCampaignV3.GetSpecialCampaignV3,
	},
	Route{
		"ApiAskerVNV3_GetSpecialCampaignWithoutLoginV3",
		"POST",
		"/get-special-campaign-without-login",
		marketingCampaignV3.GetSpecialCampaignWithoutLoginV3,
	},

	Route{
		"ApiAskerVNV3_GetMarketingCampaignDetailV3",
		"POST",
		"/get-marketing-campaign-detail",
		marketingCampaignV3.GetMarketingCampaignDetailV3,
	},
	Route{
		"ApiAskerVNV3_TrackUserCampaignV3",
		"POST",
		"/track-user-campaign",
		marketingCampaignV3.TrackUserCampaignV3,
	},
	Route{
		"ApiAskerVNV3_AddHousekeeping",
		"POST",
		"/add-housekeeping",
		housekeeping.AddHousekeepingV3,
	}, Route{
		"ApiAskerVNV3_UpdateRoomHousekeeping",
		"POST",
		"/update-room-housekeeping",
		housekeeping.UpdateRoomV3,
	}, Route{
		"ApiAskerVNV3_AddRoomHousekeeping",
		"POST",
		"/add-room-housekeeping",
		housekeeping.AddRoomV3,
	},
	Route{
		"ApiAskerVNV3_CheckTaskerConflictTime",
		"POST",
		"/check-tasker-conflict-time",
		CheckTaskerConflictTime,
	},
	Route{
		"ApiAskerVNV3_GetListTaskerWorked",
		"POST",
		"/get-list-tasker-worked",
		GetListTaskerWorked,
	},
	Route{
		"ApiAskerVNV3_GetFavoriteTaskerV2",
		"POST",
		"/get-favorite-tasker-v2",
		GetFavoriteTaskerV2,
	},
	Route{
		"ApiAskerVNV3_GetTaskerFreeTime",
		"POST",
		"/get-tasker-free-time",
		GetTaskerFreeTime,
	},
	Route{
		"ApiAskerVNV3_ArchiveChat",
		"POST",
		"/archive-chat-message",
		ArchiveChat,
	},
	Route{
		"ApiAskerVNV3_DeleteChat",
		"POST",
		"/delete-chat-message",
		DeleteChat,
	},
	Route{
		"ApiAskerVNV3_GetTaskerServices",
		"POST",
		"/get-tasker-services",
		GetTaskerServices,
	},
	Route{
		"ApiAskerVNV3_GetChatListChatMessageV2",
		"POST",
		"/get-chat-list-chat-message-v2",
		GetChatListChatMessageV2,
	},
	Route{
		"ApiAskerVNV3_SetIsReadChatMessageV2",
		"POST",
		"/set-is-read-chat-message-v2",
		SetIsReadChatMessageV2,
	},
	Route{
		"ApiAskerVNV3_SendChatMessageV2",
		"POST",
		"/send-chat-message-v2",
		SendChatMessageV2,
	},
	Route{
		"ApiAskerVNV3_GetListChatV2",
		"POST",
		"/get-list-chat-v2",
		GetListChatV2,
	},
	Route{
		"ApiAskerVNV3_LoadMoreMessages",
		"POST",
		"/load-more-messages",
		LoadMoreMessages,
	},
	// ======================
	Route{
		"ApiAskerVNV3_AddHomeMovingLocation",
		"POST",
		"/add-home-moving-location",
		AddHomeMovingLocation,
	},
	Route{
		"ApiAskerVNV3_UpdateHomeMovingLocation",
		"POST",
		"/update-home-moving-location",
		UpdateHomeMovingLocation,
	},
	Route{
		"ApiAskerVNV3_DeleteHomeMovingLocation",
		"POST",
		"/delete-home-moving-location",
		DeleteHomeMovingLocation,
	},
	Route{
		"ApiAskerVNV3_GetGiftV2",
		"POST",
		"/get-gift-v2",
		GetGiftV2,
	},
	Route{
		"ApiAskerVNV3_AskerSendSurvey",
		"POST",
		"/asker-send-survey",
		AskerSendSurvey,
	},
	Route{
		"ApiAskerVNV3_GetSurveyById",
		"POST",
		"/get-survey-by-id",
		GetSurveyById,
	},
	Route{
		"ApiAskerVNV3_AddFavouriteServices",
		"POST",
		"/add-favourite-services",
		AddFavouriteServices,
	},
	Route{
		"ApiAskerVNV3_GetRunningGameCampaign",
		"POST",
		"/get-running-game-campaign",
		GetRunningGameCampaign,
	},
	Route{
		"ApiAskerVNV3_GetReferralSetting",
		"POST",
		"/get-referral-setting",
		GetReferralSetting,
	},
	Route{
		"ApiAskerVNV3_GetFreeComboVoucher",
		"POST",
		"/get-free-combo-voucher",
		GetFreeComboVoucher,
	},
	Route{
		"ApiAskerVNV3_GetComboVoucherDetail",
		"POST",
		"/get-combo-voucher-detail",
		GetComboVoucherDetail,
	},
	Route{
		"ApiAskerVNV3_GetListComboVoucher",
		"POST",
		"/get-list-combo-voucher",
		GetListComboVoucher,
	},
	Route{
		"ApiAskerVNV3_StringeeVoiceCall",
		"GET",
		"/stringee-voice-call",
		others.StringeeVoiceCall,
	},
	Route{
		"ApiAskerVNV3_GetUpCommingTasksMiniApp",
		"POST",
		"/get-upcomming-tasks-mini-app",
		task.GetUpCommingTasksMiniApp,
	},
	Route{
		"ApiAskerVNV3_GiveLixiTet2022",
		"POST",
		"/give-lixi-tet-2022",
		others.GiveLixiTet2022,
	},
	Route{
		"ApiAskerVNV3_GetTaskerFinancialTransaction",
		"POST",
		"/get-tasker-financial-transaction",
		tasker.GetTaskerFinancialTransaction,
	},
	Route{
		"ApiAskerVNV3_GetHoldingAmount",
		"POST",
		"/get-holding-amount",
		tasker.GetHoldingAmount,
	},
	Route{
		"ApiAskerVNV3_GetTaskerMoney",
		"POST",
		"/get-tasker-money",
		tasker.GetTaskerMoney,
	},
	Route{
		"ApiAskerVNV3_AskerYearEndReport",
		"POST",
		"/asker-year-end-report",
		yearEndReport.AskerYearEndReport,
	},
	Route{
		"ApiAskerVNV3_TaskerYearEndReport",
		"POST",
		"/tasker-year-end-report",
		yearEndReport.TaskerYearEndReport,
	},
	// =========== LUCKY DRAW ========
	Route{
		"ApiAskerVNV3_GetNumberLuckyDrawByUserId",
		"POST",
		"/get-number-lucky-draw-by-user-id",
		luckyDraw.GetNumberLuckyDrawByUserId,
	},
	Route{
		"ApiAskerVNV3_GetChatHistory",
		"POST",
		"/get-chat-history",
		chatMessage.GetChatHistory,
	},
	Route{
		"ApiAskerVNV3_AddPlaceNotSupport",
		"POST",
		"/add-place-not-support",
		others.AddPlaceNotSupport,
	},
	Route{
		"ApiAskerVNV3_RemoveShoppingCardById",
		"POST",
		"/remove-shopping-card-by-id",
		groceryAssistant.RemoveShoppingCardById,
	},
	Route{
		"ApiAskerVNV3_GetStoresGroceryAssistant",
		"POST",
		"/get-stores-grocery-assistant",
		groceryAssistant.GetStores,
	},
	Route{
		"ApiAskerVNV3_UpdateShoppingCart",
		"POST",
		"/update-shopping-cart",
		groceryAssistant.UpdateShoppingCart,
	},
	Route{
		"ApiAskerVNV3_GetPromotionPaymentMethod",
		"POST",
		"/get-promotion-payment-method",
		promotionPaymentMethod.GetPromotionPaymentMethod,
	},
	Route{
		"ApiAskerVNV3_FindProductGroceryAssistant",
		"POST",
		"/find-product-grocery-assistant",
		groceryAssistant.FindProduct,
	},
	Route{
		"ApiAskerVNV3_GetShoppingCartByUserId",
		"POST",
		"/get-shopping-cart-by-userId",
		groceryAssistant.GetShoppingCartByUserId,
	},
	Route{
		"ApiAskerVNV3_FindPopularGroceryAssistant",
		"POST",
		"/find-popular-grocery-assistant",
		groceryAssistant.FindPopular,
	},
	Route{
		"ApiAskerVNV3_GetCategoryGroceryAssistant",
		"POST",
		"/get-category-grocery-assistant",
		groceryAssistant.GetCategory,
	},
	// ====================== API FOR BACKEND
	// Route{
	// 	"SendSubscriptionOrderEmailFromBackend",
	// 	"POST",
	// 	"/send-subscription-order-email-from-backend",
	// 	apiForBackEnd.SendSubscriptionOrderEmailFromBackend,
	// },
	// Route{
	// 	"ResendToViewedTaskerFromBackend",
	// 	"POST",
	// 	"/resend-to-viewed-tasker-from-backend",
	// 	apiForBackEnd.ResendToViewedTaskerFromBackend,
	// },
	// Route{
	// 	"ResendToSpecifyTaskersFromBackend",
	// 	"POST",
	// 	"/resend-to-specify-taskers-from-backend",
	// 	apiForBackEnd.ResendToSpecifyTaskersFromBackend,
	// },
	// Route{
	// 	"ResendReceiptEmailFromBackend",
	// 	"POST",
	// 	"/resend-receipt-email-from-backend",
	// 	apiForBackEnd.ResendReceiptEmailFromBackend,
	// },
	// Route{
	// 	"GetNotCommingFeeForTasker",
	// 	"POST",
	// 	"/get-not-comming-fee-for-tasker",
	// 	apiForBackEnd.GetNotCommingFeeForTasker,
	// },
	// Route{
	// 	"SendEmailRenewSubscription",
	// 	"POST",
	// 	"/send-email-renew-subscription",
	// 	apiForBackEnd.SendEmailRenewSubscription,
	// },
	// Route{
	// 	"SendNotificationByUserIds",
	// 	"POST",
	// 	"/send-notification-by-user-ids",
	// 	apiForBackEnd.SendNotificationByUserIds,
	// },
	// Route{
	// 	"CheckRemoteStatus",
	// 	"POST",
	// 	"/check-remote-status",
	// 	apiForBackEnd.CheckRemoteStatus,
	// },
	// Route{
	// 	"SendNotificationOfNewScheduleTask",
	// 	"POST",
	// 	"/send-notification-of-new-schedule-task",
	// 	apiForBackEnd.SendNotificationOfNewScheduleTask,
	// },
	// Route{
	// 	"SendNotificationOfNewTaskFromBackend",
	// 	"POST",
	// 	"/send-notification-of-new-task-from-backend",
	// 	apiForBackEnd.SendNotificationOfNewTaskFromBackend,
	// },
	// // ====================== API FOR BACKEND
	// Route{
	// 	"RechargePayment",
	// 	"POST",
	// 	"/recharge-payment",
	// 	apiForBackEnd.RechargePayment,
	// },
	// Route{
	// 	"SendNotificationFromBackend",
	// 	"POST",
	// 	"/send-notification-from-backend",
	// 	apiForBackEnd.SendNotificationFromBackend,
	// },
	// ====================== NEW APIs
	Route{
		"ApiAskerVNV3_GetSupportCity",
		"POST",
		"/get-support-city",
		settings.GetSupportCity,
	}, Route{
		"ApiAskerVNV3_ShareGift",
		"POST",
		"/share-gift",
		gift.ShareGift,
	},
	Route{
		"ApiAskerVNV3_GetAllSettingsWithoutLogin",
		"POST",
		"/get-all-settings-without-login",
		settingsV2.GetAllSettingsWithoutLogin,
	},
	Route{
		"ApiAskerVNV3_PostToSlack",
		"POST",
		"/post-to-slack",
		others.PostToSlack,
	},
	Route{
		"ApiAskerVNV3_SetReviewStore",
		"POST",
		"/set-review-store",
		SetReviewStore,
	},
	Route{
		"ApiAskerVNV3_CheckCompareVersionApp",
		"POST",
		"/check-compare-version-app",
		settings.CheckCompareVersionApp,
	},
	// ====================== SERVICE
	Route{
		"ApiAskerVNV3_GetServices",
		"POST",
		"/get-services",
		service.GetServices,
	},
	Route{
		"ApiAskerVNV3_RegisterNewService",
		"POST",
		"/register-new-service",
		service.RegisterNewService,
	},
	// ====================== TASK
	Route{
		"ApiAskerVNV3_CreateUserComplaint",
		"POST",
		"/create-user-complaint",
		task.CreateUserComplaint,
	},
	Route{
		"ApiAskerVNV3_UpdateTaskReport",
		"POST",
		"/update-task-report",
		task.UpdateTaskReport,
	},
	Route{
		"ApiAskerVNV3_GetLastPostTaskCleaning",
		"POST",
		"/get-last-post-task-cleaning",
		task.GetLastPostTaskCleaning,
	},
	Route{
		"ApiAskerVNV3_UpdateVerificationStatus",
		"POST",
		"/update-verification-status",
		task.UpdateVerificationStatus,
	},
	Route{
		"ApiAskerVNV3_GetListHistoryTasks",
		"POST",
		"/get-list-history-tasks",
		task.GetListHistoryTasks,
	},
	Route{
		"ApiAskerVNV3_GetUpCommingTasks",
		"POST",
		"/get-upcomming-tasks",
		task.GetUpCommingTasks,
	},
	Route{
		"ApiAskerVNV3_GetLastPostTask",
		"POST",
		"/get-last-post-task",
		task.GetLastPostTask,
	},
	Route{
		"ApiAskerVNV3_GetDoneTask",
		"POST",
		"/get-done-task",
		task.GetDoneTask,
	},
	Route{
		"ApiAskerVNV3_GetListEmployees",
		"POST",
		"/get-list-employees",
		task.GetListEmployees,
	},
	Route{
		"ApiAskerVNV3_GetTasksConfirmed",
		"POST",
		"/get-tasks-confirmed",
		task.GetTasksConfirmed,
	},
	Route{
		"ApiAskerVNV3_CheckTaskSameTime",
		"POST",
		"/check-task-sametime",
		task.CheckTaskSameTime,
	},
	Route{
		"ApiAskerVNV3_CollectClothes",
		"POST",
		"/collect-clothes",
		task.CollectClothes,
	},
	// Route{
	// 	"TrackingDoneTaskAiqua",
	// 	"POST",
	// 	"/tracking-done-task-aiqua",
	// 	task.TrackingDoneTaskAiqua,
	// },
	Route{
		"ApiAskerVNV3_TaskerWithdrawTask",
		"POST",
		"/tasker-withdraw-task",
		task.TaskerWithdrawTask,
	},
	Route{
		"ApiAskerVNV3_SuggestNextTasks",
		"POST",
		"/suggest-next-tasks",
		task.SuggestNextTasks,
	},
	Route{
		"ApiAskerVNV3_DoneTaskByCompany",
		"POST",
		"/done-task-by-company",
		task.DoneTaskByCompany,
	},
	Route{
		"ApiAskerVNV3_DoneTaskByTasker",
		"POST",
		"/done-task-by-tasker",
		task.DoneTaskByTasker,
	},
	Route{
		"ApiAskerVNV3_TranslateTaskNote",
		"POST",
		"/translate-task-note",
		task.TranslateTaskNote,
	},
	Route{
		"ApiAskerVNV3_GetTaskDetail",
		"POST",
		"/get-task-detail",
		task.GetTaskDetail,
	},
	Route{
		"ApiAskerVNV3_TranslateListBuy",
		"POST",
		"/translate-listbuy",
		task.TranslateListBuy,
	},
	Route{
		"ApiAskerVNV3_TaskerRefuseTask",
		"POST",
		"/tasker-refuse-task",
		task.TaskerRefuseTask,
	},
	Route{
		"ApiAskerVNV3_BeginWork",
		"POST",
		"/begin-work",
		task.BeginWork,
	},
	Route{
		"ApiAskerVNV3_GetTasksNeedRating",
		"POST",
		"/get-tasks-need-rating",
		task.GetTasksNeedRating,
	},
	Route{
		"ApiAskerVNV3_GetTasksDone",
		"POST",
		"/get-tasks-done",
		task.GetTasksDone,
	},
	Route{
		"ApiAskerVNV3_GetTasksConfirmedAsker",
		"POST",
		"/get-tasks-confirmed-asker",
		task.GetTasksConfirmedAsker,
	},
	Route{
		"ApiAskerVNV3_CloseRatingTask",
		"POST",
		"/close-rating-task",
		task.CloseRatingTask,
	},
	// ====================== TASKER TASK HISTORY
	Route{
		"ApiAskerVNV3_DoneTaskHistoryByTasker",
		"POST",
		"/done-task-history-by-tasker",
		taskerTaskHistory.DoneTaskHistoryByTasker,
	},
	// ====================== TASK SCHEDULE
	Route{
		"ApiAskerVNV3_ActiveTaskScheduleByTaskId",
		"POST",
		"/active-task-schedule-by-task-id",
		taskSchedule.ActiveTaskScheduleByTaskId,
	},
	Route{
		"ApiAskerVNV3_GetScheduleTasks",
		"POST",
		"/get-schedule-tasks",
		taskSchedule.GetScheduleTasks,
	},
	Route{
		"ApiAskerVNV3_RemoveTaskSchedule",
		"POST",
		"/remove-task-schedule",
		taskSchedule.RemoveTaskSchedule,
	},
	Route{
		"ApiAskerVNV3_ActiveTaskSchedule",
		"POST",
		"/active-task-schedule",
		taskSchedule.ActiveTaskSchedule,
	},
	Route{
		"ApiAskerVNV3_UpdateScheduleTime",
		"POST",
		"/update-schedule-time",
		taskSchedule.UpdateScheduleTime,
	},
	// ====================== SETTING
	Route{
		"ApiAskerVNV3_GetPartnerMiniAppSettings",
		"POST",
		"/get-partner-mini-app-settings",
		GetPartnerMiniAppSettings,
	},
	Route{
		"ApiAskerVNV3_GetAllSettingsV2",
		"POST",
		"/get-all-settings",
		settingsV2.GetAllSettings,
	},
	// ====================== USER
	Route{
		"ApiAskerVNV3_UpdateUserStatus",
		"POST",
		"/update-user-status",
		user.UpdateUserStatus,
	},
	Route{
		"ApiAskerVNV3_DeleteLocationAsker",
		"POST",
		"/delete-location-asker",
		user.DeleteLocationAsker,
	},
	Route{
		"ApiAskerVNV3_UpdateLocationAsker",
		"POST",
		"/update-location-asker",
		user.UpdateLocationAsker,
	},
	Route{
		"ApiAskerVNV3_GetNumberShared",
		"POST",
		"/get-number-shared",
		user.GetNumberShared,
	},
	Route{
		"ApiAskerVNV3_UpdateUserCountry",
		"POST",
		"/update-user-country",
		user.UpdateUserCountry,
	},
	Route{
		"ApiAskerVNV3_GetSuggestBlackListTaskers",
		"POST",
		"/get-suggest-blacklist-taskers",
		user.GetSuggestBlackListTaskers,
	},
	Route{
		"ApiAskerVNV3_GetBlackListTaskers",
		"POST",
		"/get-blacklist-taskers",
		user.GetBlackListTaskers,
	},
	Route{
		"ApiAskerVNV3_AddFavoriteTasker",
		"POST",
		"/add-favorite-tasker",
		user.AddFavoriteTasker,
	},
	Route{
		"ApiAskerVNV3_RemoveFavoriteTasker",
		"POST",
		"/remove-favorite-tasker",
		user.RemoveFavoriteTasker,
	},
	Route{
		"ApiAskerVNV3_AddTaskerToBlackList",
		"POST",
		"/add-blackList-tasker",
		user.AddTaskerToBlackList,
	},
	Route{
		"ApiAskerVNV3_RemoveTaskerFromBlackList",
		"POST",
		"/remove-tasker-from-blackList",
		user.RemoveTaskerFromBlackList,
	},
	Route{
		"ApiAskerVNV3_GetFavoriteTasker",
		"POST",
		"/get-favorite-tasker",
		user.GetFavoriteTasker,
	},
	Route{
		"ApiAskerVNV3_GetUser",
		"POST",
		"/get-user",
		user.GetUser,
	},
	Route{
		"ApiAskerVNV3_ClearWrongAccount",
		"POST",
		"/clear-wrong-account",
		user.ClearWrongAccount,
	},
	Route{
		"ApiAskerVNV3_GetCancelTaskHistory",
		"POST",
		"/get-cancel-task-history",
		user.GetCancelTaskHistory,
	},
	Route{
		"ApiAskerVNV3_UpdateUserInfo",
		"POST",
		"/update-user-info",
		user.UpdateUserInfo,
	},
	Route{
		"ApiAskerVNV3_GetLanguageAsker",
		"POST",
		"/get-language-asker",
		user.GetLanguageAsker,
	},
	Route{
		"ApiAskerVNV3_GetListEmployeesFromTaskerID",
		"POST",
		"/get-list-employees-from-tasker-id",
		user.GetListEmployeesFromTaskerID,
	},
	Route{
		"ApiAskerVNV3_GetUserByPhone",
		"POST",
		"/get-user-by-phone",
		user.GetUserByPhone,
	},
	Route{
		"ApiAskerVNV3_UpdateFreeSchedule",
		"POST",
		"/update-free-schedule",
		user.UpdateFreeSchedule,
	},
	Route{
		"ApiAskerVNV3_CheckUserContactV2",
		"POST",
		"/check-user-contact-v2",
		user.CheckUserContactV2,
	},
	Route{
		"ApiAskerVNV3_SaveUserHistory",
		"POST",
		"/set-user-location-history",
		user.SaveUserHistory,
	},
	Route{
		"ApiAskerVNV3_GetListUsers",
		"POST",
		"/get-list-users",
		user.GetListUsers,
	},
	Route{
		"ApiAskerVNV3_LockAbleDoQuizz",
		"POST",
		"/lock-able-do-quizz",
		user.LockAbleDoQuizz,
	},
	Route{
		"ApiAskerVNV3_LockResetClientData",
		"POST",
		"/lock-reset-client-data",
		user.LockResetClientData,
	},
	Route{
		"ApiAskerVNV3_AddLocationAsker",
		"POST",
		"/add-location-asker",
		user.AddLocationAsker,
	},
	Route{
		"ApiAskerVNV3_NotifyIncommingCall",
		"POST",
		"/notify-incomming-call",
		user.NotifyIncommingCall,
	},
	Route{
		"ApiAskerVNV3_UpdateUserLastOnline_native",
		"POST",
		"/update-user-last-online-native",
		user.UpdateUserLastOnline_native,
	},
	Route{
		"ApiAskerVNV3_GetAllMoney",
		"POST",
		"/get-all-money",
		user.GetAllMoney,
	},
	Route{
		"ApiAskerVNV3_UpdateUserLanguage",
		"POST",
		"/update-user-language",
		user.UpdateUserLanguage,
	},
	Route{
		"ApiAskerVNV3_MigrationUserBeforeLogin",
		"POST",
		"/migration-user-before-login",
		user.MigrationUserBeforeLogin,
	},
	Route{
		"ApiAskerVNV3_UpdateUserAvatar",
		"POST",
		"/update-user-avatar",
		user.UpdateUserAvatar,
	},
	Route{
		"ApiAskerVNV3_UpdateHousekeeping",
		"POST",
		"/update-housekeeping",
		housekeeping.UpdateHousekeeping,
	},
	Route{
		"ApiAskerVNV3_GetHousekeepingList",
		"POST",
		"/get-housekeeping-list",
		housekeeping.GetHousekeepingList,
	},
	Route{
		"ApiAskerVNV3_GetHousekeeping",
		"POST",
		"/get-housekeeping",
		housekeeping.GetHousekeeping,
	},
	Route{
		"ApiAskerVNV3_RemoveHousekeeping",
		"POST",
		"/remove-housekeeping",
		housekeeping.RemoveHousekeeping,
	},
	Route{
		"ApiAskerVNV3_RemoveRoomHousekeeping",
		"POST",
		"/remove-room-housekeeping",
		housekeeping.RemoveRoom,
	},
	Route{
		"ApiAskerVNV3_UpdateTaskRoomsHousekeeping",
		"POST",
		"/update-task-rooms-housekeeping",
		housekeeping.UpdateTaskRooms,
	},
	// ====================== FINANCIAL ACCOUNT
	Route{
		"ApiAskerVNV3_GetFinancialAccount",
		"POST",
		"/get-financial-account",
		financialAccount.GetFinancialAccount,
	},
	// ====================== REWARD
	Route{
		"ApiAskerVNV3_GetReward",
		"POST",
		"/get-reward",
		reward.GetReward,
	},
	Route{
		"ApiAskerVNV3_ViewReward",
		"POST",
		"/view-reward",
		reward.ViewReward,
	},
	// ====================== RATING
	Route{
		"ApiAskerVNV3_GetTaskerReviews",
		"POST",
		"/get-tasker-reviews",
		rating.GetTaskerReviews,
	},
	Route{
		"ApiAskerVNV3_GetRatingByTaskerId",
		"POST",
		"/get-rating-by-tasker-id",
		rating.GetRatingByTaskerId,
	},
	// ====================== NOTIFICATION
	Route{
		"ApiAskerVNV3_RemoveNotificationNotFromBtaskeeById",
		"POST",
		"/remove-notification-not-from-btaskee-by-id",
		notification.RemoveNotificationNotFromBtaskeeById,
	},
	Route{
		"ApiAskerVNV3_GetNotificationNotFromBtaskee",
		"POST",
		"/get-notification-not-from-btaskee",
		notification.GetNotificationNotFromBtaskee,
	},
	Route{
		"ApiAskerVNV3_GetNotification",
		"POST",
		"/get-notification",
		notification.GetNotification,
	},
	Route{
		"ApiAskerVNV3_GetNotificationV2",
		"POST",
		"/get-notification-not-from-btaskee-v2",
		GetNotificationV2,
	},
	Route{
		"ApiAskerVNV3_GetNotificationDetail",
		"POST",
		"/get-notification-detail",
		notification.GetNotificationDetail,
	},
	Route{
		"ApiAskerVNV3_V2RemoveAllNotificationsOfUser",
		"POST",
		"/remove-notification-user",
		notification.V2RemoveAllNotificationsOfUser,
	},
	Route{
		"ApiAskerVNV3_GetNotificationTasker",
		"POST",
		"/get-notification-tasker",
		notification.GetNotificationTasker,
	},
	Route{
		"ApiAskerVNV3_RemoveNotificationById",
		"POST",
		"/remove-notification-by-id",
		notification.RemoveNotificationById,
	},
	Route{
		"ApiAskerVNV3_UpdateStatusNotification",
		"POST",
		"/update-status-notification",
		notification.UpdateStatusNotification,
	},
	Route{
		"ApiAskerVNV3_UpdateReceiveNotification",
		"POST",
		"/update-receive-notification",
		notification.UpdateReceiveNotification,
	},
	Route{
		"ApiAskerVNV3_RemoveAllNotification",
		"POST",
		"/remove-all-notification",
		notification.RemoveAllNotification,
	},
	Route{
		"ApiAskerVNV3_LoadMoreNotification",
		"POST",
		"/load-more-notification",
		notification.LoadMoreNotification,
	},
	// ====================== TASKER SETTINGS
	Route{
		"ApiAskerVNV3_GetSafeWorkingProcess",
		"POST",
		"/get-safe-working-process",
		taskerSettings.GetSafeWorkingProcess,
	},
	// ====================== CHAT MESSAGE
	Route{
		"ApiAskerVNV3_SetIsReadChatMessage",
		"POST",
		"/set-is-read-chat-message",
		SetIsReadChatMessage,
	},
	Route{
		"ApiAskerVNV3_GetListChat",
		"POST",
		"/get-list-chat",
		GetListChat,
	},
	Route{
		"ApiAskerVNV3_GetChatHistoryByTask",
		"POST",
		"/get-chat-history-by-task",
		GetChatHistoryByTask,
	},
	Route{
		"ApiAskerVNV3_SendChatMessage",
		"POST",
		"/send-chat-message",
		sendChatMessage.SendChatMessage,
	},
	Route{
		"ApiAskerVNV3_InitConversation",
		"POST",
		"/init-conversation",
		chatMessage.InitConversation,
	},
	Route{
		"ApiAskerVNV3_MessageNewTasker",
		"POST",
		"/message-new-tasker",
		chatMessage.MessageNewTasker,
	},
	Route{
		"ApiAskerVNV3_TranslateMessage",
		"POST",
		"/translate-message",
		chatMessage.TranslateMessage,
	},
	Route{
		"ApiAskerVNV3_GetChatListChatMessage",
		"POST",
		"/get-chat-list-chat-message",
		GetChatListChatMessage,
	},
	// ====================== SUBSCRIPTION SUGGEST
	Route{
		"ApiAskerVNV3_CancelSubscriptionSuggestion",
		"POST",
		"/cancel-subscription-suggestion",
		subscription.CancelSubscriptionSuggestion,
	},
	Route{
		"ApiAskerVNV3_GetSubscriptionSuggestion",
		"POST",
		"/get-subscription-suggestion",
		subscription.GetSubscriptionSuggestion,
	},
	Route{
		"ApiAskerVNV3_SuggestSubscriptionGetTask",
		"POST",
		"/suggest-subscription-get-task",
		subscription.SuggestSubscriptionGetTask,
	},
	Route{
		"ApiAskerVNV3_SuggestSubscriptionNewSubscription",
		"POST",
		"/suggest-subscription-new-subscription",
		subscription.SuggestSubscriptionNewSubscription,
	},
	Route{
		"ApiAskerVNV3_SuggestSubscriptionCheckRequest",
		"POST",
		"/suggest-subscription-check-request",
		subscription.SuggestSubscriptionCheckRequest,
	},
	Route{
		"ApiAskerVNV3_SuggestSubscriptionGetSubscription",
		"POST",
		"/suggest-subscription-get",
		subscription.SuggestSubscriptionGetSubscription,
	},
	Route{
		"ApiAskerVNV3_GetInfoSubscription",
		"POST",
		"/get-info-subscription",
		subscription.GetInfoSubscription,
	},
	Route{
		"ApiAskerVNV3_GetSubscriptionByUserId",
		"POST",
		"/get-subscription-by-userId",
		subscription.GetSubscriptionByUserId,
	},
	Route{
		"ApiAskerVNV3_CancelSubscription",
		"POST",
		"/cancel-subscription",
		subscription.CancelSubscription,
	},
	// ====================== FA TRANSACTION
	Route{
		"ApiAskerVNV3_GetFinancialTransaction",
		"POST",
		"/get-financial-transaction",
		FATransaction.GetFinancialTransaction,
	},
	Route{
		"ApiAskerVNV3_GetDetailFinancialTransaction",
		"POST",
		"/get-detail-financial-transaction",
		FATransaction.GetDetailFinancialTransaction,
	},
	// Route{
	// 	"GetAdsCarTransactionHistory",
	// 	"POST",
	// 	"/get-ads-car-transaction-history",
	// 	FATransaction.GetAdsCarTransactionHistory,
	// },
	// ====================== SERVICE CHANNEL
	Route{
		"ApiAskerVNV3_GetServicesChannel",
		"POST",
		"/get-service-channel",
		serviceChannel.GetServicesChannel,
	},
	Route{
		"ApiAskerVNV3_AddUserIntoServiceChannelV2",
		"POST",
		"/add-user-to-service-channel",
		serviceChannel.AddUserIntoServiceChannelV2,
	},
	// ====================== TASK REPORT TASKER
	Route{
		"ApiAskerVNV3_GetTaskerReport",
		"POST",
		"/get-tasker-report",
		taskReportTasker.GetTaskerReport,
	},
	// ====================== WORKING PLACES
	Route{
		"ApiAskerVNV3_GetSignUpWorkingPlaces",
		"POST",
		"/get-sign-up-working-places",
		workingPlaces.GetSignUpWorkingPlaces,
	},
	// ====================== TASKER TRAINING
	// Route{
	// 	"GetScore",
	// 	"POST",
	// 	"/get-score",
	// 	taskerTraining.GetScore,
	// },
	// Route{
	// 	"SetTrainingTasker",
	// 	"POST",
	// 	"/set-training-tasker",
	// 	taskerTraining.SetTrainingTasker,
	// },
	// Route{
	// 	"SetDoQuizzResult",
	// 	"POST",
	// 	"/set-do-quizz-result",
	// 	taskerTraining.SetDoQuizzResult,
	// },
	// Route{
	// 	"SetDoSomeQuizz",
	// 	"POST",
	// 	"/set-do-some-quizz",
	// 	taskerTraining.SetDoSomeQuizz,
	// },
	// Route{
	// 	"SetDoTrainingTasker",
	// 	"POST",
	// 	"/set-do-training-tasker",
	// 	taskerTraining.SetDoTrainingTasker,
	// },
	// Route{
	// 	"SetDoTrainingInput",
	// 	"POST",
	// 	"/set-do-training-input",
	// 	taskerTraining.SetDoTrainingInput,
	// },
	// ====================== PAYMENT CARD
	Route{
		"ApiAskerVNV3_GetListCards",
		"POST",
		"/get-list-cards",
		paymentCard.GetListCards,
	},
	Route{
		"ApiAskerVNV3_SetPaymentCardDefault",
		"POST",
		"/set-payment-card-default",
		paymentCard.SetPaymentCardDefault,
	},
	// ====================== GIFT
	Route{
		"ApiAskerVNV3_CountNewGift",
		"POST",
		"/count-new-gift",
		CountNewGift,
	},
	Route{
		"ApiAskerVNV3_CountNewGiftV2",
		"POST",
		"/count-new-gift-v2",
		CountNewGiftV2,
	},
	Route{
		"ApiAskerVNV3_GetGiftDetail",
		"POST",
		"/get-gift-detail",
		gift.GetGiftDetail,
	},
	Route{
		"ApiAskerVNV3_GetNewGift",
		"POST",
		"/get-new-gift",
		gift.GetNewGift,
	},
	Route{
		"ApiAskerVNV3_GetUsedGift",
		"POST",
		"/get-used-gift",
		gift.GetUsedGift,
	},
	Route{
		"ApiAskerVNV3_GetGift",
		"POST",
		"/get-gift",
		gift.GetGift,
	},
	Route{
		"ApiAskerVNV3_GetUserGift",
		"POST",
		"/get-user-gift",
		gift.GetUserGift,
	},
	Route{
		"ApiAskerVNV3_RedeemGift",
		"POST",
		"/redeem-gift",
		gift.RedeemGift,
	},
	// ====================== RAIX PUSH APP TOKEN
	Route{
		"ApiAskerVNV3_UpdateHistoryToken",
		"POST",
		"/update-history-token",
		raixPushAppTokens.UpdateHistoryToken,
	},
	Route{
		"ApiAskerVNV3_InitRaixPushToken",
		"POST",
		"/init-raix-push-token",
		raixPushAppTokens.InitRaixPushToken,
	},
	// ====================== INCENTIVE
	Route{
		"ApiAskerVNV3_GetSpecialIncentive",
		"POST",
		"/get-special-incentive",
		incentive.GetSpecialIncentive,
	},
	Route{
		"ApiAskerVNV3_GetIncentive",
		"POST",
		"/get-incentive",
		incentive.GetIncentive,
	},
	Route{
		"ApiAskerVNV3_GetIncentiveDetail",
		"POST",
		"/get-incentive-detail",
		incentive.GetIncentiveDetail,
	},
	// ====================== POINT TRANSACTION
	Route{
		"ApiAskerVNV3_GetListPointTransactionUsed",
		"POST",
		"/get-list-point-transaction-used",
		pointTransaction.GetListPointTransactionUsed,
	},
	Route{
		"ApiAskerVNV3_GetListPointTransactionReceived",
		"POST",
		"/get-list-point-transaction-received",
		pointTransaction.GetListPointTransactionReceived,
	},
	// ====================== OTHERS
	Route{
		"ApiAskerVNV3_initialDataV2",
		"POST",
		"/initial-data-v2",
		others.InitialDataV2,
	},
	Route{
		"ApiAskerVNV3_getAnalysisTask",
		"POST",
		"/get-analysis-task",
		others.GetAnalysisTask,
	},
	// Route{
	// 	"GetAdsCarHome",
	// 	"POST",
	// 	"/get-ads-car-home",
	// 	others.GetAdsCarHome,
	// },
	// Route{
	// 	"SendLogger",
	// 	"POST",
	// 	"/send-logger",
	// 	others.SendLogger,
	// },
	// ==================== FEEDBACK
	Route{
		"ApiAskerVNV3_CreateFeedback",
		"POST",
		"/create-feedback",
		feedback.CreateFeedback,
	},
	// ==================== OutstandingPayment
	Route{
		"ApiAskerVNV3_GetOutstandingPayment",
		"POST",
		"/get-outstanding-payment",
		outstandingPayment.GetOutstandingPayment,
	},
	// ====================== REPORT TRANSACTION
	Route{
		"ApiAskerVNV3_CreateReportTransaction",
		"POST",
		"/create-report-transaction",
		reportTransaction.CreateReportTransaction,
	},
	//Noel campaign 2024
	Route{
		"ApiAskerVNV3_DailyRollCall",
		"POST",
		"/daily-roll-call",
		DailyRollCall,
	},
	Route{
		"ApiAskerVNV3_GetWeeklyRollCall",
		"POST",
		"/get-weekly-roll-call",
		GetWeeklyRollCall,
	},
	Route{
		"ApiAskerVNV3_GetHistoryReceiveSpin",
		"POST",
		"/get-history-receive-spin",
		GetHistoryReceiveSpin,
	},
	Route{
		"ApiAskerVNV3_GetHistoryReceiveReward",
		"POST",
		"/get-history-receive-reward",
		GetHistoryReceiveReward,
	},
	Route{
		"ApiAskerVNV3_SendCallChatMessage",
		"POST",
		"/send-call-chat-message",
		SendCallChatMessage,
	},
	Route{
		"ApiAskerVNV3_SendNotiToRemindFavouriteTasker",
		"POST",
		"/send-noti-to-remind-favourite-tasker",
		SendNotiToRemindFavouriteTasker,
	},
	Route{
		"ApiAskerVNV3_LixiTasker",
		"POST",
		"/lixi-tasker",
		LixiTasker,
	},
	// ====================== Bundle Voucher
	Route{
		"ApiAskerVNV3_GetListBundleVoucher",
		"POST",
		"/get-list-bundle-voucher",
		GetListBundleVoucher,
	},
	Route{
		"ApiAskerVNV3_RedeemBundleVoucher",
		"POST",
		"/redeem-bundle-voucher",
		RedeemBundleVoucher,
	},
}
