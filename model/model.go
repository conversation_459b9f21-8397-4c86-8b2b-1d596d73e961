package model

import (
	"time"

	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelMarketingCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/marketingCampaign"
	modelNextExpansion "gitlab.com/btaskee/go-services-model-v2/grpcmodel/nextExpansion"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSurveyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/surveyReport"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

type ApiRequest struct {
	ID                    string                                   `json:"id,omitempty"`
	Fields                []*string                                `json:"fields,omitempty"`
	Phone                 string                                   `json:"phone,omitempty"`
	UserId                string                                   `json:"userId,omitempty"`
	TaskStatus            string                                   `json:"taskStatus,omitempty"`
	NumberOfTransactions  int64                                    `json:"numberOfTransactions,omitempty"` //limit the number of financial account transaction
	Token                 string                                   `json:"token,omitempty"`
	ISOCode               string                                   `json:"isoCode,omitempty"`
	Metadata              *ApiRequestMetaData                      `json:"metadata,omitempty"`
	LastPostedTask        *time.Time                               `json:"lastPostedTask,omitempty"`
	TaskDate              *time.Time                               `json:"taskDate,omitempty"`
	Duration              float64                                  `json:"duration,omitempty"`
	PromotionCode         string                                   `json:"promotionCode,omitempty"`
	ServiceId             string                                   `json:"serviceId,omitempty"`
	AppVersion            string                                   `json:"appVersion,omitempty"`
	TaskId                string                                   `json:"taskId,omitempty"`
	Lat                   float64                                  `json:"lat,omitempty"`
	Lng                   float64                                  `json:"lng,omitempty"`
	Ip                    string                                   `json:"ip,omitempty"`
	RoomIds               []string                                 `json:"roomIds,omitempty"`
	CountryCode           string                                   `json:"countryCode,omitempty"`
	Email                 string                                   `json:"email,omitempty"`
	Name                  string                                   `json:"name,omitempty"`
	Introduction          string                                   `json:"introduction,omitempty"`
	Message               string                                   `json:"message,omitempty"`
	ChatId                string                                   `json:"chatId,omitempty"`
	Key                   string                                   `json:"key,omitempty"`
	Text                  string                                   `json:"text,omitempty"`
	Language              string                                   `json:"language,omitempty"`
	CreatedAt             *time.Time                               `json:"createdAt,omitempty"`
	UserIds               []string                                 `json:"userIds,omitempty"`
	ReferralCode          string                                   `json:"referralCode,omitempty"`
	Month                 interface{}                              `json:"month,omitempty"`
	IsCompany             bool                                     `json:"isCompany,omitempty"`
	ServiceName           string                                   `json:"serviceName,omitempty"`
	Cost                  float64                                  `json:"cost,omitempty"`
	Address               string                                   `json:"address,omitempty"`
	BeginAt               *time.Time                               `json:"beginAt,omitempty"`
	FreeSchedule          map[int]interface{}                      `json:"freeSchedule,omitempty"`
	Time                  *time.Time                               `json:"time,omitempty"`
	SubscriptionId        string                                   `json:"subscriptionId,omitempty"`
	CurrentCourse         map[string]interface{}                   `json:"currentCourse,omitempty"`
	EmployeeId            string                                   `json:"employeeId,omitempty"`
	IsRead                *bool                                    `json:"isRead"`
	IsDefault             bool                                     `json:"isDefault,omitempty"`
	CardId                string                                   `json:"cardId,omitempty"`
	NoReceiveNotification bool                                     `json:"noReceiveNotification"`
	IsForceUpdate         bool                                     `json:"isForceUpdate,omitempty"`
	Amount                float64                                  `json:"amount,omitempty"`
	FromDate              *time.Time                               `json:"fromDate,omitempty"`
	ToDate                *time.Time                               `json:"toDate,omitempty"`
	From                  string                                   `json:"from,omitempty"`
	ScheduleId            string                                   `json:"scheduleId,omitempty"`
	IsActive              bool                                     `json:"isActive,omitempty"`
	Weekday               []int                                    `json:"weekday,omitempty"`
	ScheduleTime          *time.Time                               `json:"scheduleTime,omitempty"`
	ScheduleDuration      float32                                  `json:"scheduleDuration,omitempty"`
	PaymentMethod         string                                   `json:"paymentMethod,omitempty"`
	TaskerId              string                                   `json:"taskerId,omitempty"`
	TaskerIds             []string                                 `json:"taskerIds,omitempty"`
	FilterBy              map[string]interface{}                   `json:"filterBy,omitempty"`
	SortBy                string                                   `json:"sortBy,omitempty"`
	GiftId                string                                   `json:"giftId,omitempty"`
	SendFrom              string                                   `json:"sendFrom,omitempty"`
	AvatarUrl             string                                   `json:"avatarUrl,omitempty"`
	Data                  map[string]interface{}                   `json:"data,omitempty"`
	CampaignId            string                                   `json:"campaignId,omitempty"`
	CampaignAction        string                                   `json:"campaignAction,omitempty"`
	LocationId            string                                   `json:"locationId,omitempty"`
	VerificationData      map[string]interface{}                   `json:"verificationData,omitempty"`
	Type                  string                                   `json:"type,omitempty"`
	ReportData            map[string]interface{}                   `json:"reportData,omitempty"`
	SubscriptionRequestId string                                   `json:"subscriptionRequestId,omitempty"`
	TransactionId         string                                   `json:"transactionId,omitempty"`
	ReasonReport          []*modelService.ServiceText              `json:"reasonReport,omitempty"`
	OtherReport           string                                   `json:"otherReport,omitempty"`
	Platform              string                                   `json:"platform,omitempty"`
	Version               string                                   `json:"version,omitempty"`
	MessageId             string                                   `json:"messageId,omitempty"`
	Place                 *modelNextExpansion.NextExpansionPlace   `json:"place,omitempty"`
	Year                  int                                      `json:"year,omitempty"`
	FromPartner           string                                   `json:"fromPartner,omitempty"`
	Reason                *modelService.ServiceText                `json:"reason,omitempty"`
	ComboVoucherId        string                                   `json:"comboVoucherId,omitempty"`
	UserComboVoucherId    string                                   `json:"userComboVoucherId,omitempty"`
	MessageTo             []string                                 `json:"messageTo,omitempty"`
	PartnerCode           string                                   `json:"partnerCode,omitempty"`
	Point                 float64                                  `json:"point,omitempty"`
	Count                 int                                      `json:"count,omitempty"`
	IsReviewStore         bool                                     `json:"isReviewStore,omitempty"`
	AskerId               string                                   `json:"askerId,omitempty"`
	FavouriteServiceIds   []string                                 `json:"favouriteServiceIds,omitempty"`
	SurveyId              string                                   `json:"surveyId,omitempty"`
	SurveyAnswers         []*modelSurveyReport.SurveyReportAnswers `json:"surveyAnswers,omitempty"`
	LoginToken            string                                   `json:"loginToken,omitempty"`
	TaskCost              float64                                  `json:"taskCost,omitempty"`
	TaskPlace             *modelTask.TaskPlace                     `json:"taskPlace,omitempty"`
	MemberIds             []string                                 `json:"memberIds,omitempty"`
	GameCampaignId        string                                   `json:"gameCampaignId,omitempty"`
	Status                string                                   `json:"status,omitempty"`
	IsExpired             bool                                     `json:"isExpired,omitempty"`
	AdditionalReason      string                                   `json:"additionalReason,omitempty"`
	EventId               string                                   `json:"eventId,omitempty"`
	ActivatedServices     []string                                 `json:"activatedServices,omitempty"`
	DetailBeautyCare      *modelTask.TaskDetailBeautyCare          `json:"detailBeautyCare,omitempty"`
	ServiceIds            []string                                 `json:"serviceIds,omitempty"`
	DetailMakeup          *modelTask.TaskDetailBeautyCare          `json:"detailMakeup,omitempty"`
	DetailHairStyling     *modelTask.TaskDetailBeautyCare          `json:"detailHairStyling,omitempty"`
	DetailNail            *modelTask.TaskDetailBeautyCare          `json:"detailNail,omitempty"`
	IsOnlyDealsCanBuy     bool                                     `json:"IsOnlyDealsCanBuy,omitempty"`
	BundleVoucherId       string                                   `json:"bundleVoucherId,omitempty"`
	// For pagination
	Page  int `json:"page,omitempty"`
	Skip  int `json:"skip,omitempty"`
	Limit int `json:"limit,omitempty"`
}

type ApiRequestMetaData struct {
	Version string `json:"version,omitempty"`
}

type UrBoxErrorCode struct {
	Message string
	Code    string
}

type ApiBackEndRequest struct {
	From                string                       `json:"from,omitempty"`
	To                  string                       `json:"to,omitempty"`
	Message             map[string]interface{}       `json:"message,omitempty"`
	TaskId              string                       `json:"taskId,omitempty"`
	TaskDate            *time.Time                   `json:"taskDate,omitempty"`
	CancelDate          *time.Time                   `json:"cancelDate,omitempty"`
	TaskCost            float64                      `json:"taskCost,omitempty"`
	CancellationReason  string                       `json:"cancellationReason,omitempty"`
	TaskerNotCommingFee *modelSettings.NotCommingFee `json:"taskerNotCommingFee,omitempty"`
	UserId              string                       `json:"userId,omitempty"`
	SubscriptionId      string                       `json:"subscriptionId,omitempty"`
	RefundReason        string                       `json:"refundReason,omitempty"`
	Option              *InputOption                 `json:"option,omitempty"`
}

type InputOption struct {
	From string                 `json:"from,omitempty"`
	Data map[string]interface{} `json:"data,omitempty"`
}

type StringeeRequest struct {
	From   string `json:"from,omitempty"`
	To     string `json:"to,omitempty"`
	Custom string `json:"custom,omitempty"`
}

type RelatedServiceInfo struct {
	XId          string                    `json:"_id,omitempty"`
	Name         string                    `json:"name,omitempty"`
	Thumbnail    string                    `json:"thumbnail,omitempty"`
	Text         *modelService.ServiceText `json:"text,omitempty"`
	IsNewService bool                      `json:"isNewService,omitempty"`
}

type MarketingCampaignDetailWithPaymentMethod struct {
	*modelMarketingCampaign.MarketingCampaign
	PaymentMethods []string `json:"paymentMethods,omitempty"`
}

type GiftDetailWithPaymentMethod struct {
	*modelGift.Gift
	PaymentMethods []string `json:"paymentMethods,omitempty"`
}

type UserGameCampaignAction struct {
	UserId    string
	TaskId    string
	ServiceId string
	Action    string
	Errors    []string
	TryTime   int32
}

type DailyRollCallResponse struct {
	IsChecked       bool      `json:"isChecked"`
	Date            time.Time `json:"date"`
	BackgroundImage string    `json:"backgroundImage"`
}

type BusinessRequest struct {
	Phones          []string             `json:"phones,omitempty"`
	BusinessId      string               `json:"businessId,omitempty"`
	MemberInfos     []BusinessMemberInfo `json:"memberInfos,omitempty"`
	MemberIds       []string             `json:"memberIds,omitempty"`
	LevelId         string               `json:"levelId,omitempty"`
	LevelIds        []string             `json:"levelIds,omitempty"`
	MemberId        string               `json:"memberId,omitempty"`
	UserId          string               `json:"userId,omitempty"`
	Name            string               `json:"name,omitempty"`
	Email           string               `json:"email,omitempty"`
	TaxCode         string               `json:"taxCode,omitempty"`
	Sector          string               `json:"sector,omitempty"`
	BusinessSize    string               `json:"businessSize,omitempty"`
	BusinessLicense []BusinessLicense    `json:"businessLicense,omitempty"`
	Amount          *float64             `json:"amount,omitempty"` // can be 0
	Month           int32                `json:"month,omitempty"`
	Year            int32                `json:"year,omitempty"`
	Address         string               `json:"address,omitempty"`
}

type BusinessMemberInfo struct {
	UserId  string `json:"userId,omitempty"`
	LevelId string `json:"levelId,omitempty"`
	Phone   string `json:"phone,omitempty"`
}

type BusinessLicense struct {
	Name string `json:"name,omitempty"`
	Url  string `json:"url,omitempty"`
}
