Version v3.7.13-asker-vn
- Date: 31/05/2025
- Improvements:
- Features:
  - Issue #142: Feature: Asker Get list history task
  - Issue #143: Feature: imporve rule get bReward
  - Issue #63: Feature: bundle voucher
- Bug fixes:

Version v3.7.12-asker-vn
- Date: 21/05/2025
- Improvements:
- Features:
  - Issue #68: Feature: chat and book with favorite tasker elderly care service
  - Issue #57: Feature: chat and book with favorite tasker child care service
  - Issue #58: Feature: chat and book with favorite tasker patient care service
- Bug fixes:

Version v3.7.11-asker-vn
- Date: 20/05/2025
- Improvements:
- Features:
  - Issue #139: [Feature] New api for service nail/makeup/hairstyling
- Bug fixes:

Version v3.7.10-asker-vn
- Date: 13/05/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #141: [Feature] [BTK-DEV-1166]

Version v3.7.9-asker-vn
- Date: 13/05/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #140: Hotfix: Update field voiceCallTokenV2 for api get user v4

Version v3.7.8-asker-vn
- Date: 13/05/2025
- Improvements:
- Features:
  - Issue #110: [Feature] [BTK-7891] Update api to send call status to chat conversation
- Bug fixes:

Version v3.7.7-asker-vn
- Date: 09/05/2025
- Improvements:
- Features:
  - Issue #138: [Feature] [BTK-DEV-473] Open grpc for redeem gift api
- Bug fixes:

Version v3.7.6-asker-vn
- Date: 08/05/2025
- Improvements:
- Features:
  - Issue #65: Feature: chat and book with favorite tasker home cooking service
  - Issue #78: Feature: Apply stream-chat-go to generate voice call token
- Bug fixes:

Version v3.7.5-asker-vn
- Date: 06/05/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #136: [Hotfix] Fix mssing member.id api getuser

Version v3.7.4-asker-vn
- Date: 29/04/2025
- Improvements:
- Features:
  - Issue #109: Feature: get business info for business member
- Bug fixes:

Version v3.7.3-asker-vn
- Date: 28/04/2025
- Improvements:
- Features:
  - Issue #79: Feature: open beauty care service
- Bug fixes:

Version v3.7.2-asker-vn
- Date: 26/04/2025
- Improvements:
- Features:
  - Issue #135: Feature: update model marketing campaign
- Bug fixes:

Version v3.7.1-asker-vn
- Date: 23/04/2025
- Improvements:
- Features:
  - Issue #127: Feature: add reward data to get marketing campaign
- Bug fixes:

Version v3.7.0-asker-vn
- Date: 23/04/2025
- Improvements:
- Features:
- Bug fixes:

Version v3.6.17-asker-vn
- Date: 22/04/2025
- Improvements:
- Features:
  - Issue #132: Feature: Apply rule reddem gift one time
- Bug fixes:

Version asker-vn-3.6.16
- Date: 17/04/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue: Update model v1.32.64

Version asker-vn-3.6.15
- Date: 15/04/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #123: [Hotfix] Fix get my reward is error grpc: received message larger than max

Version asker-vn-3.6.14
- Date: 09/04/2025
- Improvements:
- Features:
- Bug fixes:

Version asker-vn-3.6.13
- Date: 09/04/2025
- Improvements:
- Features:
  - Issue #129: [Feature] [BTK-] Add jwt handle middleware to get userid from jwt payload. add new api v5
- Bug fixes:

Version asker-vn-3.6.12
- Date: 08/04/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #130: [Hotfix] Set default currency for transaction business

Version asker-vn-3.6.11
- Date: 01/04/2025
- Improvements:
- Features:
  - Issue #112: [Feature] [BTK-7616] Add new field to show option number of month to choose when booking subscription in app to service model
- Bug fixes:

Version asker-vn-3.6.10
- Date: 29/03/2025
- Improvements:
- Features:
  - Issue #118: Feature: Add theme for community
  - Issue #125: [Feature] [BTK-8350] Open puzzle game campaign. return challanges list to app
- Bug fixes:

Version 3.6.9
- Date: 24/03/2025
- Improvements:
- Features:
  - Issue #122: Improve: Apply JWT
- Bug fixes:

Version 3.6.8
- Date: 24/03/2025
- Improvements:
- Features:
  - Issue #121: [Feature] Fix can not show subs for asker beacuse month is null
- Bug fixes:

Version 3.6.7
- Date: 16/03/2025
- Improvements:
- Features:
  - Issue #116: Improve: Apply JWT Authorization
- Bug fixes:

Version 3.6.6
- Date: 05/03/2025
- Improvements:
- Features:
  - Issue: Update model 1.32.45
- Bug fixes:

Version 3.6.5
- Date: 03/03/2025
- Improvements:
- Features:
  - Issue #111: [Feature] [BTK-7666] Add function check tasker has added this asker to blacklist, cannot book task with this tasker
- Bug fixes:

Version 3.6.4
- Date: 27/02/2025
- Improvements:
- Features:
  - Issue #108: Improve: Require update version game campaign
- Bug fixes:

Version 3.6.3
- Date: 26/02/2025
- Improvements:
- Features:
  - Issue #59: Feature: subscription voucher
  - Issue #103: Feature: special pre-booking
- Bug fixes:

Version 3.6.2
- Date: 24/02/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #107: [Hotfix] [BTK-7835] Update env var name

Version 3.6.1
- Date: 22/02/2025
- Improvements:
- Features:
- Bug fixes:

Version 3.6.0
- Date: 21/02/2025
- Improvements:
- Features:
  - Issue #105: Feature: Upgrade go version
- Bug fixes:

Version 3.5.17
- Date: 17/02/2025
- Improvements:
- Features:
  - Issue #102: Feature: Config community setting
- Bug fixes:

Version 3.5.16
- Date: 13/02/2025
- Improvements:
- Features:
  - Issue #97: Improve: add address for create asker business
- Bug fixes:

Version 3.5.15
- Date: 07/02/2025
- Improvements:
- Features:
  - Issue #70: Feature: Get gift by hour ranges
- Bug fixes:

Version 3.5.14
- Date: 26/01/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #101: [Hotfix] Fix error concurrent read and write to map

Version 3.5.13
- Date: 24/01/2025
- Improvements:
- Features:
  - Issue #99: [Feature] Open feature asker use bpoint to exchange reward then send to tasker
- Bug fixes:

Version 3.5.12
- Date: 20/01/2025
- Improvements:
- Features:
  - Issue #95: Feature: Refactor fav chat
- Bug fixes:

Version 3.5.11
- Date: 09/01/2025
- Improvements:
- Features:
  - Issue #44: [Feature] Add field not apply for service in payment method to check if asker cannot pay task of the service with this method
  - Issue #94: Feature: Improve keyword suggestion message
  - Issue #96: Improve: Update link navigate to business detail
- Bug fixes:

Version 3.5.10
- Date: 06/01/2025
- Improvements:
- Features:
  - Issue #92: [Feature] Add function create promotion code in grpcmodel promotioncode to use when create a new promotion
- Bug fixes:

Version 3.5.9
- Date: 06/01/2025
- Improvements:
- Features:
  - Issue #90: [Feature] Update check status tasker when asker book task with favtasker
- Bug fixes:

Version 3.5.8
- Date: 05/01/2025
- Improvements:
- Features:
  - Issue #76: Feature: Improve chat
- Bug fixes:

Version 3.5.7
- Date: 04/01/2025
- Improvements:
- Features:
  - Issue #74: Feature: lookback asker 2024
- Bug fixes:

Version 3.5.6
- Date: 02/01/2025
- Improvements:
- Features:
  - Issue #81: [Feature] Improve brewards performance
- Bug fixes:

Version 3.5.5
- Date: 02/01/2025
- Improvements:
- Features:
- Bug fixes:
  - Fix issue: Update model 1.32.12

Version 3.5.4
- Date: 23/12/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #87: [Hotfix] Fix function migrate if user has point but not has rankinfo

Version 3.5.3
- Date: 20/12/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #85: [Hotfix] Fix ignore function check valid promotion code if it's from system_with_partner
  - Fix issue #84: [Hotfix] Add function migrate if user has point but not has rankinfo

Version 3.5.2
- Date: 20/12/2024
- Improvements:
- Features:
  - Issue #86: Improve: Update use user model
- Bug fixes:

Version 3.5.1
- Date: 15/12/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #82: Hotfix: fix time zone to get weekly roll call

Version 3.5.0
- Date: 13/12/2024
- Improvements:
- Features:
  - Issue #53: Feature: Split collections phase 5
- Bug fixes:

Version 3.4.15
- Date: 12/12/2024
- Improvements:
- Features:
  - Issue #51: [Feature] Business for asker
- Bug fixes:

Version 3.4.14
- Date: 11/12/2024
- Improvements:
- Features:
  - Issue #80: Feature: Fix minVersion noel campaign
- Bug fixes:

Version 3.4.13
- Date: 06/12/2024
- Improvements:
- Features:
  - Issue #75: Improve: Đối với các dịch vụ đặt kèm với VSCN thì hệ thống cho phép User update ngày giờ trong vòng 15 ngày.
- Bug fixes:

Version 3.4.12
- Date: 03/12/2024
- Improvements:
- Features:
  - Issue #73: Feature: localize point transaction reason
- Bug fixes:

Version 3.4.11
- Date: 30/11/2024
- Improvements:
- Features:
  - Issue #66: Feature: Mở game noel campaign cho asker.
- Bug fixes:

Version 3.4.10
- Date: 30/11/2024
- Improvements:
- Features:
  - Issue #67: Feature: Noel campaign roll call action
- Bug fixes:

Version 3.4.9
- Date: 27/11/2024
- Improvements:
- Features:
  - Issue #71: Feature: add description for sofa industrial cleaning
- Bug fixes:

Version 3.4.8
- Date: 24/11/2024
- Improvements:
- Features:
  - Issue #72: [Feature] Temporary prevent user change to malaysia country
- Bug fixes:

Version 3.4.7
- Date: 20/11/2024
- Improvements:
- Features:
  - Issue #69: [Improve] Update message post to slack reporttransaction
- Bug fixes:

Version 3.4.6
- Date: 18/11/2024
- Improvements:
- Features:
  - Issue #61: Feature: return workingProcess field in api get-all-setting
- Bug fixes:

Version 3.4.5
- Date: 16/11/2024
- Improvements:
- Features:
  - Issue #64: [Feature] Add slack channel constant for asker report transaction
- Bug fixes:

Version 3.4.4
- Date: 14/11/2024
- Improvements:
- Features:
  - Issue #62: [Feature] Update logic get reward - by promotioncode: check promotioncode locked = true
- Bug fixes:

Version 3.4.3
- Date: 06/11/2024
- Improvements:
- Features:
  - Issue #52: [Feature] Add api get task list of task
- Bug fixes:
  - Fix issue #56: [Hotfix] Update query fields in api get-marketing-campaign-detail not to get userIds

Version 3.4.2
- Date: 28/10/2024
- Improvements:
- Features:
  - Issue #41: Feature: chat send short video
  - Issue #29: Feature: Change grpc call websocket chat to chat server v3
- Bug fixes:

Version 3.4.1
- Date: 21/10/2024
- Improvements:
- Features:
  - Issue #43: Improve: Return cost for choose tasker in priceSetting of service if exits for app asker to show when booking service
- Bug fixes:

Version 3.4.0
- Date: 15/10/2024
- Improvements:
- Features:
  - Issue #33: Feature: Split collections Phase 4
- Bug fixes:

Version 3.3.3
- Date: 16/10/2024
- Improvements:
- Features:
  - Issue #48: Improve: Update source booking add type images houseKeepingLocation DV DDBP
  - Issue #47: Feature: home moving within same building
- Bug fixes:

Version 3.3.2
- Date: 09/10/2024
- Improvements:
- Features:
  - Issue #36: Feature: Save image in housekeeping location of users
- Bug fixes:

Version 3.3.1
- Date: 03/10/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #39: Hotfix: Edit name collection raix_push_app_tokens

Version 3.3.0
- Date: 25/09/2024
- Improvements:
- Features:
  - Issue #37: Feature: Voucher Combo - Apply Target user: UserIDs
  - Issue #38: Improve: Update source constant for promotionCode when insert to database
- Bug fixes:

Version 3.2.7
- Date: 11/09/2024
- Improvements:
- Features:
  - Issue #34: Feature: fairy tales
  - Issue #35: Feature: Add campaign highlight for payment method [BTK-2407]
- Bug fixes:

Version 3.2.6
- Date: 21/08/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #32: Hotfix: Update function read key from gitlab env file

Version 3.2.5
- Date: 16/08/2024
- Improvements:
- Features:
  - Issue #31: Improve - Remove require description in location Housekeeping
- Bug fixes:

Version 3.2.4
- Date: 15/08/2024
- Improvements:
- Features:
  - Issue #30: Improve: Update function read config from gitlab environment type file
- Bug fixes:

Version 3.2.3
- Date: 12/08/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue: Update env

Version 3.2.2
- Date: 09/08/2024
- Improvements:
- Features:
  - Issue #28: Feature: Update request to new server UrBox
- Bug fixes:

Version 3.2.1
- Date: 07/08/2024
- Improvements:
- Features:
  - Issue #27: Feature: Update api for new detail housekeeping
  - Issue #26: Feature: Update some api for incentive and promotion code when add condition payment method to promotion code and incentive
- Bug fixes:

Version 3.2.0
- Date: 01/08/2024
- Improvements:
- Features:
  - Issue #15: Feature: Split collections Phase 3
- Bug fixes:

Version 3.1.8
- Date: 24/07/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #25: Hotfix: BUG - Deal bRewards has expired but is still displayed and shows an error when redeeming

Version 3.1.7
- Date: 22/07/2024
- Improvements:
- Features:
  - Issue #23: GO - Cập nhật Payment QRcode cho Thailand/Indo
- Bug fixes:

Version 3.1.6
- Date: 19/07/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue: Update model 1.29.13

Version 3.1.5
- Date: 17/07/2024
- Improvements:
- Features:
  - Issue #21: Improve: Update api asker
- Bug fixes:

Version 3.1.4
- Date: 12/07/2024
- Improvements:
- Features:
  - Issue #17: Improve: Get settings and detail contain Addons service
  - Issue #18: Feature: Update source for getChatMessage request update task detail
- Bug fixes:

Version 3.1.3
- Date: 06/07/2024
- Improvements:
- Features:
  - Issue #16: Feature: Show chat message of request increase duration for deep cleaning
- Bug fixes:

Version 3.1.2
- Date: 01/07/2024
- Improvements:
- Features:
  - Issue #14: Feature: Update source for eco subs
- Bug fixes:

Version 3.1.1
- Date: 29/06/2024
- Improvements:
- Features:
  - Issue #13: Feature: Payment failed flow phase 3
- Bug fixes:

Version 3.1.0
- Date: 24/06/2024
- Improvements:
- Features:
  - Issue #11: Split collections Phase 2
- Bug fixes:

Version 3.0.6
- Date: 21/06/2024
- Improvements:
- Features:
  - Issue #12: Cải thiện giao diện màn hình công việc lẻ của gói Sub và gói Sub - VN
- Bug fixes:

Version 3.0.5
- Date: 14/06/2024
- Improvements:
- Features:
  - Issue #2: Split collections
  - Issue #8: Update title send asker feedBack to midesk
  - Issue #9: Add new chat for multiple user
- Bug fixes:

Version 3.0.4
- Date: 07/06/2024
- Improvements:
- Features:
  - Issue #7: Update api load more message case check permission to load message
- Bug fixes:

Version 3.0.3
- Date: 07/06/2024
- Improvements:
- Features:
  - Issue #6: Update api for vote Fav Tasker
  - Issue #3: Return the time asker create payment request for task in list upcoming tasks
- Bug fixes:

Version 3.0.2
- Date: 05/06/2024
- Improvements:
- Features:
  - Issue #4: Api bình chọn Tasker
- Bug fixes:

Version 3.0.1
- Date: 30/05/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue: Update model

Version 3.0.0
- Date: 30/05/2024
- Improvements:
- Features:
  - Issue #1: Init service
- Bug fixes: