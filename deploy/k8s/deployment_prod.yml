apiVersion: v1
kind: Service
metadata:
  name: go-api-asker-vn-v3
  namespace: kong
  labels:
    app: go-api-asker-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 17100
      name: http
    - port: 81
      targetPort: 17101
      name: grpc
  selector:
    app: go-api-asker-vn-v3
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-api-asker-vn-v3
  namespace: kong
  labels:
    app: go-api-asker-vn-v3
spec:
  replicas: 2
  selector:
    matchLabels:
      app: go-api-asker-vn-v3
  template:
    metadata:
      labels:
        app: go-api-asker-vn-v3
        log-label: "go-services"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: doks.digitalocean.com/node-pool
                operator: NotIn
                values:
                - k8s-vn-8x4
                - k8s-vn-8x4-ws
      containers:
        - name: go-api-asker-vn-v3
          image: btaskeehub/go-api-service:v3.7.13-asker-vn
          resources:
            requests:
              cpu: 100m
              memory: "256Mi"
            limits:
              # https://home.robusta.dev/blog/stop-using-cpu-limits
              # cpu: 300m
              memory: "512Mi"
          env:
            - name: TZ
              value: "7"
            - name: ELASTIC_APM_SERVER_URL
              value: "http://apm-server.elastic-system.svc.cluster.local:8200"
            - name: ELASTIC_APM_SERVICE_NAME
              value: "Api Asker VN v3"
            - name: APPLICATION_MODE
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: APPLICATION_MODE
            - name: SLACK_TOKEN_VN
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: SLACK_TOKEN_VN
            - name: GOOGLE_MAP_API_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: GOOGLE_MAP_API_KEY
            - name: CLOUD_TRANSLATION_API
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: CLOUD_TRANSLATION_API
            - name: FCM_ASKER_SERVER_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: FCM_ASKER_SERVER_KEY
            - name: FCM_TASKER_SERVER_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: FCM_TASKER_SERVER_KEY
            - name: AWS3_KEY_PREFIX
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: AWS3_KEY_PREFIX
            - name: AWS3_BUCKET
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: AWS3_BUCKET
            - name: AWS3_REGION
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: AWS3_REGION
            - name: AWS3_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: AWS3_ACCESS_KEY
            - name: AWS3_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: AWS3_SECRET_KEY
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: REDIS_PASSWORD
            - name: URBOX_APP_ID
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_APP_ID
            - name: URBOX_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_APP_SECRET
            - name: URBOX_BTASKEE_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_BTASKEE_PRIVATE_KEY
            - name: URBOX_CART_PAY_VOUCHER_URL
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_CART_PAY_VOUCHER_URL
            - name: MIDESK_EMAIL
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MIDESK_EMAIL
            - name: MIDESK_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MIDESK_PASSWORD
            - name: MIDESK_LOGIN_URL
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MIDESK_LOGIN_URL
            - name: MIDESK_CREATE_TICKET_URL
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MIDESK_CREATE_TICKET_URL
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
            - containerPort: 81
              name: grpc
      imagePullSecrets:
        - name: btaskeehub-registrykey
