---
apiVersion: v1
kind: Service
metadata:
  name: go-api-asker-vn-v3
  namespace: kong
  labels:
    app: go-api-asker-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 17100
      name: http
  selector:
    app: go-api-asker-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-api-asker-vn-v3
  namespace: kong
  labels:
    app: go-api-asker-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-api-asker-vn-v3
  template:
    metadata:
      labels:
        app: go-api-asker-vn-v3
        log-label: "go-services"
    spec:
      containers:
        - name: go-api-asker-vn-v3
          image: btaskeehub/go-staging:go-api-asker-vn-v3-jwt-1
          resources:
            requests:
              cpu: 10m
              memory: "10Mi"
            limits:
              # https://home.robusta.dev/blog/stop-using-cpu-limits
              # cpu: 100m
              memory: "100Mi"
          env:
            - name: TZ
              value: "7"
            - name: APPLICATION_MODE
              value: "dev"
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
      imagePullSecrets:
        - name: btaskeehub-registrykey
