---
apiVersion: v1
kind: Service
metadata:
  name: go-api-asker-vn-v3
  namespace: kong
  labels:
    app: go-api-asker-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 17100
      name: http
  selector:
    app: go-api-asker-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-api-asker-vn-v3
  namespace: kong
  labels:
    app: go-api-asker-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-api-asker-vn-v3
  template:
    metadata:
      labels:
        app: go-api-asker-vn-v3
        log-label: "go-services"
    spec:
      containers:
        - name: go-api-asker-vn-v3
          image: linhnhdocker/go-api-service:asker-vn-3.0.0
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http