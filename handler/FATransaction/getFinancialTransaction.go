package FATransaction

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelReward "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reward"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Financial Transaction by UserId
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetFinancialTransaction(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetFATransaction(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}

	query := bson.M{
		"userId": reqBody.UserId,
		"amount": bson.M{"$gt": 0},
	}
	fields := bson.M{
		"userId":          1,
		"type":            1,
		"accountType":     1,
		"amount":          1,
		"date":            1,
		"source.value":    1,
		"source.name":     1,
		"source.reason":   1,
		"source.rewardId": 1,
	}
	var result []map[string]interface{}
	isExistUser, _ := modelUsers.IsExistByQuery(local.ISO_CODE, bson.M{"_id": reqBody.UserId, "isoCode": reqBody.ISOCode})
	if isExistUser {
		var data []*modelFATransaction.FinancialAccountTransaction
		globalDataAccess.GetAllByQueryPagingSort(
			globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
			query,
			fields,
			int64(page),
			int64(limit),
			bson.M{"date": -1},
			&data,
		)
		//get taskIds
		var taskIds []string
		if len(data) > 0 {
			for _, v := range data {
				if v.Source != nil && (v.Source.Name == globalConstant.FA_TRANSACTION_SOURCE_NAME_TASK ||
					v.Source.Name == globalConstant.FA_TRANSACTION_SOURCE_NAME_CARD_TASK ||
					v.Source.Name == globalConstant.FA_TRANSACTION_SOURCE_NAME_NOT_COMMING ||
					v.Source.Name == globalConstant.FA_TRANSACTION_SOURCE_NAME_SUPPORT_TASKER ||
					v.Source.Name == globalConstant.FA_TRANSACTION_SOURCE_NAME_COD) {
					taskIds = append(taskIds, v.Source.Value)
				}
			}
			//get tasks with taskIds
			var dataTasks []*modelTask.Task
			globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": bson.M{"$in": taskIds}}, bson.M{"address": 1, "duration": 1, "serviceId": 1, "serviceText": 1, "date": 1, "cost": 1, "collectionDate": 1}, &dataTasks)
			// Get setting country
			var settingCountry *modelSettingCountry.SettingCountry
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": reqBody.ISOCode}, bson.M{"currency": 1, "isoCode": 1}, &settingCountry)
			// Add data task to FAtransaction
			for _, v := range data {
				//get task with id
				var task *modelTask.Task
				for _, t := range dataTasks {
					if v.Source != nil && t.XId == v.Source.Value {
						task = t
						break
					}
				}
				// Map fatransaction
				b, _ := json.Marshal(v)
				mapFA := make(map[string]interface{})
				json.Unmarshal(b, &mapFA)
				if task != nil {
					mapFA["dataTask"] = task
				}
				//reward
				if v.Source != nil && v.Source.Name == globalConstant.FA_TRANSACTION_SOURCE_NAME_TASKER_MONTHLY_REWARD && v.Source.RewardId != "" {
					var dataReward *modelReward.Reward
					globalDataAccess.GetOneById(globalCollection.COLLECTION_REWARD[local.ISO_CODE], v.Source.RewardId, bson.M{"data": 1, "createdAt	": 1, "isGiven": 1}, &dataReward)
					mapFA["dataReward"] = dataReward
				}
				// Get currency
				if settingCountry != nil && settingCountry.Currency != nil {
					mapFA["currency"] = settingCountry.Currency
				}
				result = append(result, mapFA)
			}
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get List Financial Transaction by UserId
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateGetFATransaction(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	return nil
}
