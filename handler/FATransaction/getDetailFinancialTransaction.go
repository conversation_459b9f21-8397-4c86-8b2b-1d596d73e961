/*
 * @File: getDetailFinancialTransaction.go
 * @Description: Handler function get detail financial transaction
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package FATransaction

import (
	"encoding/json"
	"net/http"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelPurchaseOrder "gitlab.com/btaskee/go-services-model-v2/grpcmodel/purchaseorder"
	modelReward "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reward"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/transaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

const (
	KEY_TASKID  = "TASKID:"
	KEY_TASK_ID = "TASK ID:"
)

/*
 * @Description: Get Detail Financial Transaction by ID
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetDetailFinancialTransaction(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	if reqBody.ID == "" {
		local.Logger.Warn(lib.ERROR_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ID_REQUIRED)
		return
	}
	result := make(map[string]interface{})
	var transaction *modelFATransaction.FinancialAccountTransaction
	globalDataAccess.GetOneById(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], reqBody.ID, bson.M{"source": 1, "accountType": 1, "date": 1}, &transaction)
	if transaction != nil {
		var name, value, reason string
		if transaction.Source != nil && transaction.Source.Name != "" {
			name = transaction.Source.Name
		}
		if transaction.Source != nil && transaction.Source.Value != "" {
			value = transaction.Source.Value
		}
		if transaction.Source != nil && transaction.Source.Reason != "" {
			reason = transaction.Source.Reason
		}
		tempValue := strings.ToUpper(value)
		tempValue = strings.TrimSpace(tempValue)
		if strings.Contains(tempValue, KEY_TASKID) || strings.Contains(tempValue, KEY_TASK_ID) {
			value = getTaskIdFromSource(value)
		}
		//type sucbscription
		if transaction.AccountType == "T" || strings.Contains(name, "PAY_SUBSCRIPTION") || strings.Contains(name, "PAY_PURCHASE_ORDER") {
			result["type"] = "subscription"
			var purchaseOrder *modelPurchaseOrder.PurchaseOrder
			globalDataAccess.GetOneById(globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE], value, bson.M{"subscriptionId": 1}, &purchaseOrder)

			if purchaseOrder != nil && purchaseOrder.SubscriptionId != "" {
				var objSubscription *modelSubscription.Subscription
				globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], purchaseOrder.SubscriptionId, bson.M{"serviceId": 1, "address": 1, "startDate": 1, "endDate": 1, "duration": 1, "price": 1, "discount": 1, "description": 1, "contactName": 1, "phone": 1, "orderId": 1, "payment": 1, "schedule": 1}, &objSubscription)
				if objSubscription != nil {
					result["data"] = objSubscription
				}
			}
		} else if name == "TASK" || name == "CANCEL TASK" || name == "SUPPORT_ASKER" || name == "CHANGE TASK TO CONFIRM" || name == "PAY_OUTSTANDING" {
			//type task
			result["type"] = "task"
			var task *modelTask.Task
			globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], value, bson.M{"serviceId": 1, "serviceText": 1, "description": 1, "date": 1, "duration": 1, "cost": 1, "address": 1, "phone": 1, "contactName": 1, "status": 1, "acceptedTasker": 1, "collectionDate": 1, "payment": 1}, &task)
			if task != nil {
				b, _ := json.Marshal(task)
				mapTask := make(map[string]interface{})
				json.Unmarshal(b, &mapTask)
				//get info tasker if task.status === CONFIRMED, DONE
				if (task.Status == globalConstant.TASK_STATUS_CONFIRMED || task.Status == globalConstant.TASK_STATUS_DONE) && task.AcceptedTasker != nil && len(task.AcceptedTasker) > 0 {
					user, _ := modelUser.GetOneById(local.ISO_CODE, task.AcceptedTasker[0].TaskerId, bson.M{"avatar": 1, "name": 1})
					mapTask["infoTasker"] = user
				}
				result["data"] = mapTask
			}
		} else if name == "MONTHLY REWARD FOR ASKER" && transaction.Source != nil && transaction.Source.RewardId != "" {
			//type REWARD
			result["type"] = "reward"
			var data *modelReward.Reward
			globalDataAccess.GetOneById(globalCollection.COLLECTION_REWARD[local.ISO_CODE], transaction.Source.RewardId, bson.M{"data": 1, "createdAt": 1, "isGiven": 1}, &data)
			result["data"] = data
		} else if strings.Contains(name, "TOP_UP_CREDIT") {
			//type TOP_UP
			result["type"] = "topUp"
			var data *modelTransaction.Transaction
			globalDataAccess.GetOneById(globalCollection.COLLECTION_TRANSACTION[local.ISO_CODE], value, bson.M{"status": 1, "type": 1, "payment": 1}, &data)
			// refactor data payment with CARD
			if data != nil && data.Payment != nil && data.Payment.Method == globalConstant.PAYMENT_METHOD_CARD && data.Payment.CardInfo == nil {
				data.Payment.CardInfo = &modelTransaction.TransactionPaymentCardInfo{
					Number: data.Payment.CardNumber,
					Type:   data.Payment.CardType,
				}
			}
			result["data"] = data
		} else if name == "REFERRAL" {
			//type REFERRAL
			result["type"] = "referral"
			result["data"] = transaction
		} else if name == "PROMOTION" {
			result["type"] = "promotion"
			result["data"] = transaction
		} else {
			result["type"] = "other"
			result["data"] = transaction
		}
		result["date"] = transaction.Date
		result["source"] = map[string]interface{}{
			"name":   name,
			"value":  value,
			"reason": reason,
		}
	}
	// return isReport Transaction. Old code is wrong. reportTransaction.transactionId is eportTransaction.FATransactionId
	isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE], bson.M{"transactionId": reqBody.ID})
	result["isReport"] = isExist
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get TaskID
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func getTaskIdFromSource(value string) string {
	var arrayValue []string
	if strings.ContainsAny(value, ".") {
		arrayValue = strings.Split(value, ".")
	} else {
		arrayValue = strings.Split(value, ",")
	}
	if len(arrayValue) > 0 {
		var arrayContainTaskId string
		for _, v := range arrayValue {
			tempValue := strings.ToUpper(v)
			tempValue = strings.TrimSpace(tempValue)
			if strings.Contains(tempValue, KEY_TASKID) || strings.Contains(tempValue, KEY_TASK_ID) {
				arrayContainTaskId = v
				break
			}
		}
		if arrayContainTaskId != "" {
			value = strings.Trim(strings.Split(arrayContainTaskId, ":")[1], " ")
		}
	}
	return value
}
