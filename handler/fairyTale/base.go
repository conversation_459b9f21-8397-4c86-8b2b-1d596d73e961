package fairyTale

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelFairyTale "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fairyTale"
	"go.mongodb.org/mongo-driver/bson"
)

func GetFairyTales(query, fields bson.M) (fairyTales []*modelFairyTale.FairyTale, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FAIRY_TALE[local.ISO_CODE], query, fields, &fairyTales)
	return fairyTales, err
}

func GetFairyTale(query, fields bson.M) (fairyTale *modelFairyTale.FairyTale, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FAIRY_TALE[local.ISO_CODE], query, fields, &fairyTale)
	return fairyTale, err
}
