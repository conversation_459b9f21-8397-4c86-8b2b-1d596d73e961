package getListFairyTale

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/fairyTale"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func GetListFairyTale() (interface{}, *globalResponse.ResponseErrorCode, error) {
	fairyTales, err := fairyTale.GetFairyTales(bson.M{}, bson.M{})
	if err != nil {
		return nil, &lib.ERROR_GET_LIST_FAIRY_TALE_FAILED, err
	}
	return fairyTales, nil, nil
}
