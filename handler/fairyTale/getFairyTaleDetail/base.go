package getFairyTaleDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetFairyTaleDetail(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. get validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	// 2. get data detail
	fairyTale, errCode, err := getFairyTaleDetail(reqBody.ID)
	if errCode != nil {
		return nil, errCode, err
	}
	// 3. get related fairy tale
	result := getRelatedFairyTale(fairyTale)
	// 4. return result
	return result, nil, nil
}
