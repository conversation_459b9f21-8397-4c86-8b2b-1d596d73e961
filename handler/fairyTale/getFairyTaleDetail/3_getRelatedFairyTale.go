package getFairyTaleDetail

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/fairyTale"
	modelFairyTale "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fairyTale"
	"go.mongodb.org/mongo-driver/bson"
)

func getRelatedFairyTale(fairyTaleDetail *modelFairyTale.FairyTale) interface{} {
	fairyTales, _ := fairyTale.GetFairyTales(bson.M{"_id": bson.M{"$ne": fairyTaleDetail.XId}}, bson.M{})
	if len(fairyTales) == 0 {
		return fairyTaleDetail
	}
	result := map[string]interface{}{}
	fairyTaleData, _ := json.Marshal(fairyTaleDetail)
	json.Unmarshal(fairyTaleData, &result)
	result["relatedFairyTales"] = fairyTales
	return result
}
