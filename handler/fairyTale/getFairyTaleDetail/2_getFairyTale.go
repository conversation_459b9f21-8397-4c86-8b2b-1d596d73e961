package getFairyTaleDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/fairyTale"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFairyTale "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fairyTale"
	"go.mongodb.org/mongo-driver/bson"
)

func getFairyTaleDetail(id string) (*modelFairyTale.FairyTale, *globalResponse.ResponseErrorCode, error) {
	fairyTale, err := fairyTale.GetFairyTale(bson.M{"_id": id}, bson.M{})
	if err != nil || fairyTale == nil {
		return nil, &lib.ERROR_FAIRY_TALE_NOT_FOUND, err
	}
	return fairyTale, nil, nil
}
