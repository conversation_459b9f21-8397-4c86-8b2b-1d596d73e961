/*
 * @File: getFinancialAccount.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package financialAccount

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Financial Account by UserId
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetFinancialAccount(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetFAccount(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get user
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"fAccountId": 1})
	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	// Get currency
	var settingCountry *modelSettingCountry.SettingCountry
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": reqBody.ISOCode}, bson.M{"currency": 1}, &settingCountry)

	if settingCountry == nil || settingCountry.Currency == nil {
		local.Logger.Warn(lib.ERROR_CURRENCY_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CURRENCY_NOT_FOUND)
		return
	}
	main, promotion := globalLib.GetFAccountByIsoCode(user.FAccountId, reqBody.ISOCode)
	result := map[string]interface{}{
		"FMainAccount": main,
		"Promotion":    promotion,
		"currency":     settingCountry.Currency,
	}
	// Get bPayBusiness
	bPayBusiness := getbPayBusiness(reqBody.UserId)
	if bPayBusiness >= 0 {
		result["bPayBusiness"] = bPayBusiness
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: validate get FAccount params
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateGetFAccount(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}

func getbPayBusiness(userId string) float64 {
	business, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": userId, "status": globalConstant.BUSINESS_STATUS_ACTIVE}, bson.M{"bPay": 1})
	if business != nil {
		return business.BPay
	}
	businessMember, _ := modelBusinessMember.GetOne(local.ISO_CODE, bson.M{"userId": userId, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}, bson.M{"bPay": 1})
	if businessMember != nil {
		return businessMember.BPay
	}
	return -1
}
