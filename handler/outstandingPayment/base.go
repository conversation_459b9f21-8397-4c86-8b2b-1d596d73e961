/*
 * @File: base.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 18/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package outstandingPayment

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Outstanding Payment by UserId
 * @CreatedAt: 18/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetOutstandingPayment(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate request params
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Get list outstanding payment
	result, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_OUTSTANDING_PAYMENT[local.ISO_CODE], bson.M{"askerId": reqBody.UserId, "status": globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW}, bson.M{})

	// Get list taskIds from result
	taskIds := []string{}
	for _, r := range result {
		// Get outstandingPayment.data.taskId
		data, _ := json.Marshal(r["data"])
		dataMap := map[string]interface{}{}
		json.Unmarshal(data, &dataMap)

		taskIds = append(taskIds, dataMap["taskId"].(string))
	}

	// Get list task from db by taskIds
	var task []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": bson.M{"$in": taskIds}}, bson.M{"_id": 1, "serviceText": 1}, &task)

	// Map serviceText with outstandingPayment by taskIds
	for _, r := range result {
		// Get outstandingPayment.data.taskId
		data, _ := json.Marshal(r["data"])
		dataMap := map[string]interface{}{}
		json.Unmarshal(data, &dataMap)

		for _, t := range task {
			if t.XId == dataMap["taskId"].(string) {
				r["serviceText"] = t.ServiceText
			}
		}
	}

	globalResponse.ResponseSuccess(w, result)
}
