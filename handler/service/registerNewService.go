/*
 * @File: registerNewService.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package service

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRegisterService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/registerService"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Register New Service by IsoCode
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func RegisterNewService(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Get data
	var result []*modelRegisterService.RegisterService
	globalDataAccess.GetAllByQueryPaging(globalCollection.COLLECTION_REGISTER_SERVICE[local.ISO_CODE], bson.M{}, bson.M{}, 1, 20, &result)
	globalResponse.ResponseSuccess(w, result)
}
