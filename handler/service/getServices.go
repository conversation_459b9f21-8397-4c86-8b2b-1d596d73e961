/*
 * @File: getServices.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package service

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get Services by IsoCode
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetServices(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Get list service
	var services []*modelService.Service
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"status": globalConstant.SERVICE_STATUS_ACTIVE}, bson.M{}, &services)
	globalResponse.ResponseSuccess(w, services)
}
