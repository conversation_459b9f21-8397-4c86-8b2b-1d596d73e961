/*
 * @File: removeNotificationNotFromBtaskeeById.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 10/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package notification

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
* @Description: Remove Notification Not From btaskee by ID
* @CreatedAt: 28/12/2020
* @Author: vinhnt
* @UpdatedAt: 24/02/2021
* @UpdatedBy: ngoctb
 */
func RemoveNotificationNotFromBtaskeeById(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.ID == "" {
		local.Logger.Warn(lib.ERROR_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ID_REQUIRED)
		return
	}
	// Get data
	isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
		bson.M{
			"_id":  reqBody.ID,
			"type": bson.M{"$in": globalConstant.LIST_NOTIFY_FROM_BTASKEE},
		},
	)
	// Set data
	if isExist {
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
			reqBody.ID,
			bson.M{"$set": bson.M{
				"isForce": false,
				"isRead":  true,
			}},
		)
		globalResponse.ResponseSuccess(w, nil)
		return
	}
	err := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], reqBody.ID)
	if err != nil {
		globalResponse.ResponseError(w, lib.ERROR_REMOVE_FAILED)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}
