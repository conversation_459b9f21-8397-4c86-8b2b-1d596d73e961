package notification

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Receive Notification by UserId
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func UpdateReceiveNotification(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Set data
	currentUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": reqBody.UserId, "type": globalConstant.USER_TYPE_TASKER}, bson.M{"noReceiveNotification": 1})
	if currentUser == nil {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	if reqBody.NoReceiveNotification != currentUser.NoReceiveNotification {
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"noReceiveNotification": reqBody.NoReceiveNotification}})
	}
	if reqBody.NoReceiveNotification {
		globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
			bson.M{"userId": reqBody.UserId},
			bson.M{"$unset": bson.M{"token": 1}},
		)
	}
	globalResponse.ResponseSuccess(w, nil)
}
