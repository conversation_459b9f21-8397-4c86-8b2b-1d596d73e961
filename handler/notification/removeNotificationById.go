package notification

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

/*
 * @Description: Remove Notification by ID
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func RemoveNotificationById(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.ID == "" {
		local.Logger.Warn(lib.ERROR_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ID_REQUIRED)
		return
	}
	// Set data
	err := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], reqBody.ID)

	if err != nil {
		globalResponse.ResponseError(w, lib.ERROR_REMOVE_FAILED)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}
