package getNotificationV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Get Notification by UserId
 * @CreatedAt: 10/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetNotificationV2(reqBody *model.ApiRequest) ([]*NotificationResponse, *globalResponse.ResponseErrorCode, error) {
	defer local.Logger.Sync()

	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	notifications, errCode, err := getNotifications(reqBody)
	if errCode != nil {
		return nil, errCode, err
	}

	return notifications, nil, nil
}
