package getNotificationV2

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

type NotificationResponse struct {
	*notification.Notification `bson:",inline"`
	IsoCode                    string `json:"isoCode" bson:"isoCode"`
}

func getNotifications(reqBody *model.ApiRequest) ([]*NotificationResponse, *globalResponse.ResponseErrorCode, error) {
	// Get data
	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}
	matchQuery := bson.M{"userId": reqBody.UserId, "type": bson.M{"$nin": []int{19, 22, 24}}}
	query := []bson.M{
		{
			"$match": matchQuery,
		},
		{
			// Add a collection identifier (optional, for debugging or additional context)
			"$addFields": bson.M{"isoCode": local.ISO_CODE},
		},
	}

	for isoCode, collectionName := range globalCollection.COLLECTION_NOTIFICATION {
		// Skip the current ISO code.
		if isoCode == local.ISO_CODE {
			continue
		}
		query = append(query, bson.M{
			"$unionWith": bson.M{
				"coll": collectionName,
				"pipeline": []bson.M{
					{"$match": matchQuery},                     // Filter orders for the same userId
					{"$addFields": bson.M{"isoCode": isoCode}}, // Optional identifier
				},
			},
		})
	}

	// Add paging, sorting, and limit to the query.
	query = append(query,
		bson.M{"$sort": bson.M{"createdAt": -1}},
		bson.M{"$skip": (page - 1) * limit},
		bson.M{"$limit": limit},
	)

	var result []*NotificationResponse
	globalDataAccess.Aggregate(
		globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE],
		query,
		&result,
	)

	for _, v := range result {
		if v.IsoCode == local.ISO_CODE {
			continue
		}
		if v.Title != "" {
			v.Title = fmt.Sprintf("%s %s", globalConstant.COUNTRY_FLAG_BY_ISO_CODE[v.IsoCode], v.Title)
		}
	}

	return result, nil, nil
}
