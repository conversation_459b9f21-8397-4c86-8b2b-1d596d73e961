/*
 * @File: getScore.go
 * @Description: Handler function for get training score api
 * @CreatedAt: 27/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package taskerTraining

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskerTraining "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerTraining"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get Score Tasker Training
 * @CreatedAt: 27/09/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: vinhnt
 */
func GetScore(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	var key string
	var numberOfQuestions, numberOfTrueAnswers int32
	if reqBody.Key == "INPUT_FAIL" {
		key = globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT
	} else if reqBody.Key == "TASKER_FAIL" {
		key = globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER_OLD
	}
	var trainingTasker *modelTaskerTraining.TaskerTraining
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": reqBody.UserId, "histories.type": key}, bson.M{"histories": 1}, &trainingTasker)
	if trainingTasker != nil {
		var histories []*modelTaskerTraining.TaskerTrainingHistories
		if trainingTasker.Histories != nil && len(trainingTasker.Histories) > 0 {
			for _, v := range trainingTasker.Histories {
				if v.Type == key {
					histories = append(histories, v)
				}
			}
		}
		if len(histories) > 0 {
			trainingTaskerHistory := histories[len(histories)-1]
			numberOfQuestions = trainingTaskerHistory.NumberOfQuestions
			numberOfTrueAnswers = trainingTaskerHistory.NumberOfTrueAnswers
		}
	}
	result := map[string]interface{}{
		"numberOfQuestions":   numberOfQuestions,
		"numberOfTrueAnswers": numberOfTrueAnswers,
	}
	globalResponse.ResponseSuccess(w, result)
}
