package taskerTraining

import (
	"encoding/json"
	"io"
	"math"
	"net/http"
	"reflect"
	"strconv"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskerSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerSettings"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Set Do Quizz Result
 * @CreatedAt: 30/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SetDoQuizzResult(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateSetDoTraining(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	currentCourse := make(map[string]interface{})
	b, _ := json.Marshal(reqBody.Data["currentCourse"])
	err := json.Unmarshal(b, &currentCourse)
	if err != nil {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	// Get tasker settings
	var taskerSetting *modelTaskerSetting.TaskerSettings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"trainingTasker": 1}, &taskerSetting)
	if taskerSetting == nil || taskerSetting.TrainingTasker == nil || taskerSetting.TrainingTasker.Link == "" {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	// Get training course from http link
	resp, err := http.Get(taskerSetting.TrainingTasker.Link)
	if err != nil {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	var content []*Courses
	if resp != nil && resp.StatusCode == http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		json.Unmarshal(b, &content)
	}
	if len(content) == 0 {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	// Calculate result
	result := make(map[string]interface{})
	for _, v := range content {
		version, _ := strconv.Atoi(v.Version)
		if version == int(currentCourse["version"].(float64)) && v.ID == currentCourse["courseId"] {
			minRateScore := v.RateScore
			var lessonList []map[string]interface{}
			resp, _ := http.Get(v.Link)
			if resp != nil && resp.StatusCode == http.StatusOK {
				b, _ := io.ReadAll(resp.Body)
				json.Unmarshal(b, &lessonList)
			}
			//1. Calculate the number of question in this course
			numberOfQuestions := 0
			if len(lessonList) > 0 {
				for _, lession := range lessonList {
					if lession["type"] != nil && lession["type"].(string) == "QUIZZ" && lession["numberOfQuestions"] != nil {
						numberOfQuestions += int(lession["numberOfQuestions"].(float64))
					}
				}
			}
			//2. Calculate number of true answer
			numberOfTrueAnswers := 0
			var quizzes []map[string]interface{}
			if currentCourse["quizzes"] != nil {
				b, _ := json.Marshal(currentCourse["quizzes"])
				json.Unmarshal(b, &quizzes)
			}
			if len(quizzes) > 0 {
				for _, q := range quizzes {
					var questions []map[string]interface{}
					if q["questions"] != nil {
						b, _ := json.Marshal(q["questions"])
						json.Unmarshal(b, &questions)
					}
					if len(questions) > 0 {
						for _, ques := range questions {
							if reflect.DeepEqual(ques["choices"], ques["rightAnswer"]) {
								numberOfTrueAnswers++
							}
						}
					}
				}
			}
			//3. Calculate the score. Pass the course or not
			if numberOfQuestions > 0 {
				score := math.Floor(float64((numberOfTrueAnswers * 100) / numberOfQuestions))
				var passedCourse bool
				passedStatus := "FAILED"
				if score > float64(minRateScore) { // passed the course
					passedCourse = true
					passedStatus = "PASSED"
				}
				now := globalLib.GetCurrentTime(local.TimeZone)
				//4. Update the database
				isExistTaskerRecord, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": reqBody.UserId})
				if isExistTaskerRecord {
					globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
						bson.M{"taskerId": reqBody.UserId},
						bson.M{
							"$set": bson.M{"updatedAt": now},
							"$push": bson.M{
								"histories": bson.M{
									"answerMatrix":        currentCourse,
									"numberOfQuestions":   numberOfQuestions,
									"numberOfTrueAnswers": numberOfTrueAnswers,
									"minRateScore":        minRateScore,
									"passed":              passedCourse,
									"type":                "ALL_QUIZZ",
									"createdAt":           now,
								},
							},
						},
					)
				} else {
					dataInsert := map[string]interface{}{
						"_id":      globalLib.GenerateObjectId(),
						"taskerId": reqBody.UserId,
						"histories": []map[string]interface{}{
							{
								"answerMatrix":        currentCourse,
								"numberOfQuestions":   numberOfQuestions,
								"numberOfTrueAnswers": numberOfTrueAnswers,
								"minRateScore":        minRateScore,
								"passed":              passedCourse,
								"type":                "ALL_QUIZZ",
								"createdAt":           now,
							},
						},
						"createdAt": now,
					}
					globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], dataInsert)
				}
				// Post to slack
				wg := &sync.WaitGroup{}
				wg.Add(1)
				go func() {
					defer wg.Done()
					postTestResultsToSlack(reqBody.UserId, numberOfTrueAnswers, numberOfQuestions, 1, passedStatus, "ENTRANCE_TEST")
				}()

				result["success"] = passedCourse
				result["data"] = map[string]interface{}{
					"numQuestions": numberOfQuestions,
					"numTrueAns":   numberOfTrueAnswers,
				}
				if passedCourse {
					result["reason"] = "PASSED_COURSE"
				} else {
					result["reason"] = "DO_NOT_PASS_COURSE"
				}
				wg.Wait()
			} else {
				globalResponse.ResponseError(w, lib.ERROR_NUMBER_OF_QUESTIONS_INVALID)
				return
			}
		}
	}
	globalResponse.ResponseSuccess(w, nil)
}
