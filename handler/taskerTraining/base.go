/*
 * @File: base.go
 * @Description: Helper function for taskerTraining api
 * @CreatedAt: 27/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package taskerTraining

import (
	"fmt"
	"math"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

type Courses struct {
	ID          string `json:"id,omitempty"`
	Title       string `json:"title,omitempty"`
	Link        string `json:"link,omitempty"`
	RateScore   int    `json:"rateScore,omitempty"`
	Version     string `json:"version,omitempty"`
	NumberQuizz int    `json:"numberQuizz,omitempty"`
}

var cfg = config.GetConfig()

/*
 * @Description: Post Test Results To Slack
 * @CreatedAt: 30/09/2020
 * @Author: linhnh
 * @UpdatedAt: 13/11/2020
 * @UpdatedBy: ngoctb
 */
func postTestResultsToSlack(userId string, numberOfTrueAnswers int, numberOfQuestions int, numberTest int, passedStatus string, quizzType string) {
	user, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"name": 1, "phone": 1, "language": 1, "workingPlaces": 1})
	if user != nil {
		place := getWorkingPlaces(user.WorkingPlaces)
		lang := globalConstant.LANG_VI
		if user.Language != "" {
			lang = user.Language
		}
		message := fmt.Sprintf(
			"%s\n%s\n%s\n%s\n%s\n%s\n%s\n%s",
			localization.T(lang, "DATE_TIME", globalLib.GetCurrentTime(local.TimeZone).Format(time.RFC3339)),
			localization.T(lang, "NAME", user.Name),
			localization.T(lang, "PHONE_NUMBER", user.Phone),
			localization.T(lang, "REGISTRATION_AREA", place),
			localization.T(lang, "RESULT", fmt.Sprintf("%d/%d", numberOfTrueAnswers, numberOfQuestions)),
			localization.T(lang, "NUMBER_OF_TEST", numberTest),
			localization.T(lang, "STATUS", passedStatus),
			localization.T(lang, "QUIZZ_TYPE", localization.T(lang, quizzType)),
		)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.TRAINING_RESULT_SLACK_CHANNEL_VN, "bTaskee System", message)
	}
}

/*
 * @Description: Get Working Places
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getWorkingPlaces(workingPlace []*modelUser.UserWorkingPlaces) string {
	var place string
	if len(workingPlace) > 0 {
		var districtCustom, city, country string
		for _, v := range workingPlace {
			if districtCustom == "" {
				districtCustom = v.District
			} else {
				districtCustom = fmt.Sprintf("%s - %s", districtCustom, v.District)
			}
			city = v.City
			country = v.Country
		}
		place = fmt.Sprintf("%s, %s, %s", districtCustom, city, country)
	}
	return place
}

/*
 * @Description: Get Score Function
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getScoreFunction(numberOfTrueAnswers int, numberOfQuestions int, minRateScore float64) bool {
	var passedCourse bool
	if numberOfTrueAnswers > 0 {
		score := math.Floor(float64((numberOfTrueAnswers * 100) / numberOfQuestions))
		passedCourse = score >= float64(minRateScore)
	}
	return passedCourse
}

/*
 * @Description: Update History/Insert TASKER_TRAINING
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: vinhnt
 */
func setDatabase(checkRecord bool, typeTraining string, currentCourse map[string]interface{}, numberOfTrueAnswers int, numberOfQuestions int, minRateScore float64, passedCourse bool, userId string) {
	typeCustom := typeTraining
	if typeTraining == globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER {
		typeCustom = globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER_OLD
	}
	numberDataHistories, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": userId, "histories.type": typeCustom})
	numberTest := int(numberDataHistories) + 1
	now := globalLib.GetCurrentTime(local.TimeZone)
	if checkRecord { //update record
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
			bson.M{"taskerId": userId},
			bson.M{
				"$set": bson.M{"updatedAt": now},
				"$push": bson.M{
					"histories": bson.M{
						"answerMatrix":        currentCourse,
						"numberOfQuestions":   numberOfQuestions,
						"numberOfTrueAnswers": numberOfTrueAnswers,
						"minRateScore":        minRateScore,
						"passed":              passedCourse,
						"numberTest":          numberTest,
						"type":                typeTraining,
						"createdAt":           now,
					},
				},
			},
		)
	} else { //Insert new record
		dataInsert := map[string]interface{}{
			"_id":      globalLib.GenerateObjectId(),
			"taskerId": userId,
			"histories": []map[string]interface{}{
				{
					"answerMatrix":        currentCourse,
					"numberOfQuestions":   numberOfQuestions,
					"numberOfTrueAnswers": numberOfTrueAnswers,
					"minRateScore":        minRateScore,
					"passed":              passedCourse,
					"numberTest":          numberTest,
					"type":                typeTraining,
					"createdAt":           now,
				},
			},
			"createdAt": now,
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], dataInsert)
	}
	if typeTraining == globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT {
		passedStatus := "FAILED"
		if passedCourse {
			passedStatus = "PASSED"
		}
		postTestResultsToSlack(userId, numberOfTrueAnswers, numberOfQuestions, numberTest, passedStatus, "ENTRANCE_TEST")
	}
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateSetDoTraining(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.Data == nil {
		return &lib.ERROR_DATA_UPDATE_REQUIRED
	}
	return nil
}
