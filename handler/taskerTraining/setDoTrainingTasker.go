package taskerTraining

import (
	"encoding/json"
	"net/http"
	"reflect"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskerSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerSettings"
	modelTaskerTraining "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerTraining"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Set Do Training Tasker (UserId)
 * @CreatedAt: 30/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SetDoTrainingTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateSetDoTraining(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	currentCourse := make(map[string]interface{})
	b, _ := json.Marshal(reqBody.Data["currentCourse"])
	err := json.Unmarshal(b, &currentCourse)
	if err != nil {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	var taskerSettings *modelTaskerSetting.TaskerSettings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"trainingTasker": 1}, &taskerSettings)
	var versionSettings int32
	if taskerSettings != nil && taskerSettings.TrainingTasker != nil && taskerSettings.TrainingTasker.Version > 0 {
		versionSettings = taskerSettings.TrainingTasker.Version
	}
	var quizzes []map[string]interface{}
	b, _ = json.Marshal(currentCourse["quizzes"])
	json.Unmarshal(b, &quizzes)
	if len(quizzes) == 0 {
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"trainingPermission.resetData": true}})
		// TODO: post to slack
		globalResponse.ResponseError(w, lib.ERROR_ON_RESET_DATA)
		return
	}
	if currentCourse["version"] != nil && int32(currentCourse["version"].(float64)) != 0 && int32(currentCourse["version"].(float64)) < versionSettings {
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"trainingPermission.resetData": true}})
		globalResponse.ResponseError(w, lib.ERROR_ON_RESET_DATA)
		return
	}
	numberOfQuestions := 0
	numberOfTrueAnswers := 0
	numberQuizz := getNumberQuizz(reqBody.ISOCode)
	//find the record corresponding to taskerId
	var trainingTaskerRecord *modelTaskerTraining.TaskerTraining
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": reqBody.UserId}, bson.M{"histories": 1}, &trainingTaskerRecord)
	minRateScore := taskerSettings.TrainingTasker.MinRateScore
	var lastQuizzId string
	if currentCourse["lastQuizzId"] != nil {
		//lastQuizzId = int(currentCourse["lastQuizzId"].(float64))
		lastQuizzId, _ = currentCourse["lastQuizzId"].(string)
	}
	var reason string
	if len(quizzes) > 0 {
		// Calculate number of question and score
		for _, v := range quizzes {
			var questions []map[string]interface{}
			b, _ = json.Marshal(v["questions"])
			json.Unmarshal(b, &questions)
			if questions != nil {
				numberOfQuestions += len(questions)
			}
			if v["countPass"] != nil {
				numberOfTrueAnswers += int(v["countPass"].(float64))
			}
		}
		passedCourse := getScoreFunction(numberOfTrueAnswers, numberQuizz, minRateScore)
		// Check Record
		if trainingTaskerRecord != nil {
			var dataHistories *modelTaskerTraining.TaskerTrainingHistories
			if trainingTaskerRecord.Histories != nil && len(trainingTaskerRecord.Histories) > 0 {
				for _, v := range trainingTaskerRecord.Histories {
					if v.Type == globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER {
						dataHistories = v
						break
					}
				}
				if dataHistories != nil && checkQuestions(dataHistories, quizzes, reqBody.UserId) {
					// Update histories
					reason = updateNumberOfTrueAnswers(dataHistories, quizzes, minRateScore, lastQuizzId, reqBody.UserId, reqBody.ISOCode)
				} else if dataHistories == nil && quizzes[0]["quizzId"] != nil && quizzes[0]["quizzId"].(string) == "0" {
					setDatabase(true, globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER, currentCourse, numberOfTrueAnswers, numberQuizz, minRateScore, passedCourse, reqBody.UserId)
				}
			}
		} else {
			if quizzes[0]["quizzId"] != nil && quizzes[0]["quizzId"].(string) == "0" {
				setDatabase(false, globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER, currentCourse, numberOfTrueAnswers, numberQuizz, minRateScore, passedCourse, reqBody.UserId)
			}
		}
	}
	//5. return data to client
	var numberTest int32
	if quizzes[0]["numberTest"] != nil {
		numberTest = int32(quizzes[0]["numberTest"].(float64))
	}
	var checkPassed bool
	if quizzes[0]["passed"] != nil {
		checkPassed = quizzes[0]["passed"].(bool)
	}
	maxTest := taskerSettings.TrainingTasker.NumberTest
	result := map[string]interface{}{
		"success": true,
		"type":    globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER,
		"data": map[string]interface{}{
			"quizzId":      quizzes[0]["quizzId"],
			"numberTest":   numberTest,
			"numQuestions": numberOfQuestions,
			"numTrueAns":   numberOfTrueAnswers,
		},
	}
	if quizzes[0]["quizzId"] != nil && quizzes[0]["quizzId"].(string) == lastQuizzId && (checkPassed || numberTest == maxTest) {
		result["reason"] = reason
	} else {
		if checkPassed {
			result["reason"] = "PASSED_COURSE"
		} else {
			result["reason"] = "DO_NOT_PASS_COURSE"
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Check Questions
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: vinhnt
 */
func checkQuestions(dataHistories *modelTaskerTraining.TaskerTrainingHistories, quizzes []map[string]interface{}, userId string) bool {
	var dataQuestions []*modelTaskerTraining.TaskerTrainingHistoriesAnswerMatrixQuizzes
	if dataHistories != nil && dataHistories.AnswerMatrix != nil && dataHistories.AnswerMatrix.Quizzes != nil && len(dataHistories.AnswerMatrix.Quizzes) > 0 {
		dataQuestions = dataHistories.AnswerMatrix.Quizzes
	}
	var oldQuestions, newQuestions []*modelTaskerTraining.TaskerTrainingHistoriesAnswerMatrixQuizzesQuestions
	var oldQuizId, newQuizId string
	if len(dataQuestions) > 0 {
		oldQuestions = dataQuestions[len(dataQuestions)-1].Questions
		oldQuizId = dataQuestions[len(dataQuestions)-1].QuizzId
	}
	if len(quizzes) > 0 {
		b, _ := json.Marshal(quizzes[0]["questions"])
		json.Unmarshal(b, &newQuestions)
		newQuizId = quizzes[0]["quizzId"].(string)
	}
	if oldQuizId != "" && newQuizId != "" && oldQuizId == newQuizId {
		if reflect.DeepEqual(oldQuestions, newQuestions) {
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
				bson.M{"taskerId": userId, "histories.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER},
				bson.M{"$push": bson.M{"histories.$.answerMatrix.quizzes": quizzes[0]}},
			)
			return true
		}
		return false
	} else if oldQuizId != "" && newQuizId != "" && oldQuizId != newQuizId {
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
			bson.M{"taskerId": userId, "histories.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER},
			bson.M{"$push": bson.M{"histories.$.answerMatrix.quizzes": quizzes[0]}},
		)
		return true
	}
	return false
}

/*
 * @Description: Update Number Of True Answers
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt: 26/11/2020
 * @UpdatedBy: ngoctb
 */
func updateNumberOfTrueAnswers(dataHistories *modelTaskerTraining.TaskerTrainingHistories, quizzes []map[string]interface{}, minRateScore float64, lastQuizzId string, userId string, isoCode string) string {
	dataQuizz := dataHistories.AnswerMatrix.Quizzes
	// Custom data to calculate points
	var customData []map[string]interface{}
	numberOfQuestions := getNumberQuizz(isoCode)
	numberOfTrueAnswers := 0
	if len(dataQuizz) > 0 {
		for _, v := range dataQuizz {
			var checkNumberQuizz []*modelTaskerTraining.TaskerTrainingHistoriesAnswerMatrixQuizzes
			for _, c := range dataQuizz {
				if c.QuizzId == v.QuizzId {
					checkNumberQuizz = append(checkNumberQuizz, c)
				}
			}
			if len(checkNumberQuizz) > 0 {
				for _, t := range checkNumberQuizz {
					var checkCusData []map[string]interface{}
					for _, d := range customData {
						if d["quizzId"] != nil && d["quizzId"].(string) == t.QuizzId {
							checkCusData = append(checkCusData, d)
						}
					}
					if len(checkCusData) == 0 {
						// Push data to calculate points
						customData = append(customData, map[string]interface{}{
							"quizzId":             t.QuizzId,
							"numberOfTrueAnswers": t.CountPass,
							"numberTest":          t.NumberTest,
						})
					} else if checkCusData[0]["numberTest"].(int32) < t.NumberTest {
						var newCustomData []map[string]interface{}
						for _, d := range customData {
							if d["quizzId"] != nil && d["quizzId"].(string) == t.QuizzId {
								d["numberTest"] = t.NumberTest
								d["numberOfTrueAnswers"] = t.CountPass
							}
							newCustomData = append(newCustomData, d)
						}
						customData = newCustomData
					}
				}
			}
		}
	}
	// Calculate points to insert database
	if len(customData) > 0 {
		for _, d := range customData {
			if d["numberOfTrueAnswers"] != nil {
				numberOfTrueAnswers += int(d["numberOfTrueAnswers"].(int32))
			}
		}
	}
	passedCourse := getScoreFunction(numberOfTrueAnswers, numberOfQuestions, minRateScore)
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
		bson.M{"taskerId": userId, "histories.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER},
		bson.M{"$set": bson.M{"histories.$.numberOfTrueAnswers": numberOfTrueAnswers, "histories.$.passed": passedCourse, "updatedAt": globalLib.GetCurrentTime(local.TimeZone)}},
	)
	var result string
	if quizzes[0]["quizzId"] != nil && quizzes[0]["quizzId"].(string) == lastQuizzId &&
		((quizzes[0]["passed"] != nil && quizzes[0]["passed"].(bool)) || (quizzes[0]["numberTest"] != nil && int(quizzes[0]["numberTest"].(float64)) >= 2)) {
		quizzAfter(numberOfTrueAnswers, numberOfQuestions, passedCourse, userId)
		if passedCourse {
			result = "TASKER_PASS"
		} else {
			result = "TASKER_FAIL"
		}
	}
	return result
}

/*
 * @Description: Quizz After
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: vinhnt
 */
func quizzAfter(numberOfTrueAnswers int, numberOfQuestions int, passedCourse bool, userId string) {
	passedStatus := "TASKER_FAIL"
	statusPostSlack := "FAILED"
	if passedCourse {
		passedStatus = "TASKER_PASS"
		statusPostSlack = "PASSED"
	}
	numberDataHistories, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": userId, "histories.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER_OLD})
	numberTest := int(numberDataHistories) + 1
	modelUser.UpdateOneById(
		local.ISO_CODE,
		userId,
		bson.M{
			"$set": bson.M{
				"trainingPermission.ableDoQuizz": false,
				"trainingPermission.resetData":   false,
				"trainingPermission.testAll":     false,
				"checkInput":                     passedStatus,
			},
		},
	)
	now := globalLib.GetCurrentTime(local.TimeZone)
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
		bson.M{"taskerId": userId, "histories.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER},
		bson.M{"$set": bson.M{
			"histories.$.finishAt":   now,
			"histories.$.type":       globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER_OLD,
			"histories.$.numberTest": numberTest,
			"updatedAt":              now,
		}},
	)
	postTestResultsToSlack(userId, numberOfTrueAnswers, numberOfQuestions, numberTest, statusPostSlack, "SUMMARY_TRAINING")
}

/*
 * @Description: Get Number Quizz
 * @CreatedAt: 01/10/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getNumberQuizz(isoCode string) int {
	return 17
}
