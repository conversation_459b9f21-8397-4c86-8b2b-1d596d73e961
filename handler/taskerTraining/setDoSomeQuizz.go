package taskerTraining

import (
	"encoding/json"
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Set Do Some Result
 * @CreatedAt: 30/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SetDoSomeQuizz(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateSetDoSomeQuizz(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	currentCourse := make(map[string]interface{})
	b, _ := json.Marshal(reqBody.Data["currentCourse"])
	err := json.Unmarshal(b, &currentCourse)
	if err != nil {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	//Tasker do the quizz perfect score 100%
	minRateScore := 100
	passedCourse := true
	passedStatus := "PASSED"
	//count number of question
	numberOfQuestions := 0
	var quizzes []map[string]interface{}
	b, _ = json.Marshal(currentCourse["quizzes"])
	json.Unmarshal(b, &quizzes)
	if len(quizzes) > 0 {
		for _, q := range quizzes {
			if q["questions"] != nil {
				var questions []map[string]interface{}
				b, _ = json.Marshal(q["questions"])
				json.Unmarshal(b, &questions)
				if questions != nil {
					numberOfQuestions += len(questions)
				}
			}
		}
	}
	trainingTaskerRecord, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": reqBody.UserId})
	//save into database
	now := globalLib.GetCurrentTime(local.TimeZone)
	if trainingTaskerRecord {
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
			bson.M{"taskerId": reqBody.UserId},
			bson.M{
				"$set": bson.M{"updatedAt": now},
				"$push": bson.M{
					"histories": bson.M{
						"answerMatrix":        currentCourse,
						"numberOfQuestions":   numberOfQuestions,
						"numberOfTrueAnswers": numberOfQuestions,
						"minRateScore":        minRateScore,
						"passed":              passedCourse,
						"type":                "SOME_QUIZZ",
						"createdAt":           now,
					},
				},
			},
		)
	} else {
		//Insert new record
		dataInsert := map[string]interface{}{
			"_id":      globalLib.GenerateObjectId(),
			"taskerId": reqBody.UserId,
			"histories": []map[string]interface{}{
				{
					"answerMatrix":        currentCourse,
					"numberOfQuestions":   numberOfQuestions,
					"numberOfTrueAnswers": numberOfQuestions,
					"minRateScore":        minRateScore,
					"passed":              passedCourse,
					"type":                "SOME_QUIZZ",
					"createdAt":           now,
				},
			},
			"createdAt": now,
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], dataInsert)
	}
	// Post to slack
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		postTestResultsToSlack(reqBody.UserId, numberOfQuestions, numberOfQuestions, 1, passedStatus, "SOME_QUIZZ")
	}()

	result := make(map[string]interface{})
	result["success"] = passedCourse
	result["data"] = map[string]interface{}{
		"numQuestions": numberOfQuestions,
		"numTrueAns":   numberOfQuestions,
	}
	if passedCourse {
		result["reason"] = "PASSED_COURSE"
	} else {
		result["reason"] = "DO_NOT_PASS_COURSE"
	}
	wg.Wait()
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateSetDoSomeQuizz(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Data == nil {
		return &lib.ERROR_DATA_UPDATE_REQUIRED
	}
	return nil
}
