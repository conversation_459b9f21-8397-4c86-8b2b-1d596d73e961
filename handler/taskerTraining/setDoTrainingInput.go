package taskerTraining

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskerSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerSettings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Set Do Training Input by UserId
 * @CreatedAt: 02/10/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SetDoTrainingInput(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateSetDoTraining(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	currentCourse := make(map[string]interface{})
	b, _ := json.Marshal(reqBody.Data["currentCourse"])
	err := json.Unmarshal(b, &currentCourse)
	if err != nil {
		local.Logger.Warn(lib.ERROR_DATA_INVALID.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_DATA_INVALID)
		return
	}
	var quizzes []map[string]interface{}
	b, _ = json.Marshal(currentCourse["quizzes"])
	json.Unmarshal(b, &quizzes)
	//find the record corresponding to taskerId
	var taskerSetting *modelTaskerSetting.TaskerSettings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"trainingInput": 1, "trainingTasker": 1}, &taskerSetting)
	var version, versionSettings int32
	if currentCourse["version"] != nil {
		version = int32(currentCourse["version"].(float64))
	}
	if taskerSetting != nil && taskerSetting.TrainingTasker != nil && taskerSetting.TrainingTasker.Version > 0 {
		versionSettings = taskerSetting.TrainingTasker.Version
	}
	if len(quizzes) == 0 || (version != 0 && version < versionSettings) {
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"trainingPermission.resetData": true}})
		globalResponse.ResponseError(w, lib.ERROR_ON_RESET_DATA)
		return
	}
	//.
	numberOfQuestions := 0
	numberOfTrueAnswers := 0
	var numberTest int32
	if quizzes[0]["numberTest"] != nil {
		numberTest = int32(quizzes[0]["numberTest"].(float64))
	}
	var minRateScore float64
	var maxNumberTest int32
	if taskerSetting != nil && taskerSetting.TrainingInput != nil {
		minRateScore = taskerSetting.TrainingInput.MinRateScore
		maxNumberTest = taskerSetting.TrainingInput.NumberTest
	}
	trainingTaskerRecord, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE], bson.M{"taskerId": reqBody.UserId})
	for _, q := range quizzes {
		var questions []map[string]interface{}
		if q["questions"] != nil {
			b, _ = json.Marshal(q["questions"])
			json.Unmarshal(b, &questions)
		}
		if questions != nil {
			numberOfQuestions += len(questions)
		}
		if q["countPass"] != nil {
			numberOfTrueAnswers += int(q["countPass"].(float64))
		}
	}
	passedCourse := getScoreFunction(numberOfTrueAnswers, numberOfQuestions, minRateScore)
	if numberTest >= maxNumberTest && !passedCourse {
		if numberTest == maxNumberTest {
			setDatabase(trainingTaskerRecord, globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT, currentCourse, numberOfTrueAnswers, numberOfQuestions, minRateScore, passedCourse, reqBody.UserId)
		}
		modelUser.UpdateOneById(
			local.ISO_CODE,
			reqBody.UserId,
			bson.M{
				"$set": bson.M{
					"checkInput":                     "INPUT_FAIL",
					"trainingPermission.ableDoQuizz": false,
					"trainingPermission.testAll":     false,
					"trainingPermission.resetData":   false,
				},
			},
		)
		globalResponse.ResponseSuccess(w, map[string]interface{}{
			"type":          globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT,
			"reason":        "INPUT_FAILED",
			"maxNumberTest": maxNumberTest,
		})
		return
	} else if passedCourse && numberTest <= maxNumberTest {
		modelUser.UpdateOneById(
			local.ISO_CODE,
			reqBody.UserId,
			bson.M{
				"$set": bson.M{
					"checkInput":                     globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER,
					"trainingPermission.ableDoQuizz": true,
					"trainingPermission.testAll":     true,
					"trainingPermission.resetData":   true,
				},
			},
		)
	}
	setDatabase(trainingTaskerRecord, globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT, currentCourse, numberOfTrueAnswers, numberOfQuestions, minRateScore, passedCourse, reqBody.UserId)
	//5. return data to client
	reason := "DO_NOT_PASS_COURSE"
	if passedCourse {
		reason = "INPUT_PASSED"
	}
	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"type":   globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT,
		"reason": reason,
		"data": map[string]interface{}{
			"numberTest":   numberTest,
			"numQuestions": numberOfQuestions,
			"numTrueAns":   numberOfTrueAnswers,
		},
	})
}
