package taskerTraining

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Set Training Tasker
 * @CreatedAt: 27/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SetTrainingTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Set data
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASKER_TRAINING[local.ISO_CODE],
		bson.M{
			"taskerId":       reqBody.UserId,
			"histories.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER,
		},
		bson.M{
			"$set": bson.M{"histories.$.type": globalConstant.TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER_OLD},
		},
	)
	globalResponse.ResponseSuccess(w, nil)
}
