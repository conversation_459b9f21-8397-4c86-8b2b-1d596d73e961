package promotionPaymentMethod

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelPromotionPaymentMethod "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionPaymentMethod"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func GetPromotionPaymentMethod(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetPromotionWallet(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.PROMOTION_PAYMENT_METHOD_STATUS_ACTIVE,
	}
	isTester := isUserTester(reqBody.UserId)
	if isTester {
		delete(query, "status")
		query["$or"] = []bson.M{
			{"status": globalConstant.PROMOTION_PAYMENT_METHOD_STATUS_ACTIVE},
			{"status": globalConstant.PROMOTION_PAYMENT_METHOD_STATUS_INACTIVE, "isTesting": true},
		}
	}
	var promotionPaymentMethods, validPromotions, result []*modelPromotionPaymentMethod.PromotionPaymentMethod
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_PROMOTION_PAYMENT_METHOD[local.ISO_CODE],
		query,
		bson.M{"code": 1, "typeOfPromotion": 1, "applyFor": 1, "value": 1, "isoCode": 1},
		&promotionPaymentMethods,
	)
	if len(promotionPaymentMethods) > 0 {
		for _, v := range promotionPaymentMethods {
			switch v.TypeOfPromotion {
			// Áp dụng cho user mới
			case globalConstant.PROMOTION_PAYMENT_METHOD_TYPE_OF_PROMOTION_NEW:
				isApplyNewUser := isApplyForNewUser(reqBody.UserId)
				if isApplyNewUser {
					validPromotions = append(validPromotions, v)
				}
			// Áp dụng cho user lần đầu sử dụng
			case globalConstant.PROMOTION_PAYMENT_METHOD_TYPE_OF_PROMOTION_CURRENT:
				isApplyFirstUse := isApplyForFirstUseUser(reqBody.UserId, v.ApplyFor)
				if isApplyFirstUse {
					validPromotions = append(validPromotions, v)
				}
			case globalConstant.PROMOTION_PAYMENT_METHOD_TYPE_OF_PROMOTION_BOTH:
				isApplyNewUser := isApplyForNewUser(reqBody.UserId)
				if isApplyNewUser {
					validPromotions = append(validPromotions, v)
				} else {
					isApplyFirstUse := isApplyForFirstUseUser(reqBody.UserId, v.ApplyFor)
					if isApplyFirstUse {
						validPromotions = append(validPromotions, v)
					}
				}
			}
		}
		// Check payment method is valid in setting country
		if len(validPromotions) > 0 {
			var settingCountry *modelSettingCountry.SettingCountry
			globalDataAccess.GetOneByQuery(
				globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE],
				bson.M{"isoCode": local.ISO_CODE},
				bson.M{"paymentMethods.bookTask": 1},
				&settingCountry,
			)
			if settingCountry != nil && settingCountry.PaymentMethods != nil && settingCountry.PaymentMethods.BookTask != nil && len(settingCountry.PaymentMethods.BookTask) > 0 {
				for _, s := range settingCountry.PaymentMethods.BookTask {
					for _, v := range validPromotions {
						if v.ApplyFor == s.Name && globalLib.CompareVersion(reqBody.AppVersion, s.MinVersion) >= 0 {
							var isApply bool
							if isTester {
								if s.Status == "ACTIVE" || (s.Status == "INACTIVE" && s.IsTesting) {
									isApply = true
								}
							} else if s.Status == "ACTIVE" {
								isApply = true
							}
							if isApply {
								result = append(result, v)
							}
						}
					}
				}
			}
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

// ===============================

func validateGetPromotionWallet(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.AppVersion == "" {
		return &lib.ERROR_APP_VERSION_REQUIRED
	}
	return nil
}

func isUserTester(userId string) bool {
	user, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"phone": 1})
	if user == nil {
		return false
	}
	var setting *modelSetting.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &setting)
	if setting != nil && len(setting.Tester) > 0 && globalLib.FindStringInSlice(setting.Tester, user.Phone) >= 0 {
		return true
	}
	return false
}

func isApplyForNewUser(userId string) bool {
	var arrayStatus = [4]string{
		globalConstant.TASK_STATUS_POSTED,
		globalConstant.TASK_STATUS_WAITING,
		globalConstant.TASK_STATUS_CONFIRMED,
		globalConstant.TASK_STATUS_DONE,
	}
	isExistTask, err := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": userId, "status": bson.M{"$in": arrayStatus}})
	if !isExistTask && err == nil {
		return true
	}
	return false
}

func isApplyForFirstUseUser(userId string, paymentMethod string) bool {
	var arrayStatus = [4]string{
		globalConstant.TASK_STATUS_POSTED,
		globalConstant.TASK_STATUS_WAITING,
		globalConstant.TASK_STATUS_CONFIRMED,
		globalConstant.TASK_STATUS_DONE,
	}
	isExistTask, err := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": userId, "status": bson.M{"$in": arrayStatus}, "payment.method": paymentMethod})
	if !isExistTask && err == nil {
		return true
	}
	return false
}
