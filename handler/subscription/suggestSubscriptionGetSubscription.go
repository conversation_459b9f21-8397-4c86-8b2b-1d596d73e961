/*
 * @File: suggestSubscriptionGetSubscription.go
 * @Description: Handler function of suggesst subscription get suggesstion api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
package subscription

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionRequest"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Subscription Request by UserId
 * @CreatedAt: 25/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func SuggestSubscriptionGetSubscription(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Get data
	last30Days := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -30)
	y, m, d := last30Days.Date()
	last30Days = time.Date(y, m, d, 0, 0, 0, 0, last30Days.Location())
	var result []*modelSubscriptionRequest.SubscriptionRequest
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
		bson.M{
			"taskerId":  reqBody.UserId,
			"createdAt": bson.M{"$gte": last30Days},
		},
		bson.M{},
		&result,
	)
	globalResponse.ResponseSuccess(w, result)
}
