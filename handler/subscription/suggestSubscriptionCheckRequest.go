/*
 * @File: suggestSubscriptionCheckRequest.go
 * @Description: Handler function of suggestSubscriptionCheckRequest api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
package subscription

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Check Request
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SuggestSubscriptionCheckRequest(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody modelSubscriptionRequest.SubscriptionRequest
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Check exists request
	isExistsRequest, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], bson.M{"askerId": reqBody.AskerId, "taskerId": reqBody.TaskerId, "status": globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW})
	if isExistsRequest {
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_REQUEST_EXIST)
		return
	}
	// Check Tasker in blacklist
	asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.AskerId, bson.M{"blackList": 1})
	if asker != nil && len(asker.BlackList) > 0 && globalLib.FindStringInSlice(asker.BlackList, reqBody.TaskerId) >= 0 {
		globalResponse.ResponseError(w, lib.ERROR_TASKER_BLACKLIST)
		return
	}
	// Check exists bad rating from Asker to Tasker
	isExistBadRating, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"askerId": reqBody.AskerId, "taskerId": reqBody.TaskerId, "rate": bson.M{"$lte": 3}})
	if isExistBadRating {
		globalResponse.ResponseError(w, lib.ERROR_BAD_RATING)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}
