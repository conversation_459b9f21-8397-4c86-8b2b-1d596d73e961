/*
 * @File: suggestSubscriptionGetTask.go
 * @Description: Handler function of suggesst subscription get task api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
package subscription

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get Task
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: ngoctb3
 */
func SuggestSubscriptionGetTask(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	start := reqBody.BeginAt.Add(-1 * time.Hour)
	end := reqBody.BeginAt.Add(1 * time.Hour)
	var task *modelTask.Task
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"date":           bson.M{"$gte": start, "$lte": end},
			"phone":          reqBody.Phone,
			"contactName":    reqBody.Name,
			"serviceText.en": reqBody.ServiceName,
			"cost":           reqBody.Cost,
			"address":        reqBody.Address,
		},
		bson.M{"viewedTaskers": 0, "voiceCallHistory.createdAt": 0, "costDetail.currency": 0},
		&task)
	globalResponse.ResponseSuccess(w, task)
}
