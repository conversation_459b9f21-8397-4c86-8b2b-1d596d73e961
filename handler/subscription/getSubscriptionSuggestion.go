/*
 * @File: getSubscriptionSuggestion.go
 * @Description: Handler function of get subscription suggesstion api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package subscription

import (
	"encoding/json"
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: GetSubscriptionSuggestion by
 * @CreatedAt: 18/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetSubscriptionSuggestion(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.SubscriptionRequestId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED)
		return
	}
	// Get data
	var request *modelSubscriptionRequest.SubscriptionRequest
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
		bson.M{"_id": reqBody.SubscriptionRequestId, "status": globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW},
		bson.M{},
		&request,
	)
	if request != nil {
		now := globalLib.GetCurrentTime(local.TimeZone)
		before24h := now.Add(24 * time.Hour)
		before24h_ts := globalLib.ParseTimestampFromDate(before24h)
		// Check create and pay subscription before 24h.
		if request.Schedule[0].Seconds < before24h_ts.Seconds {
			// Subscription request EXPIRED
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
				reqBody.SubscriptionRequestId,
				bson.M{
					"$set": bson.M{
						"status":    globalConstant.SUBSCRIPTION_STATUS_EXPIRED,
						"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
			)
			// Remove notification of subscription request
			globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": request.AskerId, "data.subscriptionRequestId": reqBody.SubscriptionRequestId})
			globalResponse.ResponseSuccess(w, nil)
			return
		}
		tasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": request.TaskerId, "status": globalConstant.USER_STATUS_ACTIVE}, bson.M{})
		if tasker != nil {
			result := map[string]interface{}{}
			b, _ := json.Marshal(request)
			json.Unmarshal(b, &result)
			result["taskerInfo"] = tasker
			globalResponse.ResponseSuccess(w, result)
			return
		} else {
			// Tasker invalid at now => CANCEL subscription request
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
				reqBody.SubscriptionRequestId,
				bson.M{
					"$set": bson.M{
						"status":    globalConstant.SUBSCRIPTION_STATUS_CANCELED,
						"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
			)
			// Remove notification of subscription request
			globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": request.AskerId, "data.subscriptionRequestId": reqBody.SubscriptionRequestId})
		}
	}
	globalResponse.ResponseSuccess(w, nil)
}
