/*
 * @File: cancelSubscription.go
 * @Description: Handler function of cancel subscription by id api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package subscription

import (
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Cancel Subscription by SubscriptionId
 * @CreatedAt: 09/10/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func CancelSubscription(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.SubscriptionId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_ID_REQUIRED)
		return
	}
	// Set data
	var sub *modelSubscription.Subscription
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], reqBody.SubscriptionId, bson.M{"_id": 1, "status": 1, "payment": 1, "promotion": 1, "userId": 1}, &sub)
	if sub != nil && (sub.Status == globalConstant.SUBSCRIPTION_STATUS_NEW || sub.Status == globalConstant.SUBSCRIPTION_STATUS_EXPIRED) {
		// Check payment is processing
		if isCardPaymentProcessing(sub) {
			globalResponse.ResponseSuccess(w, nil)
			return
		}
		wg := &sync.WaitGroup{}
		// Only cancel with NEW subscription status
		wg.Add(1)
		go func() {
			defer wg.Done()
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], reqBody.SubscriptionId, bson.M{"$set": bson.M{"status": globalConstant.SUBSCRIPTION_STATUS_CANCELED, "updatedAt": globalLib.GetCurrentTime(local.TimeZone)}})
		}()
		// Update status to CANCEL all POs
		wg.Add(1)
		go func() {
			defer wg.Done()
			globalDataAccess.UpdateAllByQuery(
				globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE],
				bson.M{"subscriptionId": reqBody.SubscriptionId},
				bson.M{"$set": bson.M{"status": globalConstant.PURCHASE_ORDER_STATUS_CANCEL, "updatedAt": globalLib.GetCurrentTime(local.TimeZone)}},
			)
		}()
		// if sub has promotion, mark promotion code is unused
		if sub.Promotion != nil && sub.Promotion.Code != "" {
			wg.Add(1)
			go func() {
				defer wg.Done()
				markPromotionCodeUnused(sub.Promotion.Code, sub.UserId)
			}()
		}
		wg.Wait()
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Check Card Payment Processing
 * @CreatedAt: 08/10/2020
 * @Author: linhnh
 * @UpdatedAt: 12/11/2020
 * @UpdatedBy: vinhnt
 */
func isCardPaymentProcessing(sub *modelSubscription.Subscription) bool {
	if sub != nil && sub.Payment != nil && sub.Payment.Method == globalConstant.PAYMENT_METHOD_CARD {
		isExistTransaction, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_PAYMENT_TRANSACTION,
			bson.M{
				"data.subscriptionId": sub.XId,
				"status":              bson.M{"$ne": globalConstant.PAYMENT_TRANSACTION_STATUS_RESPONSED},
				"result.resultCode":   bson.M{"$ne": "Refused"},
			},
		)
		if isExistTransaction {
			return true
		}
	}
	return false
}

/*
 * @Description: Mark promotion code is unused when cancel subscription
 * @CreatedAt: 25/06/2021
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func markPromotionCodeUnused(code string, userId string) {
	globalDataAccess.DeleteAllByQuery(
		globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE],
		bson.M{
			"promotionCode": code,
			"userId":        userId,
		},
	)
	isGiftExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"userId": userId, "promotionCode": code})
	if isGiftExist {
		globalDataAccess.UpdateOneByQuery(
			globalCollection.COLLECTION_GIFT[local.ISO_CODE],
			bson.M{
				"userId":        userId,
				"promotionCode": code,
			},
			bson.M{
				"$unset": bson.M{"used": 1, "usedAt": 1},
			},
		)
	}
}
