/*
 * @File: suggestSubscriptionNewSubscription.go
 * @Description: Handler function of suggesst subscription new subscription api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package subscription

import (
	"fmt"
	"net/http"
	"time"

	"github.com/golang/protobuf/jsonpb"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionRequest"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type DateDuration struct {
	Date     *timestamppb.Timestamp
	Duration float64
}

var cfg = config.GetConfig()

/*
 * @Description: New SUBSCRIPTION_REQUEST
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func SuggestSubscriptionNewSubscription(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Decode request body
	var reqBody modelSubscriptionRequest.SubscriptionRequest
	err := jsonpb.Unmarshal(r.Body, &reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Check if subscription request exists
	isExistRequest, _ := globalDataAccess.IsExistByQuery(
		globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
		bson.M{
			"askerId":  reqBody.AskerId,
			"taskerId": reqBody.TaskerId,
			"status":   globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
		},
	)
	if isExistRequest {
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_REQUEST_EXIST)
		return
	}
	// Check for conflict subscriptions
	if isConflictSubscription(&reqBody) {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_CONFLICTED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_CONFLICTED)
		return
	}
	// Check for conflict tasks
	if isConflictTask(&reqBody) {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_CONFLICTED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_CONFLICTED)
		return
	}

	// Insert subscription request to database
	subscriptionRequestId := createSubscriptionRequest(&reqBody)

	users, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": [2]string{reqBody.AskerId, reqBody.TaskerId}}}, bson.M{"name": 1, "avatar": 1, "phone": 1, "language": 1})
	var asker, tasker *modelUser.Users
	for _, v := range users {
		if v.XId == reqBody.AskerId {
			asker = v
		} else if v.XId == reqBody.TaskerId {
			tasker = v
		}
	}

	// Send notification to Asker
	if asker != nil && tasker != nil {
		sendNotificationToAsker(asker, tasker, subscriptionRequestId, &reqBody)
		// Post to slack
		if cfg.SlackToken != "" {
			message := fmt.Sprintf("Có đề nghị tạo gói mới từ Tasker đến khách hàng. Tasker: %s - %s. Khách hàng: %s - %s - %s", tasker.Name, tasker.Phone, asker.Name, asker.Phone, reqBody.TaskPlace.City)
			globalLib.PostToSlack(cfg.SlackToken, "subscription-suggest", "bTaskee System", message)
		}
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: insert subscription request to database
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func createSubscriptionRequest(reqBody *modelSubscriptionRequest.SubscriptionRequest) string {
	insertData := &modelSubscriptionRequest.SubscriptionRequest{
		XId:         globalLib.GenerateObjectId(),
		AskerId:     reqBody.AskerId,
		TaskerId:    reqBody.TaskerId,
		IsoCode:     local.ISO_CODE,
		Schedule:    reqBody.Schedule,
		Address:     reqBody.Address,
		ServiceId:   reqBody.ServiceId,
		TaskPlace:   reqBody.TaskPlace,
		Location:    reqBody.Location,
		Month:       reqBody.Month,
		Duration:    reqBody.Duration,
		Description: reqBody.Description,
		TaskNote:    reqBody.TaskNote,
		HomeType:    reqBody.HomeType,
		ServiceText: reqBody.ServiceText,
		Phone:       reqBody.Phone,
		ContactName: reqBody.ContactName,
		Status:      globalConstant.SUBSCRIPTION_REQUEST_STATUS_NEW,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	globalDataAccess.InsertOne(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE], insertData)
	return insertData.XId
}

/*
 * @Description: Check Conflict Subscription
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func checkConflict(task *DateDuration, otherTask *DateDuration) bool {
	taskDateTime := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	otherTaskDateTime := globalLib.ParseDateFromTimeStamp(otherTask.Date, local.TimeZone)
	if taskDateTime.Equal(otherTaskDateTime) {
		return true
	}
	// The time when end of task and other task
	taskEndTime := taskDateTime.Add(time.Duration(task.Duration) * time.Hour)
	otherTaskEndTime := otherTaskDateTime.Add(time.Duration(otherTask.Duration) * time.Hour)
	if (otherTaskDateTime.After(taskDateTime) && otherTaskDateTime.Before(taskEndTime)) ||
		(otherTaskEndTime.After(taskDateTime) && otherTaskEndTime.Before(taskEndTime)) ||
		(otherTaskDateTime.Before(taskDateTime) && otherTaskEndTime.After(taskEndTime)) {
		return true
	}
	return false
}

/*
 * @Description: Check Conflict Subscription
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func isConflictSubscription(reqBody *modelSubscriptionRequest.SubscriptionRequest) bool {
	var subs []*modelSubscription.Subscription
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], bson.M{"forceAcceptTaskerId": reqBody.TaskerId, "status": globalConstant.SUBSCRIPTION_STATUS_ACTIVE}, bson.M{"schedule": 1, "duration": 1}, &subs)
	if len(subs) > 0 {
		var isConflict bool
		for _, v := range subs {
			for _, s := range reqBody.Schedule {
				for _, o := range v.Schedule {
					task := &DateDuration{Date: s, Duration: reqBody.Duration}
					otherTask := &DateDuration{Date: o, Duration: v.Duration}
					if checkConflict(task, otherTask) {
						isConflict = true
						break
					}
				}
				if isConflict {
					break
				}
			}
			if isConflict {
				break
			}
		}
		if isConflict {
			return true
		}
	}
	return false
}

/*
 * @Description: Check Conflict task
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func isConflictTask(reqBody *modelSubscriptionRequest.SubscriptionRequest) bool {
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"date":                    bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
			"status":                  bson.M{"$in": [2]string{globalConstant.TASK_STATUS_CONFIRMED, globalConstant.TASK_STATUS_WAITING}},
			"acceptedTasker.taskerId": reqBody.TaskerId,
		},
		bson.M{"date": 1, "duration": 1},
		&tasks,
	)
	if len(tasks) > 0 {
		var isConflict bool
		for _, v := range tasks {
			for _, s := range reqBody.Schedule {
				task := &DateDuration{Date: s, Duration: reqBody.Duration}
				otherTask := &DateDuration{Date: v.Date, Duration: v.Duration}
				if checkConflict(task, otherTask) {
					isConflict = true
					break
				}
			}
			if isConflict {
				break
			}
		}
		if isConflict {
			return true
		}
	}
	return false
}

/*
 * @Description: Send notification to asker
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func sendNotificationToAsker(asker, tasker *modelUser.Users, subscriptionRequestId string, reqBody *modelSubscriptionRequest.SubscriptionRequest) {
	lang := globalConstant.LANG_EN
	if asker.Language != "" {
		lang = asker.Language
	}
	notify := map[string]interface{}{
		"_id":         globalLib.GenerateObjectId(),
		"userId":      reqBody.AskerId,
		"type":        36,
		"description": localization.T(lang, "REQUEST_SUBSCRIPTION_NOTIFICATION_CONTENT"),
		"createdAt":   globalLib.GetCurrentTime(local.TimeZone),
		"data": map[string]interface{}{
			"subscriptionRequestId": subscriptionRequestId,
			"taskerInfo":            tasker,
		},
	}
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "REQUEST_SUBSCRIPTION_NOTIFICATION_TITLE"),
		En: localization.T("en", "REQUEST_SUBSCRIPTION_NOTIFICATION_TITLE"),
		Ko: localization.T("ko", "REQUEST_SUBSCRIPTION_NOTIFICATION_TITLE"),
		Th: localization.T("th", "REQUEST_SUBSCRIPTION_NOTIFICATION_TITLE"),
	}
	body := &modelService.ServiceText{
		Vi: localization.T("vi", "REQUEST_SUBSCRIPTION_NOTIFICATION_CONTENT"),
		En: localization.T("en", "REQUEST_SUBSCRIPTION_NOTIFICATION_CONTENT"),
		Ko: localization.T("ko", "REQUEST_SUBSCRIPTION_NOTIFICATION_CONTENT"),
		Th: localization.T("th", "REQUEST_SUBSCRIPTION_NOTIFICATION_CONTENT"),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type: 36,
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: reqBody.AskerId, Language: lang},
	}
	lib.SendNotification([]interface{}{notify}, userIds, title, body, payload, "")
}
