package getDetailSubscriptionSchedule

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

// map data schedule by task history
func mapSubsData(subs *modelSubscription.Subscription) interface{} {
	var totalTaskDone, totalTaskCancel, totalTaskProcess, totalTaskExpired int32
	// Get data tasks history
	mapTaskById := getMapTaskById(subs.TaskIds)
	// map taskId by index
	mapTaskIdByIndex := make(map[int]string)
	for i, t := range subs.TaskIds {
		mapTaskIdByIndex[i] = t
	}
	// Get data schedule
	scheduleInfos := []map[string]interface{}{}
	for i, sTime := range subs.Schedule {
		scheduleDate := sTime
		scheduleStatus := "WAITING_POSTED"
		// Get data task by index in history if exist
		if taskId, ok := mapTaskIdByIndex[i]; ok && taskId != "" {
			// Get data schedule by task if exist task
			if task, ok := mapTaskById[taskId]; ok && task != nil {
				scheduleStatus = task.Status
				scheduleDate = task.Date
				if task.Status == globalConstant.TASK_STATUS_DONE {
					totalTaskDone += 1
				}
				if task.Status == globalConstant.TASK_STATUS_CANCELED {
					totalTaskCancel += 1
				}
				if globalLib.FindStringInSlice([]string{globalConstant.TASK_STATUS_POSTED, globalConstant.TASK_STATUS_WAITING, globalConstant.TASK_STATUS_CONFIRMED}, task.Status) >= 0 {
					totalTaskProcess += 1
					scheduleStatus = "PROCESSING"
				}
				if task.Status == globalConstant.TASK_STATUS_EXPIRED {
					totalTaskExpired += 1
				}
			}
		}
		scheduleInfo := map[string]interface{}{
			"date":   globalLib.ParseDateFromTimeStamp(scheduleDate, local.TimeZone),
			"status": scheduleStatus,
		}
		scheduleInfos = append(scheduleInfos, scheduleInfo)
	}
	return map[string]interface{}{
		"totalTask":        len(subs.Schedule),
		"remainingTask":    len(subs.Schedule) - len(subs.TaskIds),
		"scheduleInfos":    scheduleInfos,
		"totalTaskDone":    totalTaskDone,
		"totalTaskCancel":  totalTaskCancel,
		"totalTaskProcess": totalTaskProcess,
		"totalTaskExpired": totalTaskExpired,
	}
}

// Get map task by id
func getMapTaskById(taskIds []string) map[string]*modelTask.Task {
	mapTaskById := make(map[string]*modelTask.Task)
	if len(taskIds) == 0 {
		return mapTaskById
	}
	taskIds = globalLib.UniqString(taskIds)
	var listTasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": taskIds}},
		bson.M{"_id": 1, "date": 1, "status": 1},
		&listTasks,
	)
	for _, t := range listTasks {
		mapTaskById[t.XId] = t
	}
	return mapTaskById
}
