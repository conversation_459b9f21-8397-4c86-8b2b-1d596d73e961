package getDetailSubscriptionSchedule

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	"go.mongodb.org/mongo-driver/bson"
)

// GetSubscription by Id
func getSubscription(subscriptionId string) (*modelSubscription.Subscription, *globalResponse.ResponseErrorCode) {
	var subscription *modelSubscription.Subscription
	globalDataAccess.GetOneById(
		globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
		subscriptionId,
		bson.M{"schedule": 1, "taskIds": 1},
		&subscription,
	)
	if subscription == nil {
		return nil, &lib.ERROR_SUBSCRIPTION_NOT_FOUND
	}
	return subscription, nil
}
