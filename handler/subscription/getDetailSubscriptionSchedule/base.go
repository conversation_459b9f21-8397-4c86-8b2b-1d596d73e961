package getDetailSubscriptionSchedule

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

// GetDetailSubscriptionSchedule
func GetDetailSubscriptionSchedule(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	errCode := validateGetScheduleTasks(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	// Get list tasks
	subscription, errCode := getSubscription(reqBody.SubscriptionId)
	if errCode != nil {
		return nil, errCode
	}
	// map data
	result := mapSubsData(subscription)
	// Return data
	return result, nil
}
