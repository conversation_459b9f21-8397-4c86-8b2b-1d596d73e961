/*
 * @File: getInfoSubscription.go
 * @Description: Handler function of get subscription info by subscriptionId api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package subscription

import (
	"net/http"
	"sort"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Subscription Info by SubscriptionId
 * @CreatedAt: 25/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetInfoSubscription(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetInfoSubscription(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	result := getSubscriptionInfo(reqBody)
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get Subscription Info by SubscriptionId
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetInfoSubscription(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.SubscriptionId == "" {
		return &lib.ERROR_SUBSCRIPTION_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}

/*
 * @Description: Get Subscription Info from db
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getSubscriptionInfo(reqBody *model.ApiRequest) map[string]interface{} {
	result := make(map[string]interface{})
	var sub *modelSubscription.Subscription
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], bson.M{"_id": reqBody.SubscriptionId, "forceAcceptTaskerId": reqBody.UserId}, bson.M{"schedule": 1}, &sub)
	if sub != nil && sub.Schedule != nil && len(sub.Schedule) > 0 {
		startDate := sub.Schedule[0]
		endDate := sub.Schedule[len(sub.Schedule)-1]
		weekday := []time.Time{}
		for _, v := range sub.Schedule {
			d := globalLib.ParseDateFromTimeStamp(v, local.TimeZone)
			var isExist bool
			for _, w := range weekday {
				if w.Weekday().String() == d.Weekday().String() {
					isExist = true
					break
				}
			}
			if !isExist {
				weekday = append(weekday, d)
			}
		}
		// Sort array weekday
		sort.SliceStable(weekday, func(i int, j int) bool {
			return weekday[i].Before(weekday[j])
		})
		result["startDate"] = globalLib.ParseDateFromTimeStamp(startDate, local.TimeZone)
		result["endDate"] = globalLib.ParseDateFromTimeStamp(endDate, local.TimeZone)
		result["weekday"] = weekday
	}
	return result
}
