/*
 * @File: getSubscriptionByUserId.go
 * @Description: Handler function of get subscription list by userId api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package subscription

import (
	"net/http"
	"sync"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Subscription by UserId
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 18/05/2021
 * @UpdatedBy: ngoctb3
 */
func GetSubscriptionByUserId(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Get data
	result, _ := getListSubscriptionByUserId(reqBody.UserId, local.ISO_CODE)
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get Subscription by UserId
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt: 18/05/2021
 * @UpdatedBy: ngoctb3
 */
func getListSubscriptionByUserId(userId string, isoCode string) ([]map[string]interface{}, error) {
	result, _ := globalDataAccess.GetAllByQuerySortMap(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
		bson.M{
			"userId": userId,
			"status": bson.M{"$ne": globalConstant.SUBSCRIPTION_STATUS_CANCELED},
			"$or": []bson.M{
				{"isHide": bson.M{"$exists": false}},
				{"isHide": false},
			},
		},
		bson.M{},
		bson.M{"createdAt": -1},
	)

	// TODO: Check cho version cũ đang dùng chung serviceId cho gói homeCleaning
	cleaningService := &modelService.Service{}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &cleaningService)
	cleaningSubscriptionSv := &modelService.Service{}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}, bson.M{"_id": 1}, &cleaningSubscriptionSv)

	if len(result) > 0 {
		var serviceIds, taskerIds []string
		for _, v := range result {
			if v["serviceId"] != nil {
				// check cho version cũ đang dùng chung serviceId cho gói homeCleaning
				if v["serviceId"].(string) == cleaningService.XId {
					v["serviceId"] = cleaningSubscriptionSv.XId
				}
				serviceIds = append(serviceIds, v["serviceId"].(string))
			}
			if v["forceAcceptTaskerId"] != nil {
				forceAcceptTaskerId := cast.ToString(v["forceAcceptTaskerId"])
				taskerIds = append(taskerIds, forceAcceptTaskerId)
			}
			if v["month"] == nil {
				v["month"] = 1
			}
			mapData.MapCostDetailForAsker(v)
		}
		if len(serviceIds) > 0 {
			w := sync.WaitGroup{}
			var mapServiceById map[string]*modelService.Service
			w.Add(1)
			go func() {
				defer w.Done()
				mapServiceById = getMapServiceById(serviceIds)
			}()
			var mapTaskerById map[string]interface{}
			w.Add(1)
			go func() {
				defer w.Done()
				mapTaskerById = getMapTaskerById(taskerIds)
			}()
			w.Wait()
			for _, v := range result {
				if v["serviceId"] != nil {
					serviceId := cast.ToString(v["serviceId"])
					if s, ok := mapServiceById[serviceId]; ok && serviceId != "" {
						v["service"] = map[string]interface{}{
							"_id":  s.XId,
							"name": s.Name,
							"text": s.Text,
							"icon": s.Icon,
						}
					}
				}
				if v["forceAcceptTaskerId"] != nil {
					forceAcceptTaskerId := cast.ToString(v["forceAcceptTaskerId"])
					if tasker, ok := mapTaskerById[forceAcceptTaskerId]; ok && forceAcceptTaskerId != "" {
						v["forceAcceptTasker"] = tasker
					}
				}
			}
		}
	}
	return result, nil
}

func getMapServiceById(serviceIds []string) map[string]*modelService.Service {
	serviceIds = globalLib.UniqString(serviceIds)
	mapServiceById := make(map[string]*modelService.Service)
	var services []*modelService.Service
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"_id": bson.M{"$in": serviceIds}}, bson.M{"_id": 1, "name": 1, "text": 1, "icon": 1}, &services)
	for _, service := range services {
		mapServiceById[service.XId] = service
	}
	return mapServiceById
}

func getMapTaskerById(taskerIds []string) map[string]interface{} {
	mapTaskerById := make(map[string]interface{})
	if len(taskerIds) == 0 {
		return mapTaskerById
	}
	taskerIds = globalLib.UniqString(taskerIds)
	taskers, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": taskerIds}}, bson.M{"_id": 1, "name": 1, "avatar": 1, "gender": 1, "isPremiumTasker": 1})
	for _, tasker := range taskers {
		mapTaskerById[tasker.XId] = tasker
	}
	return mapTaskerById
}
