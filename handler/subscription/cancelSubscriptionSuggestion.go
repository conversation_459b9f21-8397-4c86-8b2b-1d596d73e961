/*
 * @File: cancelSubscriptionSuggestion.go
 * @Description: Handler function of cancel subscription suggestion by subscriptionRequestId
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package subscription

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: CancelSubscriptionSuggestion
 * @CreatedAt: 21/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func CancelSubscriptionSuggestion(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.SubscriptionRequestId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_SUBSCRIPTION_REQUEST_ID_REQUIRED)
		return
	}
	// Get data
	var request *modelSubscriptionRequest.SubscriptionRequest
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
		bson.M{"_id": reqBody.SubscriptionRequestId, "status": bson.M{"$ne": globalConstant.SUBSCRIPTION_STATUS_CANCELED}},
		bson.M{"taskerId": 1, "address": 1, "askerId": 1},
		&request,
	)
	if request != nil {
		// Subscription request CANCELED
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION_REQUEST[local.ISO_CODE],
			reqBody.SubscriptionRequestId,
			bson.M{
				"$set": bson.M{
					"status":    globalConstant.SUBSCRIPTION_STATUS_CANCELED,
					"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
				},
			},
		)
		// Remove notification of subscription request
		globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": request.AskerId, "data.subscriptionRequestId": reqBody.SubscriptionRequestId})
		// Send notification to request tasker
		sendNotificationToTasker(request)

		globalResponse.ResponseSuccess(w, nil)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: CancelSubscriptionSuggestion
 * @CreatedAt: 21/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func sendNotificationToTasker(request *modelSubscriptionRequest.SubscriptionRequest) {
	tasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": request.TaskerId, "status": globalConstant.USER_STATUS_ACTIVE}, bson.M{"language": 1})
	if tasker != nil {
		lang := globalConstant.LANG_EN
		if tasker.Language != "" {
			lang = tasker.Language
		}
		notify := map[string]interface{}{
			"_id":         globalLib.GenerateObjectId(),
			"userId":      request.TaskerId,
			"type":        25,
			"description": localization.T(lang, "SUBSCRIPTION_REQUEST_CANCEL", request.Address),
			"createdAt":   globalLib.GetCurrentTime(local.TimeZone),
			"data": map[string]interface{}{
				"subscriptionRequestId": request.XId,
			},
		}
		title := &modelService.ServiceText{
			Vi: localization.T("vi", ""),
			En: localization.T("en", ""),
			Ko: localization.T("ko", ""),
			Th: localization.T("th", ""),
		}
		body := &modelService.ServiceText{
			Vi: localization.T("vi", "SUBSCRIPTION_REQUEST_CANCEL", request.Address),
			En: localization.T("en", "SUBSCRIPTION_REQUEST_CANCEL", request.Address),
			Ko: localization.T("ko", "SUBSCRIPTION_REQUEST_CANCEL", request.Address),
			Th: localization.T("th", "SUBSCRIPTION_REQUEST_CANCEL", request.Address),
		}
		payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
			Type: 25,
		}

		userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
			{UserId: tasker.XId, Language: lang},
		}
		lib.SendNotification([]interface{}{notify}, userIds, title, body, payload, "")
	}
}
