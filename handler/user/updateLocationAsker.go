/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"encoding/json"
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Location Asker by UserId
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func UpdateLocationAsker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody map[string]interface{}
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
	}
	// Validate data
	errCode := validateDataUpdateLocationAsker(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	//Migrate phone number
	if reqBody["phoneNumber"] != nil {
		reqBody["phoneNumber"] = globalLib.MigratePhoneNumber(reqBody["phoneNumber"].(string), local.ISO_CODE)
	}

	// Set data update array element
	userId := reqBody["userId"].(string)
	locationId := reqBody["locationId"].(string)
	delete(reqBody, "userId")
	delete(reqBody, "locationId")
	if reqBody["isDefault"] != nil && reqBody["isDefault"].(bool) {
		_, err = modelUser.UpdateOneByQuery(local.ISO_CODE,
			bson.M{"_id": userId, "locations.isDefault": true},
			bson.M{"$set": bson.M{"locations.$.isDefault": false}},
		)
		if err != nil {
			globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
			return
		}
	}
	dataUpdate := make(map[string]interface{})
	for k, v := range reqBody {
		dataUpdate[fmt.Sprintf("locations.$.%s", k)] = v
	}
	_, err = modelUser.UpdateOneByQuery(local.ISO_CODE, bson.M{"_id": userId, "locations._id": locationId}, bson.M{"$set": dataUpdate})

	if err != nil {
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}

	globalResponse.ResponseSuccess(w, nil)
}

func validateDataUpdateLocationAsker(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["userId"] == nil {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody["locationId"] == nil {
		return &lib.ERROR_LOCATION_ID_REQUIRED
	}
	if reqBody["lat"] == nil && reqBody["lng"] == nil && reqBody["country"] == nil && reqBody["city"] == nil && reqBody["district"] == nil && reqBody["address"] == nil &&
		reqBody["contact"] == nil && reqBody["phoneNumber"] == nil && reqBody["shortAddress"] == nil && reqBody["countryCode"] == nil && reqBody["isoCode"] == nil &&
		reqBody["homeType"] == nil && reqBody["description"] == nil && reqBody["isDefault"] == nil {
		return &lib.ERROR_DATA_INVALID
	}

	return nil
}
