/*
 * @File: updateFreeSchedule.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"encoding/json"
	"net/http"
	"reflect"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Free Data Schedule from Users by UserId
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func UpdateFreeSchedule(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataUpdateFreeSchedule(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Set data
	user, _ := modelUser.GetOneByQueryMap(local.ISO_CODE, bson.M{"_id": reqBody.UserId, "type": globalConstant.USER_TYPE_TASKER}, bson.M{"_id": 1, "freeSchedule": 1})
	if user == nil || user["_id"] == nil {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	var isTaskerUpdate bool
	if user["freeSchedule"] != nil {
		b, _ := json.Marshal(user["freeSchedule"])
		temp := make(map[string]interface{})
		json.Unmarshal(b, &temp)
		if temp != nil && temp["isTaskerUpdate"] != nil {
			isTaskerUpdate = temp["isTaskerUpdate"].(bool)
		}
	}
	if !isTaskerUpdate && !reflect.DeepEqual(reqBody.FreeSchedule, user["freeSchedule"]) {
		_, err := modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"freeSchedule": reqBody.FreeSchedule, "isUpdateFreeSchedule": true}})
		if err != nil {
			local.Logger.Warn(lib.ERROR_UPDATE_FAILED.ErrorCode,
				zap.String("url", r.RequestURI),
				zap.Error(err),
				zap.Any("body", reqBody),
			)
			globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
			return
		}
	}
	globalResponse.ResponseSuccess(w, nil)
}

func validateDataUpdateFreeSchedule(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.FreeSchedule == nil {
		return &lib.ERROR_FREE_SCHEDULE_REQUIRED
	}

	return nil
}
