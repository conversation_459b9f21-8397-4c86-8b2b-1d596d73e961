/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package addHousekeepingLocation

import (
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Update Location Asker by UserId
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func AddHousekeepingLocation(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	// Validate data
	errCode := validateData(reqBody)
	if errCode != nil {
		return errCode
	}

	// 1.1 validate location in use
	errCode = validateLocation(reqBody)
	if errCode != nil {
		return errCode
	}

	//Migrate phone number
	userId := migrateData(reqBody)
	// add Location Asker
	return addLocation(userId, reqBody)
}
