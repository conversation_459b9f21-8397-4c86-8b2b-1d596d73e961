package addHousekeepingLocation

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func validateLocation(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	userId := reqBody["userId"].(string)
	address := reqBody["address"].(string)
	lat := reqBody["lat"].(float64)
	lng := reqBody["lng"].(float64)

	asker, errCode := getUser(userId)
	if errCode != nil {
		return errCode
	}

	if asker != nil && len(asker.HousekeepingLocations) > 0 {
		for _, location := range asker.HousekeepingLocations {
			if location.Address == address || (location.Lng == lng && location.Lat == lat) {
				return &lib.ERROR_LOCATION_ADDRESS_IN_USE
			}
		}
	}

	return nil

}

func getUser(userId string) (*modelUser.Users, *globalResponse.ResponseErrorCode) {
	fields := bson.M{"_id": 1, "housekeepingLocations": 1}
	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, fields)
	if asker == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}

	return asker, nil
}
