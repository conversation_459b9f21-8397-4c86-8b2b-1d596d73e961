/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package addHousekeepingLocation

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func addLocation(userId string, reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	_, err := modelUser.UpdateOneById(local.ISO_CODE, userId, bson.M{"$push": bson.M{"housekeepingLocations": reqBody}})
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}
	return nil
}
