/*
 * @File: addFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"fmt"
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

/*
 * @Description: Add Favorite Tasker
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func AddFavoriteTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserIdAndTaskIds(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Set data
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$addToSet": bson.M{"favouriteTasker": bson.M{"$each": reqBody.TaskerIds}}, "$pullAll": bson.M{"blackList": reqBody.TaskerIds}})
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		modelUser.UpdateAllByQuery(local.ISO_CODE, bson.M{"_id": bson.M{"$in": reqBody.TaskerIds}}, bson.M{"$addToSet": bson.M{"favouritedAsker": reqBody.UserId}})
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		for _, taskerId := range reqBody.TaskerIds {
			createFirstMessageForFavChat(reqBody.UserId, taskerId)
		}

	}()
	wg.Wait()
	globalResponse.ResponseSuccess(w, nil)
}

func createFirstMessageForFavChat(askerId string, taskerId string) {
	var tasker, asker *modelUser.Users
	tasker, err := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": taskerId}, bson.M{"_id": 1, "name": 1, "type": 1, "avatar": 1, "language": 1})
	if err != nil {
		msg := fmt.Sprintf("[go-api-asker-vn-v3] Can not get user: userId %s. Error: %s", taskerId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	asker, err = modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": askerId}, bson.M{"_id": 1, "name": 1, "type": 1, "avatar": 1, "language": 1})
	if err != nil {
		msg := fmt.Sprintf("[go-api-asker-vn-v3] Can not get user: userId %s. Error: %s", askerId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}

	// Check if chatmessage is exists
	query := bson.M{
		"members":     bson.M{"$size": 2}, // Check the array size is 2
		"members._id": bson.M{"$all": []string{askerId, taskerId}},
	}
	fields := bson.M{"_id": 1}
	chatMessage, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("[go-api-asker-vn-v3] Get Chat conversation failed: askerId %s, taskerId %s,  Error: %s", askerId, taskerId, err.Error())
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		return
	}
	if chatMessage != nil {
		return
	}

	chatId, err := pkgChatMessage.CreateFirstMessageForFavChat(local.ISO_CODE, asker, tasker)
	if err != nil {
		msg := fmt.Sprintf("[go-api-asker-vn-v3] Create first message for favourite chat failed: askerId %s, taskerId %s,  Error: %s", askerId, taskerId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	// Send notification to received user.

	askerLanguage := globalConstant.LANG_EN
	if asker.Language != "" {
		askerLanguage = asker.Language
	}
	var arrayNotification []interface{}

	notifyAsker := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		ChatId:      chatId,
		UserId:      asker.XId,
		Type:        28,
		Description: localization.T(askerLanguage, "CHAT_NEW_MESSAGE"),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	arrayNotification = append(arrayNotification, notifyAsker)
	title := localization.GetLocalizeObject("CHAT_NEW_MESSAGE_FROM", tasker.Name)
	body := localization.GetLocalizeObject("CHAT_NEW_MESSAGE")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       28,
		ChatId:     chatId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: asker.XId, Language: askerLanguage}}
	lib.SendNotificationV2(arrayNotification, userIds, title, body, payload, "")
}
