/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Favorite Tasker by UserId
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetFavoriteTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	user, err := getUser(reqBody.UserId)
	if err != nil || user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	var favouriteTaskerIds []string
	if user != nil && len(user.FavouriteTasker) > 0 {
		favouriteTaskerIds = user.FavouriteTasker
	}
	wg := &sync.WaitGroup{}
	var favouriteTaskers []map[string]interface{}
	var allTaskers []*modelUser.Users
	wg.Add(1)
	go func() {
		defer wg.Done()
		favouriteTaskers = getFavoriteTasker(user, favouriteTaskerIds)
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		allTaskers = getListTaskerForAddFavorite(user, favouriteTaskerIds)
	}()
	wg.Wait()

	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"favouriteTaskers": favouriteTaskers,
		"allTaskers":       allTaskers,
	})
}

func getUser(userId string) (*modelUser.Users, error) {
	user, err := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"_id": 1, "favouriteTasker": 1, "blackList": 1})
	return user, err
}

func getListTaskerForAddFavorite(user *modelUser.Users, favouriteTaskerIds []string) []*modelUser.Users {
	var allFavouriteTaskerIds []string
	mapTaskerIncorrect := make(map[string]struct{})
	for _, taskerId := range append(favouriteTaskerIds, user.BlackList...) {
		mapTaskerIncorrect[taskerId] = struct{}{}
	}
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": user.XId, "status": globalConstant.TASK_STATUS_DONE}, bson.M{"acceptedTasker.taskerId": 1}, 100, bson.M{"date": -1}, &tasks)
	for _, v := range tasks {
		for _, t := range v.AcceptedTasker {
			if _, ok := mapTaskerIncorrect[t.TaskerId]; !ok {
				mapTaskerIncorrect[t.TaskerId] = struct{}{}
				allFavouriteTaskerIds = append(allFavouriteTaskerIds, t.TaskerId)
			}
		}
	}
	var allTaskers []*modelUser.Users
	if len(allFavouriteTaskerIds) > 0 {
		allTaskers, _ = modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": allFavouriteTaskerIds}, "status": globalConstant.USER_STATUS_ACTIVE, "isoCode": local.ISO_CODE}, bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1, "isPremiumTasker": 1})
	}
	return allTaskers
}

func getFavoriteTasker(asker *modelUser.Users, favouriteTaskerIds []string) []map[string]interface{} {
	favouriteTaskers, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": favouriteTaskerIds}, "status": globalConstant.USER_STATUS_ACTIVE, "isoCode": local.ISO_CODE}, bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1, "isPremiumTasker": 1})
	results := []map[string]interface{}{}
	for _, v := range favouriteTaskers {
		var services []*modelService.Service
		var task *modelTask.Task
		wg := &sync.WaitGroup{}
		wg.Add(1)
		go func() {
			defer wg.Done()
			services = getServicesByTaskerId(v.XId)
		}()
		wg.Add(1)
		go func() {
			defer wg.Done()
			task = getLastPostedTask(asker.XId, v.XId)
		}()
		result := map[string]interface{}{
			"_id":       v.XId,
			"name":      v.Name,
			"avatar":    v.Avatar,
			"avgRating": v.AvgRating,
			"taskDone":  v.TaskDone,
		}
		if v.CleaningOption != nil && v.CleaningOption.HasCleaningKit {
			result["hasCleaningKit"] = true
		}
		if v.IsPremiumTasker {
			result["isPremiumTasker"] = true
		}
		wg.Wait()
		result["services"] = services
		if task != nil {
			result["lastPostedTask"] = globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
		}
		results = append(results, result)
	}
	return results
}

func getServicesByTaskerId(taskerId string) []*modelService.Service {
	var serviceChannels []*modelServiceChannel.ServiceChannel
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"taskerList": taskerId}, bson.M{"serviceId": 1}, &serviceChannels)
	var serviceIds []string
	for _, sc := range serviceChannels {
		serviceIds = append(serviceIds, sc.ServiceId)
	}
	var services []*modelService.Service
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"_id": bson.M{"$in": serviceIds}}, bson.M{"icon": 1, "text": 1, "name": 1}, &services)
	return services
}

func getLastPostedTask(askerId, taskerId string) *modelTask.Task {
	var task *modelTask.Task
	globalDataAccess.GetOneByQuerySort(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"askerId": askerId,
			"$or": []bson.M{
				{"acceptedTasker.taskerId": taskerId},
				{"acceptedTasker.companyId": taskerId},
			},
			"status": bson.M{"$nin": globalConstant.TASK_STATUS_CANCELED},
		},
		bson.M{"date": 1},
		bson.M{"date": -1},
		&task,
	)
	return task
}
