/*
 * @File: getNumberShared.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Number Shared by UserId
 * @CreatedAt: 23/11/2020
 * @Author: ngoctb
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetNumberShared(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Find user
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"referralCode": 1})
	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	numberOfShare, _ := modelUser.CountByQuery(local.ISO_CODE, bson.M{"friendCode": user.ReferralCode})
	result := map[string]interface{}{
		"total": numberOfShare,
	}
	globalResponse.ResponseSuccess(w, result)
}
