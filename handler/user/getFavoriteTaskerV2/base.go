/*
 * @File: getFavoriteTasker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getFavoriteTaskerV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Get Favorite Tasker by UserId
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetFavoriteTasker(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	// Get data user
	user, err := getUser(reqBody.UserId)
	if err != nil || user == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	// Get favorite tasker by asker
	favouriteTaskers := getFavoriteTasker(user)
	// Return data
	return favouriteTaskers, nil, nil
}
