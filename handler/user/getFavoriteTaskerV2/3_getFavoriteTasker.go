/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getFavoriteTaskerV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getFavoriteTasker(asker *modelUser.Users) []map[string]interface{} {
	var favouriteTaskerIds []string
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		favouriteTaskerIds = asker.FavouriteTasker
	}
	if len(favouriteTaskerIds) == 0 {
		return []map[string]interface{}{}
	}

	favouriteTaskers, _ := modelUser.GetAll(
		local.ISO_CODE,
		bson.M{
			"_id":     bson.M{"$in": favouriteTaskerIds},
			"status":  globalConstant.USER_STATUS_ACTIVE,
			"isoCode": local.ISO_CODE,
		},
		bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1, "isPremiumTasker": 1, "cleaningOption": 1, "company": 1, "employeeIds": 1},
		&globalDataAccessV2.QueryOptions{Sort: bson.M{"avgRating": -1}},
	)
	results := []map[string]interface{}{}
	for _, v := range favouriteTaskers {
		task := getLastPostedTask(asker.XId, v.XId)
		result := map[string]interface{}{
			"_id":       v.XId,
			"name":      v.Name,
			"avatar":    v.Avatar,
			"avgRating": v.AvgRating,
			"taskDone":  v.TaskDone,
		}
		if v.CleaningOption != nil && v.CleaningOption.HasCleaningKit {
			result["hasCleaningKit"] = true
		}
		if v.IsPremiumTasker {
			result["isPremiumTasker"] = true
		}
		if task != nil {
			result["lastPostedTask"] = globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
		}
		// Case tasker is company or employee of company -> Return this field
		if v.Company != nil || len(v.EmployeeIds) > 0 {
			result["isCompany"] = true
		}
		results = append(results, result)
	}
	return results
}

func getLastPostedTask(askerId, taskerId string) *modelTask.Task {
	var task *modelTask.Task
	globalDataAccess.GetOneByQuerySort(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"askerId": askerId,
			"status":  globalConstant.TASK_STATUS_DONE,
			"$or": []bson.M{
				{"acceptedTasker.taskerId": taskerId},
				{"acceptedTasker.companyId": taskerId},
			},
		},
		bson.M{"date": 1},
		bson.M{"date": -1},
		&task,
	)
	return task
}
