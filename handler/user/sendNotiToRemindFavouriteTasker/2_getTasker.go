package sendNotiToRemindFavouriteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasker(taskerId string) (*modelUsers.Users, *globalResponse.ResponseErrorCode, error) {

	var tasker *modelUsers.Users
	tasker, err := modelUser.GetOneById(local.ISO_CODE, taskerId, bson.M{"_id": 1, "language": 1})
	if tasker == nil || err != nil {
		return nil, &lib.ERROR_TASKER_NOT_FOUND, err
	}
	return tasker, nil, nil
}
