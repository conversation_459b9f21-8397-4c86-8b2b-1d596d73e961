package sendNotiToRemindFavouriteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func updateChatMessage(chatId string, taskId string) {
	pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{
			"chatId":                          chatId,
			"from":                            bson.M{"$in": []string{globalConstant.CHAT_MESSAGE_FROM_SYSTEM}},
			"type":                            globalConstant.TYPE_MESSAGE_REMIND_AFTER_15_MINUTES_CREATE_TASK,
			"taskRequestData.taskInfo.taskId": taskId,
			"messageBySystem.sendTo":          globalConstant.CHAT_MESSAGE_FROM_ASKER,
		},
		bson.M{"$set": bson.M{"taskRequestData.status": globalConstant.BOOK_FORCE_TASKER_STATUS_SENT_NOTI}},
	)
}
