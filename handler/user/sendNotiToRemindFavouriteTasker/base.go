package sendNotiToRemindFavouriteTasker

import (
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func SendNotiToRemindFavouriteTasker(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	tasker, errCode, err := getTasker(reqBody.TaskerId)
	if errCode != nil {
		return errCode, err
	}

	asker, errCode, err := getAsker(reqBody.UserId)
	if errCode != nil {
		return errCode, err
	}

	task, errCode, err := getTask(reqBody.TaskId)
	if errCode != nil {
		return errCode, err
	}

	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		setDisabledMessage(reqBody.ChatId, task.XId, globalConstant.KEY_BOOK_WITH_FAV, tasker.XId, asker.XId)
		initMessageForChatTasker(task, tasker, reqBody.ChatId)
		wg.Done()
	}()
	wg.Add(1)
	go func() {
		updateChatMessage(reqBody.ChatId, reqBody.TaskId)
		wg.Done()
	}()
	return nil, nil
}
