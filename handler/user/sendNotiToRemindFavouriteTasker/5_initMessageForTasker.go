package sendNotiToRemindFavouriteTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func initMessageForChatTasker(task *modelTask.Task, tasker *modelUsers.Users, chatId string) {
	message := &modelChatMessage.ChatMessageMessages{
		XId:       globalLib.GenerateObjectId(),
		From:      globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		TaskRequestData: &modelChatMessage.ChatMessageMessagesTaskRequestData{
			TaskInfo: &modelChatMessage.ChatMessageMessagesTaskRequestDataInfo{
				TaskId:      task.XId,
				District:    task.TaskPlace.District,
				Duration:    task.Duration,
				ServiceText: task.ServiceText,
				Lat:         task.Lat,
				Lng:         task.Lng,
				ServiceId:   task.ServiceId,
				Date:        task.Date,
				Timezone:    task.Timezone,
			},
		},
		MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
			Title:  localization.GetLocalizeObject("FAVOURITE_CHAT_NOTI_ASKER_REMIND_TASKER_TITLE"),
			Text:   localization.GetLocalizeObject("FAVOURITE_CHAT_NOTI_ASKER_REMIND_TASKER_BODY"),
			SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER,
			Key:    globalConstant.KEY_BOOK_WITH_FAV,
			Actions: []*modelChatMessage.ChatMessageMessagesMessageBySystemActions{
				{
					Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY,
					Title: localization.GetLocalizeObject("BUTTON_OTHER_TIME"),
					Key:   globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CHOOSE_OTHER_TIME,
				},
				{
					Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY,
					Title: localization.GetLocalizeObject("BUTTON_ACCEPT_TASK"),
					Key:   globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_ACCEPT_TASK,
				},
			},
		},
	}
	if task.DateOptions != nil {
		for _, option := range task.DateOptions {
			dateOption := &modelChatMessage.TaskDateOptions{
				XId:  option.XId,
				Date: option.Date,
			}
			message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
		}
	} else {
		dateOption := &modelChatMessage.TaskDateOptions{
			Date: task.Date,
		}
		message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
	}

	// Insert new message
	err := pkgChatMessage.SendMessageToConversation(local.ISO_CODE, chatId, []*modelChatMessage.ChatMessageMessages{message})
	if err != nil {
		msg := fmt.Sprintf("[go-api-asker-vn-v3] Send Chat Message to conversation failed: taskId %s. Error: %s", task.XId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
	}

	// Send notification to received user.
	taskerLanguage := globalConstant.LANG_EN
	if tasker.Language != "" {
		taskerLanguage = tasker.Language
	}
	var arrayNotification []interface{}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		ChatId:      chatId,
		UserId:      tasker.XId,
		Type:        28,
		Description: localization.T(taskerLanguage, "CHAT_NEW_MESSAGE"),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		Url:         fmt.Sprintf("/tasker/chatConversation?taskId=%s&chatToUserId=%s&type=TASKER", task.XId, task.ForceTasker.TaskerId),
	}
	arrayNotification = append(arrayNotification, notify)
	title := localization.GetLocalizeObject("DIALOG_TITLE_INFORMATION")
	body := localization.GetLocalizeObject("CHAT_NEW_MESSAGE")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       28,
		ChatId:     chatId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: tasker.XId, Language: taskerLanguage}}
	handler.SendChatMessageViaSocket(nil, chatId, []string{tasker.XId}, message)
	lib.SendNotification(arrayNotification, userIds, title, body, payload, "")
}
func setDisabledMessage(chatId, taskId, key, taskerId, askerId string) {
	// Get message
	queryMessage := bson.M{
		"chatId":                          chatId,
		"taskRequestData.taskInfo.taskId": taskId,
		"messageBySystem.key":             key,
	}
	messages, err := pkgChatMessage.GetChatMessages(globalConstant.ISO_CODE_VN, queryMessage, bson.M{})
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("[go-accept-task-vn-v3] Cannot get chat message: chatId %s, taskId %s. Error: %s", chatId, taskId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	if messages == nil {
		return
	}

	// Check if the message ID matches and the message has system actions
	for _, message := range messages {
		updateData := bson.M{}
		// Loop through the actions in the message
		for i := range message.MessageBySystem.Actions {
			// Check if the action is disabled
			if !message.MessageBySystem.Actions[i].IsDisabled {
				updateData[fmt.Sprintf("messageBySystem.actions.%d.isDisabled", i)] = true
			}
		}
		if len(updateData) == 0 {
			continue
		}
		// Update the database with the new 'isDisabled' values
		_, err := pkgChatMessage.UpdateChatMessage(local.ISO_CODE, bson.M{"_id": message.XId}, bson.M{"$set": updateData})
		// Return error if the update fails
		if err != nil {
			msg := fmt.Sprintf("[go-accept-task-vn-v3] Cannot update chat message: chatId %s, taskId %s. Error: %s", chatId, taskId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
		// Set the 'isDisabled' field to true for each action
		for _, action := range message.MessageBySystem.Actions {
			action.IsDisabled = true
		}
		var sendTo []string
		switch message.MessageBySystem.SendTo {
		case globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER:
			sendTo = []string{askerId}
		case globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER:
			sendTo = []string{taskerId}
		default:
			sendTo = []string{askerId, taskerId}
		}
		// Send the updated message to the chat participants via socket
		handler.SendChatMessageViaSocket(nil, chatId, sendTo, message)
	}
}
