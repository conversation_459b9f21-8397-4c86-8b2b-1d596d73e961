package sendNotiToRemindFavouriteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTask(taskId string) (*modelTask.Task, *globalResponse.ResponseErrorCode, error) {
	var task *modelTask.Task
	err := globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"_id": 1, "askerId": 1, "forceTasker": 1, "taskPlace": 1, "duration": 1, "serviceText": 1, "dateOptions": 1, "date": 1, "lat": 1, "lng": 1, "serviceId": 1, "timezone": 1}, &task)
	if task == nil || err != nil {
		return nil, &lib.ERROR_TASK_NOT_FOUND, err
	}
	return task, nil, nil
}
