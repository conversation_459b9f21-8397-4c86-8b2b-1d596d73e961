/*
 * @File: getCancelTaskHistory.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"
	"sort"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Cancel Task History by UserId
 * @CreatedAt: 16/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetCancelTaskHistory(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"taskCancelByTasker": 1})
	type TypeCancelDate struct {
		CancelDate time.Time `json:"cancelDate,omitempty"`
	}
	var result []*TypeCancelDate
	var array []*modelUser.UsersTaskCancelByTasker
	if user != nil && user.TaskCancelByTasker != nil && len(user.TaskCancelByTasker) > 0 {
		// Get last 100 items
		num := 100
		if len(user.TaskCancelByTasker) > num {
			array = user.TaskCancelByTasker[len(user.TaskCancelByTasker)-num:]
		} else {
			array = user.TaskCancelByTasker
		}
		for _, v := range array {
			d := globalLib.ParseDateFromTimeStamp(v.CancelDate, local.TimeZone)
			item := &TypeCancelDate{
				CancelDate: d,
			}
			result = append(result, item)
		}
		sort.SliceStable(result, func(i, j int) bool {
			return result[i].CancelDate.After(result[j].CancelDate)
		})
	}
	globalResponse.ResponseSuccess(w, result)
}
