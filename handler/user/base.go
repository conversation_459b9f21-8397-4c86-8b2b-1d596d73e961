/*
 * @File: base.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package user

import (
	"fmt"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func validateUserID(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}

func validateUsers(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if len(reqBody.UserIds) == 0 {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}

func validateDataReferralCode(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ReferralCode == "" {
		return &lib.ERROR_CODE_REQUIRED
	}
	return nil
}

func validateCountryCodeAndPhone(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.CountryCode == "" {
		return &lib.ERROR_COUNTRY_CODE_REQUIRED
	}
	if reqBody.CountryCode != globalConstant.COUNTRY_CODE_VN {
		return &lib.ERROR_COUNTRY_CODE_INCORRECT
	}
	if reqBody.Phone == "" {
		return &lib.ERROR_PHONE_REQUIRED
	}
	if !lib.IsPhoneValidByCountryCode(reqBody.Phone) {
		return &lib.ERROR_PHONE_INVALID
	}
	return nil
}

func validateUserIdAndTaskIds(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if len(reqBody.TaskerIds) == 0 {
		return &lib.ERROR_ARRAY_TASKER_IDS_REQUIRED
	}
	return nil
}

// =====================================
/*
 * @Description: Refactor Phone Number
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func refactorPhoneNumber(phone string, countryCode string) string {
	if (countryCode == globalConstant.COUNTRY_CODE_VN) && strings.HasPrefix(phone, "0") {
		phone = phone[1:]
	}
	return fmt.Sprintf("%s%s", countryCode, phone)
}
