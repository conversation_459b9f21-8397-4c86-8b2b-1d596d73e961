/*
 * @File: getLanguageAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Language Asker by UserId
 * @CreatedAt: 21/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetLanguageAsker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var lang string
	if reqBody.Text != "" { // get language from google
		result, err := globalLib.DetectLanguage(reqBody.Text, cfg.CloudTranslationAPI)
		if err != nil {
			message := fmt.Sprintf("Error DetectLanguage. api: /get-language-asker. err: %s", err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], "bTaskee System", message)
		}
		if result != nil && result["language"] != nil {
			lang = result["language"].(string)
		}
	} else { // get language from user
		user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"language": 1})
		if user != nil && user.Language != "" {
			lang = user.Language
		}
	}
	globalResponse.ResponseSuccess(w, lang)
}
