/*
 * @File: getBlackListTaskers.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Black List Taskers by UserId
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetBlackListTaskers(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var result []*modelUser.Users
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"blackList": 1})
	if user != nil && len(user.BlackList) > 0 {
		result, _ = modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": user.BlackList}}, bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1, "isPremiumTasker": 1})
	}
	globalResponse.ResponseSuccess(w, result)
}
