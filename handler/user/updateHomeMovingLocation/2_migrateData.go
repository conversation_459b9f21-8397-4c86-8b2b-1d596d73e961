/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package updateHomeMovingLocation

import (
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func migrateData(reqBody map[string]interface{}) (string, string) {
	//Migrate phone number
	if reqBody["phoneNumber"] != nil {
		reqBody["phoneNumber"] = globalLib.MigratePhoneNumber(cast.ToString(reqBody["phoneNumber"]), local.ISO_CODE)
	}

	// Set data update array element
	userId := cast.ToString(reqBody["userId"])
	locationId := cast.ToString(reqBody["locationId"])
	delete(reqBody, "userId")
	delete(reqBody, "locationId")
	return userId, locationId
}
