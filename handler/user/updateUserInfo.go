/*
 * @File: updateUserInfo.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update User Info by UserId
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func UpdateUserInfo(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataUpdateUserInfo(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Update data
	currentUser, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"name": 1, "type": 1, "phone": 1, "emails": 1})
	if currentUser == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	if currentUser != nil && currentUser.Name != reqBody.Name {
		globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], bson.M{"userId": reqBody.UserId}, bson.M{"$set": bson.M{"name": reqBody.Name}})
	}
	// Don't allow Tasker change name.
	if currentUser != nil && currentUser.Type == globalConstant.USER_TYPE_TASKER && currentUser.Name != reqBody.Name {
		reqBody.Name = currentUser.Name
	}
	// Update user information
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		updateUserInformation(reqBody, currentUser)
	}()
	//Update user action history
	wg.Add(1)
	go func() {
		defer wg.Done()
		updateUserActionHistory(reqBody, currentUser)
	}()
	wg.Wait()
	globalResponse.ResponseSuccess(w, nil)
}

func updateUserInformation(reqBody *model.ApiRequest, currentUser *modelUser.Users) {
	dataSet := bson.M{}
	if reqBody.Name != "" {
		dataSet["name"] = reqBody.Name
	}
	if reqBody.Email != "" && currentUser != nil && (currentUser.Emails == nil || len(currentUser.Emails) == 0 || (currentUser.Emails != nil && len(currentUser.Emails) > 0 && reqBody.Email != currentUser.Emails[0].Address)) {
		dataSet["emails"] = []*modelUser.UsersEmail{
			{
				Address:  reqBody.Email,
				Verified: false,
			},
		}
	}
	if reqBody.Introduction != "" {
		dataSet["introduction"] = reqBody.Introduction
	}
	if reqBody.Phone != "" && reqBody.CountryCode != "" {
		dataSet["phone"] = reqBody.Phone
		dataSet["countryCode"] = reqBody.CountryCode
	}
	modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": dataSet})
}

func validateDataUpdateUserInfo(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Phone != "" && reqBody.CountryCode != "" {
		if reqBody.CountryCode != globalConstant.ISO_CODE_MAP_COUNTRY_CODE[local.ISO_CODE] {
			return &lib.ERROR_COUNTRY_CODE_INCORRECT
		}
		return nil
	}
	if reqBody.Name == "" {
		return &lib.ERROR_NAME_REQUIRED
	}
	if reqBody.Email != "" {
		// Only check when user update email.
		// Find the exist email with another user.
		isExistUser, _ := modelUser.IsExistByQuery(local.ISO_CODE, bson.M{"_id": bson.M{"$ne": reqBody.UserId}, "emails.address": reqBody.Email})
		if isExistUser {
			return &lib.ERROR_EMAIL_EXIST
		}
	}

	return nil
}

func updateUserActionHistory(reqBody *model.ApiRequest, currentUser *modelUser.Users) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_USER_ACTION_HISTORY[local.ISO_CODE],
		bson.M{"phone": currentUser.Phone},
		bson.M{"$set": bson.M{
			"name": reqBody.Name,
		}},
	)
}
