/*
 * @File: getAllMoney.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get All Money by ReferralCode
 * @CreatedAt: 30/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetAllMoney(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataGetAllMoney(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	dataTransaction := getDataTransaction(reqBody.ReferralCode)
	var dataUsers []map[string]interface{}
	if len(dataTransaction) > 0 {
		var userIds []string
		for _, v := range dataTransaction {
			userIds = append(userIds, v.UserId)
		}
		users, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1, "name": 1})
		for _, v := range dataTransaction {
			var user *modelUser.Users
			for _, u := range users {
				if u.XId == v.UserId {
					user = u
					break
				}
			}
			dataUsers = append(dataUsers, map[string]interface{}{
				"userId":   v.UserId,
				"amount":   v.Amount,
				"userName": user.Name,
				"date":     globalLib.ParseDateFromTimeStamp(v.Date, local.TimeZone),
			})
		}
	}
	globalResponse.ResponseSuccess(w, dataUsers)
}

func getDataTransaction(referralCode string) []*modelFATransaction.FinancialAccountTransaction {
	var vn_dataTransaction []*modelFATransaction.FinancialAccountTransaction
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
		bson.M{
			"source.name":  globalConstant.FA_TRANSACTION_SOURCE_NAME_REFERRAL,
			"source.value": referralCode,
		},
		bson.M{"amount": 1, "userId": 1, "date": 1},
		bson.M{"date": -1},
		&vn_dataTransaction,
	)
	return vn_dataTransaction
}

func validateDataGetAllMoney(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ReferralCode == "" {
		return &lib.ERROR_REFERRAL_CODE_REQUIRED
	}

	return nil
}
