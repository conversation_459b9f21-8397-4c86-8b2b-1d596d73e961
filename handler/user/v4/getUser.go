package v4

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcEventVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	communitySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communitySetting"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func GetUser(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	fields := bson.M{
		"taskNoteByServiceV3":    1,
		"locations":              1,
		"fAccountId":             1,
		"phone":                  1,
		"name":                   1,
		"avatar":                 1,
		"type":                   1,
		"language":               1,
		"countryCode":            1,
		"status":                 1,
		"referralCode":           1,
		"emails":                 1,
		"isoCode":                1,
		"taskDone":               1,
		"point":                  1,
		"lastPostedTask":         1,
		"rankInfo":               1,
		"voiceCallToken":         1,
		"voiceCallTokenV2":       1,
		"bPoint":                 1,
		"rankInfoByCountry":      1,
		"hospitalLocations":      1,
		"isPremiumTasker":        1,
		"numberOfLuckyDraws":     1,
		"cities":                 1,
		"gender":                 1,
		"vatInfo":                1,
		"friendCode":             1,
		"reviewStore":            1,
		"isForcedUpdatePassword": 1,
		"services.resume.loginTokens.hashedToken": 1,
		"homeMovingLocations":                     1,
		"isEco":                                   1,
		"updatePrivacyPolicyAt":                   1,
		"housekeepingLocations":                   1,
		"activatedServices":                       1,
	}
	// Get user
	user, err := modelUser.GetOneByQuery(
		local.ISO_CODE,
		bson.M{
			"_id":    reqBody.UserId,
			"status": bson.M{"$in": []string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_BLOCKED, globalConstant.USER_STATUS_DISABLED}},
		},
		fields,
	)
	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Any("error", err),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	if user.IsForcedUpdatePassword {
		local.Logger.Warn(lib.ERROR_FORCED_UPDATE_PASSWORD.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_FORCED_UPDATE_PASSWORD)
		return
	}
	mapUser := make(map[string]interface{})
	b, _ := json.Marshal(user)
	json.Unmarshal(b, &mapUser)
	delete(mapUser, "services")
	// Get location for IsoCode
	referralCode := user.ReferralCode
	var locations, hospitalLocations, homeMovingLocations, housekeepingLocations interface{}
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		var locsForIsoCode []*modelUser.UsersLocations
		if len(user.Locations) > 0 {
			for i := len(user.Locations) - 1; i >= 0; i-- {
				v := user.Locations[i]
				if v.IsoCode == user.IsoCode {
					locsForIsoCode = append(locsForIsoCode, v)
				}
			}
		}
		if len(locsForIsoCode) > 0 {
			for _, v := range locsForIsoCode {
				if v.HomeType == "" {
					v.HomeType = globalConstant.HOME_TYPE_HOME
				}
			}
			locations = locsForIsoCode
		} else {
			locations = []map[string]interface{}{}
		}
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// Get location for IsoCode
		var hospitalLocsForIsoCode []*modelUser.UsersHospitalLocations
		if len(user.HospitalLocations) > 0 {
			for i := len(user.HospitalLocations) - 1; i >= 0; i-- {
				v := user.HospitalLocations[i]
				if v.IsoCode == user.IsoCode {
					hospitalLocsForIsoCode = append(hospitalLocsForIsoCode, v)
				}
			}
		}
		if len(hospitalLocsForIsoCode) > 0 {
			hospitalLocations = hospitalLocsForIsoCode
		} else {
			hospitalLocations = []map[string]interface{}{}
		}
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// Get home moving location for IsoCode
		var homeMovingLocationsByIsoCode []*modelUser.UsersHomeMovingLocations
		if len(user.HomeMovingLocations) > 0 {
			for i := len(user.HomeMovingLocations) - 1; i >= 0; i-- {
				v := user.HomeMovingLocations[i]
				if v.IsoCode == user.IsoCode {
					homeMovingLocationsByIsoCode = append(homeMovingLocationsByIsoCode, v)
				}
			}
		}
		if len(homeMovingLocationsByIsoCode) > 0 {
			homeMovingLocations = homeMovingLocationsByIsoCode
		} else {
			homeMovingLocations = []map[string]interface{}{}
		}
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		// Get housekeeping location for IsoCode
		var housekeepingLocationsByIsoCode []*modelUser.UsersHousekeepingLocations
		if len(user.HousekeepingLocations) > 0 {
			for i := len(user.HousekeepingLocations) - 1; i >= 0; i-- {
				v := user.HousekeepingLocations[i]
				if v.IsoCode == user.IsoCode {
					housekeepingLocationsByIsoCode = append(housekeepingLocationsByIsoCode, v)
				}
			}
		}
		if len(housekeepingLocationsByIsoCode) > 0 {
			housekeepingLocations = housekeepingLocationsByIsoCode
		} else {
			housekeepingLocations = []map[string]interface{}{}
		}
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		if len(strings.TrimSpace(user.ReferralCode)) <= 6 {
			referralCode = migrageReferralCode(user.XId, user.Name, user.ReferralCode)
		}

		// Send message to check user login ACTION to add user game campaign spin
		err = callGRPCEventPublishUserGameCampaignActionLogin(user.XId)
		if err != nil {
			local.Logger.Warn(lib.ERROR_CAN_NOT_GET_GAME_CAMPAIGN.ErrorCode,
				zap.String("message", "cannot callGRPCEventPublishUserGameCampaignActionLogin"),
				zap.Any("askerId", user.XId),
				zap.Error(err),
			)
		}
	}()
	mapDataBusinessUser(user.XId, mapUser)

	wg.Add(1)
	var rankInfo *modelUser.UserRankInfo
	go func() {
		defer wg.Done()
		rankInfo = migrateUserRankInfo(user)
	}()

	wg.Add(1)
	var isTesterCommunity bool
	go func() {
		defer wg.Done()
		isTesterCommunity = getIsTesterCommunity(user)
	}()

	wg.Wait()
	mapUser["locations"] = locations
	mapUser["hospitalLocations"] = hospitalLocations
	mapUser["homeMovingLocations"] = homeMovingLocations
	mapUser["housekeepingLocations"] = housekeepingLocations
	mapUser["referralCode"] = referralCode
	mapUser["rankInfo"] = rankInfo
	mapUser["isTesterCommunity"] = isTesterCommunity

	// Send message to check user login ACTION to add user game campaign spin
	err = callGRPCEventPublishUserGameCampaignActionLogin(user.XId)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_GET_GAME_CAMPAIGN.ErrorCode,
			zap.String("message", "cannot callGRPCEventPublishUserGameCampaignActionLogin"),
			zap.Any("askerId", user.XId),
			zap.Error(err),
		)
	}
	globalResponse.ResponseSuccess(w, mapUser)
}

// Migrage ReferralCode for Asker
func migrageReferralCode(askerId, name, oldRefferalCode string) string {
	refferal := lib.CreateReferralCode(name)
	changeHistory := map[string]interface{}{
		"key": "MIGRAGE_REFFERAL_CODE",
		"content": map[string]interface{}{
			"oldValue": oldRefferalCode,
			"newValue": refferal,
		},
	}

	modelUser.UpdateOneById(
		local.ISO_CODE,
		askerId,
		bson.M{
			"$set":  bson.M{"referralCode": refferal},
			"$push": bson.M{"changeHistory": changeHistory},
		},
	)

	askers, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"friendCode": bson.M{"$regex": primitive.Regex{Pattern: fmt.Sprintf("^%s$", oldRefferalCode), Options: "i"}}}, bson.M{"_id": 1})
	askerIds := []string{}
	for _, v := range askers {
		askerIds = append(askerIds, v.XId)
	}

	modelUser.UpdateAllByQuery(local.ISO_CODE, bson.M{"_id": bson.M{"$in": askerIds}}, bson.M{"$set": bson.M{"friendCode": refferal}})
	message := fmt.Sprintf("Update referralCode asker: askerId: %v askerName: %v - from %s to %s\nList asker update friend code: %v", askerId, name, oldRefferalCode, refferal, strings.Join(askerIds, ", "))
	globalLib.PostToSlack(cfg.SlackToken, "migrage-refferal-code", "bTaskee System", message)
	return refferal
}

func callGRPCEventPublishUserGameCampaignActionLogin(askerId string) error {
	client, connect, err := grpcEventVN.ConnectGRPCEventVN(cfg.GRPC_Event_URL)
	if err != nil {
		return err
	}
	defer connect.Close()
	request := &eventMessage.EventCommonMessage{
		UserId: askerId,
	}
	_, err = client.PublishUserGameCampaignActionLogin(context.Background(), request)
	return err
}

func mapDataBusinessUser(userId string, mapUser map[string]interface{}) {
	businessId := userId
	isBusinessMember := false
	member, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{
		"userId": userId, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	}, bson.M{"_id": 1, "businessId": 1, "createdAt": 1})
	if member != nil {
		isBusinessMember = true
		if member["businessId"] != nil {
			businessId = member["businessId"].(string)
		}
		mapUser["member"] = member
	}
	mapUser["isBusiness"] = !isBusinessMember
	mapUser["isBusinessMember"] = isBusinessMember

	businessInfoFields := bson.M{
		"_id":             1,
		"status":          1,
		"bPay":            1,
		"businessLicense": 1,
		"name":            1,
		"email":           1,
		"taxCode":         1,
		"address":         1,
		"sector":          1,
		"businessSize":    1,
		"revokeSetting":   1,
		"createdAt":       1,
	}
	business, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], bson.M{
		"_id": businessId, "status": globalConstant.BUSINESS_STATUS_ACTIVE,
	}, businessInfoFields)
	if business == nil {
		return
	}
	business["numberOfMembers"], _ = globalDataAccess.CountByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"businessId": business["_id"], "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE})
	revokeStatus := globalConstant.BUSINESS_STATUS_INACTIVE
	revokeSetting := cast.ToStringMap(business["revokeSetting"])
	if v, errStatus := cast.ToStringE(revokeSetting["status"]); errStatus == nil {
		revokeStatus = v
	}
	isDisableRevoke := cast.ToBool(revokeSetting["isDisable"])
	if revokeStatus == globalConstant.BUSINESS_STATUS_ACTIVE || isDisableRevoke {
		business["isDisableRevoke"] = true
	}
	// init joinDate
	if isBusinessMember {
		business["joinedDate"] = member["createdAt"]
	} else {
		business["joinedDate"] = business["createdAt"]
	}
	delete(business, "revokeSetting")
	mapUser["business"] = business
}

func migrateUserRankInfo(user *modelUser.Users) *modelUser.UserRankInfo {
	if user == nil || user.Point == 0 || (user.RankInfo != nil && user.RankInfo.RankName != "") {
		return user.GetRankInfo()
	}

	// get setting by isoCode
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"rankSetting": 1}, &settings)
	if settings == nil || settings.RankSetting == nil {
		return &modelUser.UserRankInfo{
			Point: user.GetPoint(),
		}
	}

	var rankInfo *modelUser.UserRankInfo
	for _, rank := range settings.RankSetting {
		if user.Point < rank.Point {
			break
		}

		rankInfo = &modelUser.UserRankInfo{
			Point:    user.Point,
			RankName: rank.RankName,
			Text:     rank.Text,
		}
	}
	return rankInfo
}

func getIsTesterCommunity(user *modelUser.Users) (isTester bool) {
	if user.Phone == "" {
		return
	}

	var communitySetting *communitySetting.CommunitySetting
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_COMMUNITY_SETTING[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &communitySetting)
	if communitySetting == nil {
		return
	}
	for _, phone := range communitySetting.Tester {
		if phone == user.Phone {
			isTester = true
			return
		}
	}

	return
}
