/*
 * @File: clearWrongAccount.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Clear Wrong Account by Phone
 * @CreatedAt: 09/07/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func ClearWrongAccount(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	//Validate
	if reqBody.CountryCode == "" {
		reqBody.CountryCode = globalConstant.COUNTRY_CODE_VN
	}
	errCode = validateDataClearWrongAccount(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": reqBody.Phone, "countryCode": reqBody.CountryCode}, bson.M{"_id": 1, "status": 1, "fAccountId": 1, "type": 1})
	if user != nil && user.Status == globalConstant.USER_STATUS_INACTIVE {
		globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_ACTIVATION[local.ISO_CODE], bson.M{"userId": user.XId})
		if user.FAccountId != "" {
			globalDataAccess.DeleteOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user.FAccountId)
		}

		globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": user.XId})

		if user.Type == globalConstant.USER_TYPE_TASKER {
			globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
				bson.M{"taskerList": user.XId},
				bson.M{
					"$pull": bson.M{
						"taskerList": user.XId,
					},
				})
		}
		users.DeleteOneById(local.ISO_CODE, user.XId)
	}

	globalResponse.ResponseSuccess(w, nil)
}

func validateDataClearWrongAccount(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.Phone == "" {
		return &lib.ERROR_PHONE_REQUIRED
	}
	if !lib.IsPhoneValidByCountryCode(reqBody.Phone) {
		return &lib.ERROR_PHONE_INVALID
	}
	return nil
}
