/*
 * @File: updateUserCountry.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update User Country by UserId
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
// NOTE: Api này không sử dụng nữa
func UpdateUserCountry(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataUpdateUserCountry(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Kiểm tra tài khoản bPay đã có tại quốc gia chuyển qua chưa, nếu chưa có thì tạo mới.
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"fAccountId": 1})
	if user != nil && user.FAccountId != "" {
		isExistFinancialAccount, err := globalDataAccess.IsExistById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[reqBody.ISOCode], user.FAccountId)
		if !isExistFinancialAccount && err == nil {
			fData := map[string]interface{}{"_id": user.FAccountId, "userId": reqBody.UserId}
			switch reqBody.ISOCode {
			case globalConstant.ISO_CODE_TH:
				fData["TH_FMainAccount"] = 0
				fData["TH_Promotion"] = 0
			case globalConstant.ISO_CODE_INDO:
				fData["ID_FMainAccount"] = 0
				fData["ID_Promotion"] = 0
			default:
				fData["FMainAccount"] = 0
				fData["Promotion"] = 0
			}
			err = globalDataAccess.InsertOne(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[reqBody.ISOCode], fData)
			if err != nil {
				globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
				return
			}
		}
	}

	// Set data
	_, err := modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"isoCode": reqBody.ISOCode}})
	if err != nil {
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func validateDataUpdateUserCountry(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode == globalConstant.ISO_CODE_MY {
		return &lib.ERROR_COMING_SOON
	}
	return nil
}
