/*
 * @File: updateLocationAsker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package updateHousekeepingLocation

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validateData(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["userId"] == nil {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody["locationId"] == nil {
		return &lib.ERROR_LOCATION_ID_REQUIRED
	}
	if reqBody["lat"] == nil && reqBody["lng"] == nil && reqBody["country"] == nil && reqBody["city"] == nil && reqBody["district"] == nil && reqBody["address"] == nil &&
		reqBody["contact"] == nil && reqBody["phoneNumber"] == nil && reqBody["shortAddress"] == nil && reqBody["countryCode"] == nil &&
		reqBody["isoCode"] == nil && reqBody["description"] == nil {
		return &lib.ERROR_DATA_INVALID
	}

	return nil
}
