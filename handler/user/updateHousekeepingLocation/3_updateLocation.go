/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package updateHousekeepingLocation

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func updateLocation(userId, locationId string, reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	dataUpdate := make(map[string]interface{})
	for k, v := range reqBody {
		dataUpdate[fmt.Sprintf("housekeepingLocations.$.%s", k)] = v
	}
	_, err := modelUser.UpdateOneByQuery(local.ISO_CODE, bson.M{"_id": userId, "housekeepingLocations._id": locationId}, bson.M{"$set": dataUpdate})
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}

	return nil
}
