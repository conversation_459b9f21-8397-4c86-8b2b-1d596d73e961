/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package updateHousekeepingLocation

import (
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Update Location Asker by UserId
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func UpdateHousekeepingLocation(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	// Validate data
	errCode := validateData(reqBody)
	if errCode != nil {
		return errCode
	}
	//Migrate phone number
	userId, locationId := migrateData(reqBody)
	// Update Location Asker
	return updateLocation(userId, locationId, reqBody)
}
