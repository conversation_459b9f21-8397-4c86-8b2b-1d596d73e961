/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package updateHousekeepingLocation

import (
	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func migrateData(reqBody map[string]interface{}) (string, string) {
	//Migrate phone number
	isoCode := ""
	if reqBody["isoCode"] != nil {
		isoCode = cast.ToString(reqBody["isoCode"])
	} else {
		if reqBody["countryCode"] != nil {
			if cast.ToString(reqBody["countryCode"]) == globalConstant.COUNTRY_CODE_VN {
				isoCode = globalConstant.ISO_CODE_VN
			}
			if cast.ToString(reqBody["countryCode"]) == globalConstant.COUNTRY_CODE_TH {
				isoCode = globalConstant.ISO_CODE_TH
			}
		}
	}
	if reqBody["phoneNumber"] != nil {
		reqBody["phoneNumber"] = globalLib.MigratePhoneNumber(cast.ToString(reqBody["phoneNumber"]), isoCode)
	}

	// Set data update array element
	userId := cast.ToString(reqBody["userId"])
	locationId := cast.ToString(reqBody["locationId"])
	delete(reqBody, "userId")
	delete(reqBody, "locationId")
	return userId, locationId
}
