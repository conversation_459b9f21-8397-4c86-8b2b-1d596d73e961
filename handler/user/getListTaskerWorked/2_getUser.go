/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getListTaskerWorked

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUser(userId string) (*modelUser.Users, error) {
	user, err := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"_id": 1, "favouriteTasker": 1, "blackList": 1})
	return user, err
}
