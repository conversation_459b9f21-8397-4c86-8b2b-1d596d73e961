/*
 * @File: getFavoriteTasker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getListTaskerWorked

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getListTaskerWorked(asker *modelUser.Users) interface{} {
	var listTaskerWorked []string
	mapTaskerIncorrect := make(map[string]struct{})
	for _, taskerId := range append(asker.FavouriteTasker, asker.BlackList...) {
		mapTaskerIncorrect[taskerId] = struct{}{}
	}
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": asker.XId, "status": globalConstant.TASK_STATUS_DONE}, bson.M{"acceptedTasker.taskerId": 1}, 100, bson.M{"date": -1}, &tasks)
	for _, v := range tasks {
		for _, t := range v.AcceptedTasker {
			if _, ok := mapTaskerIncorrect[t.TaskerId]; !ok {
				mapTaskerIncorrect[t.TaskerId] = struct{}{}
				listTaskerWorked = append(listTaskerWorked, t.TaskerId)
			}
		}
	}
	var allTaskers []*modelUser.Users
	if len(listTaskerWorked) > 0 {
		allTaskers, _ = modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": listTaskerWorked}, "status": globalConstant.USER_STATUS_ACTIVE, "isoCode": local.ISO_CODE}, bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1, "isPremiumTasker": 1})
	}
	return allTaskers
}
