/*
 * @File: getListEmployeesFromTaskerID.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Employees From TaskerID (UserIds)
 * @CreatedAt: 21/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetListEmployeesFromTaskerID(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUsers(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	taskers := getListEmployees(reqBody.UserIds)
	globalResponse.ResponseSuccess(w, taskers)
}

func getListEmployees(userIds []string) []map[string]interface{} {
	taskers, _ := modelUser.GetAllByQueryMap(
		local.ISO_CODE,
		bson.M{"_id": bson.M{"$in": userIds}},
		bson.M{"_id": 1, "status": 1, "avatar": 1, "name": 1, "phone": 1, "avgRating": 1},
		&globalDataAccessV2.QueryOptions{Sort: bson.M{"avgRating": -1}},
	)
	var ratings []*modelRating.Rating
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskerId": bson.M{"$in": userIds}}, bson.M{"taskerId": 1}, &ratings)
	for _, u := range taskers {
		var number int
		for _, r := range ratings {
			if r.TaskerId == u["_id"].(string) {
				number++
			}
		}
		u["numberOfReviews"] = number
	}
	return taskers
}
