/*
 * @File: removeFavoriteTasker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Remove Favorite Tasker
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func RemoveFavoriteTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserIdAndTaskIds(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Set data
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$pullAll": bson.M{"favouriteTasker": reqBody.TaskerIds}})
	}()

	wg.Add(1)
	go func() {
		defer wg.Done()
		modelUser.UpdateAllByQuery(local.ISO_CODE, bson.M{"_id": bson.M{"$in": reqBody.TaskerIds}}, bson.M{"$pull": bson.M{"favouritedAsker": reqBody.UserId}})
	}()
	wg.Wait()
	globalResponse.ResponseSuccess(w, nil)
}
