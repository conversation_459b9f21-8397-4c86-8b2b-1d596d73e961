/*
 * @File: checkUserContactV2.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"fmt"
	"net/http"
	"regexp"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Check User Contact by username
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func CheckUserContactV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateCountryCodeAndPhone(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get data
	if reqBody.Email != "" {
		if !globalLib.IsEmailValid(reqBody.Email) {
			globalResponse.ResponseError(w, lib.ERROR_EMAIL_INVALID)
			return
		}
		existUser, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"emails.address": reqBody.Email}, bson.M{"phone": 1})
		if existUser != nil {
			// Have a exist email, throw error
			phoneHead := existUser.Phone[0 : len(existUser.Phone)-3]
			phoneHead = regexp.MustCompile(`.`).ReplaceAllString(phoneHead, "*")
			phoneTail := existUser.Phone[len(existUser.Phone)-3:]
			code := lib.ERROR_EMAIL_INVALID
			code.Message = fmt.Sprintf("%s%s", phoneHead, phoneTail)
			globalResponse.ResponseError(w, code)
			return
		}
	}
	// Check exists ID number
	if reqBody.ID != "" {
		existsIDNumber, _ := modelUser.IsExistByQuery(local.ISO_CODE, bson.M{"idNumber": reqBody.ID})
		if existsIDNumber {
			globalResponse.ResponseError(w, lib.ERROR_ID_NUMBER_EXISTS)
			return
		}
	}
	// Find user in database by user name (phone number).
	user, _ := modelUser.GetOneByQuery(
		local.ISO_CODE,
		bson.M{"username": refactorPhoneNumber(reqBody.Phone, reqBody.CountryCode)},
		bson.M{"status": 1, "type": 1, "services.facebook": 1, "services.google": 1},
	)
	globalResponse.ResponseSuccess(w, user)
}
