/*
 * @File: updateUserStatus.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update User Status by UserId
 * @CreatedAt: 07/12/2020
 * @Author: ngoctb
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func UpdateUserStatus(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	var reqBody map[string]interface{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Validate data
	errCode := validateDataUpdateUserStatus(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Check status if userType = tasker
	userStatus := []string{globalConstant.USER_STATUS_DISABLED, globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_BLOCKED, globalConstant.USER_STATUS_INACTIVE}
	if reqBody["type"] == globalConstant.USER_TYPE_ASKER && globalLib.FindStringInSlice(userStatus, reqBody["status"].(string)) == -1 {
		local.Logger.Warn(lib.ERROR_USER_STATUS_INCORRECT.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_STATUS_INCORRECT)
		return
	}

	// Check status if userType = tasker
	userStatus = append(userStatus, globalConstant.USER_STATUS_UNVERIFIED, globalConstant.USER_STATUS_IN_PROBATION)
	if reqBody["type"] == globalConstant.USER_TYPE_TASKER && globalLib.FindStringInSlice(userStatus, reqBody["status"].(string)) == -1 {
		local.Logger.Warn(lib.ERROR_USER_STATUS_INCORRECT.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_STATUS_INCORRECT)
		return
	}

	//Update user status
	_, err = modelUsers.UpdateOneById(local.ISO_CODE, reqBody["userId"].(string), bson.M{"$set": bson.M{"status": reqBody["status"]}})
	if err != nil {
		local.Logger.Warn(lib.ERROR_UPDATE_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}

	globalResponse.ResponseSuccess(w, nil)
}

func validateDataUpdateUserStatus(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["userId"] == nil || reqBody["userId"] == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody["type"] == nil || reqBody["type"] == "" {
		return &lib.ERROR_USER_TYPE_REQUIRED
	}
	if reqBody["type"] != globalConstant.USER_TYPE_ASKER && reqBody["type"] != globalConstant.USER_TYPE_TASKER {
		return &lib.ERROR_USER_TYPE_INCORRECT
	}

	return nil
}
