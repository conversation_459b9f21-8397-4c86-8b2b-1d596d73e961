/*
 * @File: migrationUserBeforeLogin.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Migration User Before Login
 * @CreatedAt: 30/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func MigrationUserBeforeLogin(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataUserBeforeLogin(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	//Update data User
	errCode = updateDataUserForMigrationUserBeforeLogin(reqBody, globalConstant.COUNTRY_CODE_VN)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

func validateDataUserBeforeLogin(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.Phone == "" {
		return &lib.ERROR_PHONE_REQUIRED
	}
	return nil
}

func updateDataUserForMigrationUserBeforeLogin(reqBody *model.ApiRequest, countryCode string) *globalResponse.ResponseErrorCode {
	user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": reqBody.Phone, "countryCode": countryCode}, bson.M{"_id": 1, "phone": 1, "countryCode": 1, "username": 1, "isoCode": 1})
	if user == nil {
		return &lib.ERROR_USER_NOT_FOUND
	}
	if user.Username == refactorPhoneNumber(reqBody.Phone, countryCode) && user.IsoCode != "" {
		return &lib.ERROR_USER_MIGRATED
	}
	newUserObj := bson.M{
		"username": refactorPhoneNumber(reqBody.Phone, countryCode),
	}
	if user.CountryCode != "" && user.IsoCode == "" {
		newUserObj["isoCode"] = globalLib.GetIsoCodeByCountry(countryCode)
	}
	_, err := modelUsers.UpdateOneById(local.ISO_CODE, user.XId, bson.M{"$set": newUserObj})
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}
	return nil
}
