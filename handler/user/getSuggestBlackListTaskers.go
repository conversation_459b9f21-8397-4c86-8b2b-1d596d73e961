/*
 * @File: getSuggestBlackListTaskers.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Suggest Black List Taskers by UserId
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetSuggestBlackListTaskers(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var ids []string
	// Get favourite taskers
	asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"favouriteTasker": 1, "blackList": 1})
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		ids = asker.FavouriteTasker
	}
	// Get the newest 10 done tasks of this asker
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": reqBody.UserId, "status": globalConstant.TASK_STATUS_DONE}, bson.M{"acceptedTasker": 1}, 10, bson.M{"createdAt": -1}, &tasks)
	if len(tasks) > 0 {
		for _, t := range tasks {
			ids = append(ids, t.AcceptedTasker[0].TaskerId)
		}
	}
	ids = globalLib.UniqString(ids)
	// Exclude the black list taskers of this asker
	if asker != nil && len(asker.BlackList) > 0 {
		var notBlackListIds []string
		for _, v := range ids {
			if globalLib.FindStringInSlice(asker.BlackList, v) < 0 {
				notBlackListIds = append(notBlackListIds, v)
			}
		}
		ids = notBlackListIds
	}
	var result []*modelUser.Users
	if len(ids) > 0 {
		result, _ = modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": ids}, "isoCode": local.ISO_CODE}, bson.M{"name": 1, "avatar": 1, "avgRating": 1, "isPremiumTasker": 1, "taskDone": 1})
	}
	globalResponse.ResponseSuccess(w, result)
}
