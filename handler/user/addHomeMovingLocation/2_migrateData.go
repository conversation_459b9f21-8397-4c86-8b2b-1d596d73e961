/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package addHomeMovingLocation

import (
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func migrateData(reqBody map[string]interface{}) string {
	//Migrate phone number
	reqBody["phoneNumber"] = globalLib.MigratePhoneNumber(reqBody["phoneNumber"].(string), reqBody["isoCode"].(string))
	// Set data
	userId := reqBody["userId"].(string)
	reqBody["_id"] = globalLib.GenerateObjectId()
	delete(reqBody, "userId")
	return userId
}
