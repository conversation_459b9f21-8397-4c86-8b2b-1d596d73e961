/*
 * @File: notifyIncommingCall.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Send Notify Incomming Call by UserId
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func NotifyIncommingCall(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataNotifyIncommingCall(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	//Send Notification
	sendNotificationForNotifyIncommingCall(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func validateDataNotifyIncommingCall(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Data == nil || reqBody.Data["fromAlias"] == nil {
		return &lib.ERROR_DATA_INVALID
	}
	return nil
}

func sendNotificationForNotifyIncommingCall(reqBody *model.ApiRequest) {
	// Get data
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "INCOMMING_CALL_TITLE"),
		En: localization.T("en", "INCOMMING_CALL_TITLE"),
		Ko: localization.T("ko", "INCOMMING_CALL_TITLE"),
		Th: localization.T("th", "INCOMMING_CALL_TITLE"),
	}
	fromAlias := reqBody.Data["fromAlias"].(string)
	body := &modelService.ServiceText{
		Vi: localization.T("vi", "INCOMMING_CALL_CONTENT", fromAlias),
		En: localization.T("en", "INCOMMING_CALL_CONTENT", fromAlias),
		Ko: localization.T("ko", "INCOMMING_CALL_CONTENT", fromAlias),
		Th: localization.T("th", "INCOMMING_CALL_CONTENT", fromAlias),
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"language": 1})

	lang := globalConstant.LANG_EN
	if user != nil && user.Language != "" {
		lang = user.Language
	}

	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: reqBody.UserId, Language: lang},
	}
	lib.SendNotification([]interface{}{}, userIds, title, body, nil, "")
}
