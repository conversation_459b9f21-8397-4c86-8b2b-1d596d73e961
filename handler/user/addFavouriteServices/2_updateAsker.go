package addFavouriteServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func updateFavourite(isoCode, askerId string, listService []string) *globalResponse.ResponseErrorCode {
	if IsExist, _ := modelUser.IsExistById(local.ISO_CODE, askerId); !IsExist {
		return &lib.ERROR_USER_NOT_FOUND
	}
	queryUpdate := bson.M{"$set": bson.M{"favouriteServiceByCountry.VN": listService}}
	_, err := modelUser.UpdateOneById(local.ISO_CODE, askerId, queryUpdate)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}
	return nil
}
