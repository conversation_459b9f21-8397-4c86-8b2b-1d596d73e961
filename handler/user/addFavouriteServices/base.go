package addFavouriteServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func AddFavouriteServices(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode
	}
	errCode = updateFavourite(reqBody.ISOCode, reqBody.UserId, reqBody.FavouriteServiceIds)
	if errCode != nil {
		return errCode
	}

	return nil
}
