package addFavouriteServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}

	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}

	if len(reqBody.FavouriteServiceIds) == 0 {
		return &lib.ERROR_LIST_SERVICE_REQUIRED
	}

	if len(reqBody.FavouriteServiceIds) > lib.MAX_LEN_FAVOURITE_SERVICES {
		return &lib.ERROR_EXCEEDED_QUANTITY_OF_SERVICES
	}

	return nil
}
