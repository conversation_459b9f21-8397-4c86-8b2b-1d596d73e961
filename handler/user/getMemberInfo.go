package user

import (
	"fmt"
	"math"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func GetMemberInfo(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. Validate
	errCode := validateUserID(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get user
	fields := bson.M{
		"_id":               1,
		"language":          1,
		"point":             1,
		"rankInfo":          1,
		"bPoint":            1,
		"rankInfoByCountry": 1,
		"resetRankHistory":  1,
	}
	user, err := modelUser.GetOneByQuery(
		local.ISO_CODE,
		bson.M{
			"_id": reqBody.UserId,
			"status": bson.M{
				"$in": []string{
					globalConstant.USER_STATUS_ACTIVE,
					globalConstant.USER_STATUS_BLOCKED,
					globalConstant.USER_STATUS_DISABLED,
				},
			},
		},
		fields,
	)
	if user == nil || err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, nil
	}

	// 3. Get setting
	settings := getSetting()

	result := make(map[string]interface{})
	// 4. Get user member info
	// 4.1. Get current rank, point info
	userBpoint, userRankInfo := getUserBpointAndRankInfo(user, settings)
	result["currentPoint"] = userBpoint
	result["currentRankInfo"] = userRankInfo

	// 4.2. Get bPoint note
	nextRankInfo, bPointNote := getBPointNote(userRankInfo, settings)
	if nextRankInfo != nil {
		result["nextRankInfo"] = nextRankInfo
	}
	if bPointNote != nil {
		result["bPointNote"] = bPointNote
	}

	// 4.3. Get bPoint reset note
	bPointResetNote := getBPointResetNote(user, userBpoint, userRankInfo, settings)
	if bPointResetNote != nil {
		result["bPointResetNote"] = bPointResetNote
	}

	// Response
	return result, nil, nil
}

func getBPointNote(userRankInfo *modelUser.UserRankInfo, settings *modelSettings.Settings) (*modelSettings.SettingsRankSetting, *modelService.ServiceText) {
	// Calculate user rank info version 2 and notification text
	userRankPoint := userRankInfo.Point
	userRankIndex := 0
	rankByCurrentPointIndex := 0
	if settings != nil {
		for i, rank := range settings.RankSetting {
			// Get rank by current point
			if rank.Point <= userRankPoint {
				rankByCurrentPointIndex = i
			}

			// Get order of rank info in list rank
			if userRankInfo != nil && userRankInfo.RankName == rank.RankName {
				userRankIndex = i
			}
		}
	}

	// Result
	// 1. Trường hợp 3
	if settings != nil && rankByCurrentPointIndex == len(settings.RankSetting)-1 {
		return nil, localization.GetLocalizeObject("MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_GET_REWARD")
	}

	// 2. Trường hợp 2
	// NOTE: needPoint := settings.RankSetting[rankByCurrentPointIndex+1].Point - userRankPoint
	if settings != nil && settings.RankSetting != nil && rankByCurrentPointIndex >= userRankIndex {
		rankInfo := settings.RankSetting[rankByCurrentPointIndex+1]
		needPoint := globalLib.FormatMoney(settings.RankSetting[rankByCurrentPointIndex+1].Point - userRankPoint)
		bPointNote := &modelService.ServiceText{
			Vi: localization.T(globalConstant.LANG_VI, "MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_LEVEL_UP", needPoint, globalLib.LocalizeServiceName(globalConstant.LANG_VI, rankInfo.Text)),
			En: localization.T(globalConstant.LANG_EN, "MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_LEVEL_UP", needPoint, globalLib.LocalizeServiceName(globalConstant.LANG_EN, rankInfo.Text)),
			Ko: localization.T(globalConstant.LANG_KO, "MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_LEVEL_UP", globalLib.LocalizeServiceName(globalConstant.LANG_KO, rankInfo.Text), needPoint),
			Th: localization.T(globalConstant.LANG_TH, "MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_LEVEL_UP", needPoint, globalLib.LocalizeServiceName(globalConstant.LANG_TH, rankInfo.Text)),
			Id: localization.T(globalConstant.LANG_ID, "MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_LEVEL_UP", needPoint, globalLib.LocalizeServiceName(globalConstant.LANG_ID, rankInfo.Text)),
		}
		return settings.RankSetting[rankByCurrentPointIndex+1], bPointNote
	}

	// 3. Trường hợp 1
	// if rankByCurrentPointIndex < userRankIndex
	// NOTE: needPoint := settings.RankSetting[userRankIndex].Point - userRankPoint
	if settings != nil && settings.RankSetting != nil {
		needPoint := globalLib.FormatMoney(settings.RankSetting[userRankIndex].Point - userRankPoint)
		bPointNote := localization.GetLocalizeObject("MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_KEEP_LEVEL", needPoint)
		return settings.RankSetting[userRankIndex], bPointNote
	}

	// Other cases (not have setting) -> Return nil
	return nil, localization.GetLocalizeObject("MEMBER_INFO_NOTE_ACCUMULATE_POINT_TO_GET_REWARD")
}

func getBPointResetNote(user *modelUser.Users, userBpoint float64, userRankInfo *modelUser.UserRankInfo, settings *modelSettings.Settings) *modelService.ServiceText {
	// Calculate bPoint will be reset
	var bPointResetNote *modelService.ServiceText
	bPointWillBeReseted := calculatePointWillBeResetedInNextPhase(user, userBpoint, userRankInfo, settings)
	if bPointWillBeReseted > 0 && settings != nil && settings.GiftSetting != nil && settings.GiftSetting.RankingCycle != nil && settings.GiftSetting.RankingCycle.ExpectedDate != nil {
		date := globalLib.ParseDateFromTimeStamp(settings.GiftSetting.RankingCycle.ExpectedDate, local.TimeZone)
		bPointResetNote = localization.GetLocalizeObject("MEMBER_INFO_NOTE_POINT_WILL_BE_RESETED_NEXT_PHASE", globalLib.FormatMoney(bPointWillBeReseted), date.Format("02/01/2006"))
		bPointResetNote.Ko = localization.T(globalConstant.LANG_KO, "MEMBER_INFO_NOTE_POINT_WILL_BE_RESETED_NEXT_PHASE", globalLib.FormatMoney(bPointWillBeReseted), date.Year(), date.Month(), date.Day())
	}
	return bPointResetNote
}

func getUserBpointAndRankInfo(user *modelUser.Users, settings *modelSettings.Settings) (float64, *modelUser.UserRankInfo) {
	// Get user bPoint info VN
	userRankInfo := user.RankInfo
	if userRankInfo == nil {
		userRankInfo = &modelUser.UserRankInfo{
			Point: 0,
		}
		if len(settings.RankSetting) > 0 {
			userRankInfo.Text = settings.RankSetting[0].Text
			userRankInfo.RankName = settings.RankSetting[0].RankName
		}
	}
	return user.Point, userRankInfo
}

func getSetting() *modelSettings.Settings {
	var setting *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE],
		bson.M{},
		bson.M{
			"rankSetting":                           1,
			"giftSetting.phaseResetPoint":           1,
			"giftSetting.rankingCycle.startDate":    1,
			"giftSetting.rankingCycle.expectedDate": 1,
		},
		&setting,
	)
	return setting
}

// Cách tính bPoint sẽ bị reset next phase
/*
 * Case 1: User tạo trong vòng 6 tháng, chưa bị reset lần nào => Không bị mất điểm nào sau khi reset
 * Còn lại:
 */
func calculatePointWillBeResetedInNextPhase(user *modelUser.Users, point float64, rankInfo *modelUser.UserRankInfo, settings *modelSettings.Settings) float64 {
	// Check user not be reset before
	if len(user.ResetRankHistory) == 0 {
		return 0
	}
	// Check setting to calculate bPoint
	if settings == nil || settings.GiftSetting == nil || settings.GiftSetting.RankingCycle == nil || settings.GiftSetting.PhaseResetPoint == 0 {
		return 0
	}
	// Get last phase reset transaction
	lastPhasePoint := getLastPhasePoint(user, settings.GiftSetting.PhaseResetPoint)
	if lastPhasePoint == 0 {
		return 0
	}
	// Điểm user đã sử dụng trong kỳ này
	usedPoint := (rankInfo.Point + lastPhasePoint) - point
	// Nếu điểm đã sử dụng lớn hơn điểm lưu trữ từ kỳ trước của user thì không bị trừ bPoint ở phase tiếp theo
	// Trả về số điểm sẽ bị trừ ở phase tiếp theo
	return math.Max(0, lastPhasePoint-usedPoint)
}

func getLastPhasePoint(user *modelUser.Users, lastPhase int32) float64 {
	// Điểm lưu trữ (không bị reset) từ kỳ trước của user
	var pTrans *modelPointTransaction.PointTransaction
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE],
		bson.M{
			"userId":      user.XId,
			"source.name": fmt.Sprintf("RESET_BPOINT_PHASE_%d", lastPhase),
		},
		bson.M{"newPoint": 1},
		&pTrans,
	)
	if err != nil || pTrans == nil {
		return 0
	}
	return pTrans.NewPoint
}
