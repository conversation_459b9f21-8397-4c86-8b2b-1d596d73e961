/*
 * @File: deleteHousekeepingLocation.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package deleteHousekeepingLocation

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Update Location Asker by UserId
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func DeleteHousekeepingLocation(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// Validate data
	errCode := validateData(reqBody)
	if errCode != nil {
		return errCode
	}
	// remove Location Asker
	return removeLocation(reqBody)
}
