/*
 * @File: updateLocationAsker.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package deleteHousekeepingLocation

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func removeLocation(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	_, err := modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$pull": bson.M{"housekeepingLocations": bson.M{"_id": reqBody.LocationId}}})
	if err != nil {
		return &lib.ERROR_REMOVE_FAILED
	}
	return nil
}
