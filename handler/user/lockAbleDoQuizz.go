/*
 * @File: lockAbleDoQuizz.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"fmt"
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update trainingPermission.ableDoQuizz by User<PERSON>d
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func LockAbleDoQuizz(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateUserID(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	isExistUser, _ := modelUsers.IsExistById(local.ISO_CODE, reqBody.UserId)
	if !isExistUser {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}

	_, err := modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, bson.M{"$set": bson.M{"trainingPermission.ableDoQuizz": false, "trainingPermission.resetData": false}})
	if err != nil {
		errCode := lib.ERROR_UPDATE_FAILED
		errCode.Message = err.Error()
		globalResponse.ResponseError(w, errCode)
		message := fmt.Sprintf("Thời gian: %s.\nFunction: 'lockAbleDoQuizz'.\nMessage: %s", globalLib.GetCurrentTime(local.TimeZone).Format(time.RFC3339), errCode.Message)
		globalLib.PostToSlack(cfg.SlackToken, "tasker-error-function", "bTaskee System", message)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}
