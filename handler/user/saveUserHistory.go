/*
 * @File: saveUserHistory.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package user

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUserLocationHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userLocationHistory"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Save User History by UserId
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func SaveUserHistory(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateDataSaveUserHistory(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	var userHistory *modelUserLocationHistory.UserLocationHistory
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE], bson.M{"userId": reqBody.UserId}, bson.M{"_id": 1, "updatedAt": 1}, &userHistory)
	newTime := reqBody.Time.In(local.TimeZone)
	if userHistory != nil {
		// Exist a record, just update it
		historyLimitNumber := 1000
		beforeMinutes := newTime.Add(-30 * time.Minute)
		updatedAt := globalLib.ParseDateFromTimeStamp(userHistory.UpdatedAt, local.TimeZone)
		if updatedAt.Before(beforeMinutes) {
			data := []*modelUserLocationHistory.UserLocationHistoryHistory{
				{
					Time: globalLib.ParseTimestampFromDate(newTime),
					Lat:  reqBody.Lat,
					Lng:  reqBody.Lng,
				},
			}
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE],
				userHistory.XId,
				bson.M{
					"$push": bson.M{
						"history": bson.M{"$each": data, "$slice": -historyLimitNumber}, // Limit 1000 latest items in history array.
					},
					"$set": bson.M{"updatedAt": globalLib.GetCurrentTime(local.TimeZone)},
				},
			)
		}
	} else {
		// Insert a new record of location history
		user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "phone": 1, "name": 1, "type": 1, "status": 1, "username": 1})
		if user != nil {
			insertUserLocalHistory(user, reqBody, &newTime)
		}
	}
	globalResponse.ResponseSuccess(w, nil)
}

func validateDataSaveUserHistory(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Time == nil {
		return &lib.ERROR_TIME_REQUIRED
	}
	if reqBody.Lat == 0 {
		return &lib.ERROR_LAT_LNG_REQUIRED
	}
	if reqBody.Lng == 0 {
		return &lib.ERROR_LAT_LNG_REQUIRED
	}
	return nil
}

func insertUserLocalHistory(user *modelUser.Users, reqBody *model.ApiRequest, newTime *time.Time) {
	data := map[string]interface{}{
		"_id":        globalLib.GenerateObjectId(),
		"userId":     user.XId,
		"phone":      user.Phone,
		"name":       user.Name,
		"type":       user.Type,
		"userStatus": user.Status,
		"history": []map[string]interface{}{
			{
				"time": newTime,
				"lat":  reqBody.Lat,
				"lng":  reqBody.Lng,
			},
		},
		"createdAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	globalDataAccess.InsertOne(globalCollection.COLLECTION_USER_LOCATION_HISTORY[local.ISO_CODE], data)
}
