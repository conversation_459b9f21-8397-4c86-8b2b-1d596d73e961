/*
 * @File: getHoldingAmount.go
 * @Description: <PERSON><PERSON>, parse api params and route to handler function
 * @CreatedAt: 01/12/2021
 * @Author: vinhnt
 */
package tasker

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
* @Description: Get Holding Amount Tasker
* @CreatedAt: 01/12/2021
* @UpdatedBy: vinhnt
 */
func GetHoldingAmount(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetHoldingAmount(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	var holdingAmount float64

	// Get list task from db by taskIds
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"acceptedTasker.taskerId": reqBody.TaskerId,
			"status":                  bson.M{"$in": []string{globalConstant.TASK_STATUS_CONFIRMED, globalConstant.TASK_STATUS_WAITING}},
		},
		bson.M{"_id": 1, "serviceId": 1, "subscriptionId": 1, "cost": 1, "acceptedTasker.isLeader": 1, "acceptedTasker.taskerId": 1, "detailDeepCleaning.costPerLeaderTasker": 1, "detailDeepCleaning.costPerTasker": 1, "detailDeepCleaning.newCostPerLeaderTasker": 1, "detailDeepCleaning.newCostPerTasker": 1, "costDetail.newFinalCost": 1, "costDetail.depositMoney": 1, "isoCode": 1, "serviceText": 1, "tip": 1, "requirements.cost": 1, "createdAt": 1, "taskPlace": 1, "costDetail.promotionBy": 1, "costDetail.finalCost": 1, "newCostDetail.finalCost": 1, "newCostDetail.cost": 1},
		&tasks,
	)
	for _, task := range tasks {
		var isLeader bool
		if globalLib.IsDeepCleaningService(task.ServiceText.En) {
			for _, tasker := range task.AcceptedTasker {
				if tasker.TaskerId == reqBody.TaskerId {
					isLeader = tasker.IsLeader
					break
				}
			}
		}
		//Get service
		var service *modelService.Service
		globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], task.ServiceId, bson.M{"city": 1, "serviceFeeLeaderTasker": 1}, &service)
		taskCost, _, taskNewCost, _ := globalLib.GetTaskCost(task, reqBody.TaskerId)
		if taskNewCost != 0 {
			taskCost = taskNewCost
		}
		taskFee := getTaskFee(taskCost, task, service, isLeader)

		holdingAmount += taskFee
	}
	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"holdingAmount": holdingAmount,
	})
}

func validateGetHoldingAmount(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskerId == "" {
		return &lib.ERROR_TASKER_ID_REQUIRED
	}
	return nil
}
