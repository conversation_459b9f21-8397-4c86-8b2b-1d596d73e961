package getTaskerServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskerServices(taskerId, appVersion string) ([]string, *globalResponse.ResponseErrorCode, error) {
	// 1. validate tasker
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, taskerId, bson.M{"status": 1})
	if tasker == nil {
		return nil, &lib.ERROR_TASKER_NOT_FOUND, nil
	}
	if tasker.GetStatus() == globalConstant.USER_STATUS_BLOCKED {
		return nil, &lib.ERROR_TASKER_IS_BLOCKED, nil
	}
	// 2. get tasker services
	query := bson.M{"taskerList": taskerId}
	fields := bson.M{"serviceId": 1}
	serviceChannels, err := handler.GetServiceChannels(query, fields)
	if err != nil {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if len(serviceChannels) == 0 {
		return nil, nil, nil
	}

	var serviceIds []string
	for _, v := range serviceChannels {
		serviceIds = append(serviceIds, v.ServiceId)
	}

	supportedServices := []string{
		globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
	}

	for service, version := range lib.MIN_REQUIRE_VERSION_FOR_GET_TASKER_SERVICES {
		if globalLib.CompareVersion(appVersion, version) >= 0 {
			supportedServices = append(supportedServices, service)
		}
	}
	query = bson.M{
		"_id":            bson.M{"$in": serviceIds},
		"name":           bson.M{"$in": supportedServices},
		"isSubscription": bson.M{"$ne": true},
	}

	fields = bson.M{"_id": 1}
	services, err := handler.GetServices(query, fields)
	if err != nil {
		return nil, &lib.SYSTEM_ERROR, err
	}

	serviceIds = []string{}
	for _, v := range services {
		serviceIds = append(serviceIds, v.XId)
	}

	// 3. return
	return serviceIds, nil, nil
}
