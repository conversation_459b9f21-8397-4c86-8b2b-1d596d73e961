package getTaskerServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetTaskerServices(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 1.1. check taskers
	errCode, err := checkTaskerValid(reqBody.UserId, reqBody.TaskerId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 2. get data
	serviceIds, errCode, err := getTaskerServices(reqBody.TaskerId, reqBody.AppVersion)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. return
	return map[string]interface{}{
		"serviceIds": serviceIds,
	}, nil, nil
}
