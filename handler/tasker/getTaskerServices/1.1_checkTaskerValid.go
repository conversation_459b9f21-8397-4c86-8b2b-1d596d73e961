package getTaskerServices

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func checkTaskerValid(userId, taskerId string) (*globalResponse.ResponseErrorCode, error) {
	tasker, err := users.GetOneById(local.ISO_CODE, taskerId, bson.M{"blacklistAskers": 1})
	if err != nil && err != mongo.ErrNoDocuments {
		return &lib.SYSTEM_ERROR, err
	}
	if tasker == nil {
		return &lib.ERROR_TASKER_NOT_FOUND, nil
	}
	if globalLib.FindStringInSlice(tasker.GetBlacklistAskers(), userId) >= 0 {
		return &lib.ERROR_ASKER_IN_TASKER_BLACKLIST, nil
	}
	return nil, nil
}
