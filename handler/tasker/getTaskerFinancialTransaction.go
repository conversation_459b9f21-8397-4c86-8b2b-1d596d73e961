package tasker

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Financial Transaction by TaskerId
 * @CreatedAt: 06/01/2022
 * @Author: vinhnt
 */
func GetTaskerFinancialTransaction(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetTaskerFinancialTransaction(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}
	isExistTasker, _ := modelUsers.IsExistByQuery(local.ISO_CODE, bson.M{"_id": reqBody.TaskerId, "isoCode": reqBody.ISOCode, "type": globalConstant.USER_TYPE_TASKER})
	if !isExistTasker {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	query := bson.M{
		"userId": reqBody.TaskerId,
		"amount": bson.M{"$gt": 0},
	}
	var data []*modelFATransaction.FinancialAccountTransaction
	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE],
		query,
		bson.M{"type": 1, "amount": 1, "date": 1, "source.name": 1, "accountType": 1},
		int64(page),
		int64(limit),
		bson.M{"date": -1},
		&data,
	)

	globalResponse.ResponseSuccess(w, data)
}

func validateGetTaskerFinancialTransaction(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskerId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	return nil
}
