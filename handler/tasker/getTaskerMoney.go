/*
 * @File: getHoldingAmount.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 01/12/2021
 * @Author: vinhnt
 */
package tasker

import (
	"math"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Tasker Money
 * @CreatedAt: 01/12/2021
 * @UpdatedBy: vinhnt
 */
func GetTaskerMoney(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetTaskerMoney(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	var holdingAmount, taskFee float64
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.TaskerId, bson.M{"fAccountId": 1})
	if tasker == nil {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	// Get financial account
	main, promotion := globalLib.GetFAccountByIsoCode(tasker.FAccountId, local.ISO_CODE)
	// Get list task from db by taskIds
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"acceptedTasker.taskerId": reqBody.TaskerId,
			"status":                  bson.M{"$in": []string{globalConstant.TASK_STATUS_CONFIRMED, globalConstant.TASK_STATUS_WAITING}},
		},
		bson.M{"_id": 1, "serviceId": 1, "subscriptionId": 1, "cost": 1, "acceptedTasker.isLeader": 1, "acceptedTasker.taskerId": 1, "detailDeepCleaning.costPerLeaderTasker": 1, "detailDeepCleaning.costPerTasker": 1, "detailDeepCleaning.newCostPerLeaderTasker": 1, "detailDeepCleaning.newCostPerTasker": 1, "costDetail.newFinalCost": 1, "costDetail.depositMoney": 1, "isoCode": 1, "serviceText": 1, "tip": 1, "requirements.cost": 1, "createdAt": 1, "taskPlace": 1, "costDetail.promotionBy": 1, "costDetail.finalCost": 1, "newCostDetail.finalCost": 1, "newCostDetail.cost": 1},
		&tasks,
	)
	for _, task := range tasks {
		var isLeader bool
		if globalLib.IsDeepCleaningService(task.ServiceText.En) {
			for _, tasker := range task.AcceptedTasker {
				if tasker.TaskerId == reqBody.TaskerId {
					isLeader = tasker.IsLeader
					break
				}
			}
		}
		//Get service
		var service *modelService.Service
		globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], task.ServiceId, bson.M{"city": 1, "serviceFeeLeaderTasker": 1}, &service)
		taskCost, _, newTaskCost, _ := globalLib.GetTaskCost(task, reqBody.TaskerId)
		if newTaskCost != 0 {
			taskCost = newTaskCost
		}
		taskFee = getTaskFee(taskCost, task, service, isLeader)

		holdingAmount += taskFee
	}
	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"FMainAccount":     main,
		"PromotionAccount": promotion,
		"holdingAmount":    holdingAmount,
		"waitingPayout":    0,
		"balance":          (main + promotion) - holdingAmount,
	})
}

func validateGetTaskerMoney(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskerId == "" {
		return &lib.ERROR_TASKER_ID_REQUIRED
	}
	return nil
}

func getTaskFee(taskCost float64, task *modelTask.Task, service *modelService.Service, isLeader bool) float64 {
	taskRate := getTaskRate(task, service, isLeader)
	excludeCost := getExcludeCost(task)

	var round float64 = 100
	if globalLib.IsGroceryAssistantService(task.ServiceText.En) && task.CostDetail != nil && task.CostDetail.DepositMoney > 0 {
		taskCost -= task.CostDetail.DepositMoney
	}
	return math.Ceil(((taskCost-excludeCost)*taskRate)/round) * round
}

func getTaskRate(task *modelTask.Task, service *modelService.Service, isLeader bool) float64 {
	taskRate := globalConstant.TASK_RATE
	if task.TaskPlace != nil && task.TaskPlace.City != "" && service.City != nil && len(service.City) > 0 {
		for _, v := range service.City {
			if v.Name == task.TaskPlace.City && v.TaskRate > 0 {
				taskRate = v.TaskRate
				break
			}
		}
	}

	if isLeader {
		if service.ServiceFeeLeaderTasker > 0 {
			taskRate = service.ServiceFeeLeaderTasker
		}
		if taskRate > 1 { // In case value of service.ServiceFeeLeaderTasker > 1 (exp: 20%, 30%)
			taskRate = taskRate / 100 // Make sure that fee is always < 1
		}
	}
	return taskRate
}

func getExcludeCost(task *modelTask.Task) float64 {
	var excludeCost float64
	if task.Tip > 0 {
		excludeCost = task.Tip
	}
	if task.Requirements != nil && len(task.Requirements) > 0 {
		for _, v := range task.Requirements {
			excludeCost += v.Cost
		}
	}
	return excludeCost
}
