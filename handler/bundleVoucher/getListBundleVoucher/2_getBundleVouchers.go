package getListBundleVoucher

import (
	handlerBundleVoucher "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bundleVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getBundleVouchers(asker *modelUsers.Users, isTester bool, reqBody *model.ApiRequest) []*modelBundleVoucher.BundleVoucher {
	query := bson.M{
		"status":    globalConstant.BUNDLE_VOUCHER_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
		"endDate": bson.M{
			"$gte": globalLib.GetCurrentTime(local.TimeZone),
		},
		"startDate": bson.M{
			"$lte": globalLib.GetCurrentTime(local.TimeZone),
		},
	}
	if asker != nil {
		// case login
		askerRank := lib.GetRankAsker(asker)
		askerPoint := lib.GetAskerPoint(asker)
		query["$or"] = []bson.M{
			{"rankRequire": bson.M{"$exists": false}},
			{"rankRequire": bson.M{"$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]}},
		}
		if reqBody.IsOnlyDealsCanBuy {
			query["exchange.requiredPoint"] = bson.M{"$lte": askerPoint}
		}
	} else {
		// case not login
		query["$or"] = []bson.M{
			{"rankRequire": bson.M{"$exists": false}},
			{"rankRequire": bson.M{"$eq": globalConstant.MAP_RANK_REQUIRE[globalConstant.ASKER_RANK_NAME_MEMBER]}},
		}
	}

	if isTester {
		delete(query, "isTesting")
	}
	fields := bson.M{
		"_id":                1,
		"image":              1,
		"title":              1,
		"content":            1,
		"endDate":            1,
		"numberOfDayDueDate": 1,
		"type":               1,
		"exchange":           1,
		"askerTurn":          1,
		"isUnlimitRedeem":    1,
		"termsAndCondition":  1,
		"vouchers":           1,
	}

	sort := bson.M{}
	if reqBody.SortBy != "" {
		if reqBody.SortBy == lib.SORT_BY_LATEST {
			sort["createdAt"] = -1
		} else if reqBody.SortBy == lib.SORT_BY_LOW_TO_HIGH {
			sort["exchange.requiredPoint"] = 1
		} else if reqBody.SortBy == lib.SORT_BY_HIGH_TO_LOW {
			sort["exchange.requiredPoint"] = -1
		}
	} else {
		sort["startDate"] = -1
	}

	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}

	var bundleVoucher []*modelBundleVoucher.BundleVoucher
	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_BUNDLE_VOUCHER[local.ISO_CODE],
		query,
		fields,
		int64(page),
		int64(limit),
		sort,
		&bundleVoucher,
	)

	if len(bundleVoucher) == 0 {
		return bundleVoucher
	}

	if asker != nil {
		// case login then check more asker turn
		var result []*modelBundleVoucher.BundleVoucher
		for _, v := range bundleVoucher {
			mapHistoryByTotalRedeem := handlerBundleVoucher.GetMapBundleVoucherHistoryByTotalRedeem(asker.GetXId(), bundleVoucher)
			if totalRedeem, ok := mapHistoryByTotalRedeem[v.GetXId()]; ok && !v.IsUnlimitRedeem && totalRedeem >= v.AskerTurn {
				continue
			}

			totalVoucher := len(v.Vouchers)
			for _, voucher := range v.Vouchers {
				if voucher.Type == globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT && voucher.Stock == 0 {
					totalVoucher--
				}
			}
			if totalVoucher == 0 {
				continue
			}
			result = append(result, v)
		}
		return refactorBundle(result)
	}

	return refactorBundle(bundleVoucher)
}

func refactorBundle(bundleVoucher []*modelBundleVoucher.BundleVoucher) []*modelBundleVoucher.BundleVoucher {
	var newBundleVoucher []*modelBundleVoucher.BundleVoucher
	for _, v := range bundleVoucher {
		// ingore voucher
		v.Vouchers = nil
		newBundleVoucher = append(newBundleVoucher, v)
	}
	return newBundleVoucher
}
