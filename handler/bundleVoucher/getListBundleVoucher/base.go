package getListBundleVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
)

func GetListBundleVoucher(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	var bundleVouchers []*modelBundleVoucher.BundleVoucher
	if reqBody.UserId != "" {
		asker, isTester, errCode := checkUser(reqBody.UserId)
		if errCode != nil {
			return nil, errCode
		}
		// for case use logged in
		bundleVouchers = getBundleVouchers(asker, isTester, reqBody)
		return bundleVouchers, nil
	}
	// for case without login
	bundleVouchers = getBundleVouchers(nil, false, reqBody)
	return bundleVouchers, nil
}
