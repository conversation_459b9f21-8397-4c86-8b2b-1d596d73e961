package redeemBundleVoucher

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var (
	cfg        = config.GetConfig()
	ctx        = context.Background()
	redisCache = cfg.RedisCache
)

func RedeemBundleVoucher(reqBody *model.ApiRequest) ([]map[string]interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// get user info
	user, isTester, errCode := getUser(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}

	// get bundle voucher
	bundleVoucher, errCode := getBundleVoucher(reqBody.BundleVoucherId)
	if errCode != nil {
		return nil, errCode
	}

	// check asker can redeem bundle voucher
	errCode = checkAskerCanRedeem(bundleVoucher, user, isTester)
	if errCode != nil {
		return nil, errCode
	}

	lockKey := fmt.Sprintf("redeem_bundle_voucher_%s", bundleVoucher.XId)
	if redisCache != nil {
		// use redis lock to prevent multiple redeem bundle voucher
		if isLoked, err := redisCache.Get(ctx, lockKey).Bool(); err == nil && isLoked {
			return nil, &lib.ERROR_BUNDLE_VOUCHER_LOCKED
		}
		redisCache.Set(ctx, lockKey, true, 5*time.Second)
	}

	// redeem bundle voucher
	gifts, errCode := redeemBundleVoucher(bundleVoucher, user)
	if errCode != nil {
		return nil, errCode
	}

	if redisCache != nil {
		// delete lock
		redisCache.DelValueByKey(ctx, lockKey)
	}

	return gifts, nil
}
