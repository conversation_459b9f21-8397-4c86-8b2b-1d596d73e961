package redeemBundleVoucher

import (
	handlerBundleVoucher "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bundleVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

// check condition asker can redeem this bundle voucher
// 1. check asker rank
// 2. check asker turn
// 3. check bundle voucher status
// 4. asker point not enough
// 5. check bundle voucher expired
// 6. check bundle voucher stock valid to redeem
func checkAskerCanRedeem(bundleVoucher *modelBundleVoucher.BundleVoucher, user *modelUser.Users, isTester bool) *globalResponse.ResponseErrorCode {
	// 1. check asker rank
	askerRank := lib.GetRankAsker(user)
	if bundleVoucher.RankRequire > globalConstant.MAP_RANK_REQUIRE[askerRank] {
		return &lib.ERROR_YOUR_RANK_CAN_NOT_REDEEM_GIFT
	}

	// 2. check asker turn
	bundleVoucherHistory := handlerBundleVoucher.GetBundleVoucherHistory(user.XId, bundleVoucher.XId)
	if bundleVoucherHistory != nil && !bundleVoucher.IsUnlimitRedeem && bundleVoucherHistory.TotalRedeem >= bundleVoucher.AskerTurn {
		return &lib.ERROR_YOUR_TURN_CAN_NOT_REDEEM_GIFT
	}

	// 3. check bundle voucher status
	if bundleVoucher.Status == globalConstant.BUNDLE_VOUCHER_STATUS_INACTIVE && !isTester {
		return &lib.ERROR_BUNDLE_VOUCHER_HAS_STOPPED
	}

	// 4. asker point not enough
	var currentPoint float64
	if user.Point > 0 {
		currentPoint = user.Point
	}
	var point float64
	if bundleVoucher.Exchange != nil && bundleVoucher.Exchange.RequiredPoint > 0 {
		point = bundleVoucher.Exchange.RequiredPoint
	}
	if point > 0 && currentPoint < point {
		return &lib.ERROR_USER_NOT_ENOUGH_POINTS_REDEEM_BUNDLE
	}

	// 5. check bundle voucher expired
	if bundleVoucher.StartDate != nil && bundleVoucher.EndDate != nil {
		startDate := globalLib.ParseDateFromTimeStamp(bundleVoucher.StartDate, local.TimeZone)
		endDate := globalLib.ParseDateFromTimeStamp(bundleVoucher.EndDate, local.TimeZone)
		now := globalLib.GetCurrentTime(local.TimeZone)
		if now.Before(startDate) || now.After(endDate) {
			return &lib.ERROR_BUNDLE_VOUCHER_EXPIRED
		}
	}

	// 6. check bundle voucher stock valid to redeem
	totalVoucher := len(bundleVoucher.Vouchers)
	for _, voucher := range bundleVoucher.Vouchers {
		if voucher.Type == globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT && voucher.Stock == 0 {
			totalVoucher--
		}

	}

	if totalVoucher == 0 {
		return &lib.ERROR_BUNDLE_VOUCHER_HAS_NO_STOCK
	}

	return nil
}
