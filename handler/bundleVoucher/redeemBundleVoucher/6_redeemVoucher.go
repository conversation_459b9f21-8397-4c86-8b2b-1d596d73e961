package redeemBundleVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/redeem"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func redeemVoucher(incentiveId string, user *modelUser.Users, bundleVoucherTransactionID string, pointTransactionId string, giftFrom string) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	// check Incentive exist
	var incentive *modelIncentive.Incentive
	globalDataAccess.GetOneById(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], incentiveId, bson.M{"createdAt": 0, "categoryName": 0, "history": 0}, &incentive)
	if incentive == nil {
		return nil, &lib.ERROR_INCENTIVE_NOT_FOUND
	}

	var err error
	// redeem gift
	respondRedeemGift := make(map[string]interface{})
	if incentive.From == "SYSTEM" {
		respondRedeemGift, err = redeem.RedeemGiftFromSystem(user.XId, incentive)
	} else if incentive.From == "SYSTEM_WITH_PARTNER" {
		respondRedeemGift, err = redeem.RedeemGiftFromSystemWithPartner(user, incentive, bundleVoucherTransactionID)
	}
	if err != nil {
		errCode := redeem.GetErrorCodeGift(err.Error())
		return nil, errCode
	}

	respondRedeemGift["bundleVoucherTransactionId"] = bundleVoucherTransactionID
	// ----------- Insert gift to gift collection ----------------------------
	giftId := redeem.AddGiftCollection(pointTransactionId, user.XId, respondRedeemGift, incentive, giftFrom)
	if giftId == "" {
		return nil, &lib.ERROR_CREATE_GIFT_FAILED
	}

	// get detail gift
	var gift *modelGift.Gift
	err = globalDataAccess.GetOneById(globalCollection.COLLECTION_GIFT[local.ISO_CODE], giftId, bson.M{"_id": 1, "title": 1, "content": 1}, &gift)
	if err != nil {
		errCode := &lib.SYSTEM_ERROR
		errCode.Message = err.Error()
		return nil, errCode
	}
	mapResult := map[string]any{
		"_id":       gift.XId,
		"title":     gift.Title,
		"content":   gift.Content,
		"expiredAt": incentive.EndDate,
		"image":     incentive.Image,
	}
	if incentive.BrandInfo != nil && incentive.BrandInfo.Name != "" {
		mapResult["from"] = incentive.BrandInfo.Name
	}
	return mapResult, nil
}
