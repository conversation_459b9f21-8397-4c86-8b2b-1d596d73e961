package redeemBundleVoucher

import (
	"encoding/json"
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bundleVoucher/redeemBundleVoucher/redeemSecretBox"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelRedeemBundleVoucherHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemBundleVoucherHistory"
	modelRedeemBundleVoucherTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemBundleVoucherTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func redeemBundleVoucher(bundleVoucher *modelBundleVoucher.BundleVoucher, user *modelUser.Users) ([]map[string]interface{}, *globalResponse.ResponseErrorCode) {
	// create RedeemBundleVOucherTransaction
	bundleVoucherTransactionData := &modelRedeemBundleVoucherTransaction.RedeemBundleVoucherTransaction{
		XId:    globalLib.GenerateObjectId(),
		UserId: user.XId,
		Status: globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_NEW,
		BundleVoucherInfo: &modelRedeemBundleVoucherTransaction.RedeemBundleVoucherTransactionBundleVoucherInfo{
			BundleVoucherId: bundleVoucher.XId,
			Title:           bundleVoucher.Title,
			Exchange:        bundleVoucher.Exchange,
		},
	}
	b, _ := json.Marshal(bundleVoucherTransactionData)
	mapData := make(map[string]interface{})
	json.Unmarshal(b, &mapData)
	mapData["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION[local.ISO_CODE], mapData)
	if err != nil {
		return nil, &lib.ERROR_CREATE_REDEEM_BUNDLE_VOUCHER_TRANSACTION_FAILED
	}

	var point float64
	if bundleVoucher.Exchange != nil && bundleVoucher.Exchange.RequiredPoint > 0 {
		point = bundleVoucher.Exchange.RequiredPoint
	}

	// Redeem Success
	var pointTransactionId string
	if point > 0 {
		// --------- Add PointTransaction --------------------------------------------
		pointTransactionId = addPointTransaction(point, user, bundleVoucher)
		if pointTransactionId == "" {
			updateRedeemBundleVoucherTransaction(bundleVoucherTransactionData.XId, map[string]interface{}{
				"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
				"data":   lib.ERROR_CREATE_POINT_TRANSACTION_FAILED.ErrorCode,
				"userId": user.XId,
			})
			return nil, &lib.ERROR_CREATE_POINT_TRANSACTION_FAILED
		}
		// update point in user
		modelUser.UpdateOneById(local.ISO_CODE, user.XId, bson.M{"$inc": bson.M{"point": -point}})
	}

	// ----------- Get list voucher ----------------------------
	var bundleVoucherType string
	var voucher *modelBundleVoucher.BundleVoucherVouchers
	var isUnlimitVoucher bool
	if bundleVoucher.Type == globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX {
		voucher, isUnlimitVoucher = redeemSecretBox.GetVoucherSecretBox(bundleVoucher.Vouchers)
		bundleVoucherType = globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX
	}

	gift := make(map[string]interface{}, 0)
	if voucher == nil {
		return nil, &lib.ERROR_BUNDLE_VOUCHER_INVALID
	}

	// if voucher quantity is 0, set quantity to 1
	quantity := int(voucher.Quantity)
	if quantity == 0 {
		quantity = 1
	}

	i := 0
	for i < quantity {
		// redeem by quantity voucher
		tmp, errCode := redeemVoucher(voucher.IncentiveId, user, bundleVoucherTransactionData.XId, pointTransactionId, bundleVoucher.Type)
		if errCode != nil {
			updateRedeemBundleVoucherTransaction(bundleVoucherTransactionData.XId, map[string]interface{}{
				"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
				"data":   lib.ERROR_REDEEM_BUNDLE_VOUCHER_FAILED.ErrorCode,
				"userId": user.XId,
			})
			return nil, errCode
		}
		if i == 0 {
			gift = tmp
		}
		i++
	}
	gift["type"] = bundleVoucherType
	gift["quantity"] = quantity

	// update status of redeem bundle voucher transaction
	updateRedeemBundleVoucherTransaction(bundleVoucherTransactionData.XId, map[string]interface{}{
		"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_SUCCESS,
		"userId": user.XId,
	})

	if !isUnlimitVoucher {
		// update stock of voucher redeem
		queryUpdate := bson.M{
			"_id":                  bundleVoucher.XId,
			"vouchers.incentiveId": voucher.IncentiveId,
		}
		dataUpdate := bson.M{
			"$inc": bson.M{
				"vouchers.$.stock": -1,
			},
		}
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_BUNDLE_VOUCHER[local.ISO_CODE], queryUpdate, dataUpdate)
	}

	// update redeem bundle voucher history
	updateRedeemBundleVoucherHistory(bundleVoucher, voucher, user)
	return []map[string]interface{}{gift}, nil
}

func updateRedeemBundleVoucherTransaction(redeemBundleVoucherTransactionId string, data map[string]interface{}) {
	if redeemBundleVoucherTransactionId == "" {
		return
	}
	objUpdate := bson.M{
		"status":    data["status"],
		"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	// when redeem success
	if data["incentiveId"] != nil {
		objUpdate["incentiveId"] = data["incentiveId"]
	}

	// when redeem success or error
	if data["data"] != nil {
		objUpdate["data"] = data["data"]
	}
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION[local.ISO_CODE], redeemBundleVoucherTransactionId, bson.M{"$set": objUpdate})
	// Post to Slack if has error
	if data["status"] != nil && data["status"].(string) == globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR && data["data"] != nil && data["data"].(string) != lib.ERROR_USER_NOT_ENOUGH_POINTS.ErrorCode {
		channel := "asker-error-tracking"
		message := fmt.Sprintf("Khách hàng %s đổi thưởng không thành công. Lỗi: %s", data["userId"].(string), data["data"].(string))
		globalLib.PostToSlack(cfg.SlackToken, channel, globalConstant.SLACK_USER_NAME, message)
	}
}

func addPointTransaction(point float64, user *modelUser.Users, bundleVoucher *modelBundleVoucher.BundleVoucher) string {
	var oldPointRank float64
	if user.RankInfo != nil && user.RankInfo.Point > 0 {
		oldPointRank = user.RankInfo.Point
	}
	data := &modelPointTransaction.PointTransaction{
		XId:          globalLib.GenerateObjectId(),
		UserId:       user.XId,
		Point:        point,
		OldPoint:     user.Point,
		OldPointRank: oldPointRank,
		Type:         "C",
		IsoCode:      user.IsoCode,
		CreatedAt:    globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	lang := globalConstant.LANG_EN
	if user.Language != "" {
		lang = user.Language
	}
	sourceServiceText := &modelService.ServiceText{
		Vi: localization.T(globalConstant.LANG_VI, "GIFT_FROM_UR_BOX"),
		En: localization.T(globalConstant.LANG_EN, "GIFT_FROM_UR_BOX"),
		Ko: localization.T(globalConstant.LANG_KO, "GIFT_FROM_UR_BOX"),
		Th: localization.T(globalConstant.LANG_TH, "GIFT_FROM_UR_BOX"),
		Id: localization.T(globalConstant.LANG_ID, "GIFT_FROM_UR_BOX"),
	}

	data.ReferenceId = bundleVoucher.XId
	data.Source = &modelPointTransaction.PointTransactionSource{
		Name:            "SYSTEM",
		Text:            localization.T(lang, "GIFT_FROM_SYSTEM"),
		BundleVoucherId: bundleVoucher.XId,
		ServiceText:     sourceServiceText,
	}

	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], data)
	if err != nil {
		return ""
	}
	return data.XId
}

func updateRedeemBundleVoucherHistory(bundleVoucher *modelBundleVoucher.BundleVoucher, voucher *modelBundleVoucher.BundleVoucherVouchers, user *modelUser.Users) {
	var bundleVoucherHistory *modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{"userId": user.XId, "bundleVoucherId": bundleVoucher.XId}, bson.M{"_id": 1}, &bundleVoucherHistory)
	if bundleVoucherHistory == nil {
		// case history not exist: create new redeem bundle voucher history
		newBundleVoucherHistory := &modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory{
			XId:             globalLib.GenerateObjectId(),
			UserId:          user.XId,
			BundleVoucherId: bundleVoucher.XId,
			TotalRedeem:     1,
			VoucherHistories: []*modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistoryVoucher{
				{
					IncentiveId: voucher.IncentiveId,
					Quantity:    voucher.Quantity,
					CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
				},
			},
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], newBundleVoucherHistory)
		return
	}

	// case history exist: update redeem bundle voucher history
	dataUpdate := bson.M{
		"$inc": bson.M{"totalRedeem": 1},
		"$push": bson.M{
			"voucherHistories": bson.M{
				"incentiveId": voucher.IncentiveId,
				"quantity":    voucher.Quantity,
				"createdAt":   globalLib.GetCurrentTimestamp(local.TimeZone),
			},
		},
	}
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bundleVoucherHistory.XId, dataUpdate)
}
