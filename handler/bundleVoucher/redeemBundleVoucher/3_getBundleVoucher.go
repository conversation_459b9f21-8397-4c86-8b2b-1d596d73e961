package redeemBundleVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func getBundleVoucher(bundleVoucherId string) (*modelBundleVoucher.BundleVoucher, *globalResponse.ResponseErrorCode) {
	// check bundleVoucher exist
	var bundleVoucher *modelBundleVoucher.BundleVoucher

	field := bson.M{
		"_id":             1,
		"type":            1,
		"startDate":       1,
		"endDate":         1,
		"status":          1,
		"title":           1,
		"exchange":        1,
		"isTesting":       1,
		"vouchers":        1,
		"rankRequire":     1,
		"askerTurn":       1,
		"isUnlimitRedeem": 1,
	}
	globalDataAccess.GetOneById(globalCollection.COLLECTION_BUNDLE_VOUCHER[local.ISO_CODE], bundleVoucherId, field, &bundleVoucher)
	if bundleVoucher == nil {
		return nil, &lib.ERROR_BUNDLE_VOUCHER_NOT_FOUND
	}

	return bundleVoucher, nil
}
