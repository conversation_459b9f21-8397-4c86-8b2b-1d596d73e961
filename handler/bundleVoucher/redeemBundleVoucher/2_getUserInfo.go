package redeemBundleVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUser(userId string) (*modelUser.Users, bool, *globalResponse.ResponseErrorCode) {
	var user *modelUser.Users
	user, _ = modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"phone": 1, "point": 1, "isoCode": 1, "bPoint": 1, "rankInfo": 1, "rankInfoByCountry": 1})
	if user == nil {
		return nil, false, &lib.ERROR_USER_NOT_FOUND
	}

	// find tester
	var isTester bool
	var setting *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &setting)
	if setting != nil && len(setting.Tester) > 0 && globalLib.FindStringInSlice(setting.Tester, user.Phone) >= 0 {
		isTester = true
	}

	return user, isTester, nil
}
