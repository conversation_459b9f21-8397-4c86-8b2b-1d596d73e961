package redeemSecretBox

import (
	"math/rand"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
)

func GetVoucherSecretBox(vouchers []*modelBundleVoucher.BundleVoucherVouchers) (*modelBundleVoucher.BundleVoucherVouchers, bool) {
	if len(vouchers) == 0 {
		return nil, false
	}

	voucherValidStock := make([]*modelBundleVoucher.BundleVoucherVouchers, 0)
	var isUnlimitVoucher bool
	for _, v := range vouchers {
		if v.Type == globalConstant.BUNDLE_VOUCHER_TYPE_LIMIT && v.Stock == 0 {
			continue
		}
		if v.Type == globalConstant.BUNDLE_VOUCHER_TYPE_UNLIMIT {
			isUnlimitVoucher = true
		}
		voucherValidStock = append(voucherValidStock, v)
	}

	if len(voucherValidStock) == 0 {
		return nil, false
	}

	// Initialize the random number generator
	rand.Seed(time.Now().UnixNano())

	// Select a random element
	randomIndex := rand.Intn(len(voucherValidStock))

	// Return the selected number of vouchers
	return voucherValidStock[randomIndex], isUnlimitVoucher
}
