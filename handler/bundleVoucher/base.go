package bundleVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelBundleVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bundleVoucher"
	modelRedeemBundleVoucherHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemBundleVoucherHistory"
	"go.mongodb.org/mongo-driver/bson"
)

func GetBundleVoucherHistory(userId, bundleVoucherId string) (bundleVoucherHistory *modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory) {
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{"userId": userId, "bundleVoucherId": bundleVoucherId}, bson.M{"_id": 1, "totalRedeem": 1}, &bundleVoucherHistory)
	return
}

func GetMapBundleVoucherHistoryByTotalRedeem(askerId string, bundleVoucher []*modelBundleVoucher.BundleVoucher) map[string]int64 {
	if len(bundleVoucher) == 0 {
		return nil
	}
	bundleVoucherIds := make([]string, len(bundleVoucher))
	for i, v := range bundleVoucher {
		bundleVoucherIds[i] = v.XId
	}
	var bundleVoucherHistory []*modelRedeemBundleVoucherHistory.RedeemBundleVoucherHistory
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY[local.ISO_CODE], bson.M{"userId": askerId, "bundleVoucherId": bson.M{"$in": bundleVoucherIds}}, bson.M{"_id": 1, "bundleVoucherId": 1, "totalRedeem": 1}, &bundleVoucherHistory)

	// map key bundleVoucherId, value totalRedeem
	mapBundleVoucherHistory := make(map[string]int64)
	for _, v := range bundleVoucherHistory {
		mapBundleVoucherHistory[v.BundleVoucherId] = v.TotalRedeem
	}
	return mapBundleVoucherHistory
}
