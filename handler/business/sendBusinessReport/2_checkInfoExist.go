package sendBusinessReport

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	"go.mongodb.org/mongo-driver/bson"
)

func checkInfoExist(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	var business *modelBusiness.Business
	err := globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], reqBody.BusinessId, bson.M{"email": 1}, &business)
	if business == nil || err != nil {
		return &lib.ERROR_BUSINESS_NOT_FOUND, err
	}

	if business.Email == "" {
		return &lib.ERROR_BUSINESS_EMAIL_NOT_FOUND, nil
	}

	return nil, nil
}
