package sendBusinessReport

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func SendBusinessReport(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. check info exist: business exist, email verified
	errCode, err := checkInfoExist(reqBody)
	if errCode != nil {
		return errCode, err
	}

	// 3. send report
	errCode, err = sendReport(reqBody)
	if errCode != nil {
		return errCode, err
	}

	return nil, nil
}
