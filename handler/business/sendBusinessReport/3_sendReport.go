package sendBusinessReport

import (
	"context"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcEmailVN"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
)

func sendReport(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	var err error
	client, connect, err := grpcEmailVN.ConnectGRPCEmailVN(cfg.GRPC_Email_Service_URL)
	if err != nil {
		return &lib.ERROR_CAN_NOT_SEND_EMAIL, err
	}
	defer connect.Close()

	req := &modelEmailSending.EmailSendBusinessReportRequest{
		BusinessId: reqBody.BusinessId,
		Month:      reqBody.Month,
		Year:       reqBody.Year,
	}
	res, err := client.SendBusinessReportEmail(context.Background(), req)
	if err != nil || res.StatusCode != http.StatusOK {
		return &lib.ERROR_CAN_NOT_SEND_EMAIL, err
	}
	return nil, nil
}
