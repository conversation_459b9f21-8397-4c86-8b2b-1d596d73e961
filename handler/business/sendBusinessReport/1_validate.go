package sendBusinessReport

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if reqBody.BusinessId == "" {
		return &lib.ERROR_BUSINESS_ID_REQUIRED
	}
	if reqBody.Month == 0 || reqBody.Year == 0 {
		return &lib.ERROR_MONTH_AND_YEAR_REQUIRED
	}
	return nil
}
