package getListMemberTransaction

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMemberTransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

type BusinessMemberTransactionDetail struct {
	*businessMemberTransaction.BusinessMemberTransaction
	ServiceText *service.ServiceText `json:"serviceText,omitempty"`
	NameText    *service.ServiceText `json:"nameText,omitempty"`
	IsReport    bool                 `json:"isReport,omitempty"`
}

func getListTransaction(reqBody *model.BusinessRequest) ([]*BusinessMemberTransactionDetail, *globalResponse.ResponseErrorCode, error) {
	// Set the time to the first day of the provided month and year
	providedTime := time.Date(int(reqBody.Year), time.Month(reqBody.Month), 1, 0, 0, 0, 0, local.TimeZone)
	start, end := globalLib.GetStartEndOfMonth(local.TimeZone, providedTime)

	// Get the list of transactions
	query := bson.M{
		"memberId": reqBody.MemberId,
		"createdAt": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	fields := bson.M{
		"type":      1,
		"amount":    1,
		"name":      1,
		"reason":    1,
		"taskId":    1,
		"createdAt": 1,
	}

	var transactions []*businessMemberTransaction.BusinessMemberTransaction
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], query, fields, bson.M{"createdAt": -1}, &transactions)

	// get reported transactions
	transactionIds := []string{}
	for _, transaction := range transactions {
		transactionIds = append(transactionIds, transaction.XId)
	}
	reportedTransactions := lib.GetReportTransactions(transactionIds)

	// Add info to transaction
	transactionsDetail := mapTransactionWithInfo(transactions, reportedTransactions)

	return transactionsDetail, nil, nil
}

func mapTransactionWithInfo(transactions []*businessMemberTransaction.BusinessMemberTransaction, reportedTransactions []string) []*BusinessMemberTransactionDetail {
	taskIds := []string{}
	for _, transaction := range transactions {
		transaction.Currency = &globalConstant.CURRENCY_SIGN_VN
		if transaction.TaskId != "" && globalLib.FindStringInSlice(taskIds, transaction.TaskId) < 0 {
			taskIds = append(taskIds, transaction.TaskId)
		}
	}

	var tasks []*task.Task
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": taskIds}},
		bson.M{"_id": 1, "serviceText": 1},
		&tasks,
	)

	taskMap := map[string]*service.ServiceText{}
	for _, task := range tasks {
		taskMap[task.XId] = task.ServiceText
	}

	var transactionsDetail []*BusinessMemberTransactionDetail
	for _, transaction := range transactions {
		isReport := globalLib.FindStringInSlice(reportedTransactions, transaction.XId) >= 0
		transactionsDetail = append(transactionsDetail, &BusinessMemberTransactionDetail{
			BusinessMemberTransaction: transaction,
			ServiceText:               taskMap[transaction.TaskId],
			NameText:                  localization.GetLocalizeObject(globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_PREFIX + transaction.Name),
			IsReport:                  isReport,
		})
	}

	return transactionsDetail
}
