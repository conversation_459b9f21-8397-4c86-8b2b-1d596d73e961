package getListMemberTransaction

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListMemberTransaction(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get list transaction by memberId
	transactions, errCode, err := getListTransaction(reqBody)
	if errCode != nil {
		return nil, errCode, err
	}

	return transactions, nil, nil
}
