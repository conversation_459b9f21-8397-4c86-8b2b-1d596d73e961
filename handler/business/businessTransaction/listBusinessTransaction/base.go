package getListBusinessTransaction

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListBusinessTransaction(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 2. Get list transaction
	transactions, errCode := getListBusinessTransaction(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	return transactions, nil
}
