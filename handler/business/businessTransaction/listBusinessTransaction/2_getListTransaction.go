package getListBusinessTransaction

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessTransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// business key, use for display in app
	BUSINESS_TRANSACTION_KEY_PREFIX             = "BUSINESS_TRANSACTION_KEY_"
	BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY        = "TOP_UP_BPAY"
	BUSINESS_TRANSACTION_KEY_TOP_UP_MEMBER      = "TOP_UP_MEMBER"
	BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER   = "USED_BPAY_MEMBER"
	BUSINESS_TRANSACTION_KEY_REVOKE_BPAY_MEMBER = "REVOKE_BPAY_MEMBER"
)

type BusinessTransaction struct {
	XId       string                                 `json:"_id,omitempty"`
	Avatar    string                                 `json:"avatar,omitempty"`
	Name      *service.ServiceText                   `json:"name,omitempty"`
	Type      string                                 `json:"type,omitempty"`
	Amount    float64                                `json:"amount,omitempty"`
	Currency  *settingCountry.SettingCountryCurrency `json:"currency,omitempty"`
	CreatedAt *timestamppb.Timestamp                 `json:"createdAt,omitempty"` // only for key TOP_UP_BPAY
	IsReport  bool                                   `json:"isReport,omitempty"`
}

type BusinessTransactionsByKey struct {
	Key          string                 `json:"key"`
	KeyText      *service.ServiceText   `json:"keyText"`
	Transactions []*BusinessTransaction `json:"transactions"`
	TotalAmount  float64                `json:"totalAmount"`
}

// the key is use for display in app
var mapBusinessTransactionNameByKey = map[string][]string{
	BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY:        {globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE},
	BUSINESS_TRANSACTION_KEY_TOP_UP_MEMBER:      {globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER},
	BUSINESS_TRANSACTION_KEY_REVOKE_BPAY_MEMBER: {globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER},
}
var orderKeys = []string{BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY, BUSINESS_TRANSACTION_KEY_TOP_UP_MEMBER, BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER, BUSINESS_TRANSACTION_KEY_REVOKE_BPAY_MEMBER}

func getListBusinessTransaction(reqBody *model.BusinessRequest) ([]*BusinessTransactionsByKey, *globalResponse.ResponseErrorCode) {
	// Set the time to the first day of the provided month and year
	providedTime := time.Date(int(reqBody.Year), time.Month(reqBody.Month), 1, 0, 0, 0, 0, local.TimeZone)
	start, end := globalLib.GetStartEndOfMonth(local.TimeZone, providedTime)
	query := bson.M{
		"businessId": reqBody.BusinessId,
		"createdAt": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	fields := bson.M{
		"type":      1,
		"amount":    1,
		"name":      1,
		"levelInfo": 1,
		"createdAt": 1,
	}
	sort := bson.M{"createdAt": -1}
	var transactions []*businessTransaction.BusinessTransaction
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], query, fields, sort, &transactions)

	// get reported transactions
	transactionIds := []string{}
	for _, transaction := range transactions {
		transactionIds = append(transactionIds, transaction.XId)
	}
	reportedTransactions := lib.GetReportTransactions(transactionIds)

	// Reverse map: from transaction name to key
	nameToKeyMap := map[string]string{}
	for key, names := range mapBusinessTransactionNameByKey {
		for _, name := range names {
			nameToKeyMap[name] = key
		}
	}
	transactionByKeyMap := initTransactionGroup()
	mapTransactionByGroup := map[string]map[string]*BusinessTransaction{} // map[key][levelId]BusinessTransaction

	// if transaction is top up business bPay, return all the detail, else return transaction group by level info
	for _, transaction := range transactions {
		switch nameToKeyMap[transaction.Name] {
		case BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY:
			isReported := globalLib.FindStringInSlice(reportedTransactions, transaction.XId) >= 0
			transactionByKeyMap[BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY].Transactions = append(transactionByKeyMap[BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY].Transactions, &BusinessTransaction{
				XId:       transaction.XId,
				Name:      localization.GetLocalizeObject(globalConstant.BUSINESS_TRANSACTION_NAME_PREFIX + transaction.Name),
				Type:      transaction.Type,
				Amount:    transaction.Amount,
				Currency:  &globalConstant.CURRENCY_SIGN_VN,
				CreatedAt: transaction.CreatedAt,
				IsReport:  isReported,
			})
			transactionByKeyMap[BUSINESS_TRANSACTION_KEY_TOP_UP_BPAY].TotalAmount += transaction.Amount
		case BUSINESS_TRANSACTION_KEY_TOP_UP_MEMBER, BUSINESS_TRANSACTION_KEY_REVOKE_BPAY_MEMBER:
			if transaction.LevelInfo == nil {
				continue
			}
			key := nameToKeyMap[transaction.Name]
			if _, ok := mapTransactionByGroup[key]; !ok {
				mapTransactionByGroup[key] = map[string]*BusinessTransaction{}
			}

			if _, ok := mapTransactionByGroup[key][transaction.GetLevelInfo().GetLevelId()]; !ok {
				mapTransactionByGroup[key][transaction.GetLevelInfo().GetLevelId()] = &BusinessTransaction{
					XId:      transaction.XId,
					Name:     lib.ConvertStringToServiceText(transaction.GetLevelInfo().GetLevelName()),
					Type:     transaction.Type,
					Amount:   transaction.Amount,
					Currency: &globalConstant.CURRENCY_SIGN_VN,
				}
			} else {
				mapTransactionByGroup[key][transaction.LevelInfo.LevelId].Amount += transaction.Amount
			}
		}
	}

	// move transaction from mapTransactionByGroup to transactionByKeyMap
	for key, levelTransaction := range mapTransactionByGroup {
		for _, transaction := range levelTransaction {
			transactionByKeyMap[key].Transactions = append(transactionByKeyMap[key].Transactions, transaction)
			transactionByKeyMap[key].TotalAmount += transaction.Amount
		}
	}

	transactionByKeyMap[BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER] = getUsedMemberTransactions(reqBody.BusinessId, start, end)

	// sort by custom order
	transactionsByKeys := []*BusinessTransactionsByKey{}
	for _, key := range orderKeys {
		transactionsByKeys = append(transactionsByKeys, transactionByKeyMap[key])
	}

	return transactionsByKeys, nil
}

// init default 4 group to display even if the group has no transaction
func initTransactionGroup() map[string]*BusinessTransactionsByKey {
	mapTransactionByGroup := map[string]*BusinessTransactionsByKey{}
	for _, key := range orderKeys {
		if _, ok := mapTransactionByGroup[key]; !ok {
			mapTransactionByGroup[key] = &BusinessTransactionsByKey{
				Key:          key,
				KeyText:      localization.GetLocalizeObject(BUSINESS_TRANSACTION_KEY_PREFIX + key),
				Transactions: []*BusinessTransaction{},
				TotalAmount:  0,
			}
		}
	}
	return mapTransactionByGroup
}

type MemberTransactionTotal struct {
	MemberID    string  `bson:"_id"`
	Name        string  `bson:"name"`
	Avatar      string  `bson:"avatar"`
	TotalAmount float64 `bson:"totalAmount"`
}

func getUsedMemberTransactions(businessId string, start, end time.Time) (usedMemberTransaction *BusinessTransactionsByKey) {
	usedMemberTransaction = &BusinessTransactionsByKey{
		Key:          BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER,
		KeyText:      localization.GetLocalizeObject(BUSINESS_TRANSACTION_KEY_PREFIX + BUSINESS_TRANSACTION_KEY_USED_BPAY_MEMBER),
		Transactions: []*BusinessTransaction{},
		TotalAmount:  0,
	}
	query := bson.M{
		"businessId": businessId,
		"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
		"name": bson.M{
			"$ne": globalConstant.BUSINESS_MEMBER_TRANSACTION_NAME_REVOKE_BPAY_BY_BUSINESS,
		},
		"createdAt": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	aggregateQuery := []bson.M{
		{
			"$match": query,
		},
		{
			"$lookup": bson.M{
				"from":         globalCollection.COLLECTION_USERS,
				"localField":   "userId",
				"foreignField": "_id",
				"as":           "user",
			},
		},
		{
			"$unwind": bson.M{
				"path":                       "$user",
				"preserveNullAndEmptyArrays": true, // Keep entries with no matching user
			},
		},
		{
			"$group": bson.M{
				"_id": "$memberId",
				"name": bson.M{
					"$first": "$user.name",
				},
				"avatar": bson.M{
					"$first": "$user.avatar",
				},
				"totalAmount": bson.M{
					"$sum": "$amount",
				},
			},
		},
	}

	var memberList []*MemberTransactionTotal
	err := globalDataAccess.Aggregate(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], aggregateQuery, &memberList)
	if err != nil {
		return usedMemberTransaction
	}

	for _, member := range memberList {
		usedMemberTransaction.Transactions = append(usedMemberTransaction.Transactions, &BusinessTransaction{
			Name:     lib.ConvertStringToServiceText(member.Name),
			Avatar:   member.Avatar,
			Type:     globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
			Amount:   member.TotalAmount,
			Currency: &globalConstant.CURRENCY_SIGN_VN,
		})
		usedMemberTransaction.TotalAmount += member.TotalAmount
	}
	return usedMemberTransaction
}
