package removeMembersLevel

import (
	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func removeMembersLevel(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	errorResult := []map[string]interface{}{}
	membersData, _ := member.GetMembers(bson.M{"_id": bson.M{"$in": reqBody.MemberIds}, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE, "businessId": reqBody.BusinessId}, bson.M{"_id": 1, "levelId": 1, "userId": 1})
	if len(membersData) == 0 {
		return nil, &lib.ERROR_MEMBER_IN_VALID
	}
	mapMemberByMemberId := map[string]*modelBusinessMember.BusinessMember{}
	for _, v := range membersData {
		mapMemberByMemberId[v.XId] = v
	}
	listMemberValid := []string{}
	for _, v := range reqBody.MemberIds {
		if _, ok := mapMemberByMemberId[v]; !ok {
			errorResult = append(errorResult, map[string]interface{}{
				"memberId": v,
				"error":    "MEMBER_INVALID",
			})
			continue
		}
		listMemberValid = append(listMemberValid, v)
	}
	if len(listMemberValid) > 0 {
		oldLevelId := reqBody.LevelId
		if oldLevelId == "" {
			oldLevelId = membersData[0].LevelId
		}
		_, err := globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"_id": bson.M{"$in": listMemberValid}},
			bson.M{
				"$unset": bson.M{"levelId": ""},
				"$push": bson.M{"changeHistories": bson.M{
					"key": "REMOVE_LEVEL",
					"content": map[string]interface{}{
						"levelId": oldLevelId,
					},
					"createdBy": reqBody.BusinessId,
					"createdAt": globalLib.GetCurrentTime(local.TimeZone),
				}},
			})
		if err != nil {
			return nil, &lib.ERROR_UPDATE_FAILED
		}
		businessInfo, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": reqBody.BusinessId}, bson.M{"name": 1})
		levelInfo, _ := modelBusinessLevel.GetOne(local.ISO_CODE, bson.M{"_id": oldLevelId}, bson.M{"_id": 1, "name": 1})
		for _, memberId := range listMemberValid {
			pkgBusiness.SendNotificationSuccess(mapMemberByMemberId[memberId].UserId, "DIALOG_TITLE_INFORMATION", "BUSINESS_REMOVE_LEVEL_TO_MEMBER_SUCCESS_BODY", "", businessInfo.GetName(), levelInfo.GetName())
		}
		return errorResult, nil
	}
	return errorResult, nil
}
