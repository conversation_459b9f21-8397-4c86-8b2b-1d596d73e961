package addMembersLevel

import (
	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func addMembersLevel(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Check level active
	levelData := level.GetLevel(bson.M{"_id": reqBody.LevelId, "businessId": reqBody.BusinessId}, bson.M{"_id": 1, "name": 1, "status": 1})
	if levelData == nil {
		return nil, &lib.ERROR_LEVEL_NOT_FOUND
	}
	if levelData.Status != globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE {
		return nil, &lib.ERROR_LEVEL_INVALID
	}

	errorResult := []map[string]interface{}{}

	membersData, _ := member.GetMembers(bson.M{"_id": bson.M{"$in": reqBody.MemberIds}, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE, "businessId": reqBody.BusinessId}, bson.M{"_id": 1, "levelId": 1, "userId": 1})
	mapMemberByMemberId := map[string]*modelBusinessMember.BusinessMember{}
	for _, v := range membersData {
		mapMemberByMemberId[v.XId] = v
	}
	listMemberValid := []string{}
	for _, v := range reqBody.MemberIds {
		memberData, ok := mapMemberByMemberId[v]
		if !ok {
			errorResult = append(errorResult, map[string]interface{}{
				"memberId": v,
				"error":    "MEMBER_INVALID",
			})
			continue
		}
		if memberData.LevelId != "" {
			errorResult = append(errorResult, map[string]interface{}{
				"memberId": v,
				"error":    "MEMBER_LEVEL_EXIST",
			})
			continue
		}
		listMemberValid = append(listMemberValid, v)
	}
	if len(listMemberValid) > 0 {
		_, err := globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"_id": bson.M{"$in": listMemberValid}},
			bson.M{
				"$set": bson.M{"levelId": reqBody.LevelId},
				"$push": bson.M{"changeHistories": bson.M{
					"key": "ADD_LEVEL",
					"content": map[string]interface{}{
						"levelId": reqBody.LevelId,
					},
					"createdBy": reqBody.BusinessId,
					"createdAt": globalLib.GetCurrentTime(local.TimeZone),
				}},
			})
		if err != nil {
			return nil, &lib.ERROR_UPDATE_FAILED
		}
		return errorResult, nil
	}
	businessInfo, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": reqBody.BusinessId}, bson.M{"name": 1})
	for _, memberId := range listMemberValid {
		pkgBusiness.SendNotificationSuccess(memberId, "DIALOG_TITLE_INFORMATION", "BUSINESS_UPDATE_LEVEL_TO_MEMBER_SUCCESS_BODY", "", businessInfo.GetName(), levelData.GetName())
	}
	return errorResult, nil
}
