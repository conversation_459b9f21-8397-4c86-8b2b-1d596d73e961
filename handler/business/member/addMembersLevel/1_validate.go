package addMembersLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validateRequest(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if len(reqBody.MemberIds) == 0 {
		return &lib.ERROR_MEMBER_IDS_REQUIRED
	}
	if reqBody.LevelId == "" {
		return &lib.ERROR_LEVEL_REQUIRED
	}
	if reqBody.BusinessId == "" {
		return &lib.ERROR_BUSINESS_REQUIRED
	}
	return nil
}
