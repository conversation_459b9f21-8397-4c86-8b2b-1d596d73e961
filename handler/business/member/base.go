package member

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func GetMember(query, fields bson.M) *modelBusinessMember.BusinessMember {
	var member *modelBusinessMember.BusinessMember
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], query, fields, &member)
	return member
}

func CheckUsersInBusiness(askerIds []string) map[string]*modelBusinessMember.BusinessMember {
	var members []*modelBusinessMember.BusinessMember
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"userId": bson.M{"$in": askerIds}, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}, bson.M{"userId": 1, "businessId": 1}, &members)
	if members == nil {
		return nil
	}
	mapMemberByUserId := make(map[string]*modelBusinessMember.BusinessMember)
	for _, v := range members {
		mapMemberByUserId[v.UserId] = v
	}
	return mapMemberByUserId
}

func GetMembers(query, fields bson.M) ([]*modelBusinessMember.BusinessMember, error) {
	var members []*modelBusinessMember.BusinessMember
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], query, fields, &members)
	return members, err
}
