package removeMemberFromBusiness

import (
	"context"

	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	"go.mongodb.org/mongo-driver/bson"
)

func removeMember(reqBody *model.BusinessRequest, member *businessMember.BusinessMember) (*globalResponse.ResponseErrorCode, error) {
	// revoke bPay
	_, err := revokeMembersGRPC(&businessMember.MemberRequestPayment{
		BusinessId: member.BusinessId,
		MemberId:   member.XId,
		Amount:     member.BPay,
	})
	if err != nil {
		return &lib.ERROR_REVOKE_MEMBER_FAILED, err
	}

	// update status member to INACTIVE
	_, err = globalDataAccess.UpdateOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], member.XId,
		bson.M{
			"$set": bson.M{
				"status": globalConstant.BUSINESS_MEMBER_STATUS_INACTIVE,
			},
			"$unset": bson.M{
				"levelId": 1,
			},
			"$push": bson.M{
				"changeHistories": bson.M{
					"key":       "REMOVE_MEMBER_FROM_BUSINESS",
					"createdBy": reqBody.BusinessId,
					"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					"content": bson.M{
						"oldStatus": member.Status,
						"newStatus": globalConstant.BUSINESS_MEMBER_STATUS_INACTIVE,
					},
				},
			},
		})
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}
	businessInfo, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": reqBody.BusinessId}, bson.M{"name": 1})
	pkgBusiness.SendNotificationSuccess(member.UserId, "DIALOG_TITLE_INFORMATION", "BUSINESS_REMOVE_MEMBER_TO_MEMBER_SUCCESS_BODY", "", businessInfo.GetName())
	return nil, nil
}

func revokeMembersGRPC(requestData *businessMember.MemberRequestPayment) (result *response.Response, err error) {
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	if err != nil {
		return nil, err
	}
	defer connect.Close()
	response, err := client.BusinessRevokeMember(context.Background(), requestData)
	if err != nil {
		return nil, err
	}
	return response, nil
}
