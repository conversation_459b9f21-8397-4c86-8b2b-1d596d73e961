package removeMemberFromBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func getMemberInfo(reqBody *model.BusinessRequest) (member *businessMember.BusinessMember, errCode *globalResponse.ResponseErrorCode) {
	globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], reqBody.MemberId, bson.M{"status": 1, "bPay": 1, "businessId": 1, "userId": 1}, &member)
	if member == nil || member.Status != globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE {
		return nil, &lib.ERROR_BUSINESS_MEMBER_NOT_FOUND
	}
	if member.BusinessId != reqBody.BusinessId {
		return nil, &lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS
	}
	return member, nil
}
