package removeMemberFromBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func RemoveMemberFromBusiness(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. check user exist
	member, errCode := getMemberInfo(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 3. remove member
	errCode, err := removeMember(reqBody, member)
	if errCode != nil {
		return errCode, err
	}

	return nil, nil
}
