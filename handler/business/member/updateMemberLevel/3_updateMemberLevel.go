package updateMemberLevel

import (
	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func updateMemberLevel(reqBody *model.BusinessRequest, memberInfo *businessMember.BusinessMember, levelName string) (*globalResponse.ResponseErrorCode, error) {
	queryUpdate := bson.M{
		"$set": bson.M{
			"levelId": reqBody.LevelId,
		},
		"$push": bson.M{
			"changeHistories": bson.M{
				"key":       "UPDATE_MEMBER_LEVEL",
				"createdBy": reqBody.BusinessId,
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
				"content": bson.M{
					"oldLevelId": memberInfo.LevelId,
					"newLevelId": reqBody.LevelId,
				},
			},
		},
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], reqBody.MemberId, queryUpdate)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}
	businessInfo, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": reqBody.BusinessId}, bson.M{"name": 1})
	pkgBusiness.SendNotificationSuccess(memberInfo.UserId, "DIALOG_TITLE_INFORMATION", "BUSINESS_UPDATE_LEVEL_TO_MEMBER_SUCCESS_BODY", "", businessInfo.GetName(), levelName)

	return nil, nil
}
