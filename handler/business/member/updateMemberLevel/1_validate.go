package updateMemberLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if reqBody.BusinessId == "" {
		return &lib.ERROR_BUSINESS_ID_REQUIRED
	}
	if reqBody.MemberId == "" {
		return &lib.ERROR_BUSINESS_MEMBER_ID_REQUIRED
	}
	if reqBody.LevelId == "" {
		return &lib.ERROR_BUSINESS_LEVEL_ID_REQUIRED
	}
	return nil
}
