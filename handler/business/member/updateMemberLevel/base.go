package updateMemberLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func UpdateMemberLevel(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// get member info
	member, levelName, errCode, err := getMemberInfo(reqBody)
	if errCode != nil {
		return errCode, err
	}

	// update member level
	errCode, err = updateMemberLevel(reqBody, member, levelName)
	if errCode != nil {
		return errCode, err
	}

	return nil, nil
}
