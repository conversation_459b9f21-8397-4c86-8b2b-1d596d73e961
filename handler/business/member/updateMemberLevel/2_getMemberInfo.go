package updateMemberLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func getMemberInfo(reqBody *model.BusinessRequest) (*businessMember.BusinessMember, string, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{
		"_id":    reqBody.MemberId,
		"status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	}
	var memberInfo *businessMember.BusinessMember
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], query, bson.M{"businessId": 1, "levelId": 1, "userId": 1}, &memberInfo)
	if err != nil || memberInfo == nil {
		return nil, "", &lib.ERROR_BUSINESS_MEMBER_NOT_FOUND, err
	}
	if memberInfo.BusinessId != reqBody.BusinessId {
		return nil, "", &lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS, nil
	}

	query = bson.M{
		"_id":    reqBody.LevelId,
		"status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	}
	var levelInfo *businessLevel.BusinessLevel
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], query, bson.M{"businessId": 1, "name": 1}, &levelInfo)
	if err != nil || levelInfo == nil {
		return nil, "", &lib.ERROR_BUSINESS_LEVEL_NOT_FOUND, err
	}
	if levelInfo.BusinessId != reqBody.BusinessId {
		return nil, "", &lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS, nil
	}

	return memberInfo, levelInfo.GetName(), nil, nil
}
