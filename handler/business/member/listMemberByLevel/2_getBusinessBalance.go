package listMembersByLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	"go.mongodb.org/mongo-driver/bson"
)

func getUserInfo(businessId string) (balance float64, errCode *globalResponse.ResponseErrorCode, err error) {
	// Get user, userId and businessId are the same
	var business *business.Business
	err = globalDataAccess.GetOneById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], businessId, bson.M{"bPay": 1}, &business)
	if business == nil {
		return 0, &lib.ERROR_BUSINESS_NOT_FOUND, err
	}

	return business.BPay, nil, nil
}
