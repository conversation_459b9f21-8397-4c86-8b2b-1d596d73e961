package listMembersByLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func ListMembersByLevel(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get user balance
	balance, errCode, err := getUserInfo(reqBody.BusinessId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. get levels in business
	members, errCode, err := getMembers(reqBody.BusinessId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 4. map member by level
	levels := mapMemberByLevel(reqBody, members)

	result := map[string]interface{}{
		"balance": balance,
		"levels":  levels,
	}

	return result, nil, nil
}
