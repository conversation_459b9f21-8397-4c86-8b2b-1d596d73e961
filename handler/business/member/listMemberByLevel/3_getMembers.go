package listMembersByLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

type BusinessMemberDetail struct {
	*businessMember.BusinessMember
	Name   string `json:"name,omitempty"`
	Avatar string `json:"avatar,omitempty"`
	Email  string `json:"email,omitempty"`
	Phone  string `json:"phone,omitempty"`
}

func getMembers(businessId string) ([]*BusinessMemberDetail, *globalResponse.ResponseErrorCode, error) {
	// get business user info
	fields := bson.M{"userId": 1, "levelId": 1, "bPay": 1}
	query := bson.M{"businessId": businessId, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}
	var members []*businessMember.BusinessMember
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], query, fields, &members)

	// get origin user info: avatar, email
	memberUserIds := []string{}
	for _, member := range members {
		memberUserIds = append(memberUserIds, member.UserId)
	}
	memberUserInfos, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": memberUserIds}}, bson.M{"name": 1, "phone": 1, "avatar": 1, "emails": 1})
	mapUserById := map[string]*modelUser.Users{}
	for _, user := range memberUserInfos {
		mapUserById[user.XId] = user
	}

	// map result
	membersDetail := []*BusinessMemberDetail{}
	for _, member := range members {
		memberUser := mapUserById[member.UserId]
		memberDetail := &BusinessMemberDetail{
			BusinessMember: member,
			Name:           memberUser.GetName(),
			Avatar:         memberUser.GetAvatar(),
			Phone:          memberUser.GetPhone(),
		}
		if len(memberUser.GetEmails()) > 0 {
			memberDetail.Email = memberUser.Emails[0].Address // email array always have 1 email
		}
		membersDetail = append(membersDetail, memberDetail)
	}
	return membersDetail, nil, nil
}
