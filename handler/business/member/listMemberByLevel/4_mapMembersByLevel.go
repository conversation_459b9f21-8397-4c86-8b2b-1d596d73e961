package listMembersByLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

type BusinessLevelDetail struct {
	*businessLevel.BusinessLevel `json:"detail,omitempty"`
	NameText                     *service.ServiceText    `json:"nameText,omitempty"`
	TotalMember                  int32                   `json:"totalMember"`
	Members                      []*BusinessMemberDetail `json:"members"`
}

func mapMemberByLevel(reqBody *model.BusinessRequest, members []*BusinessMemberDetail) []*BusinessLevelDetail {
	// member by level
	memberByLevelId := map[string][]*BusinessMemberDetail{}
	memberWithoutLevel := []*BusinessMemberDetail{}
	for _, member := range members {

		// separate member by level and without level
		if member.LevelId == "" {
			memberWithoutLevel = append(memberWithoutLevel, member)
		} else {
			memberByLevelId[member.LevelId] = append(memberByLevelId[member.LevelId], member)
		}
		member.UserId = ""  // ignore field return to client
		member.LevelId = "" // ignore field return to client
	}

	// get level info
	levelInfos := []*businessLevel.BusinessLevel{}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], bson.M{"businessId": reqBody.BusinessId, "status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE}, bson.M{"name": 1, "amount": 1, "status": 1}, &levelInfos)

	// map result
	memberByLevel := []*BusinessLevelDetail{}
	memberByLevel = append(memberByLevel, &BusinessLevelDetail{
		NameText:    localization.GetLocalizeObject("BUSINESS_LEVEL_EMPTY"),
		TotalMember: int32(len(memberWithoutLevel)),
		Members:     memberWithoutLevel,
	})

	for _, level := range levelInfos {
		level.Status = "" // ignore status return
		if memberByLevelId[level.XId] == nil {
			memberByLevelId[level.XId] = []*BusinessMemberDetail{} // level without member
		}
		members := memberByLevelId[level.XId]
		memberByLevel = append(memberByLevel, &BusinessLevelDetail{
			BusinessLevel: level,
			NameText:      lib.ConvertStringToServiceText(level.Name),
			TotalMember:   int32(len(members)),
			Members:       members,
		})
	}
	return memberByLevel
}
