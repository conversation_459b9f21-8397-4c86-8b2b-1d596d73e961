package business

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcProxy/grpcWebsocket"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

func SendNotificationSuccess(userId, titleKey, bodyKey, navigateTo string, bodyParams ...interface{}) {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"language": 1})
	lang := globalConstant.LANG_VI
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      userId,
		Type:        25,
		Title:       localization.T(lang, titleKey),
		Description: localization.T(lang, bodyKey, bodyParams...),
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	title := localization.GetLocalizeObject(titleKey)
	body := localization.GetLocalizeObject(bodyKey, bodyParams...)
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type: 25,
	}
	if navigateTo != "" {
		notify.NavigateTo = navigateTo
		payload.NavigateTo = navigateTo
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: userId, Language: globalConstant.LANG_EN}}
	lib.SendNotification([]interface{}{notify}, userIds, title, body, payload, "")
	wsMessage, err := grpcWebsocket.SendSocketNotification(local.ISO_CODE, []interface{}{notify}, cfg.GRPC_Websocket_Service_URL, userIds, payload.Type, title, body)
	if err != nil {
		local.Logger.Error(lib.ERROR_WEBSOCKET_ERROR.ErrorCode,
			zap.Error(err),
			zap.Any("body", wsMessage),
		)
	}
}

func CheckUsersIsBusiness(askerIds []string) map[string]*modelBusiness.Business {
	var business []*modelBusiness.Business
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": askerIds}, "status": bson.M{"$in": []string{globalConstant.BUSINESS_STATUS_ACTIVE, globalConstant.BUSINESS_STATUS_REGISTERED, globalConstant.BUSINESS_STATUS_VERIFYING}}},
		bson.M{"_id": 1},
		&business,
	)
	if business == nil {
		return nil
	}
	mapBusinessByUserId := make(map[string]*modelBusiness.Business)
	for _, v := range business {
		mapBusinessByUserId[v.XId] = v
	}
	return mapBusinessByUserId
}
