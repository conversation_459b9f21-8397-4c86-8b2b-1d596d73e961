package revokeMembersLevel

import (
	"context"
	"fmt"
	"strings"

	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
)

func processRevokeMembers(businessId string, levelIds []string, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember) {
	listMemberTopupFail := []string{}
	for _, levelId := range levelIds {
		members := mapMembersByLevelId[levelId]
		for _, v := range members {
			_, err := revokeMembersGRPC(&modelBusinessMember.MemberRequestPayment{
				BusinessId: businessId,
				MemberId:   v.XId,
				Amount:     v.BPay,
			})
			if err != nil {
				listMemberTopupFail = append(listMemberTopupFail, fmt.Sprintf("memberId: %s - error: %v", v.XId, err))
			}
		}
	}
	if len(listMemberTopupFail) > 0 {
		// Post slack
		msg := fmt.Sprintf("Lỗi thu hồi tiền bPay members business: id: %s - listMemberFail:\n%v", businessId, strings.Join(listMemberTopupFail, "\n"))
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.BUSINESS_PARTNERSHIP_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	} else {
		pkgBusiness.SendNotificationSuccess(businessId, "BUSINESS_REVOKE_SUCCESS_TITLE", "BUSINESS_REVOKE_SUCCESS_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS)
	}
}

func revokeMembersGRPC(requestData *modelBusinessMember.MemberRequestPayment) (result *response.Response, err error) {
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	if err != nil {
		return nil, err
	}
	defer connect.Close()
	response, err := client.BusinessRevokeMember(context.Background(), requestData)
	if err != nil {
		return nil, err
	}
	return response, nil
}
