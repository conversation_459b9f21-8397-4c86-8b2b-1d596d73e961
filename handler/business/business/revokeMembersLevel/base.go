package revokeMembersLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func RevokeMembersLevel(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	// Validate data request
	if errCode := validateRequest(reqBody); errCode != nil {
		return errCode
	}
	// Get business info
	businessInfo, errCode := getBusinessInfo(reqBody.BusinessId)
	if errCode != nil {
		return errCode
	}
	// Get members by level
	membersMap, errCode := getMembersByLevel(reqBody.LevelIds)
	if errCode != nil {
		return errCode
	}
	go processRevokeMembers(businessInfo.XId, reqBody.LevelIds, membersMap)

	return nil
}
