package getTotalRevokeBPay

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	"go.mongodb.org/mongo-driver/bson"
)

func getBusinessInfo(businessId string) (*modelBusiness.Business, *globalResponse.ResponseErrorCode) {
	// Get business Info
	businessData, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": businessId}, bson.M{"_id": 1, "bPay": 1})
	if businessData == nil {
		return nil, &lib.ERROR_BUSINESS_NOT_FOUND
	}
	return businessData, nil
}
