package getTotalRevokeBPay

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"go.mongodb.org/mongo-driver/bson"
)

func getTotalRevoke(levelIds []string) float64 {
	// Get members by level
	members, _ := member.GetMembers(bson.M{"levelId": bson.M{"$in": levelIds}, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}, bson.M{"_id": 1, "levelId": 1, "bPay": 1})
	totalRevoke := float64(0)
	for _, v := range members {
		totalRevoke += v.BPay
	}

	return totalRevoke
}
