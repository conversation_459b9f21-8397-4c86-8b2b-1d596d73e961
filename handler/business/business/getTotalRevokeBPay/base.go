package getTotalRevokeBPay

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetTotalRevokeBPay(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	if errCode := validateRequest(reqBody); errCode != nil {
		return nil, errCode
	}
	// Get business info
	businessInfo, errCode := getBusinessInfo(reqBody.BusinessId)
	if errCode != nil {
		return nil, errCode
	}
	// Get total revoke
	bPayRevoke := getTotalRevoke(reqBody.LevelIds)

	return map[string]interface{}{
		"bPay":       businessInfo.BPay,
		"bPayRevoke": bPayRevoke,
		"remain":     businessInfo.BPay + bPayRevoke,
	}, nil
}
