package getBusinessTopUpSettingInfo

import (
	"math"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
)

func mapDataTopUpSetting(business *modelBusiness.Business, totalAmount float64) interface{} {
	var isNotEnoughMoney bool = false
	var amount float64 = 0
	if business.BPay >= totalAmount {
		isNotEnoughMoney = false
		amount = 0
	} else {
		isNotEnoughMoney = true
		amount = totalAmount - business.BPay
	}
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	nextTime := globalLib.ParseDateFromTimeStamp(business.GetTopUpSetting().GetNextTime(), local.TimeZone)
	remainingDay := nextTime.Sub(currentTime).Hours() / 24

	return map[string]interface{}{
		"isNotEnoughMoney": isNotEnoughMoney,
		"amount":           amount,
		"nextTime":         nextTime.Format(globalConstant.LAYOUT_DATE),
		"remainingDay":     math.Ceil(remainingDay),
		"dayInMonth":       business.GetTopUpSetting().GetDayInMonth(),
	}
}
