package getBusinessTopUpSettingInfo

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func getTotalAmountTopup(businessId string) (float64, *globalResponse.ResponseErrorCode) {
	// Get level info
	levelInfos := level.GetLevels(bson.M{"businessId": businessId, "status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE}, bson.M{"_id": 1, "name": 1, "amount": 1})
	if len(levelInfos) == 0 {
		return 0, &lib.ERROR_LEVEL_INVALID
	}
	mapAmountByLevelId := map[string]float64{}
	for _, v := range levelInfos {
		mapAmountByLevelId[v.XId] = v.Amount
	}
	// Get members by level
	members, _ := member.GetMembers(bson.M{"businessId": businessId, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}, bson.M{"_id": 1, "levelId": 1})
	totalAmount := float64(0)
	for _, v := range members {
		totalAmount += mapAmountByLevelId[v.LevelId]
	}
	return totalAmount, nil
}
