package getBusinessTopUpSettingInfo

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

type MapMemberLevelAmount struct {
	XId    string
	Amount float64
}

func GetBusinessTopUpSettingInfo(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	if errCode := validateRequest(reqBody); errCode != nil {
		return nil, errCode
	}
	// Get business info
	businessInfo, errCode := getBusinessInfo(reqBody.BusinessId)
	if errCode != nil {
		return nil, errCode
	}
	if businessInfo.GetTopUpSetting() == nil || businessInfo.GetTopUpSetting().GetStatus() != globalConstant.BUSINESS_STATUS_ACTIVE {
		return nil, nil
	}
	// Get members by level
	totalAmount, errCode := getTotalAmountTopup(reqBody.BusinessId)
	if errCode != nil {
		return errCode, nil
	}
	// Get business top up setting
	return mapDataTopUpSetting(businessInfo, totalAmount), nil
}
