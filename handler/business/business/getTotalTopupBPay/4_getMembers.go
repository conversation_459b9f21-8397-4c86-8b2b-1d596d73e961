package getTotalTopupBPay

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

func getMembersByLevel(levelIds []string) (map[string][]*modelBusinessMember.BusinessMember, *globalResponse.ResponseErrorCode) {
	// Get members by level
	members, _ := member.GetMembers(bson.M{"levelId": bson.M{"$in": levelIds}, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}, bson.M{"_id": 1, "levelId": 1})
	// return membersData, nil
	mapMembersByLevelId := map[string][]*modelBusinessMember.BusinessMember{}
	for _, v := range members {
		if _, ok := mapMembersByLevelId[v.LevelId]; !ok {
			mapMembersByLevelId[v.LevelId] = []*modelBusinessMember.BusinessMember{}
		}
		mapMembersByLevelId[v.LevelId] = append(mapMembersByLevelId[v.LevelId], v)
	}

	return mapMembersByLevelId, nil
}
