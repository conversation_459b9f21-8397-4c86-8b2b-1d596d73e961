package getTotalTopupBPay

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetTotalTopupBPay(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	if errCode := validateRequest(reqBody); errCode != nil {
		return nil, errCode
	}
	// Get business info
	businessInfo, errCode := getBusinessInfo(reqBody.BusinessId)
	if errCode != nil {
		return nil, errCode
	}
	// Get level info
	levelInfos, errCode := getLevelInfo(reqBody.LevelIds)
	if errCode != nil {
		return nil, errCode
	}
	// Get members by level
	membersMap, errCode := getMembersByLevel(reqBody.LevelIds)
	if errCode != nil {
		return nil, errCode
	}
	//checkBusinessbPay
	bPayTopup := getBPayTopup(levelInfos, membersMap)

	return map[string]interface{}{
		"bPay":      businessInfo.BPay,
		"bPayTopup": bPayTopup,
		"remain":    businessInfo.BPay - bPayTopup,
	}, nil
}
