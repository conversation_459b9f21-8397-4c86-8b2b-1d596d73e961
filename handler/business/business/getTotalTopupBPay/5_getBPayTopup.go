package getTotalTopupBPay

import (
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
)

func getBPayTopup(levelInfos []*modelBusinessLevel.BusinessLevel, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember) float64 {
	// Check business bpay
	var totalAmount float64 = 0
	for _, level := range levelInfos {
		totalAmount += (level.Amount * float64(len(mapMembersByLevelId[level.XId])))
	}
	return totalAmount
}
