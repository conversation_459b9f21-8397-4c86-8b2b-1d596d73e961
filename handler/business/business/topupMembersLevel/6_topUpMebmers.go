package topupMembersLevel

import (
	"context"
	"fmt"
	"strings"

	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
)

func processTopUpMembers(businessId string, levelsInfo []*modelBusinessLevel.BusinessLevel, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember, keyCache string) {
	defer cfg.RedisCache.Del(context.Background(), keyCache)
	listMemberTopupFail := []string{}
	for _, levelInfo := range levelsInfo {
		members := mapMembersByLevelId[levelInfo.XId]
		for _, v := range members {
			_, err := topUpMemberGRPC(&modelBusinessMember.MemberRequestPayment{
				BusinessId: businessId,
				MemberId:   v.XId,
				Amount:     levelInfo.Amount,
			})
			if err != nil {
				listMemberTopupFail = append(listMemberTopupFail, fmt.Sprintf("memberId: %s - amount: %f - error: %v", v.XId, levelInfo.Amount, err))
			}
		}
	}
	if len(listMemberTopupFail) > 0 {
		// Post slack
		msg := fmt.Sprintf("Lỗi nạp tiền bPay cho members business: %s - listMemberTopupFail:\n%v", businessId, strings.Join(listMemberTopupFail, "\n"))
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.BUSINESS_PARTNERSHIP_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	} else {
		pkgBusiness.SendNotificationSuccess(businessId, "BUSINESS_TOPUP_SUCCESS_TITLE", "BUSINESS_TOPUP_SUCCESS_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS)
	}
}

func topUpMemberGRPC(requestData *modelBusinessMember.MemberRequestPayment) (result *response.Response, err error) {
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	if err != nil {
		return nil, err
	}
	defer connect.Close()
	response, err := client.BusinessTopupMember(context.Background(), requestData)
	if err != nil {
		return nil, err
	}
	return response, nil
}
