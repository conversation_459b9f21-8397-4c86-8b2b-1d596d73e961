package topupMembersLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
)

func checkBusinessbPay(business *modelBusiness.Business, levelInfos []*modelBusinessLevel.BusinessLevel, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember) *globalResponse.ResponseErrorCode {
	// Check business bpay
	var totalAmount float64 = 0
	for _, level := range levelInfos {
		totalAmount += (level.Amount * float64(len(mapMembersByLevelId[level.XId])))
	}
	if business.BPay < totalAmount {
		return &lib.ERROR_BPAY_INSUFFICIENT
	}
	return nil
}
