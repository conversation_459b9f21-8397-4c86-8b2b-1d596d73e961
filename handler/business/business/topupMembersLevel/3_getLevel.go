package topupMembersLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"go.mongodb.org/mongo-driver/bson"
)

func getLevelInfo(LevelIds []string) ([]*modelBusinessLevel.BusinessLevel, *globalResponse.ResponseErrorCode) {
	// Get level info
	levelInfos := level.GetLevels(bson.M{"_id": bson.M{"$in": LevelIds}, "status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE}, bson.M{"_id": 1, "name": 1, "amount": 1})
	if len(levelInfos) == 0 {
		return nil, &lib.ERROR_LEVEL_INVALID
	}
	if len(LevelIds) != len(levelInfos) {
		return nil, &lib.ERROR_LEVEL_INVALID
	}
	return levelInfos, nil
}
