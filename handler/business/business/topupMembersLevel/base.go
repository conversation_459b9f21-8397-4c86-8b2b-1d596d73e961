package topupMembersLevel

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func TopupMembersLevel(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	// Check topup processing
	keyCache := fmt.Sprintf("topupMembersLevel-%s", reqBody.BusinessId)
	if cfg.RedisCache != nil {
		var ctx = context.Background()
		if isProcess, err := cfg.RedisCache.Get(ctx, keyCache).Bool(); err == nil && isProcess {
			return &lib.HAVE_PROCESS_TOPUP_RUNNING
		}
		cfg.RedisCache.Set(ctx, keyCache, true, 1*time.Hour)
	}
	// Validate data request
	if errCode := validateRequest(reqBody); errCode != nil {
		return errCode
	}
	// Get business info
	businessInfo, errCode := getBusinessInfo(reqBody.BusinessId)
	if errCode != nil {
		return errCode
	}
	// Get level info
	levelInfos, errCode := getLevelInfo(reqBody.LevelIds)
	if errCode != nil {
		return errCode
	}
	// Get members by level
	membersMap, errCode := getMembersByLevel(reqBody.LevelIds)
	if errCode != nil {
		return errCode
	}
	//checkBusinessbPay
	errCode = checkBusinessbPay(businessInfo, levelInfos, membersMap)
	if errCode != nil {
		return errCode
	}
	go processTopUpMembers(businessInfo.XId, levelInfos, membersMap, keyCache)

	return nil
}
