package topupMembersLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validateRequest(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if reqBody.BusinessId == "" {
		return &lib.ERROR_BUSINESS_REQUIRED
	}
	if len(reqBody.LevelIds) == 0 {
		return &lib.ERROR_LEVEL_REQUIRED
	}
	return nil
}
