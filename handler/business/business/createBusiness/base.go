package createBusiness

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func CreateBusiness(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. get user info
	user, errCode := getUserInfo(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 3. check conflict taxCode and email
	errCode = checkConflictData(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 4. init business data
	businessData, errCode := initBusinessData(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 5. insert business
	errCode, err := insertBusinessData(businessData)
	if errCode != nil {
		return errCode, err
	}

	// 6. post to slack
	message := fmt.Sprintf("KH [%s] đã đăng ký tạo tài khoản Doanh nghiệp. Xem chi tiết: http://ares.btaskee.com/partner-management/account-business/%s/level", user.Phone, businessData.XId)
	globalLib.PostToSlack(cfg.SlackToken, globalConstant.BUSINESS_PARTNERSHIP_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)

	return nil, nil
}
