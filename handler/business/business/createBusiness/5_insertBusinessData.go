package createBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
)

func insertBusinessData(business *business.Business) (*globalResponse.ResponseErrorCode, error) {
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], business)
	if err != nil {
		return &lib.ERROR_INSERT_FAILED, err
	}
	return nil, nil
}
