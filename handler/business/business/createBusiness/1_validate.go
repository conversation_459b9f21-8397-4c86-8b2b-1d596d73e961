package createBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Name == "" {
		return &lib.ERROR_NAME_REQUIRED
	}
	if reqBody.Email == "" {
		return &lib.ERROR_EMAIL_REQUIRED
	}
	return nil
}
