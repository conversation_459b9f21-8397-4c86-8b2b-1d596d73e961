package createBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func checkConflictData(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	isExistBusiness, _ := globalDataAccess.IsExistById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], reqBody.UserId)
	if isExistBusiness {
		return &lib.ERROR_BUSINESS_EXIST
	}
	isMemberBusiness, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"userId": reqBody.UserId, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE})
	if isMemberBusiness {
		return &lib.ERROR_USER_IS_MEMBER_OF_ANOTHER_BUSINESS
	}
	query := bson.M{"email": reqBody.Email}
	isExistEmail, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], query)
	if isExistEmail {
		return &lib.ERROR_EMAIL_EXIST
	}

	query = bson.M{"taxCode": reqBody.TaxCode}
	isExistTaxCode, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], query)
	if isExistTaxCode {
		return &lib.ERROR_TAX_CODE_EXIST
	}

	return nil
}
