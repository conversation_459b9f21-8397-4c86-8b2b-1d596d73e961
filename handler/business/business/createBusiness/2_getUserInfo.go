package createBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUserInfo(reqBody *model.BusinessRequest) (user *modelUser.Users, errCode *globalResponse.ResponseErrorCode) {
	user, _ = modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"phone": 1})
	if user == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}
	return user, nil
}
