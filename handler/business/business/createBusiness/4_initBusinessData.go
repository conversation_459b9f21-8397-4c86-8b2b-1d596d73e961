package createBusiness

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
)

func initBusinessData(reqBody *model.BusinessRequest) (*business.Business, *globalResponse.ResponseErrorCode) {
	businessData := &business.Business{
		XId:          reqBody.UserId,
		Name:         reqBody.Name,
		TaxCode:      reqBody.TaxCode,
		Email:        reqBody.Email,
		Sector:       reqBody.Sector,
		BusinessSize: reqBody.BusinessSize,
		Address:      reqBody.Address,
		Status:       globalConstant.BUSINESS_STATUS_REGISTERED,
		CreatedAt:    globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	businessLicense := []*business.BusinessLicense{}
	if len(reqBody.BusinessLicense) > 0 {
		for _, license := range reqBody.BusinessLicense {
			businessLicense = append(businessLicense, &business.BusinessLicense{
				Name: license.Name,
				Url:  license.Url,
			})
		}
	}
	businessData.BusinessLicense = businessLicense
	return businessData, nil
}
