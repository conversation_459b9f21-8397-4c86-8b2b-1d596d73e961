package removeBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func RemoveBusinessLevel(reqBody *model.BusinessRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. get level info
	levelInfo, errCode, err := getLevelInfo(reqBody)
	if errCode != nil {
		return errCode, err
	}

	// 3. remove level
	errCode, err = removeLevel(reqBody, levelInfo)
	if errCode != nil {
		return errCode, err
	}

	return nil, nil
}
