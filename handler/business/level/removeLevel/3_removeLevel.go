package removeBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"go.mongodb.org/mongo-driver/bson"
)

func removeLevel(reqBody *model.BusinessRequest, levelInfo *businessLevel.BusinessLevel) (*globalResponse.ResponseErrorCode, error) {
	// update level status and changeHistories
	queryUpdate := bson.M{
		"$set": bson.M{
			"status": globalConstant.BUSINESS_LEVEL_STATUS_DELETED,
		},
		"$push": bson.M{
			"changeHistories": bson.M{
				"key":       "REMOVE_LEVEL",
				"createdBy": reqBody.BusinessId,
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
				"content": bson.M{
					"oldStatus": levelInfo.Status,
					"newStatus": globalConstant.BUSINESS_LEVEL_STATUS_DELETED,
				},
			},
		},
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], reqBody.LevelId, queryUpdate)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}

	// update member: remove level id
	queryUpdate = bson.M{
		"$unset": bson.M{
			"levelId": 1,
		},
		"$push": bson.M{
			"changeHistories": bson.M{
				"key":       "REMOVE_LEVEL",
				"createdBy": reqBody.BusinessId,
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
				"content": bson.M{
					"oldLevelId": levelInfo.XId,
					"newLevelId": nil,
				},
			},
		},
	}
	_, err = globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{"levelId": reqBody.LevelId}, queryUpdate)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}
	return nil, nil
}
