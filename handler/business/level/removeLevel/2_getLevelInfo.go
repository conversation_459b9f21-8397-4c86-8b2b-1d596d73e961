package removeBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"go.mongodb.org/mongo-driver/bson"
)

func getLevelInfo(reqBody *model.BusinessRequest) (*businessLevel.BusinessLevel, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{
		"_id":    reqBody.LevelId,
		"status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	}
	var levelInfo *businessLevel.BusinessLevel
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], query, bson.M{"status": 1, "businessId": 1}, &levelInfo)
	if err != nil || levelInfo == nil {
		return nil, &lib.ERROR_BUSINESS_LEVEL_NOT_FOUND, err
	}
	if levelInfo.BusinessId != reqBody.BusinessId {
		return nil, &lib.ERROR_RESOURCE_NOT_BELONG_TO_BUSINESS, nil
	}
	return levelInfo, nil, nil
}
