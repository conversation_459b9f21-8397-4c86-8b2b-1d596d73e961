package createBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
)

func insertLevel(reqBody *model.BusinessRequest) (level *businessLevel.BusinessLevel, errCode *globalResponse.ResponseErrorCode, err error) {
	businessLevel := &businessLevel.BusinessLevel{
		XId:        globalLib.GenerateObjectId(),
		Name:       reqBody.Name,
		BusinessId: reqBody.BusinessId,
		Amount:     *reqBody.Amount,
		Status:     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
		CreatedAt:  globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], businessLevel)
	if err != nil {
		return nil, &lib.ERROR_INSERT_FAILED, err
	}
	return businessLevel, nil, nil
}
