package createBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func CreateLevel(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. check business exist
	errCode = checkBusinessExist(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. insert level
	level, errCode, err := insertLevel(reqBody)
	if errCode != nil {
		return nil, errCode, err
	}

	return map[string]interface{}{
		"detail": map[string]interface{}{
			"_id":    level.XId,
			"name":   level.Name,
			"amount": level.Amount,
		},
	}, nil, nil
}
