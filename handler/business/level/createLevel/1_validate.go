package createBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if reqBody.BusinessId == "" {
		return &lib.ERROR_BUSINESS_ID_REQUIRED
	}
	if reqBody.Name == "" {
		return &lib.ERROR_NAME_REQUIRED
	}
	if reqBody.Amount == nil || *reqBody.Amount < 0 {
		return &lib.ERROR_BUSINESS_LEVEL_AMOUNT_INVALID
	}
	return nil
}
