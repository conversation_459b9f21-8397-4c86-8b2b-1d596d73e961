package createBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func checkBusinessExist(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	isExist, _ := globalDataAccess.IsExistById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], reqBody.BusinessId)
	if !isExist {
		return &lib.ERROR_BUSINESS_NOT_FOUND
	}
	return nil
}
