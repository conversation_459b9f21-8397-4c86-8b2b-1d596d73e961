package updateBusinessLevel

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"go.mongodb.org/mongo-driver/bson"
)

func updateLevelInfo(reqBody *model.BusinessRequest, levelInfo *businessLevel.BusinessLevel) (*globalResponse.ResponseErrorCode, error) {
	querySet := bson.M{}
	contentChange := bson.M{}
	if reqBody.Name != "" {
		querySet["name"] = reqBody.Name
		contentChange["oldName"] = levelInfo.Name
		contentChange["newName"] = reqBody.Name
	}
	if reqBody.Amount != nil {
		querySet["amount"] = *reqBody.Amount
		contentChange["oldAmount"] = levelInfo.Amount
		contentChange["newAmount"] = *reqBody.Amount
	}

	queryUpdate := bson.M{
		"$set": querySet,
		"$push": bson.M{
			"changeHistories": bson.M{
				"key":       "UPDATE_LEVEL_INFO",
				"createdBy": reqBody.BusinessId,
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
				"content":   contentChange,
			},
		},
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], reqBody.LevelId, queryUpdate)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}

	return nil, nil
}
