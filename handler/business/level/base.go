package level

import (
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"go.mongodb.org/mongo-driver/bson"
)

func GetLevels(query, fields bson.M) []*modelBusinessLevel.BusinessLevel {
	var levels []*modelBusinessLevel.BusinessLevel
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], query, fields, &levels)
	return levels
}

func GetLevel(query, fields bson.M) *modelBusinessLevel.BusinessLevel {
	var level *modelBusinessLevel.BusinessLevel
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], query, fields, &level)
	return level
}

func GetLevelsInfoByBusinessId(businessId string) interface{} {
	levels, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], bson.M{"businessId": businessId, "status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE}, bson.M{})
	levelIds := []string{}
	for _, v := range levels {
		levelIds = append(levelIds, cast.ToString(v["_id"]))
	}
	if len(levelIds) == 0 {
		return nil
	}
	aggregateQuery := []bson.M{
		// Step 1: Match documents with `levelId` in `levelIds`
		{
			"$match": bson.M{
				"levelId": bson.M{"$in": levelIds},
				"status":  globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
			},
		},
		// Step 2: Group by levelId and collect distinct userIds
		{
			"$group": bson.M{
				"_id":             "$levelId",
				"distinctMembers": bson.M{"$addToSet": "$_id"}, // Collect distinct userId
			},
		},
		// Step 3: Project to count distinct members
		{
			"$project": bson.M{
				"levelId":     "$_id",
				"memberCount": bson.M{"$size": "$distinctMembers"}, // Count of unique userId
				"_id":         0,
			},
		},
	}
	var memberCount []map[string]interface{}
	globalDataAccess.Aggregate(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], aggregateQuery, &memberCount)
	mapNumberCountByLevelId := map[string]interface{}{}
	for _, v := range memberCount {
		mapNumberCountByLevelId[cast.ToString(v["levelId"])] = v["memberCount"]
	}
	for _, v := range levels {
		v["memberCount"] = mapNumberCountByLevelId[cast.ToString(v["_id"])]
	}

	return levels
}
