package addMembers

import (
	pkgBusiness "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func addMembersToBusiness(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	memberInfos := reqBody.MemberInfos
	var userIds []string
	for _, v := range memberInfos {
		userIds = append(userIds, v.UserId)
	}
	errorResult := []map[string]interface{}{}
	users, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1})
	mapUserById := make(map[string]bool)
	for _, v := range users {
		mapUserById[v.XId] = true
	}
	businessInfo, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": reqBody.BusinessId}, bson.M{"name": 1})
	if businessInfo == nil {
		return nil, &lib.ERROR_BUSINESS_NOT_FOUND
	}
	// check user is member
	mapMemberByUserId := member.CheckUsersInBusiness(userIds)
	// check user is business
	mapBusinessByUserId := pkgBusiness.CheckUsersIsBusiness(userIds)
	// Get business level
	activeLevels := level.GetLevels(bson.M{"businessId": reqBody.BusinessId, "status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE}, bson.M{"_id": 1, "name": 1})
	mapActiveLevelById := map[string]*modelBusinessLevel.BusinessLevel{}
	for _, v := range activeLevels {
		mapActiveLevelById[v.XId] = v
	}
	for _, v := range memberInfos {
		// check user not exist
		if !mapUserById[v.UserId] {
			errorResult = append(errorResult, map[string]interface{}{
				"phone": v.Phone,
				"error": "USER_NOT_EXIST",
			})
			continue
		}
		// check user is member
		if _, ok := mapMemberByUserId[v.UserId]; ok {
			errorResult = append(errorResult, map[string]interface{}{
				"phone": v.Phone,
				"error": "IS_MEMBER",
			})
			continue
		}
		// check user is business
		if _, ok := mapBusinessByUserId[v.UserId]; ok {
			errorResult = append(errorResult, map[string]interface{}{
				"phone": v.Phone,
				"error": "IS_BUSINESS",
			})
			continue
		}
		// check level
		if _, ok := mapActiveLevelById[v.LevelId]; !ok && v.LevelId != "" {
			errorResult = append(errorResult, map[string]interface{}{
				"phone": v.Phone,
				"error": "LEVEL_INVALID",
			})
			continue
		}
		errCode := addMemberToBusiness(v, reqBody.BusinessId)
		if errCode != nil {
			errorResult = append(errorResult, map[string]interface{}{
				"phone": v.Phone,
				"error": errCode.Message,
			})
		} else {
			pkgBusiness.SendNotificationSuccess(v.UserId, "DIALOG_TITLE_INFORMATION", "BUSINESS_ADD_MEMBER_TO_MEMBER_SUCCESS_BODY", "", businessInfo.Name)
		}
	}
	return errorResult, nil
}

func addMemberToBusiness(memberInfo model.BusinessMemberInfo, businessId string) *globalResponse.ResponseErrorCode {
	// Add member to business
	dataMember := &modelBusinessMember.BusinessMember{
		XId:        globalLib.GenerateObjectId(),
		BusinessId: businessId,
		UserId:     memberInfo.UserId,
		LevelId:    memberInfo.LevelId,
		Status:     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
		BPay:       0,
		CreatedAt:  globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], dataMember)
	if err != nil {
		return &lib.ERROR_INSERT_FAILED
	}
	return nil
}
