package addMembers

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func AddMembers(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	if errCode := validateRequest(reqBody); errCode != nil {
		return nil, errCode
	}
	// Add member to business
	return addMembersToBusiness(reqBody)
}
