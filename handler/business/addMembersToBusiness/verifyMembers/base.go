package verifyMembers

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/level"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func VerifyMembers(reqBody *model.BusinessRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data
	if errCode := validateRequest(reqBody); errCode != nil {
		return nil, errCode
	}
	// Get user info
	userInfo := getAskerStatus(reqBody.Phones, reqBody.BusinessId)
	// Return data
	result := map[string]interface{}{
		"levels": level.GetLevelsInfoByBusinessId(reqBody.BusinessId),
		"users":  userInfo,
	}
	return result, nil
}
