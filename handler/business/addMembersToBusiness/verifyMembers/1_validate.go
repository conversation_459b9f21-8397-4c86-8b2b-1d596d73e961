package verifyMembers

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validateRequest(reqBody *model.BusinessRequest) *globalResponse.ResponseErrorCode {
	if len(reqBody.Phones) == 0 {
		return &lib.ERROR_PHONE_REQUIRED
	}
	if len(reqBody.BusinessId) == 0 {
		return &lib.ERROR_BUSINESS_REQUIRED
	}
	return nil
}
