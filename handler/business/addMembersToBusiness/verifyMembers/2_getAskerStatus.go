package verifyMembers

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/business"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/business/member"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAskerStatus(phones []string, businessId string) interface{} {
	askers, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"phone": bson.M{"$in": phones}}, bson.M{"_id": 1, "phone": 1, "name": 1, "avatar": 1})

	mapAskerByPhone := make(map[string]*modelUser.Users)
	results := []map[string]interface{}{}
	askerIds := []string{}
	for _, v := range askers {
		askerIds = append(askerIds, v.XId)
		mapAskerByPhone[v.Phone] = v
	}
	// check user is member
	mapMemberByUserId := member.CheckUsersInBusiness(askerIds)
	// check user is business
	mapBusinessByUserId := business.CheckUsersIsBusiness(askerIds)
	for _, v := range phones {
		if user, ok := mapAskerByPhone[v]; ok {
			userInfo := map[string]interface{}{
				"_id":    user.XId,
				"phone":  v,
				"name":   user.Name,
				"avatar": user.Avatar,
				"status": "REGISTERED",
			}
			if v, ok := mapMemberByUserId[user.XId]; ok {
				if v.BusinessId == businessId {
					userInfo["status"] = "IS_MEMBER"
				} else {
					userInfo["status"] = "IS_OTHER_MEMBER"
				}
			}
			if v, ok := mapBusinessByUserId[user.XId]; ok {
				if v.XId == businessId {
					userInfo["status"] = "IS_BUSINESS"
				} else {
					userInfo["status"] = "IS_OTHER_BUSINESS"
				}
			}
			results = append(results, userInfo)
		} else {
			results = append(results, map[string]interface{}{
				"phone":  v,
				"status": "NOT_REGISTERED",
			})
		}
	}
	return results
}
