/*
 * @File: viewReward.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
package reward

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelReward "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reward"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Reward by UserId
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetReward(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Get data
	var reward *modelReward.Reward
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REWARD[local.ISO_CODE], bson.M{"userId": reqBody.UserId, "isShow": true, "isViewed": false}, bson.M{}, &reward)
	globalResponse.ResponseSuccess(w, reward)
}
