package getReferralSetting

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAsker(userId string) (*modelUser.Users, error) {
	if userId == "" {
		return nil, nil
	}
	// Get inviter
	fields := bson.M{"phone": 1}
	user, err := modelUser.GetOneById(local.ISO_CODE, userId, fields)
	if err != nil || user == nil {
		return nil, err
	}
	return user, nil
}
