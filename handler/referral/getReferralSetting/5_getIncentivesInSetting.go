package getReferralSetting

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelAskerReferralCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerReferralCampaign"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getIncentivesInCampaign(asker *modelUser.Users, referralCampaign *modelAskerReferralCampaign.AskerReferralCampaign) (map[string]*modelIncentive.Incentive, *globalResponse.ResponseErrorCode, error) {
	// Case not have campaign
	if referralCampaign == nil {
		return nil, nil, nil
	}

	// 1. get all incentiveId from setting
	incentiveIds := []string{}
	for _, v := range referralCampaign.Invitee {
		if v.IncentiveId != "" {
			incentiveIds = append(incentiveIds, v.IncentiveId)
		}
	}
	for _, v := range referralCampaign.Inviter {
		if v.IncentiveId != "" {
			incentiveIds = append(incentiveIds, v.IncentiveId)
		}
	}
	if len(incentiveIds) == 0 {
		return nil, nil, nil
	}

	// 2. get all incentive with the ids from database
	query := bson.M{
		"_id":       bson.M{"$in": incentiveIds},
		"status":    globalConstant.INCENTIVE_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}
	if lib.CheckIsUserTester(asker) {
		delete(query, "isTesting")
	}
	fields := bson.M{"_id": 1, "startDate": 1, "endDate": 1, "from": 1, "codeList": 1, "title": 1, "brandInfo": 1}
	incentives, err := handler.GetIncentives(query, fields)
	if err != nil {
		return nil, &lib.ERROR_INCENTIVE_NOT_FOUND, err
	}

	// 3. add to a map with key is incentiveId
	mapIncentiveById := make(map[string]*modelIncentive.Incentive)
	for _, v := range incentives {
		mapIncentiveById[v.XId] = v
	}
	return mapIncentiveById, nil, nil
}
