package getReferralSetting

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerReferralCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerReferralCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getReferralCampaign(asker *modelUser.Users) (*modelAskerReferralCampaign.AskerReferralCampaign, error) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.REFERRAL_CAMPAIGN_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}
	if lib.CheckIsUserTester(asker) {
		delete(query, "isTesting")
	}
	fields := bson.M{}
	return handler.GetAskerReferralCampaign(query, fields)
}
