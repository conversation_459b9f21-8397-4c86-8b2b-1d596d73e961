package getReferralSetting

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerReferralCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerReferralCampaign"
	modelAskerReferralSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerReferralSetting"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
)

func updateIncentivesInformation(referralSetting *modelAskerReferralSetting.AskerReferralSetting, referralCampaign *modelAskerReferralCampaign.AskerReferralCampaign, mapIncentiveById map[string]*modelIncentive.Incentive) map[string]interface{} {
	result := make(map[string]interface{})
	if referralSetting != nil && referralSetting.Image != "" {
		result["image"] = referralSetting.Image
	}
	if referralCampaign != nil && referralCampaign.Image != "" {
		result["image"] = referralCampaign.Image
	}

	// Create field gift
	gifts := []map[string]interface{}{}

	// Get list gift for inviter
	inviterGifts := []map[string]interface{}{}
	if referralSetting != nil && referralSetting.Inviter != nil {
		inviterGifts = append(inviterGifts, getDefaultGiftsInfo(referralSetting.Inviter)...)
	}
	if referralCampaign != nil && len(referralCampaign.Inviter) > 0 {
		bonusGifts := getVouchersFromSetting(referralCampaign.Inviter, mapIncentiveById)
		inviterGifts = append(inviterGifts, bonusGifts...)
	}
	if len(inviterGifts) > 0 {
		giftForYou := map[string]interface{}{
			"title": map[string]interface{}{
				"vi": "Quà dành cho bạn",
				"en": "Gifts for you",
				"ko": "당신을 위한 선물",
				"th": "ของขวัญสำหรับคุณ",
				"id": "Gifts for you",
			},
			"description": map[string]interface{}{
				"vi": "Nhận ngay ưu đãi khi bạn bè hoàn thành công việc đầu tiên.",
				"en": "Get instant offers when your friends done the first task booking",
				"ko": "친구가 첫  예약을 완료 시 즉시 쿠폰을 드려요. ",
				"th": "เมื่อเพื่อนของคุณใช้บริการครั้งแรกสำเร็จ เตรียมรับส่วนลดได้เลย",
				"id": "Get instant offers when your friends done the first task booking",
			},
			"vouchers": inviterGifts,
		}
		gifts = append(gifts, giftForYou)
	}

	// Get list gift for invitee
	inviteeGifts := []map[string]interface{}{}
	if referralSetting != nil && referralSetting.Invitee != nil {
		inviteeGifts = append(inviteeGifts, getDefaultGiftsInfo(referralSetting.Invitee)...)
	}
	if referralCampaign != nil && len(referralCampaign.Invitee) > 0 {
		bonusGifts := getVouchersFromSetting(referralCampaign.Invitee, mapIncentiveById)
		inviteeGifts = append(inviteeGifts, bonusGifts...)
	}
	if len(inviteeGifts) > 0 {
		giftForYourFriend := map[string]interface{}{
			"title": map[string]interface{}{
				"vi": "Quà dành cho bạn bè",
				"en": "Gifts for your friends",
				"ko": "친구를 위한 선물",
				"th": "ของขวัญสำหรับเพื่อนคุณ",
				"id": "Gifts for your friends",
			},
			"description": map[string]interface{}{
				"vi": "Nhận ngay các ưu đãi khi bạn bè đăng ký tài khoản thành công",
				"en": "Receive offers immediately when your friends successfully register for an account",
				"ko": "친구가 계정이 성공적으로 등록되면 할인 쿠폰을 즉시 받습니다.",
				"th": "รับส่วนลดทันทีเมื่อเพื่อนของคุณสมัครบัญชีสำเร็จ",
				"id": "Receive offers immediately when your friends successfully register for an account",
			},
			"vouchers": inviteeGifts,
		}
		gifts = append(gifts, giftForYourFriend)
	}

	result["gifts"] = gifts
	return result
}

func getVouchersFromSetting(gifts []*modelAskerReferralCampaign.AskerReferralCampaignDetail, mapIncentiveById map[string]*modelIncentive.Incentive) []map[string]interface{} {
	vouchers := []map[string]interface{}{}
	for _, v := range gifts {
		voucherItem := make(map[string]interface{})
		voucherItem["amount"] = v.Quantity

		// Type bReward
		incentive, ok := mapIncentiveById[v.IncentiveId]

		// if data incentive is not found || expired -> Ignore this incentive
		if !ok || isExpiredIncentive(incentive) {
			continue
		}

		voucherItem = map[string]interface{}{
			"brandInfo": incentive.BrandInfo,
			"title":     incentive.Title,
			"amount":    v.Quantity,
		}

		// push to result
		vouchers = append(vouchers, voucherItem)
	}
	return vouchers
}

func isExpiredIncentive(incentive *modelIncentive.Incentive) bool {
	// Only accept type codeList or bTaskee
	now := globalLib.GetCurrentTime(local.TimeZone)
	incentiveStartDate := globalLib.ParseDateFromTimeStamp(incentive.StartDate, local.TimeZone)
	incentiveEndDate := globalLib.ParseDateFromTimeStamp(incentive.EndDate, local.TimeZone)
	if now.Before(incentiveStartDate) || now.After(incentiveEndDate) {
		// Case not in incentive time -> Expired
		return true
	}

	// 1. From system
	if incentive.From == globalConstant.GIFT_SOURCE_SYSTEM {
		return false
	}

	// 2. From system with partner + codeList
	if incentive.From == globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER && len(incentive.CodeList) > 0 {
		// Check code list is used up. If code is used up -> Expired
		codeIsUsedUp := true
		for _, v := range incentive.CodeList {
			if !v.IsUsed {
				codeIsUsedUp = false
				break
			}
		}
		// another case of this incentive.from -> Not expired
		return codeIsUsedUp
	}

	// Another case out of 2 above type -> Return expired
	return true
}

func getDefaultGiftsInfo(gift *modelAskerReferralSetting.AskerReferralSettingDetail) []map[string]interface{} {
	if gift == nil || len(gift.DefaultGifts) == 0 {
		return []map[string]interface{}{}
	}

	result := []map[string]interface{}{}
	for _, v := range gift.DefaultGifts {
		result = append(result, map[string]interface{}{
			"brandInfo": v.BrandInfo,
			"title":     v.Title,
			"amount":    v.Amount,
		})
	}
	return result
}
