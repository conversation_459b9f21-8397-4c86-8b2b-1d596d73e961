package getReferralSetting

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/mongo"
)

var cfg = config.GetConfig()

func GetReferralSetting(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate. Không cần đăng nhập
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get asker
	asker, _ := getAsker(reqBody.UserId)

	// 3. get asker referral setting to get default voucher
	referralSetting, err := getAskerReferralSetting()
	if err != nil {
		// Post slack if an error occur
		msg := fmt.Sprintf("GetReferralSetting %s: Error cannot get default referral setting. Error: %v", reqBody.ISOCode, err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	}

	// 2.1 get referral campaing
	referralCampaign, err := getReferralCampaign(asker)
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("GetReferralSetting %s: Error cannot get referral campaign. Error: %v", reqBody.ISOCode, err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	}

	// 3. get incentive if setting has any incentive
	mapIncentiveById, _, _ := getIncentivesInCampaign(asker, referralCampaign)

	// 4. check and map incentive information to setting. Only accept the incentive (source: SYSTEM || SYSTEM_WITH_PARTNER has codeList and has code not used)
	result := updateIncentivesInformation(referralSetting, referralCampaign, mapIncentiveById)
	return result, nil, nil
}
