package getReferralFriends

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetReferralFriends(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get user
	asker, err := getAsker(reqBody.UserId)
	if asker == nil || err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}

	// 3. get list friends
	listFriends := getListReferralFriends(asker)
	return map[string]interface{}{
		"listFriends": listFriends,
	}, nil, nil
}
