package getReferralFriends

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func getListReferralFriends(user *modelUser.Users) []map[string]interface{} {
	// Get list friends
	query := bson.M{"friendCode": bson.M{"$regex": primitive.Regex{Pattern: fmt.Sprintf("^%s$", user.ReferralCode), Options: "i"}}}
	fields := bson.M{"avatar": 1, "phone": 1, "name": 1, "createdAt": 1, "totalTaskDone": 1, "type": 1}
	sortBy := bson.M{"createdAt": -1}
	friends, err := modelUser.GetAll(local.ISO_CODE, query, fields, &globalDataAccessV2.QueryOptions{Sort: sortBy})
	if err != nil || len(fields) == 0 {
		return nil
	}

	listFriends := []map[string]interface{}{}
	for _, u := range friends {
		status := "PROCESSING"
		if u.TotalTaskDone > 0 {
			status = "DONE"
		}
		listFriends = append(listFriends, map[string]interface{}{
			"avatar": u.Avatar,
			"phone":  u.Phone,
			"date":   globalLib.ParseDateFromTimeStamp(u.CreatedAt, local.TimeZone),
			"status": status,
			"name":   u.Name,
			"type":   u.Type,
		})
	}
	return listFriends
}
