/*
 * @File: getSignUpWorkingPlaces.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2020
 * @Author: vinhnt
 */
package workingPlaces

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelWorkingPlaces "gitlab.com/btaskee/go-services-model-v2/grpcmodel/workingPlaces"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Sign Up Working Places by CountryCode
 * @CreatedAt: 25/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: vinhnt
 */
func GetSignUpWorkingPlaces(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetSignUpWorkingPlaces(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get datak
	var result []*modelWorkingPlaces.WorkingPlaces
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_WORKINGPLACES[local.ISO_CODE], bson.M{"countryCode": reqBody.CountryCode}, bson.M{}, &result)
	globalResponse.ResponseSuccess(w, result)
}

func validateGetSignUpWorkingPlaces(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.CountryCode == "" {
		return &lib.ERROR_COUNTRY_CODE_REQUIRED
	}
	return nil
}
