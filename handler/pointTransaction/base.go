/*
 * @File: base.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 05/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package pointTransaction

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get List Point Transaction
 * @CreatedAt: 05/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 12/11/2020
 * @UpdatedBy: vinhnt
 */
func getListPointTransaction(reqPage, reqLimit int, reqUserId, reqType string) ([]map[string]interface{}, error) {
	page := 1
	limit := lib.PAGING_LIMIT
	if reqPage > 0 {
		page = reqPage
	}
	if reqLimit > 0 {
		limit = reqLimit
	}

	var pointTransactions []*modelPointTransaction.PointTransaction
	err := globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE],
		bson.M{"userId": reqUserId, "type": reqType, "point": bson.M{"$gt": 0}},
		bson.M{"source": 1, "type": 1, "point": 1, "createdAt": 1},
		int64(page),
		int64(limit),
		bson.M{"createdAt": -1},
		&pointTransactions,
	)

	results := refactorPointTransactionTitle(pointTransactions)
	return results, err
}

func refactorPointTransactionTitle(pointTransactions []*modelPointTransaction.PointTransaction) []map[string]interface{} {
	results := []map[string]interface{}{}
	for _, v := range pointTransactions {
		var reason *modelService.ServiceText
		if v.Source != nil {
			if v.Source.ServiceText != nil {
				reason = v.Source.ServiceText
			} else if v.Source.Name == "GAME_CAMPAIGN" {
				reason = localization.GetLocalizeObject(v.Source.GameCampaignName)
			}
		}

		item := make(map[string]interface{})
		itemData, _ := json.Marshal(v)
		json.Unmarshal(itemData, &item)
		if item == nil {
			continue
		}

		item["reason"] = reason
		results = append(results, item)
	}
	return results
}
