package getRewardsForBookTask

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetRewardsForBookTask(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validateRequest(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker, isTester, errCode := checkUser(reqBody.UserId)
	if asker == nil || errCode != nil {
		return nil, errCode
	}
	rewards := getRewards(asker, isTester, reqBody)
	return rewards, nil
}
