package getRewardsForBookTask

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func checkUser(askerId string) (*modelUser.Users, bool, *globalResponse.ResponseErrorCode) {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, askerId, bson.M{"phone": 1, "point": 1, "bPoint": 1, "rankInfo": 1, "rankInfoByCountry": 1})
	if asker == nil {
		return nil, false, &lib.ERROR_USER_ID_REQUIRED
	}
	return asker, lib.CheckIsUserTester(asker), nil
}
