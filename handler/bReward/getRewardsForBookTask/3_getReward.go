package getRewardsForBookTask

import (
	"encoding/json"
	"math"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewards(asker *modelUser.Users, isTester bool, reqBody *model.ApiRequest) []map[string]interface{} {
	listIncentive := getIncentive(asker, isTester, reqBody)
	mapFSInfos := bReward.GetMapFSInfos(isTester)
	results := []map[string]interface{}{}

	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, listIncentive)
	for _, v := range listIncentive {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		result := map[string]interface{}{}
		b, _ := json.Marshal(v)
		json.Unmarshal(b, &result)
		if v.Exchange != nil {
			result["point"] = v.Exchange.Point
			delete(result, "exchange")
		}
		if v, ok := mapFSInfos[v.XId]; ok {
			result["point"] = v.Point
			result["originalPoint"] = v.OriginalPoint
			result["discountPercent"] = 100 - math.Round((v.Point*100)/v.OriginalPoint)
		}
		results = append(results, result)
	}
	return results
}

func getIncentive(asker *modelUser.Users, isTester bool, reqBody *model.ApiRequest) []*modelIncentive.Incentive {
	askerPoint := lib.GetAskerPoint(asker)
	askerRank := lib.GetRankAsker(asker)
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"status":         globalConstant.INCENTIVE_STATUS_ACTIVE,
		"startDate":      bson.M{"$lte": now},
		"endDate":        bson.M{"$gte": now},
		"isTesting":      bson.M{"$ne": true},
		"exchange.point": bson.M{"$lte": askerPoint},
		"from":           globalConstant.INCENTIVE_FROM_SYSTEM,
		"$and": []bson.M{
			{
				"$or": []bson.M{
					{"applyFor.service": bson.M{"$exists": false}},
					{"applyFor.service": reqBody.ServiceId},
				},
			},
			{
				"$or": []bson.M{
					{"rankRequire": bson.M{"$exists": false}},
					{"rankRequire": bson.M{"$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]}},
				},
			},
		},
	}
	// incentive.ApplyFor.Service
	if isTester {
		delete(query, "isTesting")
	}
	fields := bson.M{"_id": 1, "title": 1, "image": 1, "exchange": 1, "type": 1, "from": 1, "isRedeemOneTime": 1}

	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}
	var incentives []*modelIncentive.Incentive
	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		fields,
		int64(page),
		int64(limit),
		bson.M{"startDate": -1},
		&incentives,
	)
	return incentives
}
