package bReward

import (
	"math"
	"sort"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelFlashSaleIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/flashSaleIncentive"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

type Respone struct {
	Text    *modelService.ServiceText `json:"text,omitempty"`
	Weight  int32
	Rewards []Reward   `json:"rewards,omitempty"`
	Type    string     `json:"type,omitempty"`
	EndDate *time.Time `json:"endDate,omitempty"`
}

type Reward struct {
	Id              string                    `json:"_id,omitempty"`
	Title           *modelService.ServiceText `json:"title,omitempty"`
	Image           string                    `json:"image,omitempty"`
	Point           float64                   `json:"point,omitempty"`
	IsShowRedeem    bool                      `json:"isShowRedeem,omitempty"`
	BrandText       *modelService.ServiceText `json:"brandText,omitempty"`
	OriginalPoint   float64                   `json:"originalPoint,omitempty"`
	DiscountPercent float64                   `json:"discountPercent,omitempty"`
	From            string                    `json:"from,omitempty"`
}

var BRAND_TEXT_BTASKEE = &modelService.ServiceText{
	Vi: "bTaskee",
	En: "bTaskee",
	Ko: "bTaskee",
	Th: "bTaskee",
	Id: "bTaskee",
}

func GetRawQueryBReward(isTester bool, asker *modelUser.Users) (query bson.M) {
	askerRank := lib.GetRankAsker(asker)
	now := globalLib.GetCurrentTime(local.TimeZone)
	query = bson.M{
		"status":    globalConstant.INCENTIVE_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"isTesting": bson.M{"$ne": true},
		"$or": []bson.M{
			{"rankRequire": bson.M{"$exists": false}},
			{"rankRequire": bson.M{"$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]}},
		},
	}
	if isTester {
		delete(query, "isTesting")
	}
	return
}

func GetQueryRecommendForU(askerRank string, askerPoint float64, isTester bool) (query bson.M) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	query = bson.M{
		"status":         globalConstant.INCENTIVE_STATUS_ACTIVE,
		"startDate":      bson.M{"$lte": now},
		"endDate":        bson.M{"$gte": now},
		"isTesting":      bson.M{"$ne": true},
		"exchange.point": bson.M{"$lte": askerPoint},
		"$or": []bson.M{
			{
				"from": globalConstant.INCENTIVE_FROM_SYSTEM,
			}, {
				"from":        globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
				"rankRequire": bson.M{"$exists": true, "$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]},
			},
		},
	}
	if isTester {
		delete(query, "isTesting")
	}
	return
}

func GetTopDealIncentivePartner() []string {
	var gifts []*modelGift.Gift
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"source":    globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			"createdAt": bson.M{"$gte": currentTime.AddDate(0, 0, -7)},
		},
		bson.M{"_id": 1, "incentiveId": 1},
		&gifts,
	)
	mapCountIncentive := make(map[string]int32)
	for _, v := range gifts {
		if v.IncentiveId != "" {
			mapCountIncentive[v.IncentiveId]++
		}
	}
	var listTopDeals []map[string]interface{}
	for k, v := range mapCountIncentive {
		deal := map[string]interface{}{
			"incentiveId": k,
			"count":       v,
		}
		listTopDeals = append(listTopDeals, deal)
	}
	sort.Slice(listTopDeals, func(i, j int) bool {
		return (listTopDeals[i]["count"].(int32) > listTopDeals[j]["count"].(int32) || (listTopDeals[i]["count"].(int32) == listTopDeals[j]["count"].(int32) && (listTopDeals[i]["incentiveId"].(string) > listTopDeals[j]["incentiveId"].(string))))
	})
	var listIncentiveIds []string
	for _, v := range listTopDeals {
		listIncentiveIds = append(listIncentiveIds, v["incentiveId"].(string))
	}
	return listIncentiveIds
}

func GetFlashSaleIncentive(isTester bool) *modelFlashSaleIncentive.FlashSaleIncentive {
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate": bson.M{"$lte": currentTime},
		"endDate":   bson.M{"$gte": currentTime},
		"status":    globalConstant.FLASH_SALE_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}
	if isTester {
		delete(query, "isTesting")
	}
	return GetDataFlashSale(query, bson.M{"_id": 1, "incentiveInfos": 1, "endDate": 1})
}

func GetDataFlashSale(query, fileds bson.M) *modelFlashSaleIncentive.FlashSaleIncentive {
	var flashSale *modelFlashSaleIncentive.FlashSaleIncentive
	globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_ASKER_FLASH_SALE_INCENTIVE[local.ISO_CODE],
		query,
		fileds,
		&flashSale,
	)
	return flashSale
}

func GetMapFSInfos(isTester bool) map[string]Reward {
	mapDiscountInfos := make(map[string]Reward)
	flashSale := GetFlashSaleIncentive(isTester)
	if flashSale == nil {
		return mapDiscountInfos
	}
	for _, v := range flashSale.IncentiveInfos {
		mapDiscountInfos[v.XId] = Reward{
			OriginalPoint: v.OriginalPoint,
			Point:         v.Point,
		}
	}
	return mapDiscountInfos
}

func MapDataToRewards(incentive *modelIncentive.Incentive, isShowRedeem bool) Reward {
	var rewardPoint float64 = 0
	if incentive.Exchange != nil {
		rewardPoint = incentive.Exchange.Point
	}
	brandText := BRAND_TEXT_BTASKEE
	if incentive.BrandInfo != nil {
		brandText = incentive.BrandInfo.Text
	}
	result := Reward{
		Id:           incentive.XId,
		Title:        incentive.Title,
		Image:        incentive.Image,
		Point:        rewardPoint,
		IsShowRedeem: isShowRedeem,
		BrandText:    brandText,
		From:         incentive.From,
	}
	if incentive.OriginalPoint > 0 {
		result.OriginalPoint = incentive.OriginalPoint
		result.DiscountPercent = 100 - math.Round((result.Point*100)/result.OriginalPoint)
	}

	return result
}

func GetValidReward(myRewards []*modelGift.Gift) []*modelGift.Gift {
	codes := []string{}
	for _, gift := range myRewards {
		if gift.Source == globalConstant.GIFT_SOURCE_SYSTEM && gift.PromotionCode != "" {
			codes = append(codes, gift.PromotionCode)
		}
	}
	var promotionCodes []*modelPromotionCode.PromotionCode
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE],
		bson.M{
			"code":   bson.M{"$in": codes},
			"locked": bson.M{"$ne": true},
		},
		bson.M{"code": 1},
		&promotionCodes,
	)
	mapCodeValid := map[string]bool{}
	for _, code := range promotionCodes {
		mapCodeValid[code.Code] = true
	}
	var validRewards []*modelGift.Gift
	for _, reward := range myRewards {
		// Nếu là voucher của bTaskee thì mới cần kiểm tra code có bị block hay không
		if reward.Source == globalConstant.GIFT_SOURCE_SYSTEM {
			// Trường hợp quà bị block thì không trả về
			if _, ok := mapCodeValid[cast.ToString(reward.PromotionCode)]; !ok {
				continue
			}
		}

		// Trường hợp quà không phải của bTaskee || là quà của bTaskee nhưng code không bị block
		validRewards = append(validRewards, reward)
	}
	return validRewards
}
