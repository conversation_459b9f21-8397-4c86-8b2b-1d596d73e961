package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func sendNotificationToTaskers(asker *modelUsers.Users, taskers []*TaskerInfo) {
	for _, tasker := range taskers {
		language := globalConstant.LANG_VI
		if tasker.Language != "" {
			language = tasker.Language
		}
		var arrayNotification []interface{}

		notifyAsker := &notification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      tasker.XId,
			Type:        25,
			Description: localization.T(language, "RECEIVED_LIXI_NOTIFICATION_TITLE", asker.GetName()),
			CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
			NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_USER_BPAY,
		}
		arrayNotification = append(arrayNotification, notifyAsker)
		title := localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_TITLE")
		body := localization.GetLocalizeObject("RECEIVED_LIXI_NOTIFICATION_TITLE", asker.GetName())
		payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
			Type:       25,
			NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_USER_BPAY,
		}
		userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: tasker.XId, Language: language}}
		lib.SendNotificationV2(arrayNotification, userIds, title, body, payload, "")
	}
}
