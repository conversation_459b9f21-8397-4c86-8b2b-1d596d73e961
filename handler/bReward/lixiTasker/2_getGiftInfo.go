package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"go.mongodb.org/mongo-driver/bson"
)

func getGiftInfo(userId, giftId string) (*gift.Gift, *globalResponse.ResponseErrorCode, error) {
	var giftInfo *gift.Gift
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"_id":    giftId,
			"userId": userId,
		},
		bson.M{"_id": 1, "incentiveId": 1, "type": 1, "used": 1, "expired": 1, "shareGiftInfo": 1},
		&giftInfo,
	)
	if err != nil || giftInfo == nil {
		return nil, &lib.ERROR_GIFT_NOT_FOUND, err
	}
	if giftInfo.Used {
		return nil, &lib.ERROR_GIFT_IS_USED, nil
	}
	if giftInfo.Expired != nil {
		expiredAt := globalLib.ParseDateFromTimeStamp(giftInfo.Expired, local.TimeZone)
		if expiredAt.Before(globalLib.GetCurrentTime(local.TimeZone)) {
			return nil, &lib.ERROR_GIFT_IS_EXPIRED, nil
		}
	}
	if giftInfo.GetShareGiftInfo().GetType() != globalConstant.PROMOTION_TYPE_MONEY || giftInfo.GetShareGiftInfo().GetValue() <= 0 {
		return nil, &lib.ERROR_GIFT_INVALID, nil
	}
	return giftInfo, nil, nil
}
