package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAsker(userId string) (*modelUsers.Users, *globalResponse.ResponseErrorCode, error) {
	asker, err := modelUsers.GetOneById(local.ISO_CODE, userId, bson.M{"name": 1})
	if err != nil || asker == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	return asker, nil, nil
}
