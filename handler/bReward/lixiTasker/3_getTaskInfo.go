package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskInfo(taskId string) (*modelTask.Task, *globalResponse.ResponseErrorCode, error) {
	taskInfo, err := modelTask.GetOne(local.ISO_CODE, bson.M{"_id": taskId}, bson.M{"_id": 1, "acceptedTasker": 1})
	if err != nil || taskInfo == nil {
		return nil, &lib.ERROR_TASK_NOT_FOUND, err
	}

	// Case company
	if len(taskInfo.GetAcceptedTasker()) > 0 && taskInfo.AcceptedTasker[0].GetCompanyId() != "" {
		return nil, &lib.ERROR_CAN_NOT_LIXI_TO_COMPANY, nil
	}
	return taskInfo, nil, nil
}
