package lixiTasker

import (
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

func lixiToTasker(reqBody *model.ApiRequest, asker *modelUsers.Users, giftInfo *gift.Gift, taskers []*TaskerInfo) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. update gift info
	errCode, err := updateGiftInfo(reqBody.GiftId, taskers)
	if errCode != nil {
		return nil, errCode, err
	}

	// 2. topup tasker
	errCode, err = topUpTaskerAccount(asker, taskers, giftInfo, reqBody.TaskId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. send notification
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		sendNotificationToTaskers(asker, taskers)
	}()

	// 4. send message if conversation exists
	wg.Add(1)
	go func() {
		defer wg.Done()
		sendMessageToTaskers(reqBody.UserId, taskers)
	}()

	if cfg.ApplicationMode != "prod" {
		wg.Wait()
	}
	return map[string]interface{}{
		"taskers":         taskers,
		"promotionAmount": giftInfo.GetShareGiftInfo().GetValue(),
	}, nil, nil
}
