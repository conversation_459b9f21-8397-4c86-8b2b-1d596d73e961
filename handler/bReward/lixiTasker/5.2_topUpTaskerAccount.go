package lixiTasker

import (
	"fmt"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func topUpTaskerAccount(asker *modelUsers.Users, taskers []*TaskerInfo, giftInfo *modelGift.Gift, taskId string) (*globalResponse.ResponseErrorCode, error) {
	leaderAmount, taskerAmount := calculateAmountByTasker(giftInfo.GetShareGiftInfo().GetValue(), cast.ToFloat64(len(taskers)))
	for _, tasker := range taskers {
		amount := taskerAmount
		if tasker.IsLeader {
			amount = leaderAmount
		}

		// Reassign amount to tasker
		tasker.PromotionAmount = amount

		// Insert FATransaction
		now_ts := globalLib.GetCurrentTimestamp(local.TimeZone)
		faTransaction := &modelFATransaction.FinancialAccountTransaction{
			XId:         globalLib.GenerateObjectId(),
			UserId:      tasker.XId,
			AccountType: globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION,
			Type:        globalConstant.FA_TRANSACTION_TYPE_DEPOSIT,
			Source: &modelFATransaction.FATransactionSource{
				Name:       globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER,
				Value:      asker.XId,
				TaskId:     taskId,
				GiftId:     giftInfo.XId,
				ReasonText: localization.GetLocalizeObject("ASKER_LIXI_TASKER", asker.Name),
			},
			Amount:    amount,
			Date:      now_ts,
			CreatedAt: now_ts,
			IsoCode:   globalConstant.ISO_CODE_VN,
			Currency:  &globalConstant.CURRENCY_SIGN_VN,
		}
		err := globalDataAccess.InsertOne(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], faTransaction)
		if err != nil {
			return &lib.ERROR_INSERT_FAILED, err
		}

		// Update fAccount
		_, err = globalDataAccess.UpdateOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE],
			tasker.FAccountId,
			bson.M{"$inc": bson.M{"Promotion": faTransaction.GetAmount()}},
		)
		if err != nil {
			// Rollback FATransaction
			errRollback := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], faTransaction.XId)
			if errRollback != nil {
				// Post to slack
				msg := fmt.Sprintf("VN. Tasker can not update FMainAccount with err: %v, try to deleted FATransaction but have this error: %s. UserId: %s. FATransactionId: %s. GiftId: %s", err, errRollback.Error(), tasker.XId, faTransaction.XId, giftInfo.XId)
				globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
				err = fmt.Errorf("%s - rollback err: %s", err.Error(), errRollback.Error())
			}
			return &lib.ERROR_UPDATE_FAILED, err
		}
	}
	return nil, nil
}

func calculateAmountByTasker(promotionValue float64, numberOfTasker float64) (float64, float64) {
	amountForNormalTasker := globalLib.RoundDownMoney(promotionValue/numberOfTasker, local.ISO_CODE)
	amountForLeaderTasker := promotionValue - amountForNormalTasker*(numberOfTasker-1)
	return float64(amountForLeaderTasker), float64(amountForNormalTasker)
}
