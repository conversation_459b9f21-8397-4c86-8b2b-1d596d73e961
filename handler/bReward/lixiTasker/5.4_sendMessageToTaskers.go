package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcProxy/grpcChatServer"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func sendMessageToTaskers(askerId string, taskers []*TaskerInfo) {
	for _, tasker := range taskers {
		conversationId := getConversationId(askerId, tasker.XId)
		if conversationId == "" {
			continue
		}

		image := "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/dJpdupQCq7cc2Mqi6"
		messageToAsker := &modelChatMessage.ChatMessageMessages{
			XId:       globalLib.GenerateObjectId(),
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
			From:      globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
			MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
				Title:  localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_TITLE"),
				Text:   localization.GetLocalizeObject("SEND_LIXI_SUCCESS_MESSAGE_BODY", globalLib.FormatMoney(tasker.PromotionAmount), globalConstant.CURRENCY_SIGN_VN.Code),
				SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER,
				Image:  image,
			},
		}
		messageToTasker := &modelChatMessage.ChatMessageMessages{
			XId:       globalLib.GenerateObjectId(),
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
			From:      globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
			MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
				Title:  localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_TITLE"),
				Text:   localization.GetLocalizeObject("RECEIVED_LIXI_MESSAGE_BODY", globalLib.FormatMoney(tasker.PromotionAmount), globalConstant.CURRENCY_SIGN_VN.Code),
				SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER,
				Image:  image,
			},
		}

		// Insert
		err := insertChatMessage(conversationId, messageToAsker, messageToTasker)
		if err != nil {
			continue
		}

		go sendNotificationNewChat(conversationId, tasker)
		go sendSocketChatMessage(conversationId, messageToAsker, askerId, globalConstant.USER_TYPE_ASKER)
		go sendSocketChatMessage(conversationId, messageToTasker, tasker.XId, globalConstant.USER_TYPE_TASKER)
	}
}

func getConversationId(askerId, taskerId string) string {
	memberIds := []string{askerId, taskerId}
	query := bson.M{
		"members._id": bson.M{"$all": memberIds},
		"members":     bson.M{"$size": len(memberIds)},
	}
	fields := bson.M{"_id": 1, "members": 1, "version": 1}
	chatConversation, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil || chatConversation == nil {
		return ""
	}
	return chatConversation.XId
}

func insertChatMessage(conversationId string, messageToTasker, messageToAsker *modelChatMessage.ChatMessageMessages) error {
	err := pkgChatMessage.SendMessageToConversation(local.ISO_CODE, conversationId, []*modelChatMessage.ChatMessageMessages{messageToAsker, messageToTasker})
	return err
}

func sendNotificationNewChat(conversationId string, tasker *TaskerInfo) {
	title := &modelService.ServiceText{}
	body := localization.GetLocalizeObject("CHAT_NEW_MESSAGE")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       25,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
		ChatId:     conversationId,
	}

	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{
			UserId: tasker.XId, Language: tasker.Language,
		},
	}
	lib.SendNotification([]interface{}{}, userIds, title, body, payload, "")
}

func sendSocketChatMessage(conversationId string, message *modelChatMessage.ChatMessageMessages, userId string, userType string) {
	receivers := []*kafkaEvent.ConversationMessageReceiver{
		{
			UserId: userId,
			Type:   userType,
		},
	}
	grpcChatServer.SendSocketChatMessage(local.ISO_CODE, "", cfg.GRPC_Chat_Server_VN_V3_URL, conversationId, receivers, message, nil)
}
