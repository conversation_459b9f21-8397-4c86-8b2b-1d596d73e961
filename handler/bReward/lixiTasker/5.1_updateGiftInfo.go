package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"go.mongodb.org/mongo-driver/bson"
)

func updateGiftInfo(giftId string, taskers []*TaskerInfo) (*globalResponse.ResponseErrorCode, error) {
	receivers := []*gift.GiftShareGiftInfoReceiver{}
	for _, tasker := range taskers {
		receivers = append(receivers, &gift.GiftShareGiftInfoReceiver{
			UserId: tasker.XId,
			Name:   tasker.Name,
		})
	}

	queryUpdate := bson.M{
		"$set": bson.M{
			"used":                    true,
			"usedAt":                  globalLib.GetCurrentTime(local.TimeZone),
			"shareGiftInfo.receivers": receivers,
		},
	}
	updatedCount, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		giftId,
		queryUpdate,
	)

	if updatedCount == 0 || err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}
	return nil, nil
}
