package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

var cfg = config.GetConfig()

func LixiTasker(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// Get asker
	asker, errCode, err := getAsker(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, err
	}

	// get gift info
	gift, errCode, err := getGiftInfo(reqBody.UserId, reqBody.GiftId)
	if errCode != nil {
		return nil, errCode, err
	}

	// get task info if exists
	var task *modelTask.Task
	if reqBody.TaskId != "" {
		task, errCode, err = getTaskInfo(reqBody.TaskId)
		if errCode != nil {
			return nil, errCode, err
		}
	}

	// Get tasker info
	taskers, errCode, err := getTaskersInfo(reqBody, task)
	if errCode != nil {
		return nil, errCode, err
	}

	// Lixi to tasker
	result, errCode, err := lixiToTasker(reqBody, asker, gift, taskers)
	if errCode != nil {
		return nil, errCode, err
	}

	return result, nil, nil
}
