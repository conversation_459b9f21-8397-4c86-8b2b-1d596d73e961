package lixiTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

type TaskerInfo struct {
	XId             string  `json:"_id,omitempty"`
	Language        string  `json:"-"`
	IsLeader        bool    `json:"-"`
	FAccountId      string  `json:"-"`
	PromotionAmount float64 `json:"-"`
	Name            string  `json:"name,omitempty"`
}

func getTaskersInfo(reqBody *model.ApiRequest, task *modelTask.Task) ([]*TaskerInfo, *globalResponse.ResponseErrorCode, error) {
	// Get taskerIds
	mapTaskerLeader := make(map[string]bool)
	taskerIds := []string{}
	if reqBody.TaskerId != "" {
		taskerIds = []string{reqBody.TaskerId}
	} else {
		for _, v := range task.GetAcceptedTasker() {
			taskerIds = append(taskerIds, v.GetTaskerId())
			mapTaskerLeader[v.GetTaskerId()] = v.GetIsLeader()
		}
	}

	// Get list tasker from taskerIds
	taskers, err := modelUsers.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": taskerIds}}, bson.M{"_id": 1, "language": 1, "fAccountId": 1, "name": 1, "company": 1, "employeeIds": 1})
	if err != nil {
		return nil, &lib.ERROR_TASKER_NOT_FOUND, err
	}
	if len(taskers) != len(taskerIds) {
		return nil, &lib.ERROR_TASKER_NOT_FOUND, nil
	}

	// Convert to taskerInfo
	taskerInfos := []*TaskerInfo{}
	for _, v := range taskers {
		if v.Company != nil || len(v.EmployeeIds) > 0 {
			return nil, &lib.ERROR_CAN_NOT_LIXI_TO_COMPANY, nil
		}

		taskerInfos = append(taskerInfos, &TaskerInfo{
			XId:        v.GetXId(),
			Language:   v.GetLanguage(),
			FAccountId: v.GetFAccountId(),
			IsLeader:   mapTaskerLeader[v.GetXId()],
			Name:       v.GetName(),
		})
	}
	return taskerInfos, nil, nil
}
