package getMyRewardV2

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	"go.mongodb.org/mongo-driver/bson"
)

func getGiftByGroup(reqBody *model.ApiRequest) []map[string]interface{} {
	var myRewards []*modelGift.Gift
	now := globalLib.GetCurrentTime(local.TimeZone)
	before1Month := now.AddDate(0, -1, 0)
	query := bson.M{
		"userId": reqBody.UserId,
		"$and": []bson.M{
			{"$or": []bson.M{
				{"used": bson.M{"$exists": false}},
				{"used": false}},
			},
			{"$or": []bson.M{
				{"expiredIsInfinite": true},
				{"expired": bson.M{"$gte": now}},
				{
					"source":    globalConstant.GIFT_SOURCE_SYSTEM,
					"expired":   bson.M{"$exists": false},
					"createdAt": bson.M{"$gte": before1Month},
				}},
			},
			{
				"from.from": bson.M{"$in": []string{globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION}},
			},
		},
	}

	isUsed, ok := reqBody.Data["isUsed"].(bool)
	if ok && isUsed {
		query = bson.M{
			"userId": reqBody.UserId,
			"$or": []bson.M{
				{"used": true},
				{"expired": bson.M{"$lt": now}},
			},
			"from.from": bson.M{"$in": []string{globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION}},
		}
	}

	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "image": 1, "title": 1, "expired": 1, "content": 1, "note": 1, "brandInfo.image": 1, "brandInfo.text": 1, "social": 1, "office": 1, "promotionCode": 1, "createdAt": 1, "cart.code_link_gift.code_image": 1, "expiredIsInfinite": 1, "used": 1, "usedAt": 1, "source": 1, "incentiveType": 1, "from": 1},
		bson.M{"createdAt": -1},
		&myRewards,
	)
	myRewards = bReward.GetValidReward(myRewards)
	return refactorGiftResultByType(myRewards)
}

func refactorGiftResultByType(gifts []*modelGift.Gift) []map[string]interface{} {
	result := []map[string]interface{}{}
	// the gift has same userVoucherId will have same information
	quantityGiftByVoucherId := map[string]int{}
	quantityGiftByBundleVoucherId := map[string]int{}

	for _, gift := range gifts {
		if gift.From != nil && gift.From.UserVoucherId != "" {
			quantityGiftByVoucherId[gift.From.UserVoucherId]++
		}
		if gift.From != nil && gift.From.From == globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX && gift.From.BundleVoucherTransactionId != "" {
			quantityGiftByBundleVoucherId[gift.From.BundleVoucherTransactionId]++
		}
	}
	isVoucherReturn := map[string]bool{}

	for _, v := range gifts {
		// if gift info is returned with same userVoucherId, then continue
		if v.From != nil && v.From.UserVoucherId != "" && isVoucherReturn[v.From.UserVoucherId] {
			continue
		}
		if v.From != nil && v.From.From == globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX && v.From.BundleVoucherTransactionId != "" && isVoucherReturn[v.From.BundleVoucherTransactionId] {
			continue
		}

		if v.Source == globalConstant.GIFT_SOURCE_SYSTEM && v.Expired == nil {
			expired := globalLib.ParseDateFromTimeStamp(v.CreatedAt, local.TimeZone).AddDate(0, 1, 0)
			v.Expired = globalLib.ParseTimestampFromDate(expired)
		}
		if v.BrandInfo == nil {
			v.BrandInfo = &modelIncentive.IncentiveBrandInfo{
				Text: bReward.BRAND_TEXT_BTASKEE,
			}
		}

		quantity := 1
		if v.From != nil && v.From.UserVoucherId != "" && quantityGiftByVoucherId[v.From.UserVoucherId] > 0 {
			quantity = quantityGiftByVoucherId[v.From.UserVoucherId]
			isVoucherReturn[v.From.UserVoucherId] = true
		}
		if v.From != nil && v.From.From == globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX && v.From.BundleVoucherTransactionId != "" && quantityGiftByBundleVoucherId[v.From.BundleVoucherTransactionId] > 0 {
			quantity = quantityGiftByBundleVoucherId[v.From.BundleVoucherTransactionId]
			isVoucherReturn[v.From.BundleVoucherTransactionId] = true
		}

		vData, _ := json.Marshal(v)
		giftMap := make(map[string]interface{})
		json.Unmarshal(vData, &giftMap)

		giftMap["quantity"] = quantity

		if v.Cart != nil && len(v.Cart.CodeLinkGift) > 0 {
			giftMap["barCode"] = v.Cart.CodeLinkGift[0].CodeImage
		}

		delete(giftMap, "cart")
		giftMap["type"] = "GIFT"
		result = append(result, giftMap)
	}
	return result
}
