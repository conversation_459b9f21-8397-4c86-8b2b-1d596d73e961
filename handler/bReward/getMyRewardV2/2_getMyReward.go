package getMyRewardV2

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	"go.mongodb.org/mongo-driver/bson"
)

func getMyReward(reqBody *model.ApiRequest) ([]map[string]interface{}, bool) {
	var myRewards []*modelGift.Gift
	now := globalLib.GetCurrentTime(local.TimeZone)
	before1Month := now.AddDate(0, -1, 0)
	query := bson.M{
		"userId": reqBody.UserId,
		"$and": []bson.M{
			{"$or": []bson.M{
				{"used": bson.M{"$exists": false}},
				{"used": false}},
			},
			{"$or": []bson.M{
				{"expiredIsInfinite": true},
				{"expired": bson.M{"$gte": now}},
				{
					"source":    globalConstant.GIFT_SOURCE_SYSTEM,
					"expired":   bson.M{"$exists": false},
					"createdAt": bson.M{"$gte": before1Month},
				}},
			},
			{
				"from.from": bson.M{"$nin": []string{globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION}},
			},
		},
	}
	isUsed, ok := reqBody.Data["isUsed"].(bool)
	if ok && isUsed {
		query = bson.M{
			"userId": reqBody.UserId,
			"$or": []bson.M{
				{"used": true},
				{"expired": bson.M{"$lt": now}},
			},
			"from.from": bson.M{"$nin": []string{globalConstant.BUNDLE_VOUCHER_TYPE_SECRET_BOX, globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER, globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION}},
		}
	}
	// Get data
	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}

	// Check filter
	var isReturnGiftOnly bool
	if reqBody.FilterBy != nil {
		isReturnGiftOnly = updateQueryFilter(query, reqBody.FilterBy)
	}

	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "image": 1, "title": 1, "expired": 1, "content": 1, "note": 1, "brandInfo.image": 1, "brandInfo.text": 1, "social": 1, "office": 1, "promotionCode": 1, "createdAt": 1, "cart.code_link_gift.code_image": 1, "expiredIsInfinite": 1, "used": 1, "usedAt": 1, "source": 1, "incentiveType": 1, "from": 1},
		int64(page),
		int64(limit),
		bson.M{"createdAt": -1},
		&myRewards,
	)
	myRewards = bReward.GetValidReward(myRewards)
	return refactorGiftResult(myRewards), isReturnGiftOnly
}

func refactorGiftResult(gifts []*modelGift.Gift) []map[string]interface{} {
	result := []map[string]interface{}{}

	for _, v := range gifts {
		if v.Source == globalConstant.GIFT_SOURCE_SYSTEM && v.Expired == nil {
			expired := globalLib.ParseDateFromTimeStamp(v.CreatedAt, local.TimeZone).AddDate(0, 1, 0)
			v.Expired = globalLib.ParseTimestampFromDate(expired)
		}
		if v.BrandInfo == nil {
			v.BrandInfo = &modelIncentive.IncentiveBrandInfo{
				Text: bReward.BRAND_TEXT_BTASKEE,
			}
		}

		vData, _ := json.Marshal(v)
		giftMap := make(map[string]interface{})
		json.Unmarshal(vData, &giftMap)

		if v.Cart != nil && len(v.Cart.CodeLinkGift) > 0 {
			giftMap["barCode"] = v.Cart.CodeLinkGift[0].CodeImage
		}

		delete(giftMap, "cart")
		giftMap["type"] = "GIFT"
		result = append(result, giftMap)
	}
	return result
}
