package getMyRewardV2

import (
	"sort"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func sortResult(result []map[string]interface{}) {
	sort.SliceStable(result, func(i, j int) bool {
		// checkType
		iEndDate := getEndDate(result[i])
		jEndDate := getEndDate(result[j])
		return iEndDate.Before(jEndDate)
	})
}

func getEndDate(data map[string]interface{}) time.Time {
	if data == nil || data["expired"] == nil {
		return time.Time{}
	}

	// If type == time.Time
	if t, err := cast.ToTimeE(data["expired"]); err == nil {
		return t
	}

	// If type == timestamppb.Timestamp (map[string]interface{})
	if m, err := cast.ToStringMapE(data["expired"]); err == nil {
		endDate_ts := &timestamppb.Timestamp{
			Seconds: cast.ToInt64(m["seconds"]),
			Nanos:   cast.ToInt32(m["nanos"]),
		}
		return globalLib.ParseDateFromTimeStamp(endDate_ts, local.TimeZone)
	}

	return time.Time{}
}
