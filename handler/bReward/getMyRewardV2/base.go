package getMyRewardV2

import (
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetMyReward(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validateRequest(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	giftHasGroup := []map[string]interface{}{}
	if reqBody.Page > 0 && reqBody.Page == 1 {
		giftHasGroup = getGiftByGroup(reqBody)
	}

	// User gift
	myRewards, isReturnRewardOnly := getMyReward(reqBody)
	result := []map[string]interface{}{}
	result = append(result, myRewards...)

	// Marketing campaign
	if isUsed := cast.ToBool(reqBody.Data["isUsed"]); !isUsed && !isReturnRewardOnly {
		marketingCampaignForUser := getMarketingCampaignForUser(reqBody)
		result = append(result, marketingCampaignForUser...)
	}

	sortResult(result)
	if len(giftHasGroup) > 0 {
		result = append(giftHasGroup, result...)
	}
	return result, nil
}
