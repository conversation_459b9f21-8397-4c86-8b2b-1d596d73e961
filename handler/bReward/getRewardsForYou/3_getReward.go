package getRewardsForYou

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewards(asker *modelUser.Users, isTester bool) []bReward.Reward {
	askerPoint := lib.GetAskerPoint(asker)
	askerRank := lib.GetRankAsker(asker)
	query := bReward.GetQueryRecommendForU(askerRank, askerPoint, isTester)
	fields := bson.M{"_id": 1, "title": 1, "image": 1, "exchange": 1, "type": 1, "from": 1, "brandInfo": 1, "isRedeemOneTime": 1}
	incentives := getRecommendForU(asker, query, fields)
	isShowRedeem := true
	if len(incentives) == 0 {
		incentives = getDataWithoutPoint(query, fields)
		isShowRedeem = false
	}
	mapFSInfos := bReward.GetMapFSInfos(isTester)

	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, incentives)

	results := []bReward.Reward{}
	for _, v := range incentives {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		if fsInfo, ok := mapFSInfos[v.XId]; ok && v.Exchange != nil {
			v.OriginalPoint = fsInfo.OriginalPoint
			v.Exchange.Point = fsInfo.Point
		}
		results = append(results, bReward.MapDataToRewards(v, isShowRedeem))
	}
	return results
}

func getRecommendForU(_ *modelUser.Users, query, fields bson.M) []*modelIncentive.Incentive {
	incentives := getIncentive(query, fields)
	return incentives
}

func getDataWithoutPoint(query, fields bson.M) []*modelIncentive.Incentive {
	delete(query, "exchange.point")
	incentives := getIncentive(query, fields)
	return incentives
}

func getIncentive(query, fields bson.M) []*modelIncentive.Incentive {
	var incentives []*modelIncentive.Incentive
	globalDataAccess.GetAllByQueryLimitSort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		fields,
		10,
		bson.M{"startDate": -1},
		&incentives,
	)
	return incentives
}
