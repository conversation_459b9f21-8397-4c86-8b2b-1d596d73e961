package getRewardsForYou

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetRewardsForYou(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validateRequest(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker, isTester := checkUser(reqBody.UserId)
	if asker == nil {
		return nil, nil
	}
	rewards := getRewards(asker, isTester)
	return rewards, nil
}
