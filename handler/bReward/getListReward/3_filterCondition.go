package getListReward

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func filterCondition(filterBy map[string]interface{}, asker *modelUser.Users) (bson.M, map[string]bReward.Reward, []string, string) {
	isTester := lib.CheckIsUserTester(asker)
	lang := globalConstant.LANG_EN
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	query := bReward.GetRawQueryBReward(isTester, asker)
	var topDeals []string
	var keyType string
	queryOr := []bson.M{}
	if filterBy != nil {
		if searchText, ok := filterBy["searchText"].(string); ok {
			queryOr = append(queryOr, bson.M{"$or": []bson.M{
				{fmt.Sprintf("%s.%s", "title", lang): bson.M{"$regex": primitive.Regex{Pattern: searchText, Options: "i"}}},
				{fmt.Sprintf("%s.%s", "brandInfo.text", lang): bson.M{"$regex": primitive.Regex{Pattern: searchText, Options: "i"}}},
			}})
		}
		if incenticeType, ok := filterBy["type"]; ok {
			switch incenticeType {
			case globalConstant.INCENTIVE_TYPE_RECOMMEND_FOR_YOU:
				updateQueryRecommendForU(query, asker, isTester)
			case globalConstant.INCENTIVE_TYPE_TOP_DEAL:
				keyType = globalConstant.INCENTIVE_TYPE_TOP_DEAL
				topDeals = updateQueryTopDeal(query)
			case globalConstant.INCENTIVE_TYPE_FLASH_SALE:
				keyType = globalConstant.INCENTIVE_TYPE_FLASH_SALE
				updateQueryFlashSale(query, isTester)
			case globalConstant.INCENTIVE_TYPE_LIXI_TASKER:
				query["type"] = incenticeType
			default:
				query["type"] = incenticeType
			}
		}
		if categoryName, ok := filterBy["categoryName"]; ok {
			query["categoryName"] = categoryName
		}
		if exchangedPoint, ok := filterBy["exchangedPoint"]; ok {
			query["exchange.point"] = bson.M{"$lte": exchangedPoint}
		}
		if from, ok := filterBy["from"]; ok {
			query["from"] = from
		}
	}
	if len(queryOr) > 0 {
		query["$and"] = queryOr
	}
	mapFSInfos := bReward.GetMapFSInfos(isTester)
	return query, mapFSInfos, topDeals, keyType
}

func updateQueryRecommendForU(query bson.M, asker *modelUser.Users, isTester bool) bson.M {
	askerPoint := lib.GetAskerPoint(asker)
	askerRank := lib.GetRankAsker(asker)
	queryCount := bReward.GetQueryRecommendForU(askerRank, askerPoint, isTester)
	countReward, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], queryCount)
	query["$or"] = []bson.M{
		{
			"from": globalConstant.INCENTIVE_FROM_SYSTEM,
		}, {
			"from":        globalConstant.INCENTIVE_FROM_SYSTEM_WITH_PARTNER,
			"rankRequire": bson.M{"$exists": true, "$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]},
		},
	}
	if countReward > 0 {
		query["exchange.point"] = bson.M{"$lte": askerPoint}
	}
	return query
}

func updateQueryTopDeal(query bson.M) []string {
	incentiveIds := bReward.GetTopDealIncentivePartner()
	query["_id"] = bson.M{"$in": incentiveIds}
	return incentiveIds
}

func updateQueryFlashSale(query bson.M, isTester bool) {
	flashSale := bReward.GetFlashSaleIncentive(isTester)
	if flashSale == nil {
		return
	}
	var incentiveIds []string
	for _, v := range flashSale.IncentiveInfos {
		incentiveIds = append(incentiveIds, v.XId)
	}
	query["_id"] = bson.M{"$in": incentiveIds}
}
