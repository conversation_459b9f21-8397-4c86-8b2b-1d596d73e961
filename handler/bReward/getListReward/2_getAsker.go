package getListReward

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAsker(askerId string) *modelUser.Users {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, askerId, bson.M{"phone": 1, "language": 1, "point": 1, "rankInfo": 1, "bPoint": 1, "rankInfoByCountry": 1})
	return asker
}
