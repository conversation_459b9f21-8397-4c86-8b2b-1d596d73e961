package getListReward

import (
	"encoding/json"
	"math"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getListReward(query bson.M, page, limit int, sortBy bson.M, keyType string, mapFSInfos map[string]bReward.Reward, topDeals []string, asker *modelUser.Users) []map[string]interface{} {
	var listIncentive []*modelIncentive.Incentive
	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "title": 1, "image": 1, "exchange": 1, "startDate": 1, "endDate": 1, "content": 1, "note": 1, "brandInfo": 1, "office": 1, "social": 1, "isRedeemOneTime": 1},
		int64(page),
		int64(limit),
		sortBy,
		&listIncentive,
	)
	results := []map[string]interface{}{}
	if keyType == globalConstant.INCENTIVE_TYPE_TOP_DEAL {
		var bkListIncentive []*modelIncentive.Incentive
		for _, v := range topDeals {
			for _, k := range listIncentive {
				if k.XId == v {
					bkListIncentive = append(bkListIncentive, k)
				}
			}
			if len(bkListIncentive) >= 10 {
				break
			}
		}
		listIncentive = bkListIncentive
	}

	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, listIncentive)
	for _, v := range listIncentive {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		if v.BrandInfo == nil {
			v.BrandInfo = &modelIncentive.IncentiveBrandInfo{
				Text: bReward.BRAND_TEXT_BTASKEE,
			}
		}
		result := map[string]interface{}{}
		b, _ := json.Marshal(v)
		json.Unmarshal(b, &result)
		if v.Exchange != nil {
			result["point"] = v.Exchange.Point
			delete(result, "exchange")
		}
		if v, ok := mapFSInfos[v.XId]; ok {
			result["point"] = v.Point
			result["originalPoint"] = v.OriginalPoint
			result["discountPercent"] = 100 - math.Round((v.Point*100)/v.OriginalPoint)
		}
		results = append(results, result)
	}
	return results
}
