package getListReward

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListReward(reqBody *model.ApiRequest) ([]map[string]interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validateRequest(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker := getAsker(reqBody.UserId)
	incentiveQuery, mapFSInfos, topDeals, keyType := filterCondition(reqBody.FilterBy, asker)
	sortBy := sortCondition(reqBody.SortBy)
	page, limit := getPaging(reqBody.Page, reqBody.Limit)
	listReward := getListReward(incentiveQuery, page, limit, sortBy, keyType, mapFSInfos, topDeals, asker)
	return listReward, nil
}
