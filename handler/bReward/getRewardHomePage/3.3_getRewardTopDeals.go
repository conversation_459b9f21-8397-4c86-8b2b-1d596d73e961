package getRewardHomePage

import (
	"github.com/jinzhu/copier"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewardTopDeals(rawQuery, fields bson.M, mapFSInfos map[string]bReward.Reward, asker *modelUser.Users) []bReward.Reward {
	query := bson.M{}
	copier.Copy(&query, rawQuery)

	listTopDeals := bReward.GetTopDealIncentivePartner()
	query["_id"] = bson.M{"$in": listTopDeals}
	listIncentive, err := queryIncentive(query, fields)
	if err != nil {
		listIncentive, _ = queryIncentive(query, fields)
	}
	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, listIncentive)

	listReward := []bReward.Reward{}
	for _, v := range listIncentive {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		if fsInfo, ok := mapFSInfos[v.XId]; ok && v.Exchange != nil {
			v.OriginalPoint = fsInfo.OriginalPoint
			v.Exchange.Point = fsInfo.Point
		}
		listReward = append(listReward, bReward.MapDataToRewards(v, false))
	}
	results := []bReward.Reward{}
	for _, v := range listTopDeals {
		for _, k := range listReward {
			if k.Id == v {
				results = append(results, k)
			}
		}
		if len(results) >= 10 {
			break
		}
	}
	return results
}
