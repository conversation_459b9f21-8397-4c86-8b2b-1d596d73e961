package getRewardHomePage

import (
	"sort"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func getListReward(asker *modelUser.Users, isTester bool) interface{} {
	query := bReward.GetRawQueryBReward(isTester, asker)
	fields := bson.M{"_id": 1, "title": 1, "image": 1, "exchange": 1, "brandInfo": 1, "type": 1, "from": 1, "isRedeemOneTime": 1}

	// mapRespone := make(map[string]Respone)
	var listRewardRFU, listRewardED, listRewardTD, listRewardFS []bReward.Reward
	var fSEndDate time.Time
	mapFSInfos := bReward.GetMapFSInfos(isTester)
	w := &sync.WaitGroup{}
	w.Add(1)
	go func() {
		defer w.Done()
		listRewardRFU = getRewardRecommedForU(fields, asker, mapFSInfos, isTester)
	}()
	w.Add(1)
	go func() {
		defer w.Done()
		listRewardED = getRewardExclusiveDeal(query, fields, mapFSInfos, asker)
	}()
	w.Add(1)
	go func() {
		defer w.Done()
		listRewardTD = getRewardTopDeals(query, fields, mapFSInfos, asker)
	}()
	w.Add(1)
	go func() {
		defer w.Done()
		listRewardFS, fSEndDate = getRewardFlashSale(query, fields, isTester, asker)
	}()
	w.Wait()
	mapRespone := mapDataRespone(listRewardRFU, listRewardED, listRewardTD, listRewardFS, fSEndDate)
	var respone []bReward.Respone
	for _, v := range mapRespone {
		respone = append(respone, v)
	}
	sort.Slice(respone, func(i, j int) bool {
		return respone[i].Weight < respone[j].Weight
	})
	return respone
}

func queryIncentive(query, fields bson.M) ([]*modelIncentive.Incentive, error) {
	var listIncentive []*modelIncentive.Incentive
	err := globalDataAccess.GetAllByQueryLimitSort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		fields,
		10,
		bson.M{"startDate": -1},
		&listIncentive,
	)
	return listIncentive, err
}

func mapDataRespone(listRewardRFU, listRewardED, listRewardTD, listRewardFS []bReward.Reward, fSEndDate time.Time) map[string]bReward.Respone {
	mapRespone := make(map[string]bReward.Respone)
	if len(listRewardRFU) > 0 {
		keyType := globalConstant.INCENTIVE_TYPE_RECOMMEND_FOR_YOU
		mapRespone[keyType] = bReward.Respone{
			Weight:  1,
			Type:    keyType,
			Text:    mapTypeToText(keyType),
			Rewards: listRewardRFU,
		}
	}

	if len(listRewardFS) > 0 {
		keyType := globalConstant.INCENTIVE_TYPE_FLASH_SALE
		mapRespone[keyType] = bReward.Respone{
			Weight:  2,
			Type:    keyType,
			Text:    mapTypeToText(keyType),
			Rewards: listRewardFS,
			EndDate: &fSEndDate,
		}
	}

	if len(listRewardED) > 0 {
		keyType := globalConstant.INCENTIVE_TYPE_EXCLUSIVE_DEAL
		mapRespone[keyType] = bReward.Respone{
			Weight:  3,
			Type:    keyType,
			Text:    mapTypeToText(keyType),
			Rewards: listRewardED,
		}
	}

	if len(listRewardTD) > 0 {
		keyType := globalConstant.INCENTIVE_TYPE_TOP_DEAL
		mapRespone[keyType] = bReward.Respone{
			Weight:  4,
			Type:    keyType,
			Text:    mapTypeToText(keyType),
			Rewards: listRewardTD,
		}
	}

	return mapRespone
}

func mapTypeToText(rewardType string) *modelService.ServiceText {
	return &modelService.ServiceText{
		Vi: localization.T("vi", rewardType),
		En: localization.T("en", rewardType),
		Ko: localization.T("ko", rewardType),
		Th: localization.T("th", rewardType),
		Id: localization.T("id", rewardType),
	}
}
