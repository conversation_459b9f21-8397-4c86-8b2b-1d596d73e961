package getRewardHomePage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func countRewardByUser(askerId string) int64 {
	now := globalLib.GetCurrentTime(local.TimeZone)
	before1Month := now.AddDate(0, -1, 0)
	query := bson.M{
		"userId": askerId,
		"$and": []bson.M{
			{"$or": []bson.M{
				{"used": bson.M{"$exists": false}},
				{"used": false}},
			},
			{"$or": []bson.M{
				{"expiredIsInfinite": true},
				{"expired": bson.M{"$gte": now}},
				{
					"source":    globalConstant.GIFT_SOURCE_SYSTEM,
					"expired":   bson.M{"$exists": false},
					"createdAt": bson.M{"$gte": before1Month},
				}},
			},
		},
	}
	numberReward, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], query)
	return numberReward
}
