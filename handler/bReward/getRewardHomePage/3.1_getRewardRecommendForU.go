package getRewardHomePage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewardRecommedForU(fields bson.M, asker *modelUser.Users, mapFSInfos map[string]bReward.Reward, isTester bool) (listReward []bReward.Reward) {
	if asker == nil {
		return
	}
	askerPoint := lib.GetAskerPoint(asker)
	askerRank := lib.GetRankAsker(asker)
	query := bReward.GetQueryRecommendForU(askerRank, askerPoint, isTester)
	listIncentive, err := queryIncentive(query, fields)
	if err != nil {
		listIncentive, _ = queryIncentive(query, fields)
	}

	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, listIncentive)
	for _, v := range listIncentive {
		// remove incentive redeem on time if asker already redeem
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		if fsInfo, ok := mapFSInfos[v.XId]; ok && v.Exchange != nil {
			v.OriginalPoint = fsInfo.OriginalPoint
			v.Exchange.Point = fsInfo.Point
		}
		listReward = append(listReward, bReward.MapDataToRewards(v, true))
	}

	if len(listIncentive) == 0 {
		delete(query, "exchange.point")
		listIncentive, err := queryIncentive(query, fields)
		if err != nil {
			listIncentive, _ = queryIncentive(query, fields)
		}
		for _, v := range listIncentive {
			// remove incentive redeem on time if asker already redeem
			if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
				continue
			}

			if fsInfo, ok := mapFSInfos[v.XId]; ok && v.Exchange != nil {
				v.OriginalPoint = fsInfo.OriginalPoint
				v.Exchange.Point = fsInfo.Point
			}
			listReward = append(listReward, bReward.MapDataToRewards(v, false))
		}
	}

	return
}
