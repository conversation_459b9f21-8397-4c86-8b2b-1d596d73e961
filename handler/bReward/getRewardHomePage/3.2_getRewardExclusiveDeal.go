package getRewardHomePage

import (
	"github.com/jinzhu/copier"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewardExclusiveDeal(rawQuery, fields bson.M, mapFSInfos map[string]bReward.Reward, asker *modelUser.Users) (listReward []bReward.Reward) {
	keyType := globalConstant.INCENTIVE_TYPE_EXCLUSIVE_DEAL
	query := bson.M{}
	copier.Copy(&query, rawQuery)
	query["type"] = keyType
	listIncentive, err := queryIncentive(query, fields)
	if err != nil {
		listIncentive, _ = queryIncentive(query, fields)
	}
	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, listIncentive)

	for _, v := range listIncentive {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}
		if fsInfo, ok := mapFSInfos[v.XId]; ok && v.Exchange != nil {
			v.OriginalPoint = fsInfo.OriginalPoint
			v.Exchange.Point = fsInfo.Point
		}
		listReward = append(listReward, bReward.MapDataToRewards(v, false))
	}
	return
}
