package getRewardHomePage

import (
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetRewardHomePage(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	asker, isTester := checkUser(reqBody.UserId)
	var listReward interface{}
	var numberReward int64
	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer wg.Done()
		listReward = getListReward(asker, isTester)
	}()
	go func() {
		defer wg.Done()
		numberReward = countRewardByUser(reqBody.UserId)
	}()
	wg.Wait()
	return map[string]interface{}{
		"numberRewardOfUser": numberReward,
		"listReward":         listReward,
	}, nil
}
