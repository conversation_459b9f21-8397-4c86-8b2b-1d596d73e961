package getRewardHomePage

import (
	"time"

	"github.com/jinzhu/copier"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewardFlashSale(rawQuery, fields bson.M, isTester bool, asker *modelUser.Users) (listReward []bReward.Reward, fsEndDate time.Time) {
	query := bson.M{}
	copier.Copy(&query, rawQuery)
	flashSale := bReward.GetFlashSaleIncentive(isTester)
	if flashSale == nil {
		return
	}
	var listIncentives []string
	mapFlashInfos := make(map[string]bReward.Reward)
	for _, v := range flashSale.IncentiveInfos {
		mapFlashInfos[v.XId] = bReward.Reward{
			OriginalPoint: v.OriginalPoint,
			Point:         v.Point,
		}
		listIncentives = append(listIncentives, v.XId)
	}
	query["_id"] = bson.M{"$in": listIncentives}
	listIncentive, err := queryIncentive(query, fields)
	if err != nil {
		listIncentive, _ = queryIncentive(query, fields)
	}

	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, listIncentive)

	for _, v := range listIncentive {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		if fsInfo, ok := mapFlashInfos[v.XId]; ok && v.Exchange != nil {
			v.OriginalPoint = fsInfo.OriginalPoint
			v.Exchange.Point = fsInfo.Point
		}
		listReward = append(listReward, bReward.MapDataToRewards(v, false))
	}
	return listReward, globalLib.ParseDateFromTimeStamp(flashSale.EndDate, local.TimeZone)
}
