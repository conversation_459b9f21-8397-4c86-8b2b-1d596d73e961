package getRewardDetail

import (
	"encoding/json"
	"math"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getReward(incentiveId, userId string) map[string]interface{} {
	var incentive *modelIncentive.Incentive
	globalDataAccess.GetOneById(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		incentiveId,
		bson.M{"_id": 1, "title": 1, "image": 1, "exchange": 1, "startDate": 1, "endDate": 1, "content": 1, "note": 1, "brandInfo": 1, "office": 1, "social": 1, "isoCode": 1, "from": 1},
		&incentive,
	)
	result := map[string]interface{}{}
	if incentive != nil {
		if incentive.BrandInfo == nil {
			incentive.BrandInfo = &modelIncentive.IncentiveBrandInfo{
				Text: bReward.BRAND_TEXT_BTASKEE,
			}
		}
		b, _ := json.Marshal(incentive)
		json.Unmarshal(b, &result)
		if incentive.Exchange != nil {
			result["point"] = incentive.Exchange.Point
			delete(result, "exchange")
		}
		originalPoint, point, fsEndDate := getPointFlashSale(incentiveId, userId)
		if point > 0 {
			result["point"] = point
			result["originalPoint"] = originalPoint
			result["discountPercent"] = 100 - math.Round((point*100)/originalPoint)
			result["flashSaleEndDate"] = fsEndDate
		}
	}
	return result
}

func getPointFlashSale(incentiveId, userId string) (originalPoint, point float64, endDate time.Time) {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"phone": 1})
	isTester := lib.CheckIsUserTester(asker)
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate":          bson.M{"$lte": currentTime},
		"endDate":            bson.M{"$gte": currentTime},
		"status":             globalConstant.FLASH_SALE_STATUS_ACTIVE,
		"isTesting":          bson.M{"$ne": true},
		"incentiveInfos._id": incentiveId,
	}
	if isTester {
		delete(query, "isTesting")
	}
	flashSale := bReward.GetDataFlashSale(query, bson.M{"_id": 1, "incentiveInfos": 1, "endDate": 1})
	if flashSale == nil {
		return
	}
	endDate = globalLib.ParseDateFromTimeStamp(flashSale.EndDate, local.TimeZone)
	for _, v := range flashSale.IncentiveInfos {
		if incentiveId == v.XId {
			point = v.Point
			originalPoint = v.OriginalPoint
		}
	}
	return
}
