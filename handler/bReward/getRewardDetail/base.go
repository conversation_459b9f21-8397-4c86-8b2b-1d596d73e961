package getRewardDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetRewardDetail(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validateRequest(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	reward := getReward(reqBody.ID, reqBody.UserId)
	return reward, nil
}
