package getRewardsFlashSale

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func checkUser(askerId string) (*modelUser.Users, bool) {
	if askerId == "" {
		return nil, false
	}
	asker, _ := modelUser.GetOneById(local.ISO_CODE, askerId, bson.M{"phone": 1, "point": 1})
	if asker == nil {
		return nil, false
	}
	return asker, lib.CheckIsUserTester(asker)
}
