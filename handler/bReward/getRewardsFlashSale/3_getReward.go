package getRewardsFlashSale

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getRewards(asker *modelUser.Users, isTester bool) (results []bReward.Reward, fsEndDate time.Time) {
	query := bReward.GetRawQueryBReward(isTester, asker)
	flashSale := bReward.GetFlashSaleIncentive(isTester)
	if flashSale == nil {
		return nil, fsEndDate
	}
	var listIncentives []string
	mapFlashInfos := make(map[string]bReward.Reward)
	for _, v := range flashSale.IncentiveInfos {
		mapFlashInfos[v.XId] = bReward.Reward{
			OriginalPoint: v.OriginalPoint,
			Point:         v.Point,
		}
		listIncentives = append(listIncentives, v.XId)
	}
	query["_id"] = bson.M{"$in": listIncentives}

	incentives := getIncentive(query)

	incentiveIdAlreadyRedeem := lib.GetIncentiveIdAlreadyRedeem(asker, incentives)
	for _, v := range incentives {
		if v.IsRedeemOneTime && globalLib.FindStringInSlice(incentiveIdAlreadyRedeem, v.XId) >= 0 {
			continue
		}

		if fsInfo, ok := mapFlashInfos[v.XId]; ok && v.Exchange != nil {
			v.OriginalPoint = fsInfo.OriginalPoint
			v.Exchange.Point = fsInfo.Point
		}
		results = append(results, bReward.MapDataToRewards(v, false))
	}
	return results, globalLib.ParseDateFromTimeStamp(flashSale.EndDate, local.TimeZone)
}

func getIncentive(query bson.M) []*modelIncentive.Incentive {
	var incentives []*modelIncentive.Incentive
	globalDataAccess.GetAllByQueryLimitSort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "title": 1, "image": 1, "exchange": 1, "type": 1, "from": 1, "brandInfo": 1, "isRedeemOneTime": 1},
		10,
		bson.M{"startDate": -1},
		&incentives,
	)
	return incentives
}
