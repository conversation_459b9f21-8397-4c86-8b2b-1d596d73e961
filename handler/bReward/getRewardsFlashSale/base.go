package getRewardsFlashSale

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func GetRewardsFlashSale(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validateRequest(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker, isTester := checkUser(reqBody.UserId)
	if asker == nil {
		return nil, nil
	}
	rewards, fsEndDate := getRewards(asker, isTester)

	// Return data FLASH_SALE
	keyType := globalConstant.INCENTIVE_TYPE_FLASH_SALE
	return bReward.Respone{
		Type:    keyType,
		EndDate: &fsEndDate,
		Text: &modelService.ServiceText{
			Vi: localization.T(globalConstant.LANG_VI, keyType),
			En: localization.T(globalConstant.LANG_EN, keyType),
			Ko: localization.T(globalConstant.LANG_KO, keyType),
			Th: localization.T(globalConstant.LANG_TH, keyType),
			Id: localization.T(globalConstant.LANG_ID, keyType),
		},
		Rewards: rewards,
	}, nil
}
