package getComboVoucherDetail

import (
	"encoding/json"

	comboVoucherHelper "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func getComboVoucher(comboVoucherId string, isUserTester bool) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	query := bson.M{
		"_id":       comboVoucherId,
		"status":    globalConstant.COMBO_VOUCHER_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": globalLib.GetCurrentTime(local.TimeZone)},
		"endDate":   bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
		"isTesting": bson.M{"$ne": true},
	}
	if isUserTester {
		delete(query, "isTesting")
	}
	var comboVoucher *modelComboVoucher.ComboVoucher
	err := globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE],
		query,
		bson.M{"createdAt": 0, "isTesting": 0, "status": 0},
		&comboVoucher,
	)
	if comboVoucher == nil || err != nil {
		return nil, &lib.ERROR_COMBO_VOUCHER_NOT_FOUND
	}
	result := make(map[string]interface{})
	result["type"] = comboVoucherHelper.GetComboVoucherType(comboVoucher.IsSubscription)
	b, _ := json.Marshal(comboVoucher)
	json.Unmarshal(b, &result)
	var vouchers []map[string]interface{}
	for _, voucher := range comboVoucher.Vouchers {
		var voucherPrice float64
		if voucher.Promotion != nil {
			voucherPrice = voucher.Promotion.Value
			if voucher.Promotion.MaxValue > 0 {
				voucherPrice = voucher.Promotion.MaxValue
			}
		}
		voucherMap := map[string]interface{}{
			"title":      voucher.Title,
			"price":      voucherPrice,
			"quantity":   voucher.Quantity,
			"image":      voucher.GetBrandInfo().GetImage(),
			"serviceIds": voucher.GetApplyFor().GetServices(),
		}
		vouchers = append(vouchers, voucherMap)
	}
	result["vouchers"] = vouchers
	return result, nil
}
