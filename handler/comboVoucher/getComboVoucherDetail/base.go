package getComboVoucherDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetComboVoucherDetail(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Check input
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	isUserTester := CheckIsUserTester(reqBody.UserId)
	result, errCode := getComboVoucher(reqBody.ComboVoucherId, isUserTester)
	if errCode != nil {
		return nil, errCode
	}

	return result, nil
}
