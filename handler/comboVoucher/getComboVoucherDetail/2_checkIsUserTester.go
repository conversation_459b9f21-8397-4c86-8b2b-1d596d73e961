package getComboVoucherDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func CheckIsUserTester(userId string) bool {
	if userId == "" {
		return false
	}
	// Get asker
	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"phone": 1})
	if asker == nil {
		return false
	}
	return lib.CheckIsUserTester(asker)
}
