package getListComboVoucherV2

import (
	"sort"
	"time"

	comboVoucherHelper "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucherTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucherTransaction"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getComboVouchers(user *modelUsers.Users, isUserTester bool) []map[string]interface{} {
	applyForUserQuery := []bson.M{
		{
			"target": bson.M{
				"$exists": true,
				"$in":     getTypeOfVoucherUserCanBuy(user),
			},
		},
	}
	// if have userId in params, check the voucher apply for this user
	if user != nil {
		applyForUserQuery = append(applyForUserQuery, bson.M{
			"userIds": bson.M{
				"$exists": true,
				"$in":     []string{user.XId},
			},
		})
	}
	query := bson.M{
		"status":    globalConstant.COMBO_VOUCHER_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": globalLib.GetCurrentTime(local.TimeZone)},
		"endDate":   bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
		"isTesting": bson.M{"$ne": true},
		"$or":       applyForUserQuery,
	}
	if isUserTester {
		delete(query, "isTesting")
	}

	receivedIds := getReceivedFreeComboVoucher(user)
	if len(receivedIds) > 0 {
		query["_id"] = bson.M{"$nin": receivedIds}
	}

	var comboVouchers []map[string]interface{}
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE],
		query,
		bson.M{"vouchers": 0, "isTesting": 0, "termsAndCondition": 0, "status": 0},
		bson.M{"createdAt": -1},
		&comboVouchers,
	)
	for _, comboVoucher := range comboVouchers {
		var isSubscription bool
		if comboVoucher["isSubscription"] != nil {
			isSubscription = comboVoucher["isSubscription"].(bool)
		}
		comboVoucher["type"] = comboVoucherHelper.GetComboVoucherType(isSubscription)
	}

	// sort combo voucher by type first and then by createdAt
	sortByTypeAndCreatedAt(comboVouchers)

	return comboVouchers
}

func getTypeOfVoucherUserCanBuy(user *modelUsers.Users) []string {
	result := []string{globalConstant.PROMOTION_APPLY_BOTH}
	if user == nil {
		return result
	}

	if user.TaskDone > 0 {
		result = append(result, globalConstant.PROMOTION_APPLY_CURRENT)
	} else {
		userIds := []string{user.XId}

		// Get oldUser id if this phone is deleted before
		var oldUsers []*modelUsers.Users
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USERS_DELETED[local.ISO_CODE], bson.M{"phone": user.Phone}, bson.M{"_id": 1}, &oldUsers)
		for _, v := range oldUsers {
			userIds = append(userIds, v.XId)
		}

		// Check if user has task before
		existsTask, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
			bson.M{
				"askerId": bson.M{"$in": userIds},
				"status":  bson.M{"$nin": [2]string{globalConstant.TASK_STATUS_CANCELED, globalConstant.TASK_STATUS_EXPIRED}},
			},
		)

		// Return type of combo voucher user can buy
		if existsTask {
			result = append(result, globalConstant.PROMOTION_APPLY_CURRENT)
		} else {
			result = append(result, globalConstant.PROMOTION_APPLY_NEW)
		}
	}
	return result
}

func getReceivedFreeComboVoucher(user *modelUsers.Users) []string {
	if user == nil {
		return nil
	}
	var comboTransactions []*modelComboVoucherTransaction.ComboVoucherTransaction
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE],
		bson.M{
			"userId": user.XId,
			"$or": []bson.M{
				{"amount": bson.M{"$exists": false}},
				{"amount": 0},
			},
		},
		bson.M{"comboVoucherId": 1},
		&comboTransactions,
	)
	receivedIds := []string{}
	for _, v := range comboTransactions {
		receivedIds = append(receivedIds, v.ComboVoucherId)
	}
	return receivedIds
}

// Define the sorting function
func sortByTypeAndCreatedAt(comboVouchers []map[string]interface{}) {
	sort.Slice(comboVouchers, func(i, j int) bool {
		// Get the type and createdAt for the two items being compared
		typeI := comboVouchers[i]["type"].(string)
		typeJ := comboVouchers[j]["type"].(string)

		// Determine the priority for the type
		typePriority := func(t string) int {
			if t == globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION {
				return 1
			}
			if t == globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER {
				return 2
			}
			return 3
		}

		// Compare by type priority first
		if typePriority(typeI) != typePriority(typeJ) {
			return typePriority(typeI) < typePriority(typeJ)
		}

		// Parse createdAt as a time.Time
		createdAtI, _ := time.Parse(time.RFC3339, comboVouchers[i]["createdAt"].(string))
		createdAtJ, _ := time.Parse(time.RFC3339, comboVouchers[j]["createdAt"].(string))

		// Compare by createdAt (descending order)
		return createdAtI.After(createdAtJ)
	})
}
