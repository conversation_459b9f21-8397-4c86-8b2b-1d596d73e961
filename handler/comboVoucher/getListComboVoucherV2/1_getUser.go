package getListComboVoucherV2

import (
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUser(userId string) (*modelUsers.Users, error) {
	if userId == "" {
		return nil, nil
	}

	var user *modelUsers.Users
	err := globalDataAccess.GetOneById(globalCollection.COLLECTION_USERS, userId, bson.M{"_id": 1, "phone": 1, "taskDone": 1}, &user)
	return user, err
}
