package getUserComboVoucherDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetUserComboVoucherDetail(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 2. check user: check is exist
	errCode = checkUser(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}

	// 3. get combo voucher info
	result, errCode := getUserComboVoucherDetail(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 4. return result
	return result, nil
}
