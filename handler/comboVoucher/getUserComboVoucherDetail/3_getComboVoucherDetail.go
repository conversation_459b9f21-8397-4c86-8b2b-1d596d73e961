package getUserComboVoucherDetail

import (
	comboVoucherHelper "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUserComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type UserComboVoucherDetail struct {
	*modelComboVoucher.ComboVoucher
	XId               string                            `json:"_id"`
	ComboVoucherId    string                            `json:"comboVoucherId"`
	Status            string                            `json:"status"`
	VoucherDetails    []*UserComboVoucherVouchersDetail `json:"voucherDetails"`
	ExpiredDate       *timestamppb.Timestamp            `json:"expiredDate"`
	ExtendedHistories []*timestamppb.Timestamp          `json:"extendedHistories"`
	CreatedAt         *timestamppb.Timestamp            `json:"createdAt"`
	IsCancelled       bool                              `json:"isCancelled"`
	Type              string                            `json:"type"`
}
type UserComboVoucherVouchersDetail struct {
	XId            string               `json:"_id"`
	VoucherId      string               `json:"voucherId"`
	Image          string               `json:"image"`
	Title          *service.ServiceText `json:"title"`
	Quantity       int32                `json:"quantity"`
	RemainQuantity int32                `json:"remainQuantity"`
	GiftId         string               `json:"giftId"`
	ServiceIds     []string             `json:"serviceIds"`
}

func getUserComboVoucherDetail(reqBody *model.ApiRequest) (*UserComboVoucherDetail, *globalResponse.ResponseErrorCode) {
	fields := bson.M{
		"comboVoucherId":    1,
		"status":            1,
		"expiredDate":       1,
		"createdAt":         1,
		"extendedHistories": 1,
		"vouchers":          1,
		"isCancelled":       1,
	}
	var userComboVoucher *modelUserComboVoucher.UserComboVoucher
	globalDataAccess.GetOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], reqBody.UserComboVoucherId,
		fields, &userComboVoucher)
	if userComboVoucher == nil {
		return nil, &lib.ERROR_USER_COMBO_VOUCHER_NOT_FOUND
	}

	var comboVoucher *modelComboVoucher.ComboVoucher
	globalDataAccess.GetOneById(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], userComboVoucher.ComboVoucherId,
		bson.M{"createdAt": 0, "isTesting": 0, "status": 0, "expiredDate": 0, "numberOfDayDueDate": 0, "vouchers": 0}, &comboVoucher)
	if comboVoucher == nil {
		return nil, &lib.ERROR_COMBO_VOUCHER_NOT_FOUND
	}

	result := &UserComboVoucherDetail{
		ComboVoucher:      comboVoucher,
		XId:               userComboVoucher.XId,
		ComboVoucherId:    userComboVoucher.ComboVoucherId,
		Status:            userComboVoucher.Status,
		VoucherDetails:    mapVoucherInfo(reqBody.UserId, userComboVoucher.Vouchers),
		ExpiredDate:       userComboVoucher.ExpiredDate,
		ExtendedHistories: userComboVoucher.ExtendedHistories,
		CreatedAt:         userComboVoucher.CreatedAt,
		IsCancelled:       userComboVoucher.IsCancelled,
		Type:              comboVoucherHelper.GetComboVoucherType(comboVoucher.IsSubscription),
	}

	// ignore field return
	result.ComboVoucher.XId = ""

	return result, nil
}

func mapVoucherInfo(userId string, userVouchers []*modelUserComboVoucher.UserComboVoucherVoucher) []*UserComboVoucherVouchersDetail {

	result := []*UserComboVoucherVouchersDetail{}
	for _, userVoucher := range userVouchers {
		promotionUsed, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE],
			bson.M{"promotionCode": bson.M{"$in": userVoucher.PromotionCodes}, "userId": userId})

		remainQuantity := int64(userVoucher.Quantity) - promotionUsed
		if remainQuantity < 0 {
			remainQuantity = 0
		}
		result = append(result, &UserComboVoucherVouchersDetail{
			XId:            userVoucher.XId,
			VoucherId:      userVoucher.VoucherId,
			Image:          userVoucher.Image,
			Title:          userVoucher.Title,
			Quantity:       int32(userVoucher.Quantity),
			RemainQuantity: int32(remainQuantity),
			ServiceIds:     userVoucher.ServiceIds,
		})
	}

	return result
}
