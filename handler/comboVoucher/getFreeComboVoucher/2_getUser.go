package getFreeComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUser(userId string) (*modelUser.Users, *globalResponse.ResponseErrorCode, error) {
	user, err := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"phone": 1})
	if user == nil || err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	return user, nil, nil
}
