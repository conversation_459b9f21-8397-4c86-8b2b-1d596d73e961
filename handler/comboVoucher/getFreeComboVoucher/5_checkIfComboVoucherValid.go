package getFreeComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func checkIfComboVoucherValid(comboVoucher *modelComboVoucher.ComboVoucher, userId string) *globalResponse.ResponseErrorCode {
	// Is comboVoucher free
	if comboVoucher.Price > 0 {
		return &lib.ERROR_COMBO_VOUCHER_NOT_FREE
	}
	// Is user receive voucher before
	isExists, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{"comboVoucherId": comboVoucher.XId, "userId": userId})
	if isExists {
		return &lib.ERROR_COMBO_VOUCHER_CAN_GET_ONCE
	}
	return nil
}
