package getFreeComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func getComboVoucher(comboVoucherId string, isUserTester bool) (*modelComboVoucher.ComboVoucher, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{
		"_id":       comboVoucherId,
		"status":    globalConstant.COMBO_VOUCHER_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": globalLib.GetCurrentTime(local.TimeZone)},
		"endDate":   bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
		"isTesting": bson.M{"$ne": true},
	}
	if isUserTester {
		delete(query, "isTesting")
	}
	var comboVoucher *modelComboVoucher.ComboVoucher
	err := globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "price": 1, "currency": 1, "title": 1, "content": 1, "cost": 1},
		&comboVoucher,
	)
	if comboVoucher == nil || err != nil {
		return nil, &lib.ERROR_COMBO_VOUCHER_NOT_FOUND, err
	}
	return comboVoucher, nil, nil
}
