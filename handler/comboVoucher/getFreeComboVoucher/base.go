package getFreeComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func GetFreeComboVoucher(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get user
	user, errCode, err := getUser(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. check user tester
	isUserTester := lib.CheckIsUserTester(user)

	// 4. get comboVoucher
	comboVoucher, errCode, err := getComboVoucher(reqBody.ComboVoucherId, isUserTester)
	if errCode != nil {
		return nil, errCode, err
	}

	// 5. check if combovoucher is free
	errCode = checkIfComboVoucherValid(comboVoucher, reqBody.UserId)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 6. Create voucher for user
	err = createComboVoucher(comboVoucher, reqBody.UserId)
	if err != nil {
		return nil, &lib.ERROR_CREATE_COMBO_VOUCHER_FAILED, err
	}

	result := map[string]interface{}{
		"detail": map[string]interface{}{
			"currency": comboVoucher.Currency,
			"title":    comboVoucher.Title,
			"content":  comboVoucher.Content,
			"price":    comboVoucher.Price,
			"cost":     comboVoucher.Cost,
		},
		"success": true,
	}

	return result, nil, nil
}
