package getFreeComboVoucher

import (
	"context"
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	modelComboVoucherTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucherTransaction"
	modelPaymentRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest"
)

func createComboVoucher(comboVoucher *modelComboVoucher.ComboVoucher, userId string) error {
	// Create redeem transaction
	comboVoucherTransactionId, err := createComboVoucherTransaction(comboVoucher, userId)
	if err != nil {
		return err
	}

	// call payment sẻvice
	err = callPaymentServiceCreateVoucher(comboVoucher.XId, comboVoucherTransactionId, userId)
	if err != nil {
		globalDataAccess.DeleteOneById(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], comboVoucherTransactionId)
		return err
	}

	return nil
}

func callPaymentServiceCreateVoucher(comboVoucherId, comboVoucherTransactionId, userId string) error {
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	if err != nil {
		message := fmt.Sprintf("COMBO_VOUCHER_VN: Lỗi connect payment service để tạo voucher. Vui lòng thông báo DEV xử lý, userId: %v, comboVoucherTransaction.XId: %v, err: %v", userId, comboVoucherTransactionId, err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return err
	}
	defer connect.Close()

	_, err = client.CreateGiftByComboVoucherVN(context.Background(), &modelPaymentRequest.PaymentRequest{
		AskerId:                   userId,
		ComboVoucherId:            comboVoucherId,
		ComboVoucherTransactionId: comboVoucherTransactionId,
	})
	if err != nil {
		message := fmt.Sprintf("COMBO_VOUCHER_VN: Lỗi connect payment service để tạo voucher. Vui lòng thông báo DEV xử lý, userId: %v, comboVoucherTransaction.XId: %v, err: %v", userId, comboVoucherTransactionId, err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return err
	}
	return nil
}

func createComboVoucherTransaction(comboVoucher *modelComboVoucher.ComboVoucher, userId string) (string, error) {
	transaction := &modelComboVoucherTransaction.ComboVoucherTransaction{
		XId:            globalLib.GenerateObjectId(),
		PaymentMethod:  globalConstant.PAYMENT_METHOD_CASH,
		Status:         globalConstant.TRANSACTION_STATUS_PAID,
		UserId:         userId,
		ComboVoucherId: comboVoucher.XId,
		CreatedAt:      globalLib.GetCurrentTimestamp(local.TimeZone),
		Amount:         comboVoucher.Price,
	}
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], transaction)
	return transaction.XId, err
}
