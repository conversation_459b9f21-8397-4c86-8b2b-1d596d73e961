package getListComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListComboVoucher(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. get user
	user, err := getUser(reqBody.UserId)
	if err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}

	// 2. check is user tester
	isUserTester := CheckIsUserTester(user)

	// 3. get combo voucher
	result := getComboVouchers(user, isUserTester)

	// 4. return result
	return result, nil
}
