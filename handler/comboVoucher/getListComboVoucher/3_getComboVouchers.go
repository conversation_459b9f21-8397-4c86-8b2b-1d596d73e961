package getListComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucherTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucherTransaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getComboVouchers(user *modelUser.Users, isUserTester bool) []map[string]interface{} {
	applyForUserQuery := []bson.M{
		{
			"target": bson.M{
				"$exists": true,
				"$in":     getTypeOfVoucherUserCanBuy(user),
			},
		},
	}
	// if have userId in params, check the voucher apply for this user
	if user != nil {
		applyForUserQuery = append(applyForUserQuery, bson.M{
			"userIds": bson.M{
				"$exists": true,
				"$in":     []string{user.XId},
			},
		})
	}
	query := bson.M{
		"status":         globalConstant.COMBO_VOUCHER_STATUS_ACTIVE,
		"startDate":      bson.M{"$lte": globalLib.GetCurrentTime(local.TimeZone)},
		"endDate":        bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
		"isTesting":      bson.M{"$ne": true},
		"$or":            applyForUserQuery,
		"isSubscription": bson.M{"$ne": true},
	}
	if isUserTester {
		delete(query, "isTesting")
	}

	receivedIds := getReceivedFreeComboVoucher(user)
	if len(receivedIds) > 0 {
		query["_id"] = bson.M{"$nin": receivedIds}
	}

	var comboVouchers []map[string]interface{}
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE],
		query,
		bson.M{"vouchers": 0, "createdAt": 0, "isTesting": 0, "termsAndCondition": 0, "status": 0},
		bson.D{{Key: "startDate", Value: -1}, {Key: "createdAt", Value: -1}},
		&comboVouchers,
	)
	return comboVouchers
}

func getTypeOfVoucherUserCanBuy(user *modelUser.Users) []string {
	result := []string{globalConstant.PROMOTION_APPLY_BOTH}
	if user == nil {
		return result
	}

	if user.TaskDone > 0 {
		result = append(result, globalConstant.PROMOTION_APPLY_CURRENT)
	} else {
		userIds := []string{user.XId}

		// Get oldUser id if this phone is deleted before
		var oldUsers []*modelUsers.Users
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USERS_DELETED[local.ISO_CODE], bson.M{"phone": user.Phone}, bson.M{"_id": 1}, &oldUsers)
		for _, v := range oldUsers {
			userIds = append(userIds, v.XId)
		}

		// Check if user has task before
		existsTask, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
			bson.M{
				"askerId": bson.M{"$in": userIds},
				"status":  bson.M{"$nin": [2]string{globalConstant.TASK_STATUS_CANCELED, globalConstant.TASK_STATUS_EXPIRED}},
			},
		)

		// Return type of combo voucher user can buy
		if existsTask {
			result = append(result, globalConstant.PROMOTION_APPLY_CURRENT)
		} else {
			result = append(result, globalConstant.PROMOTION_APPLY_NEW)
		}
	}
	return result
}

func getReceivedFreeComboVoucher(user *modelUser.Users) []string {
	if user == nil {
		return nil
	}
	var comboTransactions []*modelComboVoucherTransaction.ComboVoucherTransaction
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE],
		bson.M{
			"userId": user.XId,
			"$or": []bson.M{
				{"amount": bson.M{"$exists": false}},
				{"amount": 0},
			},
		},
		bson.M{"comboVoucherId": 1},
		&comboTransactions,
	)
	receivedIds := []string{}
	for _, v := range comboTransactions {
		receivedIds = append(receivedIds, v.ComboVoucherId)
	}
	return receivedIds
}
