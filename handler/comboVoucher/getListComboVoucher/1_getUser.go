package getListComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUser(userId string) (*modelUser.Users, error) {
	if userId == "" {
		return nil, nil
	}

	user, err := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"_id": 1, "phone": 1, "taskDone": 1})
	return user, err
}
