package cancelComboVoucherSubscription

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func cancelSubscription(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	querySet := bson.M{
		"isCancelled":        true,
		"cancelledAt":        globalLib.GetCurrentTime(local.TimeZone),
		"cancellationReason": reqBody.Reason,
	}
	if reqBody.AdditionalReason != "" {
		querySet["additionalCancellationReason"] = reqBody.AdditionalReason
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], reqBody.UserComboVoucherId, bson.M{"$set": querySet})
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}
	return nil
}
