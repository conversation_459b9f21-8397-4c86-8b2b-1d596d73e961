package cancelComboVoucherSubscription

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.UserComboVoucherId == "" {
		return &lib.ERROR_USER_COMBO_VOUCHER_ID_REQUIRED
	}
	if reqBody.Reason == nil {
		return &lib.ERROR_REASON_REQUIRED
	}
	return nil
}
