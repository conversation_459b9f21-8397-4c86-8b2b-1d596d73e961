package cancelComboVoucherSubscription

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func CancelComboVoucherSubscription(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode
	}

	// 2. check data
	errCode = checkData(reqBody)
	if errCode != nil {
		return errCode
	}

	// 3. cancel
	errCode = cancelSubscription(reqBody)
	if errCode != nil {
		return errCode
	}

	return nil
}
