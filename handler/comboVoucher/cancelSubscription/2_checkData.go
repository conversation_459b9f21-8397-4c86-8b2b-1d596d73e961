package cancelComboVoucherSubscription

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func checkData(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	var userComboVoucher *userComboVoucher.UserComboVoucher
	globalDataAccess.GetOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], reqBody.UserComboVoucherId, bson.M{"userId": 1, "isSubscription": 1, "isCancelled": 1}, &userComboVoucher)
	if userComboVoucher == nil {
		return &lib.ERROR_USER_COMBO_VOUCHER_NOT_FOUND
	}
	if userComboVoucher.UserId != reqBody.UserId {
		return &lib.ERROR_NOT_HAVE_PERMISSION
	}
	if !userComboVoucher.IsSubscription {
		return &lib.ERROR_COMBO_VOUCHER_IS_NOT_SUBSCRIPTION
	}
	if userComboVoucher.IsCancelled {
		return &lib.ERROR_SUBSCRIPTION_IS_CANCELLED
	}

	return nil
}
