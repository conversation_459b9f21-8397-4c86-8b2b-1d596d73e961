package getListComboVoucherTransactionHistories

import (
	comboVoucherHelper "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucherTransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

type TransactionHistory struct {
	*comboVoucherTransaction.ComboVoucherTransaction
	Title *service.ServiceText `json:"title"`
	Type  string               `json:"type"`
}

func getListTransactionHistories(reqBody *model.ApiRequest) []*TransactionHistory {
	var transactions []*comboVoucherTransaction.ComboVoucherTransaction
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{"userId": reqBody.UserId}, bson.M{}, bson.M{"createdAt": -1}, &transactions)

	// get combo voucher info
	comboVoucherIds := []string{}
	for _, transaction := range transactions {
		comboVoucherIds = append(comboVoucherIds, transaction.ComboVoucherId)
	}
	var comboVoucherInfos []*modelComboVoucher.ComboVoucher
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], bson.M{"_id": bson.M{"$in": comboVoucherIds}},
		bson.M{"title": 1, "isSubscription": 1}, &comboVoucherInfos)
	mapComboVoucher := map[string]*modelComboVoucher.ComboVoucher{}
	for _, comboVoucherInfo := range comboVoucherInfos {
		mapComboVoucher[comboVoucherInfo.XId] = comboVoucherInfo
	}

	result := []*TransactionHistory{}
	for _, transaction := range transactions {
		comboVoucherInfo, ok := mapComboVoucher[transaction.ComboVoucherId]
		if !ok {
			continue
		}
		comboVoucherType := comboVoucherHelper.GetComboVoucherType(comboVoucherInfo.IsSubscription)
		result = append(result, &TransactionHistory{
			ComboVoucherTransaction: &comboVoucherTransaction.ComboVoucherTransaction{
				XId:           transaction.XId,
				PaymentMethod: transaction.PaymentMethod,
				Status:        transaction.Status,
				TransactionId: transaction.TransactionId,
				CreatedAt:     transaction.CreatedAt,
				Amount:        transaction.Amount,
			},
			Title: comboVoucherInfo.Title,
			Type:  comboVoucherType,
		})
	}

	return result
}
