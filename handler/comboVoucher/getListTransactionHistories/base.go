package getListComboVoucherTransactionHistories

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListComboVoucherTransactionHistories(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 2. get list transaction history
	result := getListTransactionHistories(reqBody)
	return result, nil
}
