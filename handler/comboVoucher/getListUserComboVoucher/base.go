package getListUserComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListUserComboVoucher(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 2. get user
	errCode = checkUserExist(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}

	// 3. get combo voucher
	result := getComboVouchers(reqBody)

	// 4. return result
	return result, nil
}
