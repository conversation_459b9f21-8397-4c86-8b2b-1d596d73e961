package getListUserComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func checkUserExist(userId string) *globalResponse.ResponseErrorCode {
	isUserExist, err := globalDataAccess.IsExistById(globalCollection.COLLECTION_USERS, userId)
	if !isUserExist || err != nil {
		return &lib.ERROR_USER_NOT_FOUND
	}
	return nil
}
