package getListUserComboVoucher

import (
	"sort"

	comboVoucherHelper "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/comboVoucher"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type UserComboVoucherDetail struct {
	*modelComboVoucher.ComboVoucher
	Status         string                 `json:"status,omitempty"`
	ExpiredDate    *timestamppb.Timestamp `json:"expiredDate,omitempty"`
	ComboVoucherId string                 `json:"comboVoucherId,omitempty"`
	IsCancelled    bool                   `json:"isCancelled,omitempty"`
	Type           string                 `json:"type,omitempty"`
}

func getComboVouchers(reqBody *model.ApiRequest) []*UserComboVoucherDetail {
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	// get user combo voucher active
	query := bson.M{
		"userId": reqBody.UserId,
	}
	if reqBody.IsExpired {
		query["$or"] = []bson.M{
			{"status": globalConstant.USER_COMBO_VOUCHER_STATUS_EXPIRED},
			{"expiredDate": bson.M{
				"$lt": currentTime,
			}},
		}
	} else {
		query["status"] = globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE
		query["expiredDate"] = bson.M{
			"$gte": currentTime,
		}
	}
	var userComboVouchers []*userComboVoucher.UserComboVoucher
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], query,
		bson.M{"comboVoucherId": 1, "expiredDate": 1, "status": 1, "isCancelled": 1, "isSubscription": 1, "createdAt": 1}, &userComboVouchers)

	// prepare data for map detail
	comboVoucherIds := []string{}
	for _, userComboVoucher := range userComboVouchers {
		comboVoucherIds = append(comboVoucherIds, userComboVoucher.ComboVoucherId)
	}

	// get detail of combo voucher
	var comboVouchers []*modelComboVoucher.ComboVoucher
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], bson.M{"_id": bson.M{"$in": comboVoucherIds}},
		bson.M{"title": 1, "content": 1, "isSubscription": 1, "thumbnail": 1, "createdAt": 1}, &comboVouchers)
	mapComboVoucherById := map[string]*modelComboVoucher.ComboVoucher{}
	for _, comboVoucher := range comboVouchers {
		mapComboVoucherById[comboVoucher.XId] = comboVoucher
	}
	userComboVoucherResults := []*UserComboVoucherDetail{}

	sortByTypeAndCreatedAt(userComboVouchers)

	for _, userComboVoucherDetail := range userComboVouchers {
		comboVoucher, ok := mapComboVoucherById[userComboVoucherDetail.ComboVoucherId]
		if !ok {
			continue
		}
		comboVoucherType := comboVoucherHelper.GetComboVoucherType(comboVoucher.IsSubscription)
		userComboVoucherResults = append(userComboVoucherResults, &UserComboVoucherDetail{
			ComboVoucher: &modelComboVoucher.ComboVoucher{
				XId:       userComboVoucherDetail.XId,
				Title:     comboVoucher.Title,
				Content:   comboVoucher.Content,
				Thumbnail: comboVoucher.Thumbnail,
			},
			Type:           comboVoucherType,
			Status:         userComboVoucherDetail.Status,
			ExpiredDate:    userComboVoucherDetail.ExpiredDate,
			ComboVoucherId: userComboVoucherDetail.ComboVoucherId,
			IsCancelled:    userComboVoucherDetail.IsCancelled,
		})
	}

	return userComboVoucherResults
}

func sortByTypeAndCreatedAt(userComboVouchers []*userComboVoucher.UserComboVoucher) {
	sort.Slice(userComboVouchers, func(i, j int) bool {
		// Get the type and createdAt for the two items being compared
		typeI := comboVoucherHelper.GetComboVoucherType(userComboVouchers[i].IsSubscription)
		typeJ := comboVoucherHelper.GetComboVoucherType(userComboVouchers[j].IsSubscription)

		// Determine the priority for the type
		typePriority := func(t string) int {
			if t == globalConstant.COMBO_VOUCHER_TYPE_SUBSCRIPTION {
				return 1
			}
			if t == globalConstant.COMBO_VOUCHER_TYPE_COMBO_VOUCHER {
				return 2
			}
			return 3
		}

		// Compare by type priority first
		if typePriority(typeI) != typePriority(typeJ) {
			return typePriority(typeI) < typePriority(typeJ)
		}

		// Parse createdAt as a time.Time
		createdAtI := globalLib.ParseDateFromTimeStamp(userComboVouchers[i].CreatedAt, local.TimeZone)
		createdAtJ := globalLib.ParseDateFromTimeStamp(userComboVouchers[j].CreatedAt, local.TimeZone)

		// Compare by createdAt (descending order)
		return createdAtI.After(createdAtJ)
	})
}
