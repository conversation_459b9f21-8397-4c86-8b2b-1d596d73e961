/*
 * @File: getTaskerReviews.go
 * @Description: Handler function for get tasker reviews api
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package rating

import (
	"math/rand"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Tasker Reviews by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTaskerReviews(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Get data
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"avatar": 1, "name": 1, "status": 1, "avgRating": 1, "taskDone": 1, "badges": 1, "isVaccinatedCovid19": 1, "covid19Vaccine": 1, "isPremiumTasker": 1})
	if tasker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}

	// Get reviews and count by taskerId
	reviews, reviewCount := getReviewsByUserId(reqBody.UserId)

	var setting *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"numberOfTaskCanSeeRatingTasker": 1}, &setting)
	avgRating := tasker.AvgRating
	// Default 5* cho tasker trong 20 CV dau
	var numberOfTaskCanSeeRatingTasker int32 = 20
	if setting != nil && setting.NumberOfTaskCanSeeRatingTasker > 0 {
		numberOfTaskCanSeeRatingTasker = setting.NumberOfTaskCanSeeRatingTasker
	}
	if tasker.TaskDone <= numberOfTaskCanSeeRatingTasker {
		avgRating = 5
	}
	result := map[string]interface{}{
		"avatar":           tasker.Avatar,
		"name":             tasker.Name,
		"status":           tasker.Status,
		"avgRating":        avgRating,
		"numberOfTaskDone": tasker.TaskDone,
		"numberOfReview":   reviewCount,
		"reviews":          reviews,
		"badges":           tasker.Badges,
		"isPremiumTasker":  tasker.IsPremiumTasker,
		// "isVaccinatedCovid19": tasker.IsVaccinatedCovid19,
		// "covid19Vaccine":      tasker.Covid19Vaccine,
		// "medals":           []string{},
	}
	if reqBody.AskerId != "" {
		asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.AskerId, bson.M{"favouriteTasker": 1})
		if asker != nil && len(asker.FavouriteTasker) > 0 {
			result["isFavouriteTasker"] = (globalLib.FindStringInSlice(asker.FavouriteTasker, reqBody.UserId) > -1)
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get list review of tasker id
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getReviewsByUserId(userId string) ([]string, int64) {
	var ratings []*modelRating.Rating
	globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_RATING[local.ISO_CODE],
		bson.M{"taskerId": userId, "rate": 5, "review": bson.M{"$exists": true, "$ne": ""}},
		bson.M{"review": 1, "createdAt": 1},
		50,
		bson.M{"createdAt": -1},
		&ratings,
	)
	var reviews []string
	//Count tasker reviews
	reviewCount, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskerId": userId, "rate": bson.M{"$exists": true}})
	// Get random 5 review
	if len(ratings) > 5 {
		for i := 0; i < 5; i++ {
			// Random index
			indexRandom := rand.Intn(len(ratings))
			// Get review
			reviews = append(reviews, ratings[indexRandom].Review)
			// Remove element with index
			ratings = append(ratings[:indexRandom], ratings[indexRandom+1:]...)
		}
	} else {
		// Get all reviews
		for _, v := range ratings {
			reviews = append(reviews, v.Review)
		}
	}
	return reviews, reviewCount
}
