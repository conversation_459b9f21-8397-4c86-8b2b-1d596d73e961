/*
 * @File: getTaskerReviews.go
 * @Description: Handler function for get tasker reviews api
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package getTaskerReviewsV5

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get Tasker Reviews by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTaskerReviews(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	defer local.Logger.Sync()

	// Validate data request
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	// Get data
	tasker, err := modelUser.GetOneById(local.ISO_CODE, reqBody.TaskerId, bson.M{"avatar": 1, "name": 1, "status": 1, "avgRating": 1, "taskDone": 1, "badges": 1, "isVaccinatedCovid19": 1, "covid19Vaccine": 1, "isPremiumTasker": 1})
	if tasker == nil || err != nil {
		return nil, &lib.ERROR_TASKER_NOT_FOUND, err
	}

	// Get reviews and count by taskerId
	reviews, reviewCount := getReviewsByUserId(reqBody.TaskerId)

	var setting *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"numberOfTaskCanSeeRatingTasker": 1}, &setting)

	avgRating := tasker.AvgRating
	// Default 5* cho tasker trong 20 CV dau
	var numberOfTaskCanSeeRatingTasker int32 = 20
	if setting != nil && setting.NumberOfTaskCanSeeRatingTasker > 0 {
		numberOfTaskCanSeeRatingTasker = setting.NumberOfTaskCanSeeRatingTasker
	}
	if tasker.TaskDone <= numberOfTaskCanSeeRatingTasker {
		avgRating = 5
	}
	result := map[string]interface{}{
		"avatar":           tasker.Avatar,
		"name":             tasker.Name,
		"status":           tasker.Status,
		"avgRating":        avgRating,
		"numberOfTaskDone": tasker.TaskDone,
		"numberOfReview":   reviewCount,
		"reviews":          reviews,
		"badges":           tasker.Badges,
		"isPremiumTasker":  tasker.IsPremiumTasker,
		// "isVaccinatedCovid19": tasker.IsVaccinatedCovid19,
		// "covid19Vaccine":      tasker.Covid19Vaccine,
		// "medals":           []string{},
	}
	if reqBody.UserId != "" {
		asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"favouriteTasker": 1})
		if asker != nil && len(asker.FavouriteTasker) > 0 {
			result["isFavouriteTasker"] = (globalLib.FindStringInSlice(asker.FavouriteTasker, reqBody.TaskerId) > -1)
		}
	}
	return result, nil, nil
}
