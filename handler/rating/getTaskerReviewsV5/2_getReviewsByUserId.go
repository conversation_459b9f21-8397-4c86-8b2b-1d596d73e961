package getTaskerReviewsV5

import (
	"math/rand"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get list review of tasker id
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getReviewsByUserId(taskerId string) ([]string, int64) {
	var ratings []*modelRating.Rating
	globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_RATING[local.ISO_CODE],
		bson.M{"taskerId": taskerId, "rate": 5, "review": bson.M{"$exists": true, "$ne": ""}},
		bson.M{"review": 1, "createdAt": 1},
		50,
		bson.M{"createdAt": -1},
		&ratings,
	)
	var reviews []string
	//Count tasker reviews
	reviewCount, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskerId": taskerId, "rate": bson.M{"$exists": true}})
	// Get random 5 review
	if len(ratings) > 5 {
		for i := 0; i < 5; i++ {
			// Random index
			indexRandom := rand.Intn(len(ratings))
			// Get review
			reviews = append(reviews, ratings[indexRandom].Review)
			// Remove element with index
			ratings = append(ratings[:indexRandom], ratings[indexRandom+1:]...)
		}
	} else {
		// Get all reviews
		for _, v := range ratings {
			reviews = append(reviews, v.Review)
		}
	}
	return reviews, reviewCount
}
