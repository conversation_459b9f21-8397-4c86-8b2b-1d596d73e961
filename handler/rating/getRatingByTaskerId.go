/*
 * @File: getRatingByTaskerId.go
 * @Description: Handler function for get rating by taskerId api
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
package rating

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Rating By TaskerId
 * @CreatedAt: 21/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetRatingByTaskerId(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	var result []*modelRating.Rating
	globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_RATING[local.ISO_CODE],
		bson.M{"taskerId": reqBody.UserId},
		bson.M{"review": 1, "date": 1, "feedBack": 1, "rate": 1},
		30,
		bson.M{"createdAt": -1},
		&result,
	)
	globalResponse.ResponseSuccess(w, result)
}
