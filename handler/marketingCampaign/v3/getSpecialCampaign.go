/*
 * @File: getMarketingCampaign.go
 * @Description: Handler function of get special marketing campaign api
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
package v3

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelMarketingCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/marketingCampaign"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Marketing Campaign Status Active by UserId
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func GetSpecialCampaignV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate request params
	errCode = validateGetSpecialCampaign(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"totalTaskDone": 1, "createdAt": 1, "phone": 1, "isOldUser": 1})
	if asker != nil {
		// Get data
		currentDate := globalLib.GetCurrentTime(local.TimeZone)
		query := bson.M{
			"status":            globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
			"startDate":         bson.M{"$lte": currentDate},
			"endDate":           bson.M{"$gte": currentDate},
			"isSpecialCampaign": true,
			"isTesting":         bson.M{"$ne": true},
		}
		var query1, query2 bson.M
		query1 = bson.M{
			"$or": []bson.M{
				{fmt.Sprintf("userIds.%s", reqBody.UserId): 0},
				{fmt.Sprintf("userIds.%s", reqBody.UserId): bson.M{"$exists": false}},
			},
		}
		if asker.IsOldUser || asker.TotalTaskDone > 0 {
			query2 = bson.M{
				"$or": []bson.M{
					{"applyForUser.target": bson.M{"$in": [2]string{globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_CURRENT, globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH}}},
					{"applyForUser.userIds": reqBody.UserId},
				},
			}
		} else {
			query2 = bson.M{
				"$or": []bson.M{
					{"applyForUser.target": bson.M{"$in": [2]string{globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW, globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH}}},
					{"applyForUser.userIds": reqBody.UserId},
				},
			}
		}
		query["$and"] = []bson.M{
			query1,
			query2,
		}
		isUserTester := lib.CheckIsUserTester(asker)
		if isUserTester {
			delete(query, "isTesting")
		}

		var campaign *modelMarketingCampaign.MarketingCampaign
		globalDataAccess.GetOneByQuerySort(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], query, bson.M{"userIds": 0, "emailTemplate": 0, "notificationTemplate": 0}, bson.M{"createdAt": -1}, &campaign)
		if campaign == nil {
			globalResponse.ResponseSuccess(w, nil)
			return
		}

		result := &model.MarketingCampaignDetailWithPaymentMethod{
			MarketingCampaign: campaign,
		}
		if lib.IsMarketingCampaignTypePromotion(result.MarketingCampaign) {
			var promotionCode *modelPromotionCode.PromotionCode
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"code": result.GetPromotion().GetCode()}, bson.M{"paymentMethods": 1}, &promotionCode)
			result.PaymentMethods = promotionCode.GetPaymentMethods()
		}

		globalResponse.ResponseSuccess(w, result)
	} else {
		globalResponse.ResponseSuccess(w, nil)
	}
}

/*
 * @Description: validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetSpecialCampaign(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
