/*
 * @File: getMarketingCampaign.go
 * @Description: Handler function of get marketing campaign detail api
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
package v3

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelMarketingCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/marketingCampaign"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Marketing Campaign Detail by Id
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func GetMarketingCampaignDetailV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetMarketingCampaignDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data from database
	var campaign *modelMarketingCampaign.MarketingCampaign
	globalDataAccess.GetOneById(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], reqBody.CampaignId, bson.M{"userIds": 0}, &campaign)
	if campaign == nil {
		globalResponse.ResponseSuccess(w, nil)
		return
	}

	// Convert to response with payment method
	result := &model.MarketingCampaignDetailWithPaymentMethod{
		MarketingCampaign: campaign,
	}
	if lib.IsMarketingCampaignTypePromotion(result.MarketingCampaign) {
		var promotionCode *modelPromotionCode.PromotionCode
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{"code": result.GetPromotion().GetCode()}, bson.M{"paymentMethods": 1}, &promotionCode)
		result.PaymentMethods = promotionCode.GetPaymentMethods()
	}

	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: validate request params
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func validateGetMarketingCampaignDetail(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.CampaignId == "" {
		return &lib.ERROR_CAMPAIGN_ID_REQUIRED
	}
	return nil
}
