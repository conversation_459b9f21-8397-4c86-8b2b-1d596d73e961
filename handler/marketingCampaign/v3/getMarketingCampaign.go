/*
 * @File: getMarketingCampaignV3.go
 * @Description: Handler function of get marketing campaign v3 api
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
package v3

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

/*
 * @Description: Get Marketing Campaign v3 by UserId
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func GetMarketingCampaignV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetMarketingCampaignV3(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get marketing campaign. Move to handler/base.go to use in api getMyReward
	result := lib.GetMarketingCampaignForUserV3(reqBody.UserId, "")
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: validate request params
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func validateGetMarketingCampaignV3(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
