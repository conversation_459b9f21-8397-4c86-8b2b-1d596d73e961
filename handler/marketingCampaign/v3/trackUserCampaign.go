package v3

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Track User Campaign by UserId
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func TrackUserCampaignV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateTrackUserCampaignV3(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	IsExistCampaign, _ := globalDataAccess.IsExistById(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], reqBody.CampaignId)
	if !IsExistCampaign {
		globalResponse.ResponseError(w, lib.ERROR_MARKETING_CAMPAIGN_NOT_FOUND)
		return
	}

	if _, ok := globalConstant.MARKETING_CAMPAIGN_ACTION_MAP[reqBody.CampaignAction]; !ok {
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}

	// TODO: Fix to get marketing action:40 in api /get-marketing-campaign
	if globalConstant.MARKETING_CAMPAIGN_ACTION_MAP[reqBody.CampaignAction] == globalConstant.MARKETING_CAMPAIGN_ACTION_BOOK {
		globalResponse.ResponseSuccess(w, nil)
		return
	}

	// Update marketing campaign
	dataUpdate := bson.M{
		"$bit": bson.M{
			fmt.Sprintf("userIds.%s", reqBody.UserId): bson.M{"or": globalConstant.MARKETING_CAMPAIGN_ACTION_MAP[reqBody.CampaignAction]},
		},
		"$set": bson.M{
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		},
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], reqBody.CampaignId, dataUpdate)
	if err != nil {
		local.Logger.Warn("UPDATE_FAILED",
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "UPDATE_FAILED", Message: err.Error()})
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func validateTrackUserCampaignV3(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.CampaignId == "" {
		return &lib.ERROR_CAMPAIGN_ID_REQUIRED
	}
	if reqBody.CampaignAction == "" {
		return &lib.ERROR_CAMPAIGN_ACTION_REQUIRED
	}
	return nil
}
