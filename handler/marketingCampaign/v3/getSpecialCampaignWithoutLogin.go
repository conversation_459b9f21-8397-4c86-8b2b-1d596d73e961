package v3

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelMarketingCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/marketingCampaign"
	"go.mongodb.org/mongo-driver/bson"
)

func GetSpecialCampaignWithoutLoginV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Get data
	currentDate := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"status":              globalConstant.MARKETING_CAMPAIGN_STATUS_ACTIVE,
		"startDate":           bson.M{"$lte": currentDate},
		"endDate":             bson.M{"$gte": currentDate},
		"isSpecialCampaign":   true,
		"applyForUser.target": bson.M{"$in": [2]string{globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_NEW, globalConstant.MARKETING_CAMPAIN_TARGET_APPLY_BOTH}},
		"isTesting":           bson.M{"$ne": true},
	}
	var result *modelMarketingCampaign.MarketingCampaign
	globalDataAccess.GetOneByQuerySort(globalCollection.COLLECTION_MARKETING_CAMPAIGN[local.ISO_CODE], query, bson.M{"userIds": 0, "emailTemplate": 0, "notificationTemplate": 0}, bson.M{"createdAt": -1}, &result)

	globalResponse.ResponseSuccess(w, result)
}
