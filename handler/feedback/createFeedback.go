/*
 * @File: createFeedback.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 16/11/2020
 * @Author: ngoctb
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package feedback

import (
	"encoding/json"
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFeedback "gitlab.com/btaskee/go-services-model-v2/grpcmodel/feedback"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
* @Description: Create Feedback
* @CreatedAt: 16/11/2020
* @Author: ngoctb
* @UpdatedAt: 24/02/2021
* @UpdatedBy: ngoctb
 */
func CreateFeedback(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody *modelFeedback.Feedback
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Validate params
	resErr := validateCreateFeedback(reqBody)
	if resErr != nil {
		local.Logger.Warn(resErr.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *resErr)
		return
	}
	// Post to slack
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"emails": 1, "locations": 1})

	slackChannel := lib.SLACK_CHANNEL_ASKER_FEEDBACK_VN
	message := fmt.Sprintf("App-v3: %s, %s, %s", reqBody.PhoneNumber, reqBody.Name, reqBody.Feedback)
	globalLib.PostToSlack(cfg.SlackToken, slackChannel, "bTaskee System", message)

	// Insert feedback to db
	reqBody.XId = globalLib.GenerateObjectId()
	reqBody.CreatedAt = globalLib.GetCurrentTimestamp(local.TimeZone)
	reqBody.IsoCode = local.ISO_CODE
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_FEEDBACK[local.ISO_CODE], reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_INSERT_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_INSERT_FAILED)
		return
	}
	// Send feedback to ticket Midesk - VN
	go sendFeedbackToMiDesk(reqBody, user)
	globalResponse.ResponseSuccess(w, nil)
}

func validateCreateFeedback(reqBody *modelFeedback.Feedback) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.PhoneNumber == "" {
		return &lib.ERROR_PHONE_REQUIRED
	}
	if reqBody.Name == "" {
		return &lib.ERROR_NAME_REQUIRED
	}
	if reqBody.Feedback == "" {
		return &lib.ERROR_FEEDBACK_REQUIRED
	}
	return nil
}

func sendFeedbackToMiDesk(reqBody *modelFeedback.Feedback, user *modelUser.Users) {
	token := globalLib.GetMiDeskAccessToken(cfg.MiDeskConfig.LoginUrl, cfg.MiDeskConfig.Email, cfg.MiDeskConfig.Password, cfg.SlackToken)
	if token != "" {
		var userEmail string
		if user != nil && len(user.Emails) > 0 {
			userEmail = user.Emails[0].Address
		}
		var askerCity string
		if len(user.Locations) > 0 {
			askerCity = user.Locations[0].City
		}
		// Create ticket
		globalLib.CallCreateMiDeskTicket(cfg.MiDeskConfig.CreateTicketUrl, token, cfg.SlackToken, map[string]interface{}{
			"title":    fmt.Sprintf("Feedback from Asker - %s - %s - %s", askerCity, reqBody.Name, reqBody.PhoneNumber),
			"content":  reqBody.Feedback,
			"channel":  "Asker feedback",
			"priority": "3", // 1: Khẩn cấp 2: Cao 3: Trung bình
			"contact": map[string]interface{}{
				"name":  reqBody.Name,
				"email": userEmail,
				"phone": reqBody.PhoneNumber,
			},
		})
	}
}
