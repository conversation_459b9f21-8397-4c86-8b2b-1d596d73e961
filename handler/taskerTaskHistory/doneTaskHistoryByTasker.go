/*
 * @File: doneTaskHistoryByTasker.go
 * @Description: Handler function of DoneTaskHistoryByTasker api
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package taskerTaskHistory

import (
	"net/http"
	"reflect"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskerTaskHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerTaskHistory"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Done Task History by Tasker (UserId)
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func DoneTaskHistoryByTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	now := globalLib.GetCurrentTime(local.TimeZone)
	gte := now
	lt := now
	// Check for supporting old staging version
	typeof := reflect.TypeOf(reqBody.Month)
	if typeof.Kind() == reflect.Int {
		gte = time.Date(now.Year(), time.Month(reqBody.Month.(int)), 1, 0, 0, 0, 0, local.TimeZone)
		lt = time.Date(now.Year(), time.Month(reqBody.Month.(int))+1, 0, 0, 0, 0, 0, local.TimeZone)
	} else {
		t, err := time.Parse(time.RFC3339, reqBody.Month.(string))
		if err != nil {
			local.Logger.Warn(lib.ERROR_TIME_NOT_ALLOW.ErrorCode,
				zap.String("url", r.RequestURI),
				zap.Any("body", reqBody),
			)
			globalResponse.ResponseError(w, lib.ERROR_TIME_NOT_ALLOW)
			return
		}
		gte = time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
		lt = time.Date(t.Year(), t.Month()+1, 0, 0, 0, 0, 0, t.Location())
	}
	var total, totalByMonth float64
	if reqBody.IsCompany {
		var resultCompany []map[string]interface{}
		var historyData []*modelTaskerTaskHistory.TaskerTaskHistory
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_TASK_HISTORY[local.ISO_CODE], bson.M{"doneTask.companyId": reqBody.UserId}, bson.M{}, &historyData)
		if len(historyData) > 0 {
			var userIds []string
			for _, v := range historyData {
				userIds = append(userIds, v.XId)
				if v.DoneTask != nil && len(v.DoneTask) > 0 {
					var newDoneTasks []*modelTaskerTaskHistory.TaskerTaskHistoryDoneTask
					for _, d := range v.DoneTask {
						if d.CompanyId == reqBody.UserId {
							total += d.Cost
							newDoneTasks = append(newDoneTasks, d)
						}
					}
					v.DoneTask = newDoneTasks
				}
			}
			users, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1, "name": 1})
			for _, v := range historyData {
				var totalCost float64
				if v.DoneTask != nil && len(v.DoneTask) > 0 {
					for _, d := range v.DoneTask {
						totalCost += d.Cost
						endAt := globalLib.ParseDateFromTimeStamp(d.EndAt, local.TimeZone)
						if endAt.After(gte) && endAt.Before(lt) {
							totalByMonth += d.Cost
						}
						user := &modelUser.Users{}
						for _, u := range users {
							if u.XId == v.XId {
								user = u
								break
							}
						}
						item := map[string]interface{}{
							"_id":       v.XId,
							"name":      user.Name,
							"data":      v.DoneTask,
							"totalCost": totalCost,
						}
						resultCompany = append(resultCompany, item)
					}
				}
			}
		}
		result := map[string]interface{}{
			"data":         resultCompany,
			"total":        total,
			"totalByMonth": totalByMonth,
		}
		globalResponse.ResponseSuccess(w, result)
		return
	}
	var task *modelTaskerTaskHistory.TaskerTaskHistory
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASKER_TASK_HISTORY[local.ISO_CODE], reqBody.UserId, bson.M{}, &task)
	var newDoneTasks []*modelTaskerTaskHistory.TaskerTaskHistoryDoneTask
	if task != nil && task.DoneTask != nil && len(task.DoneTask) > 0 {
		for _, d := range task.DoneTask {
			total += d.Cost
			endAt := globalLib.ParseDateFromTimeStamp(d.EndAt, local.TimeZone)
			if endAt.After(gte) && endAt.Before(lt) {
				totalByMonth += d.Cost
				newDoneTasks = append(newDoneTasks, d)
			}
		}
	}
	result := map[string]interface{}{
		"data":         newDoneTasks,
		"total":        total,
		"totalByMonth": totalByMonth,
	}
	globalResponse.ResponseSuccess(w, result)
}
