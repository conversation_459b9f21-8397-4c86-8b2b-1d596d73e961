/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getTaskersForVote

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetTaskersForVote(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	// get voted tasker
	taskerVoted := votedFavTasker(reqBody.UserId)
	if taskerVoted != nil {
		return map[string]interface{}{
			"taskerVoted": taskerVoted,
		}, nil, nil
	}
	// Get data user
	user, err := getUser(reqBody.UserId)
	if err != nil || user == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	// Get favorite tasker by asker
	listFavTasker := getTaskers(user)
	// Return data
	return map[string]interface{}{
		"listFavTasker": listFavTasker,
	}, nil, nil
}
