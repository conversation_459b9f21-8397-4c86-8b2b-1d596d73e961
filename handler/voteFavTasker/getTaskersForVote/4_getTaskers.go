/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getTaskersForVote

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskers(asker *modelUser.Users) []*modelUser.Users {
	// Get data tasker by favourite
	// allTaskers := getTaskersData(asker.FavouriteTasker, asker.IsoCode)
	allTaskers := getTaskersData(asker)
	// Filter tasker by status and Return data tasker
	return filterTaskers(allTaskers)
}

func getTaskersData(asker *modelUser.Users) []*modelUser.Users {
	var listTaskerWorked []string
	mapTaskerIncorrect := make(map[string]struct{})
	for _, t := range asker.BlackList {
		mapTaskerIncorrect[t] = struct{}{}
	}
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": asker.XId, "status": globalConstant.TASK_STATUS_DONE}, bson.M{"acceptedTasker.taskerId": 1, "serviceName": 1}, &tasks)
	for _, v := range tasks {
		if globalLib.FindStringInSlice(globalConstant.LIST_COMPANY_SERVICES, v.ServiceName) >= 0 {
			continue
		}
		for _, t := range v.AcceptedTasker {
			if _, ok := mapTaskerIncorrect[t.TaskerId]; !ok {
				mapTaskerIncorrect[t.TaskerId] = struct{}{}
				listTaskerWorked = append(listTaskerWorked, t.TaskerId)
			}
		}
	}
	var allTaskers []*modelUser.Users
	if len(listTaskerWorked) > 0 {
		allTaskers, _ = modelUser.GetAll(
			local.ISO_CODE,
			bson.M{
				"_id":     bson.M{"$in": listTaskerWorked},
				"isoCode": local.ISO_CODE,
			},
			bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1, "status": 1, "lastLockedAt": 1, "isIndefiniteLocking": 1, "gender": 1},
			&globalDataAccessV2.QueryOptions{Sort: bson.M{"avgRating": -1}},
		)
	}
	return allTaskers
}

func filterTaskers(allTaskers []*modelUser.Users) []*modelUser.Users {
	if len(allTaskers) == 0 {
		return nil
	}
	var listFavTasker []*modelUser.Users
	sevenDayAgo := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -7)
	for _, v := range allTaskers {
		if v.Gender == globalConstant.GENDER_MALE {
			continue
		}
		if v.Status == globalConstant.USER_STATUS_BLOCKED && !v.IsIndefiniteLocking {
			lastLockedAt := globalLib.ParseDateFromTimeStamp(v.LastLockedAt, local.TimeZone)
			if !lastLockedAt.Before(sevenDayAgo) {
				listFavTasker = append(listFavTasker, v)
				continue
			}
		}
		if v.Status == globalConstant.USER_STATUS_ACTIVE {
			listFavTasker = append(listFavTasker, v)
			continue
		}
	}

	return listFavTasker
}
