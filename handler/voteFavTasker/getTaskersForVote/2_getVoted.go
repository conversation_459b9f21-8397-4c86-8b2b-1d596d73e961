/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getTaskersForVote

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelVoteFavTasker "gitlab.com/btaskee/go-services-model-v2/grpcmodel/voteFavTasker"
	"go.mongodb.org/mongo-driver/bson"
)

func votedFavTasker(userId string) *modelUser.Users {
	var votedTasker *modelVoteFavTasker.VoteFavTasker
	// Check asker voted
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_VOTE_FAV_TASKER[local.ISO_CODE], bson.M{"askerId": userId}, bson.M{"taskerId": 1}, &votedTasker)
	if votedTasker == nil || votedTasker.TaskerId == "" {
		return nil
	}
	// Get data tasker voted
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, votedTasker.TaskerId, bson.M{"name": 1, "avatar": 1, "avgRating": 1, "taskDone": 1})
	// Return data tasker
	return tasker
}
