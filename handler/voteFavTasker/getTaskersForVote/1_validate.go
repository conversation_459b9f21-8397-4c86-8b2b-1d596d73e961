/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package getTaskersForVote

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// Validate data asker Id
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
