package voteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func checkAskerVoted(userId string) *globalResponse.ResponseErrorCode {
	// Check asker voted fav tasker
	isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_VOTE_FAV_TASKER[local.ISO_CODE], bson.M{"askerId": userId})
	// Return error if asker voted
	if isExist {
		return &lib.ERROR_ASKER_VOTED_FAV_TASKER
	}
	return nil
}
