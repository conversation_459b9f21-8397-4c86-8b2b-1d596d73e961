/*
 * @File: getFavoriteTasker.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 04/03/2021
 * @Author: vinhnt
 */
package voteTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

var cfg = config.GetConfig()

func sendBPointToAsker(user *modelUser.Users) (interface{}, *globalResponse.ResponseErrorCode, error) {
	point := 25.0 // 25 bpoint for asker vote tasker
	// Get old point rank asker
	oldPointRank := 0.0
	if user.RankInfo != nil {
		oldPointRank = user.RankInfo.Point
	}

	// Create point transaction
	pointTransaction := &modelPointTransaction.PointTransaction{
		XId:    globalLib.GenerateObjectId(),
		UserId: user.XId,
		Source: &modelPointTransaction.PointTransactionSource{
			Name:        globalConstant.BPOINT_TRANSACTION_SOURCE_NAME_VOTE_FAV_TASKER,
			ServiceText: localization.GetLocalizeObject("POINT_TRANSACTION_ASKER_VOTE_FAV_TASKER"),
		},
		Point:        point,
		OldPoint:     user.Point,
		OldPointRank: oldPointRank,
		NewPoint:     user.Point + point,
		Type:         "D",
		IsoCode:      local.ISO_CODE,
		CreatedAt:    globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	// Insert point transaction
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], pointTransaction)
	// Check error
	if err != nil {
		// Post to slack if error and return
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, fmt.Sprintf("Error insert point transaction asker vote fav tasker: data: %v, error: %v", pointTransaction, err))
		return nil, &lib.ERROR_INSERT_FAILED, err
	}

	// Increase bPoint
	_, err = modelUser.UpdateOneById(
		local.ISO_CODE,
		user.XId,
		bson.M{
			"$inc": bson.M{
				"point":          point,
				"rankInfo.point": point,
			},
		},
	)
	if err != nil {
		// Post to slack if error and return
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, fmt.Sprintf("Error inc point for asker vote fav tasker: point: %v, error: %v", point, err))
		return nil, &lib.ERROR_INSERT_FAILED, err
	}
	// Send notification
	sendNotification(user, int32(point))
	// Return
	return map[string]interface{}{
		"reward": &modelService.ServiceText{
			Vi: fmt.Sprintf("%v bPoints", point),
			En: fmt.Sprintf("%v bPoints", point),
			Ko: fmt.Sprintf("%v bPoints", point),
			Th: fmt.Sprintf("%v bPoints", point),
			Id: fmt.Sprintf("%v bPoints", point),
		},
	}, nil, nil
}

func sendNotification(asker *modelUser.Users, point int32) {
	//Send notification
	title := localization.GetLocalizeObject("ASKER_VOTED_FAV_TASKER_TITLE", point)
	body := localization.GetLocalizeObject("ASKER_VOTED_FAV_TASKER_TEXT")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_BPOINTS_HISTORY,
	}
	listNotification := []interface{}{}
	lang := globalConstant.LANG_EN
	if asker.Language != "" {
		lang = asker.Language
	}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      asker.XId,
		Type:        32,
		Description: localization.T(lang, "ASKER_VOTED_FAV_TASKER_TEXT"),
		Title:       localization.T(lang, "ASKER_VOTED_FAV_TASKER_TITLE", point),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_BPOINTS_HISTORY,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	listNotification = append(listNotification, notify)
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: asker.XId, Language: lang},
	}
	// Send and insert Notification
	lib.SendNotification(listNotification, userIds, title, body, payload, "")
}
