package voteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func VoteTasker(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	// Check asker voted
	errCode = checkAskerVoted(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, nil
	}
	// Get data user
	asker, err := getAsker(reqBody.UserId)
	if err != nil || asker == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	// Insert data
	errCode, err = insertAskerVote(reqBody.UserId, reqBody.TaskerId)
	if errCode != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	// Send bPoint to Asker
	result, _, _ := sendBPointToAsker(asker)
	// Return data
	return result, nil, nil
}
