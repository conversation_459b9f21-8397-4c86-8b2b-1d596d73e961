package voteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAsker(userId string) (*modelUser.Users, error) {
	// Get data asker
	user, err := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"_id": 1, "favouriteTasker": 1, "blackList": 1, "language": 1})
	return user, err
}
