package voteTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelVoteFavTasker "gitlab.com/btaskee/go-services-model-v2/grpcmodel/voteFavTasker"
)

func insertAskerVote(askerId, taskerId string) (*globalResponse.ResponseErrorCode, error) {
	// Insert data asker vote fav tasker
	askerVoteFavTasker := &modelVoteFavTasker.VoteFavTasker{
		XId:       globalLib.GenerateObjectId(),
		AskerId:   askerId,
		TaskerId:  taskerId,
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	// Insert data
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_VOTE_FAV_TASKER[local.ISO_CODE], askerVoteFavTasker)
	// Return error if insert failed
	if err != nil {
		return &lib.ERROR_INSERT_FAILED, err
	}
	return nil, nil
}
