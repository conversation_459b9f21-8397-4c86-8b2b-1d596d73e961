/*
 * @File: getListEmployees.go
 * @Description: Handler function for getListEmployees api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List  Employees by UserIds
 * @CreatedAt: 21/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetListEmployees(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateGetListEmployees(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var users []map[string]interface{}
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"date": 1, "autoChooseTasker": 1, "serviceText": 1, "collectionDate": 1, "originCurrency": 1}, &task)
	if task != nil && globalLib.IsLaundryService(task.ServiceText.En) {
		collectionDate := globalLib.ParseDateFromTimeStamp(task.CollectionDate, local.TimeZone)
		beforeTime := collectionDate.Add(-30 * time.Minute)
		afterTime := collectionDate.Add(30 * time.Minute)
		//find task conflict time before and after 30min
		users, _ = modelUser.GetAllByQueryMap(
			local.ISO_CODE,
			bson.M{"_id": bson.M{"$in": reqBody.UserIds}},
			bson.M{"_id": 1, "name": 1, "avatar": 1, "phone": 1, "status": 1},
		)
		var tasks []*modelTask.Task
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
			bson.M{
				"date":                    bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
				"acceptedTasker.taskerId": bson.M{"$in": reqBody.UserIds},
				"status":                  globalConstant.TASK_STATUS_CONFIRMED,
				"detailLaundry":           bson.M{"$exists": true},
				"collectionDate":          bson.M{"$gte": beforeTime, "$lte": afterTime},
			},
			bson.M{"acceptedTasker": 1, "collectionDate": 1},
			&tasks,
		)
		//map task in users
		if len(tasks) > 0 && len(users) > 0 {
			for _, u := range users {
				var number int
				for _, t := range tasks {
					if t.AcceptedTasker[0].TaskerId == u["_id"].(string) {
						number++
					}
				}
				u["numberTaskConfict"] = number
			}
		}
	}
	result := map[string]interface{}{
		"users": users,
		"task":  task,
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetListEmployees(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if len(reqBody.UserIds) == 0 {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	return nil
}
