/*
 * @File: collectClothes.go
 * @Description: Handler function for collectClothes api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Collect Clothes by UserId
 * @CreatedAt: 25/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func CollectClothes(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateCollectClothes(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Set data
	var task *modelTask.Task
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"_id": reqBody.TaskId,
			"$or": []bson.M{
				{"acceptedTasker.taskerId": reqBody.UserId},
				{"acceptedTasker.companyId": reqBody.UserId},
			},
			"status": globalConstant.TASK_STATUS_CONFIRMED,
		},
		bson.M{"_id": 1, "askerId": 1},
		&task,
	)
	if task == nil {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}
	// Task is valid, update task status to RECEIVED
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		reqBody.TaskId,
		bson.M{"$set": bson.M{
			"detailLaundry.isReceived": true,
			"updatedAt":                globalLib.GetCurrentTime(local.TimeZone),
		}},
	)
	// Send notification to Asker that Clothes is collected
	sendNotificationToAsker(task)
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Send notification to asker
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func sendNotificationToAsker(task *modelTask.Task) {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"language": 1})
	lang := globalConstant.LANG_EN
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      task.AskerId,
		TaskId:      task.XId,
		Type:        25,
		Description: localization.T(lang, "NOTIFICATION_COLLECT_CLOTHES"),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_COLLECT_CLOTHES"),
		En: localization.T("en", "NOTIFICATION_COLLECT_CLOTHES"),
		Ko: localization.T("ko", "NOTIFICATION_COLLECT_CLOTHES"),
		Th: localization.T("th", "NOTIFICATION_COLLECT_CLOTHES"),
	}
	body := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_COLLECT_CLOTHES"),
		En: localization.T("en", "NOTIFICATION_COLLECT_CLOTHES"),
		Ko: localization.T("ko", "NOTIFICATION_COLLECT_CLOTHES"),
		Th: localization.T("th", "NOTIFICATION_COLLECT_CLOTHES"),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       25,
		TaskId:     task.XId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}

	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: task.AskerId, Language: lang},
	}
	lib.SendNotification([]interface{}{notify}, userIds, title, body, payload, "")
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateCollectClothes(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	return nil
}
