/*
 * @File: getTasksConfirmed.go
 * @Description: Handler function for getTasksConfirmed api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Task Confirmed by UserId
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTasksConfirmed(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	//get task confirmed, done only current date
	now := globalLib.GetCurrentTime(local.TimeZone)
	startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 0, 0, now.Location())
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"$and": []bson.M{
				{"$or": []bson.M{
					{"acceptedTasker.taskerId": reqBody.UserId},
					{"acceptedTasker.companyId": reqBody.UserId},
				}},
				{"$or": []bson.M{
					{"status": globalConstant.TASK_STATUS_CONFIRMED},
					{"date": bson.M{"$gte": startOfDay, "$lte": endOfDay}, "status": globalConstant.TASK_STATUS_DONE},
				}},
			},
		},
		bson.M{"date": 1, "duration": 1, "address": 1, "serviceText": 1, "serviceId": 1, "status": 1, "fromPartner": 1},
		bson.M{"date": 1},
		&tasks,
	)
	globalResponse.ResponseSuccess(w, tasks)
}
