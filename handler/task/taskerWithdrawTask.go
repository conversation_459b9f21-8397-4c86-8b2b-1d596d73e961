/*
 * @File: taskerWithdrawTask.go
 * @Description: Handler function for taskerWithdrawTask api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Tasker Withdraw Task by TaskId, UserId
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func TaskerWithdrawTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateTaskerWithdrawTask(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	var task *modelTask.Task
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": reqBody.TaskId, "status": globalConstant.TASK_STATUS_CONFIRMED}, bson.M{"acceptedTasker": 1}, &task)
	if task != nil && task.AcceptedTasker != nil && len(task.AcceptedTasker) > 0 {
		var taskers []*modelTask.TaskAcceptedTasker
		for _, v := range task.AcceptedTasker {
			if (v.CompanyId != "" && v.CompanyId != reqBody.UserId) || (v.TaskerId != reqBody.UserId) {
				taskers = append(taskers, v)
			}
		}
		if len(taskers) > 0 {
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
				reqBody.TaskId,
				bson.M{"$set": bson.M{"acceptedTasker": taskers}},
			)
		} else {
			globalDataAccess.UpdateOneById(
				globalCollection.COLLECTION_TASK[local.ISO_CODE],
				reqBody.TaskId,
				bson.M{
					"$set": bson.M{
						"status": globalConstant.TASK_STATUS_POSTED,
					},
					"$unset": bson.M{"acceptedTasker": 1, "intervalForAccept": 1},
				},
			)
		}
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateTaskerWithdrawTask(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
