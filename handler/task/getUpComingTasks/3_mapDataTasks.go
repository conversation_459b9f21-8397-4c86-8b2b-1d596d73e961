/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package getUpComingTasks

import (
	"github.com/spf13/cast"
	pkgTask "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
)

/*
 * @Description: Get UpComming Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func mapDataTasks(tasks []map[string]interface{}) []map[string]interface{} {
	newTasks := []map[string]interface{}{}
	mapTaskByScheduleId := make(map[string][]map[string]interface{})
	mapTaskBySubscriptionId := make(map[string][]map[string]interface{})
	// Get service
	for _, v := range tasks {
		// Update taskDone of acceptedTasker if it nil
		pkgTask.UpdateFieldTaskDone(v)
		mapData.MapCostDetailForAsker(v)
		pkgTask.RefactorTaskPaymentStatus(v)
		pkgTask.RefactorIfTaskChangeToCashBecausePaymentFailed(v)
		// Map task by scheduleId
		scheduleId := cast.ToString(v["scheduleId"])
		if scheduleId != "" {
			mapTaskByScheduleId[scheduleId] = append(mapTaskByScheduleId[scheduleId], v)
			continue
		}
		// Map task by subscriptionId
		subscriptionId := cast.ToString(v["subscriptionId"])
		if subscriptionId != "" {
			mapTaskBySubscriptionId[subscriptionId] = append(mapTaskBySubscriptionId[subscriptionId], v)
			continue
		}
		newTasks = append(newTasks, v)
	}
	// Set list task
	tasks = newTasks
	// Map task by scheduleId
	if len(mapTaskByScheduleId) > 0 {
		tasks = append(tasks, pkgTask.MapRelatedTasksToTask(mapTaskByScheduleId)...)
	}
	// Map task by subscriptionId
	if len(mapTaskBySubscriptionId) > 0 {
		tasks = append(tasks, pkgTask.MapRelatedTasksToTask(mapTaskBySubscriptionId)...)
	}
	return tasks
}
