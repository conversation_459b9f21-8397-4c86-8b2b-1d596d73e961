/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package getUpComingTasks

import (
	"encoding/json"

	"github.com/spf13/cast"
	pkgTask "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get UpComming Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func mapDataHomeMoving(tasks []map[string]interface{}) []map[string]interface{} {
	var services []*modelService.Service
	serviceQuery := bson.M{
		"$or": []bson.M{
			{"status": globalConstant.SERVICE_STATUS_ACTIVE},
			{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
		},
	}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceQuery, bson.M{"_id": 1, "name": 1, "text": 1, "icon": 1, "detailService.homeMoving": 1}, &services)
	// Convert list to map
	mapServiceById := map[string]*modelService.Service{}
	for _, v := range services {
		mapServiceById[v.XId] = v
	}

	// Map data tasks. And check if task has relatedTasks
	relatedTaskIds := []string{}
	shouldRefactorListTask := false
	for _, t := range tasks {
		// Update service info
		serviceId := cast.ToString(t["serviceId"])
		if s, ok := mapServiceById[serviceId]; ok {
			t["service"] = map[string]interface{}{
				"_id":  s.XId,
				"name": s.Name,
				"text": s.Text,
				"icon": s.Icon,
			}
		}

		// Check if the task has relatedTasks
		if t["relatedTasks"] != nil || t["sourceTaskId"] != nil {
			shouldRefactorListTask = true
			relatedTaskIds = append(relatedTaskIds, pkgTask.GetRelatedTaskIdsOfTask(t)...)
		}
		service := mapServiceById[serviceId]
		if service != nil && service.Name == globalConstant.SERVICE_KEY_NAME_HOME_MOVING {
			updateDetailHomeMovingTaskUpComing(t, service)
		}
	}

	// NOTE: function này cần để cuối cùng trước khi return to client. Vì nó sẽ lấy các task con đưa vào task cha. Như vậy khi loop lại mapTasks sau bước này sẽ không lặp qua các task con nữa
	if shouldRefactorListTask {
		tasks = pkgTask.RefactorListTaskHasRelatedTasks(tasks, mapServiceById, relatedTaskIds)
	}
	return tasks
}

func updateDetailHomeMovingTaskUpComing(task map[string]interface{}, service *modelService.Service) {
	startWorking := cast.ToStringMap(task["startWorking"])
	isStart := cast.ToBool(startWorking["isStart"])
	detailHomeMoving := cast.ToStringMap(task["detailHomeMoving"])
	taskDetailHomeMoving := &modelTask.TaskDetailHomeMoving{}
	taskDetailHomeMovingData, _ := json.Marshal(detailHomeMoving)
	json.Unmarshal(taskDetailHomeMovingData, &taskDetailHomeMoving)
	if taskDetailHomeMoving != nil {
		if isStart {
			detailHomeMoving["stepInProgress"] = pkgTask.GetHomeMovingStepInProgress(taskDetailHomeMoving, service)
		}
		if service.DetailService != nil && service.DetailService.HomeMoving != nil {
			taskPlace := cast.ToStringMap(task["taskPlace"])
			taskCity := cast.ToString(taskPlace["city"])
			for _, v := range service.DetailService.HomeMoving.City {
				if v.Name == taskCity {
					detailHomeMoving["waitingFeeSetting"] = v.WaitingFeeSetting
				}
			}
		}
		task["detailHomeMoving"] = detailHomeMoving
	}
}
