/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package getUpComingTasks

import (
	"github.com/jinzhu/copier"
	pkgTask "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get UpComming Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func getTasks(reqBody *model.ApiRequest) []map[string]interface{} {
	// Get task
	date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -lib.MAX_RATING_DATE)
	query := bson.M{
		"askerId":      reqBody.UserId,
		"date":         bson.M{"$gte": date},
		"status":       bson.M{"$in": [3]string{globalConstant.TASK_STATUS_POSTED, globalConstant.TASK_STATUS_WAITING, globalConstant.TASK_STATUS_CONFIRMED}},
		"isoCode":      reqBody.ISOCode,
		"sourceTaskId": bson.M{"$exists": false},
	}
	fields := bson.M{}
	copier.Copy(&fields, &pkgTask.TASK_FIELDS)
	fields["changesHistory"] = 1
	tasks, _ := globalDataAccess.GetAllByQuerySortMap(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		bson.M{"date": 1},
	)
	return tasks
}
