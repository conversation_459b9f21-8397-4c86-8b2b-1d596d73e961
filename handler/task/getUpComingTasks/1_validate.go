/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package getUpComingTasks

import (
	pkgTask "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/task"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	return pkgTask.ValidateGetListTasks(reqBody)
}
