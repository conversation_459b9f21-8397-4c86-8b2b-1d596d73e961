/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package getUpComingTasks

import (
	"sort"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

// sort task has payment.status == error || payment.paymentUpdatedAt > 15 minute before to top
func sortFailedPaymentTask(tasks []map[string]interface{}) {
	// Sort tasks
	sort.SliceStable(tasks, func(i, j int) bool {
		// Sort failed to top if one fail and one not fail
		iPaymentStatus := getTaskPaymentStatus(tasks[i])
		jPaymentStatus := getTaskPaymentStatus(tasks[j])
		if iPaymentStatus == globalConstant.TASK_PAYMENT_STATUS_ERROR && jPaymentStatus != globalConstant.TASK_PAYMENT_STATUS_ERROR {
			return true
		}
		if iPaymentStatus != globalConstant.TASK_PAYMENT_STATUS_ERROR && jPaymentStatus == globalConstant.TASK_PAYMENT_STATUS_ERROR {
			return false
		}

		// Sort createdAt -1 if both equal
		iCreatedAt := cast.ToTime(tasks[i]["date"])
		jCreatedAt := cast.ToTime(tasks[j]["date"])
		return iCreatedAt.Before(jCreatedAt)
	})
}

func getTaskPaymentStatus(task map[string]interface{}) string {
	payment := cast.ToStringMap(task["payment"])
	if payment == nil {
		return ""
	}
	return cast.ToString(payment["status"])
}
