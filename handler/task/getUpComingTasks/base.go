/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package getUpComingTasks

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Get UpComming Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetUpComingTasks(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	// Get task
	tasks := getTasks(reqBody)
	if len(tasks) == 0 {
		return tasks, nil
	}
	// Map data to task
	tasks = mapDataTasks(tasks)
	// Map data for task homeMoving
	tasks = mapDataHomeMoving(tasks)
	// Sort data list task
	sortFailedPaymentTask(tasks)
	// globalResponse.ResponseSuccess(w, tasks)
	return tasks, nil
}
