package checkTaskerConflictTime

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.TaskDate == nil {
		return &lib.ERROR_DATE_REQUIRED
	}
	if reqBody.Duration == 0 {
		return &lib.ERROR_DURATION_REQUIRED
	}
	if reqBody.TaskerId == "" {
		return &lib.ERROR_TASKER_ID_REQUIRED
	}
	if reqBody.Lat == 0 || reqBody.Lng == 0 {
		return &lib.ERROR_LAT_LNG_REQUIRED
	}
	if reqBody.ServiceId == "" {
		return &lib.ERROR_SERVICE_ID_REQUIRED
	}
	return nil
}
