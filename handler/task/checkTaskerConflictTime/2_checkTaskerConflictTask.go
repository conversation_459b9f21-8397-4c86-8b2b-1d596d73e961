package checkTaskerConflictTime

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

// checkTaskerConflictTask checks if a new task conflicts with existing tasks.
// It takes a pointer to a new task as input and returns a pointer to a response error code.
// If there is a conflict, it returns the error code corresponding to tasker conflict time.
// Otherwise, it returns nil.

func checkTaskerConflictTask(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// get service
	service := getService(reqBody.ServiceId, reqBody.ISOCode)
	// if service is not laundry no need check
	if globalLib.IsLaundryServiceByKeyName(service.Name) {
		return nil
	}
	// get taskDate
	taskDate := reqBody.TaskDate.In(local.TimeZone)
	// get new task
	newTask := &modelTask.Task{
		Date:     globalLib.ParseTimestampFromDate(taskDate),
		Duration: reqBody.Duration,
		Lat:      reqBody.Lat,
		Lng:      reqBody.Lng,
	}
	// get tasks in day by date
	tasks := getTasks(reqBody, taskDate)
	if len(tasks) == 0 {
		return nil
	}
	// get time in between
	timeInBetweenTask := getTimeInBetweenTask(reqBody.ISOCode)
	//	check conflict
	for _, v := range tasks {
		if globalLib.IsConflictTask(newTask, v, timeInBetweenTask, local.TimeZone) {
			return &lib.ERROR_TASKER_CONFLICT_TIME
		}
	}

	return nil
}
