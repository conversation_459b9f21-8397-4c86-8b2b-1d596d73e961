package checkTaskerConflictTime

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

// CheckTaskerConflictTime calculates the new price for a task with an increased duration.
// It takes an Input object as the request body and returns a ResponseErrorCode and an error.
func CheckTaskerConflictTime(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. Validate the request body
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 3. Recalculate the price with the new duration
	errCode = checkTaskerConflictTask(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	return nil, nil
}
