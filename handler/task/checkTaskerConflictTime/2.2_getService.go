package checkTaskerConflictTime

import (
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

func getService(serviceId, isoCode string) *modelService.Service {
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[isoCode], serviceId, bson.M{"postingLimits": 1, "name": 1}, &service)
	return service
}
