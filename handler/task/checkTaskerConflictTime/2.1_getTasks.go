package checkTaskerConflictTime

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasks(reqBody *model.ApiRequest, taskDate time.Time) []*modelTask.Task {
	startOfDay := globalLib.StartADay(taskDate)
	endOfDay := globalLib.EndADay(taskDate)
	// get task in day
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_TASK[reqBody.ISOCode],
		bson.M{
			"acceptedTasker.taskerId": reqBody.TaskerId,
			"status":                  globalConstant.TASK_STATUS_CONFIRMED,
			"date":                    bson.M{"$gte": startOfDay, "$lte": endOfDay},
		},
		bson.M{"date": 1, "lat": 1, "lng": 1, "duration": 1},
		bson.M{"date": -1},
		&tasks,
	)
	return tasks
}
