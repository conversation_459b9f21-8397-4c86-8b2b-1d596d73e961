package getTaskerFreeTime

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

func getTaskerFreeTime(reqBody *model.ApiRequest) interface{} {
	type HourMin struct {
		Hour int
		Min  int
	}
	formatDate := map[string][]HourMin{
		"morning": []HourMin{
			{Hour: 6, Min: 0}, {Hour: 6, Min: 30}, {Hour: 7, Min: 0}, {Hour: 7, Min: 30}, {Hour: 8, Min: 0}, {Hour: 8, Min: 30},
			{Hour: 9, Min: 0}, {Hour: 9, Min: 30}, {Hour: 10, Min: 0}, {Hour: 10, Min: 30}, {Hour: 11, Min: 0}, {Hour: 11, Min: 30}, {Hour: 12, Min: 0},
		},
		"afternoon": []HourMin{
			{Hour: 12, Min: 30}, {Hour: 13, Min: 0}, {Hour: 13, Min: 30}, {Hour: 14, Min: 0}, {Hour: 14, Min: 30}, {Hour: 15, Min: 0},
			{Hour: 15, Min: 30}, {Hour: 16, Min: 0}, {Hour: 16, Min: 30}, {Hour: 17, Min: 0}, {Hour: 17, Min: 30}, {Hour: 18, Min: 0},
		},
		"evening": []HourMin{
			{Hour: 18, Min: 30}, {Hour: 19, Min: 0}, {Hour: 19, Min: 30}, {Hour: 20, Min: 0}, {Hour: 20, Min: 30}, {Hour: 21, Min: 0},
			{Hour: 21, Min: 30}, {Hour: 22, Min: 0}, {Hour: 22, Min: 30}, {Hour: 23, Min: 0}, {Hour: 23, Min: 30},
		},
	}
	taskDate := reqBody.TaskDate.In(local.TimeZone)
	freeTime := make(map[string][]time.Time)
	service := getService(reqBody.ServiceId, reqBody.ISOCode)

	// Thời gian đăng việc phải trước 6h
	limitPostTime := globalLib.GetCurrentTime(local.TimeZone).Add(6 * time.Hour)
	// If laundry service dont need check conflict time
	if globalLib.IsLaundryServiceByKeyName(service.Name) {
		for k, v := range formatDate {
			for _, t := range v {
				timeDate := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), t.Hour, t.Min, 0, 0, local.TimeZone)
				// Check limit post time
				if timeDate.Before(limitPostTime) || timeDate.Equal(limitPostTime) {
					continue
				}
				// Check limit post task by service
				if isLimitPostTask(service, timeDate, reqBody.Duration) {
					continue
				}
				// Add time to free time
				if _, ok := freeTime[k]; !ok {
					freeTime[k] = []time.Time{}
				}
				freeTime[k] = append(freeTime[k], timeDate)
			}
		}
		return freeTime
	}
	// get time in between
	timeInBetweenTask := getTimeInBetweenTask(reqBody.ISOCode)
	// Get task in day by date
	tasks := getTasks(reqBody, taskDate)
	// get free time
	for k, v := range formatDate {
		for _, t := range v {
			timeDate := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), t.Hour, t.Min, 0, 0, local.TimeZone)
			// Check limit post time
			if timeDate.Before(limitPostTime) || timeDate.Equal(limitPostTime) {
				continue
			}
			// Check limit post task by service
			if isLimitPostTask(service, timeDate, reqBody.Duration) {
				continue
			}
			isConflictTask := false
			// Check time has conflict
			for _, task := range tasks {
				task2 := &modelTask.Task{
					Date:     globalLib.ParseTimestampFromDate(timeDate),
					Duration: reqBody.Duration,
					Lat:      reqBody.Lat,
					Lng:      reqBody.Lng,
				}
				// Check time has conflict with task
				if globalLib.IsConflictTask(task, task2, timeInBetweenTask, local.TimeZone) {
					isConflictTask = true
					break
				}
			}
			// if has conflict then continue
			if isConflictTask {
				continue
			}
			// Add time to free time
			if _, ok := freeTime[k]; !ok {
				freeTime[k] = []time.Time{}
			}
			freeTime[k] = append(freeTime[k], timeDate)
		}
	}

	return freeTime
}
