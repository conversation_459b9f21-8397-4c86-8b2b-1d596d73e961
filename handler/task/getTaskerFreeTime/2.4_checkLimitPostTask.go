package getTaskerFreeTime

import (
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
)

func isLimitPostTask(service *modelService.Service, taskDate time.Time, duration float64) bool {
	// Not set limit time
	if service.PostingLimits == nil || service.PostingLimits.From == "" || service.PostingLimits.To == "" {
		return false
	}
	beforeTime, _ := time.Parse(globalConstant.LAYOUT_TIME, service.PostingLimits.From)
	afterTime, _ := time.Parse(globalConstant.LAYOUT_TIME, service.PostingLimits.To)

	// if service is HOME_COOKING, posting time is about eating time, not post task
	// so need to minus duration to convert to post task
	if globalLib.IsHomeCookingServiceByKeyName(service.Name) {
		beforeTime = beforeTime.Add(-1 * time.Duration(duration) * time.Hour)
		afterTime = afterTime.Add(-1 * time.Duration(duration) * time.Hour)
	}

	// Check posting limit
	startTaskDateTimeFormat, _ := time.Parse(globalConstant.LAYOUT_TIME, taskDate.Format(globalConstant.LAYOUT_TIME))
	endTaskDateTime := taskDate.Add(time.Duration(duration) * time.Hour)
	endTaskDateTimeFormat, _ := time.Parse(globalConstant.LAYOUT_TIME, endTaskDateTime.Format(globalConstant.LAYOUT_TIME))
	if startTaskDateTimeFormat.Before(beforeTime) || startTaskDateTimeFormat.After(afterTime) ||
		endTaskDateTimeFormat.Before(beforeTime) || endTaskDateTimeFormat.After(afterTime) {
		return true
	}
	return false
}
