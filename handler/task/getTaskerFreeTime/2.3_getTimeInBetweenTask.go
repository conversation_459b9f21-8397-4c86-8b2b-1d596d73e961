package getTaskerFreeTime

import (
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	"go.mongodb.org/mongo-driver/bson"
)

func getTimeInBetweenTask(isoCode string) int {
	timeInBetweenTask := 30
	var setting *modelSetting.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[isoCode], bson.M{}, bson.M{"timeInBetweenTask": 1}, &setting)
	if setting != nil && setting.TimeInBetweenTask > 0 {
		timeInBetweenTask = int(setting.TimeInBetweenTask)
	}

	return timeInBetweenTask
}
