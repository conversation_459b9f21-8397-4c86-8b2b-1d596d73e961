/*
 * @File: updateTaskReport.go
 * @Description: Handler function for updateTaskReport api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Data Task Report
 * @CreatedAt: 09/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func UpdateTaskReport(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateUpdateTaskReport(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Update data to database
	data := reqBody.ReportData
	data["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"$set": bson.M{"report": data}})
	if err != nil {
		local.Logger.Warn(lib.ERROR_UPDATE_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}

	// wg.Wait()
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Update Data Task Report
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateUpdateTaskReport(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.ReportData == nil {
		return &lib.ERROR_REPORT_DATA_REQUIRED
	}
	return nil
}
