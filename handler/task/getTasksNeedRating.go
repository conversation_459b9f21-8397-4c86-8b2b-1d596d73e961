/*
 * @File: getTasksNeedRating.go
 * @Description: Handler function for getTasksNeedRating api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Tasks Need Rating by UserId
 * @CreatedAt: 09/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTasksNeedRating(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	d := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -7)
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"askerId": reqBody.UserId,
			"isoCode": reqBody.ISOCode,
			"date":    bson.M{"$gte": d},
			"status":  globalConstant.TASK_STATUS_DONE,
			"$and": []bson.M{
				{
					"$or": []bson.M{
						{"rated": bson.M{"$exists": false}},
						{"rated": false},
					},
				},
				{
					"$or": []bson.M{
						{"isCloseRating": bson.M{"$exists": false}},
						{"isCloseRating": false},
					},
				},
			},
		},
		bson.M{"_id": 1, "acceptedTasker": 1},
		&tasks,
	)
	globalResponse.ResponseSuccess(w, tasks)
}
