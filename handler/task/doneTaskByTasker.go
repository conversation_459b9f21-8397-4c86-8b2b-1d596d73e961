/*
 * @File: doneTaskByTasker.go
 * @Description: Handler function for doneTaskByTasker api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Done Task by Tasker (UserId)
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func DoneTaskByTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	page := 1
	limit := 20
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}
	// Get data
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQueryPagingSort(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"acceptedTasker.taskerId": reqBody.UserId, "status": globalConstant.TASK_STATUS_DONE},
		bson.M{"_id": 1, "serviceId": 1},
		int64(page),
		int64(limit),
		bson.M{"createdAt": -1},
		&tasks,
	)

	var result []map[string]interface{}
	if len(tasks) > 0 {
		var serviceIds, taskIds []string
		for _, t := range tasks {
			serviceIds = append(serviceIds, t.ServiceId)
			taskIds = append(taskIds, t.XId)
		}
		var services []*modelService.Service
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"_id": bson.M{"$in": serviceIds}}, bson.M{"text": 1}, &services)
		var ratings []*modelRating.Rating
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE],
			bson.M{"taskId": bson.M{"$in": taskIds}},
			bson.M{"taskId": 1, "rate": 1, "review": 1, "feedBack": 1},
			&ratings,
		)
		for _, t := range tasks {
			item := make(map[string]interface{})
			for _, s := range services {
				if s.XId == t.ServiceId {
					item["service"] = s
					break
				}
			}
			for _, r := range ratings {
				if r.TaskId == t.XId {
					item["rating"] = r
					break
				}
			}
			if item["service"] != nil || item["rating"] != nil {
				result = append(result, item)
			}
		}
	}
	globalResponse.ResponseSuccess(w, map[string]interface{}{"data": result})
}
