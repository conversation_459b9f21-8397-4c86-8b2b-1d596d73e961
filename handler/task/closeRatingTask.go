/*
 * @File: closeRatingTask.go
 * @Description: Handler function for closeRatingTask api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Close Rating Task by TaskId, UserId
 * @CreatedAt: 17/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func CloseRatingTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateCloseRatingTask(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Update task
	_, err := globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": reqBody.TaskId, "askerId": reqBody.UserId},
		bson.M{
			"$set": bson.M{
				"isCloseRating": true,
				"updatedAt":     globalLib.GetCurrentTime(local.TimeZone),
			},
		})
	if err != nil {
		local.Logger.Warn(lib.ERROR_UPDATE_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Close Rating Task by TaskId, UserId
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateCloseRatingTask(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	return nil
}
