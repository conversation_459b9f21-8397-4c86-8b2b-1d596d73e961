/*
 * @File: taskerRefuseTask.go
 * @Description: Handler function for taskerRefuseTask api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Tasker Refuse Task by TaskId, UserId
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func TaskerRefuseTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateTaskerRefuseTask(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"askerId": 1, "serviceText": 1, "isTaskerRefuse": 1}, &task)
	if task == nil {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}
	if task.IsTaskerRefuse {
		globalResponse.ResponseError(w, lib.ERROR_TASK_REFUSED)
		return
	}
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"name": 1})
	if tasker == nil {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	// Send notification
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		sendTaskerRefuseTaskNotificationToAsker(task, reqBody, tasker)
	}()
	// Update task refuse status
	now_ts := globalLib.GetCurrentTime(local.TimeZone)
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		reqBody.TaskId, bson.M{
			"$set": bson.M{
				"taskerRefuse": bson.M{"status": true, "timeRefuse": now_ts},
				"updatedAt":    now_ts,
			},
		})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": reqBody.UserId, "taskId": reqBody.TaskId, "type": 35})
	wg.Wait()

	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Send refuse notification to asker
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func sendTaskerRefuseTaskNotificationToAsker(task *modelTask.Task, reqBody *model.ApiRequest, tasker *modelUser.Users) {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"language": 1})
	lang := globalConstant.LANG_VI
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      task.AskerId,
		TaskId:      reqBody.TaskId,
		Type:        25,
		Description: localization.T(lang, "NOTIFICATION_TASKER_REFUSE", tasker.Name),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "DIALOG_TITLE_INFORMATION"),
		En: localization.T("en", "DIALOG_TITLE_INFORMATION"),
		Ko: localization.T("ko", "DIALOG_TITLE_INFORMATION"),
		Th: localization.T("th", "DIALOG_TITLE_INFORMATION"),
	}
	body := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_TASKER_REFUSE", tasker.Name),
		En: localization.T("en", "NOTIFICATION_TASKER_REFUSE", tasker.Name),
		Ko: localization.T("ko", "NOTIFICATION_TASKER_REFUSE", tasker.Name),
		Th: localization.T("th", "NOTIFICATION_TASKER_REFUSE", tasker.Name),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       25,
		TaskId:     reqBody.TaskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}

	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: task.AskerId, Language: lang},
	}
	lib.SendNotification([]interface{}{notify}, userIds, title, body, payload, "")
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateTaskerRefuseTask(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
