/*
 * @File: getDoneTask.go
 * @Description: Handler function for doneTaskByTasker api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Task Done by UserId
 * @CreatedAt: 16/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetDoneTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Get data
	minDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -3)
	y, m, d := minDate.Date()
	minDateStartOfHour := time.Date(y, m, d, 0, 0, 0, 0, minDate.Location())
	now := globalLib.GetCurrentTime(local.TimeZone)
	var result *modelTask.Task
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"date": bson.M{"$gte": minDateStartOfHour, "$lt": now},
			"$or": []bson.M{
				{"acceptedTasker.taskerId": reqBody.UserId},
				{"acceptedTasker.companyId": reqBody.UserId},
			},
			"status":      globalConstant.TASK_STATUS_DONE,
			"taskerRated": false,
		},
		bson.M{"_id": 1, "askerId": 1, "serviceText": 1, "acceptedTasker": 1, "serviceId": 1, "duration": 1, "date": 1, "address": 1, "contactName": 1, "fromPartner": 1},
		&result,
	)
	globalResponse.ResponseSuccess(w, result)
}
