package getTaskTodoList

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

func checkPermission(task *modelTask.Task, userId string) *globalResponse.ResponseErrorCode {
	if task.AskerId != userId {
		return &lib.ERROR_NOT_HAVE_PERMISSION_GET_CHECKLIST
	}
	return nil
}
