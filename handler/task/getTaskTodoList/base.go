package getTaskTodoList

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetTaskTodoList(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1 validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get task
	task, errCode, err := getTask(reqBody.TaskId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. Check permission
	errCode = checkPermission(task, reqBody.UserId)
	if errCode != nil {
		return nil, errCode, nil
	}

	return map[string]interface{}{
		"todoList": task.TodoList,
	}, nil, nil
}
