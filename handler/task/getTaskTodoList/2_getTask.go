package getTaskTodoList

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTask(taskId string) (*task.Task, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": taskId}
	fields := bson.M{"_id": 1, "status": 1, "acceptedTasker": 1, "todoList": 1, "askerId": 1}
	task, err := handler.GetTask(query, fields)
	if err != nil || task == nil {
		return nil, &lib.ERROR_TASK_NOT_FOUND, err
	}
	if task.Status != globalConstant.TASK_STATUS_CONFIRMED {
		return nil, &lib.ERROR_TASK_STATUS_NOT_ALLOW_VIEW_TASK_LIST, nil
	}
	return task, nil, nil
}
