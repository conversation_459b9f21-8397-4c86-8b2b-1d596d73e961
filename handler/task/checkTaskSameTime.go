/*
 * @File: checkTaskSameTime.go
 * @Description: Handler function for checkTaskSameTime api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Check Task Same Time by UserId
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 25/10/2021
 * @UpdatedBy: vinhnt
 */
func CheckTaskSameTime(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateCheckTaskSameTime(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check data
	var result bool
	isUserExist, _ := modelUsers.IsExistById(local.ISO_CODE, reqBody.UserId)
	if isUserExist {
		var service *modelService.Service
		globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], reqBody.ServiceId, bson.M{"text": 1}, &service)
		if service != nil && service.Text != nil {
			// if globalLib.IsHomeCleaningService(service.Text.En) || globalLib.IsACService(service.Text.En) || globalLib.IsHomeCookingService(service.Text.En) || globalLib.IsPatientCareService(service.Text.En) || globalLib.IsElderlyCareService(service.Text.En) {
			taskDate := reqBody.TaskDate.In(local.TimeZone)
			// Check cho task đi chợ. Default duration sẽ là 1h. sẽ chỉnh sau này nếu có update
			if globalLib.IsGroceryAssistantService(service.Text.En) {
				taskDurationGroceryAssistant := 1 // Default duration for GroceryAssistant
				taskDate = taskDate.Add(time.Duration(-taskDurationGroceryAssistant) * time.Hour)
			}
			start := taskDate.Add(-10 * time.Minute)
			end := taskDate.Add(10 * time.Minute)
			query := bson.M{
				"date":      bson.M{"$gte": start, "$lte": end},
				"askerId":   reqBody.UserId,
				"status":    bson.M{"$nin": [3]string{globalConstant.TASK_STATUS_CANCELED, globalConstant.TASK_STATUS_EXPIRED, globalConstant.TASK_STATUS_DONE}},
				"serviceId": reqBody.ServiceId,
			}

			if reqBody.ISOCode != "" {
				query["isoCode"] = reqBody.ISOCode
			}

			result, _ = globalDataAccess.IsExistByQuery(
				globalCollection.COLLECTION_TASK[local.ISO_CODE],
				query,
			)
		}
		// }
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateCheckTaskSameTime(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ServiceId == "" {
		return &lib.ERROR_SERVICE_ID_REQUIRED
	}
	if reqBody.TaskDate == nil {
		return &lib.ERROR_DATE_REQUIRED
	}
	return nil
}
