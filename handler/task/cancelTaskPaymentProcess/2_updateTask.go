package cancelTaskPaymentProcess

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

// Update payment.status thánh CANCELED.
// Nếu payment.status đã là PAID thì không thể update.
// Nếu payment.isPaymentAttempted đã là true thì không update để show trạng thái sẽ chuyển sang tiền mặt sau 15 phút trên app
func updateTask(taskId string, paymentMethod string) (*globalResponse.ResponseErrorCode, error) {
	_, err := globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"_id":                        taskId,
			"payment.method":             paymentMethod,
			"payment.status":             bson.M{"$ne": globalConstant.TASK_PAYMENT_STATUS_PAID},
			"payment.isPaymentAttempted": bson.M{"$ne": true},
		},
		bson.M{
			"$set": bson.M{
				"payment.status": globalConstant.TASK_PAYMENT_STATUS_CANCELED,
			},
		},
	)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}
	return nil, nil
}
