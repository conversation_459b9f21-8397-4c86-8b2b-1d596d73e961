/*
 * @File: getUpCommingTasks.go
 * @Description: Handler function for getUpCommingTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get UpComming Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetUpCommingTasksMiniApp(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get task
	date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -lib.MAX_RATING_DATE)
	fields := bson.M{
		"_id":                  1,
		"isoCode":              1,
		"serviceId":            1,
		"duration":             1,
		"originCurrency":       1,
		"status":               1,
		"acceptedTasker":       1,
		"costDetail.finalCost": 1,
		"costDetail.currency":  1,
		"payment":              1,
		"createdAt":            1,
		"date":                 1,
		"address":              1,
		"askerId":              1,
		"description":          1,
		"isPrepayTask":         1,
	}
	query := bson.M{
		"askerId": reqBody.UserId,
		"date":    bson.M{"$gte": date},
		"status":  bson.M{"$in": [3]string{globalConstant.TASK_STATUS_POSTED, globalConstant.TASK_STATUS_WAITING, globalConstant.TASK_STATUS_CONFIRMED}},
		"isoCode": reqBody.ISOCode,
	}
	if reqBody.FromPartner != "" {
		query["fromPartner"] = reqBody.FromPartner
	}
	tasks, _ := globalDataAccess.GetAllByQuerySortMap(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		bson.M{"createdAt": -1},
	)
	if len(tasks) > 0 {
		var serviceIds []string
		for _, v := range tasks {
			// Update taskDone of acceptedTasker if it nil
			UpdateFieldTaskDone(v)

			// Get list service of all tasks
			if v["serviceId"] != nil {
				serviceIds = append(serviceIds, v["serviceId"].(string))
			}
		}
		if len(serviceIds) > 0 {
			serviceIds = globalLib.UniqString(serviceIds)
			var services []*modelService.Service
			globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"_id": bson.M{"$in": serviceIds}}, bson.M{"_id": 1, "name": 1, "text": 1, "icon": 1}, &services)
			if len(services) > 0 {
				for _, t := range tasks {
					for _, s := range services {
						if t["serviceId"] != nil && t["serviceId"].(string) == s.XId {
							t["service"] = map[string]interface{}{
								"_id":  s.XId,
								"name": s.Name,
								"text": s.Text,
								"icon": s.Icon,
							}
							break
						}
					}
				}
			}
		}
	}

	globalResponse.ResponseSuccess(w, tasks)
}
