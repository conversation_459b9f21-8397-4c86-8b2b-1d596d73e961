/*
 * @File: translateTaskNote.go
 * @Description: Handler function for translateTaskNote api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Translate Task Note by TaskkId
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func TranslateTaskNote(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.TaskId == "" {
		local.Logger.Warn(lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_BOOKING_ID_REQUIRED)
		return
	}
	// Set data
	toLanguage := globalConstant.LANG_VI
	if reqBody.Language != "" {
		toLanguage = reqBody.Language
	}
	result := make(map[string]interface{})
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"taskNote": 1, "taskNoteTranslated": 1}, &task)
	if task != nil {
		if task.TaskNoteTranslated != nil {
			result["translated"] = true
			result["translatedText"] = task.TaskNoteTranslated.Text
		} else {
			//1. Check note's language
			lang, err := globalLib.DetectLanguage(task.TaskNote, cfg.CloudTranslationAPI)
			if err != nil {
				message := fmt.Sprintf("Error DetectLanguage. api: /translate-task-note. err: %s", err.Error())
				globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], "bTaskee System", message)
			}
			if lang == nil || lang["confidence"].(int) == 0 {
				result["translated"] = false
				result["detail"] = "Can not detect language"
			} else if lang["language"].(string) == globalConstant.LANG_VI {
				//update database
				globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
					reqBody.TaskId,
					bson.M{"$set": bson.M{
						"taskNoteTranslated": bson.M{
							"text":         task.TaskNote,
							"translatedAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					}})
				result["translated"] = true
				result["translatedText"] = task.TaskNote
			} else {
				translatedNote, err := globalLib.TranslateFromGoogle(task.TaskNote, toLanguage, cfg.CloudTranslationAPI)
				if err != nil {
					message := fmt.Sprintf("Error TranslateFromGoogle. api: /translate-task-note. err: %s", err.Error())
					globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], "bTaskee System", message)
				}
				if translatedNote == nil {
					result["translated"] = false
					result["detail"] = "Can not translate"
				} else {
					//update database
					globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
						reqBody.TaskId,
						bson.M{"$set": bson.M{
							"taskNoteTranslated": bson.M{
								"text":         translatedNote["translatedText"],
								"translatedAt": globalLib.GetCurrentTime(local.TimeZone),
							},
						}},
					)
					result["translated"] = true
					result["translatedText"] = translatedNote["translatedText"]
				}
			}
		}
	}
	globalResponse.ResponseSuccess(w, result)
}
