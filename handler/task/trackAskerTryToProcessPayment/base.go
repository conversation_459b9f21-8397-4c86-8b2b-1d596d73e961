package trackAskerTryToProcessPayment

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func TrackAskerTryToProcessPayment(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. Validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. Update task
	return updateTask(reqBody.TaskId, reqBody.PaymentMethod)
}
