package trackAskerTryToProcessPayment

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func updateTask(taskId string, paymentMethod string) (*globalResponse.ResponseErrorCode, error) {
	_, err := globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"_id":            taskId,
			"payment.status": bson.M{"$ne": globalConstant.TASK_PAYMENT_STATUS_PAID},
			"payment.method": paymentMethod,
			"isPrepayTask":   true,
		},
		bson.M{"$set": bson.M{"payment.isPaymentAttempted": true}},
	)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}
	return nil, nil
}
