/*
 * @File: getListHistoryTasks.go
 * @Description: Handler function for getListHistoryTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"encoding/json"
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelPaymentTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

/*
 * @Description: Get List History Tasks by UserId
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetListHistoryTasks(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	limit := lib.PAGING_LIMIT
	page := 1
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	// Get task
	fields := bson.M{"currency": 0, "pricing": 0, "viewedTaskers": 0, "visibility": 0, "voiceCallHistory": 0, "costDetail.currency": 0}
	tasks, _ := globalDataAccess.GetAllByQueryPagingSortMap(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"askerId":      reqBody.UserId,
			"status":       bson.M{"$in": [3]string{globalConstant.TASK_STATUS_DONE, globalConstant.TASK_STATUS_EXPIRED, globalConstant.TASK_STATUS_CANCELED}},
			"isoCode":      reqBody.ISOCode,
			"sourceTaskId": bson.M{"$exists": false},
		},
		fields,
		int64(page),
		int64(limit),
		bson.M{"date": -1},
	)
	var mapTasks []map[string]interface{}
	currentTS := globalLib.GetCurrentTimestamp(local.TimeZone)
	if len(tasks) > 0 {
		// Get users
		var taskIds []string
		for _, t := range tasks {
			// Update taskDone of acceptedTasker if it nil.
			UpdateFieldTaskDone(t)

			taskIds = append(taskIds, t["_id"].(string))
			mapData.MapCostDetailForAsker(t)
		}
		// Get rating
		var rates []*modelRating.Rating
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskId": bson.M{"$in": taskIds}}, bson.M{}, &rates)

		// Get collection name by isoCode
		var faTransactions []*modelFATransaction.FinancialAccountTransaction
		var refundTransactions []*modelFATransaction.FinancialAccountTransaction
		var paymentTransactions []*modelPaymentTransaction.PaymentTransaction
		var refundReqs []*modelRefundRequest.RefundRequest

		collectionFATransaction := globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE]
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PAYMENT_TRANSACTION,
			bson.M{"data.taskId": bson.M{"$in": taskIds}},
			bson.M{"cost": 1, "data": 1},
			&paymentTransactions,
		)
		// Get FA transactions
		globalDataAccess.GetAllByQuery(collectionFATransaction,
			bson.M{"source.value": bson.M{"$in": taskIds}, "source.name": globalConstant.FA_TRANSACTION_SOURCE_NAME_CANCEL_TASK},
			bson.M{"amount": 1, "source": 1},
			&faTransactions,
		)
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE],
			bson.M{"taskId": bson.M{"$in": taskIds}},
			bson.M{"status": 1, "amount": 1, "cancelTaskFee": 1, "taskId": 1},
			&refundReqs,
		)
		globalDataAccess.GetAllByQuery(collectionFATransaction,
			bson.M{"source.value": bson.M{"$in": taskIds}, "source.name": bson.M{"$regex": primitive.Regex{Pattern: globalConstant.FA_TRANSACTION_SOURCE_NAME_REFUND_CANCEL_TASK, Options: "i"}}},
			bson.M{"source": 1, "amount": 1},
			&refundTransactions,
		)
		// Get services
		var services []*modelService.Service
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{}, bson.M{"_id": 1, "name": 1, "text": 1, "icon": 1, "tetBookingDates": 1}, &services)
		serviceMap := make(map[string]*modelService.Service)
		for _, s := range services {
			serviceMap[s.XId] = s
		}
		// Get list outStandingPayment of list task
		outstandingPaymentByTaskId := getTaskNewOutstandingPaymentByUserId(taskIds, reqBody.UserId)

		relatedTaskIds := []string{}
		shouldRefactorListTask := false
		for _, t := range tasks {
			taskId, _ := t["_id"].(string)
			if t["rated"].(bool) && rates != nil && len(rates) > 0 {
				for _, r := range rates {
					if r.TaskId == taskId {
						t["rate"] = r
						break
					}
				}
			}
			// Get cancellation fee
			payment, err := t["payment"].(map[string]interface{})
			if t["status"].(string) == globalConstant.TASK_STATUS_CANCELED && err {
				paymentStatus, _ := payment["status"].(string)
				if paymentStatus == globalConstant.PAYMENT_STATUS_PAID {
					var cancelFeeExist = false
					for _, ref := range refundReqs {
						if ref.TaskId == taskId {
							t["refund"] = map[string]interface{}{
								"status": ref.Status,
								"amount": ref.Amount,
							}
							t["cancellationFee"] = ref.CancelTaskFee
							cancelFeeExist = true
							break
						}
					}
					if !cancelFeeExist {
						for _, tr := range refundTransactions {
							if tr.Source != nil && tr.Source.Value == taskId && tr.Amount > 0 {
								t["refund"] = map[string]interface{}{
									"status": globalConstant.REFUND_REQUEST_STATUS_PAID,
									"amount": tr.Amount,
								}
								t["cancellationFee"] = tr.Source.CancelTaskFee
								break
							}
						}
					}
				} else {
					paymentMethod, _ := payment["method"].(string)
					switch paymentMethod {
					case globalConstant.PAYMENT_METHOD_CARD:
						for _, tr := range paymentTransactions {
							if tr.Data != nil && tr.Data.TaskId == taskId && tr.Cost > 0 {
								t["cancellationFee"] = tr.Cost
								break
							}
						}
					default:
						if len(faTransactions) > 0 {
							for _, tr := range faTransactions {
								if tr.Source != nil && tr.Source.Value == taskId && tr.Amount > 0 {
									t["cancellationFee"] = tr.Amount
									break
								}
							}
						}
					}
				}
			}
			// Check if asker can rating task
			now := globalLib.GetCurrentTime(local.TimeZone)
			tDate, _ := t["date"].(string)
			taskDate, _ := time.ParseInLocation(time.RFC3339, tDate, local.TimeZone)
			if t["status"].(string) == globalConstant.TASK_STATUS_DONE && now.After(taskDate.AddDate(0, 0, 7)) {
				t["askerCanNotRate"] = true
			}
			service, ok := serviceMap[t["serviceId"].(string)]
			if ok {
				t["service"] = map[string]interface{}{
					"_id":  service.XId,
					"name": service.Name,
					"text": service.Text,
					"icon": service.Icon,
				}
			}

			// Set outStandingPayment to task if exists
			outStandingPayment := map[string]interface{}{}
			if outstandingPaymentByTaskId[taskId] != nil {
				// Add card info to outStandingPayment
				if outstandingPaymentByTaskId[taskId].CardInfo != nil {
					outStandingPayment["cardNumber"] = outstandingPaymentByTaskId[taskId].CardInfo.Number
					outStandingPayment["cardType"] = outstandingPaymentByTaskId[taskId].CardInfo.Type
					outStandingPayment["date"] = globalLib.ParseDateFromTimeStamp(outstandingPaymentByTaskId[taskId].CreatedAt, local.TimeZone)
					// append to task object
					t["outStandingPayment"] = outStandingPayment
				}
			}
			if service != nil && service.TetBookingDates == nil || (service != nil && service.TetBookingDates != nil && (currentTS.Seconds < service.TetBookingDates.FromDate.Seconds || currentTS.Seconds > service.TetBookingDates.ToDate.Seconds)) {
				delete(t, "isTetBooking")
			}
			mapTasks = append(mapTasks, t)

			// Convert to map to refactor list task
			if t["sourceTaskId"] != nil || t["relatedTasks"] != nil {
				shouldRefactorListTask = true
				relatedTaskIds = append(relatedTaskIds, GetRelatedTaskIdsOfTask(t)...)
			}
		}

		// NOTE: function này cần để cuối cùng trước khi return to client. Vì nó sẽ lấy các task con đưa vào task cha. Như vậy khi loop lại mapTasks sau bước này sẽ không lặp qua các task con nữa
		if shouldRefactorListTask {
			mapTasks = RefactorListTaskHasRelatedTasks(mapTasks, serviceMap, relatedTaskIds)
		}
	}
	globalResponse.ResponseSuccess(w, mapTasks)
}

func GetRelatedTaskIdsOfTask(task map[string]interface{}) []string {
	if task["relatedTasks"] == nil {
		return nil
	}

	var relatedTasks *modelTask.TaskRelatedTask
	relatedTasksData, _ := json.Marshal(task["relatedTasks"])
	json.Unmarshal(relatedTasksData, &relatedTasks)
	if relatedTasks == nil {
		return nil
	}
	var relatedTaskIds []string
	for _, t := range relatedTasks.OldHomeMovingSubTasks {
		relatedTaskIds = append(relatedTaskIds, t.XId)
	}
	for _, t := range relatedTasks.NewHomeMovingSubTasks {
		relatedTaskIds = append(relatedTaskIds, t.XId)
	}
	return relatedTaskIds
}
