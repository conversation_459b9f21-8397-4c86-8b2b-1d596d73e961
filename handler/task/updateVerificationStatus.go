/*
 * @File: updateVerificationStatus.go
 * @Description: Handler function for updateVerificationStatus api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Verification Status Task
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func UpdateVerificationStatus(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateUpdateVerificationStatus(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Set data
	var task *modelTask.Task
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"_id": reqBody.TaskId,
			"acceptedTasker.taskerId": reqBody.VerificationData["vTaskerId"],
		},
		bson.M{"_id": 1, "acceptedTasker": 1},
		&task,
	)
	if task == nil {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}

	dataVerification := bson.M{
		"verification": bson.M{
			"status":    reqBody.VerificationData["status"].(bool),
			"vTaskId":   reqBody.VerificationData["vTaskId"].(string),
			"vTaskerId": reqBody.VerificationData["vTaskerId"].(string),
			"createdAt": globalLib.GetCurrentTime(local.TimeZone),
		},
		"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	// Task is valid, update task Verification Status
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		reqBody.TaskId,
		bson.M{"$set": dataVerification},
	)

	if err != nil {
		local.Logger.Warn(lib.ERROR_UPDATE_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_UPDATE_FAILED)
		return
	}

	result := make(map[string]interface{})
	for _, v := range task.AcceptedTasker {
		if v.TaskerId == reqBody.VerificationData["vTaskerId"] {
			result["avatar"] = v.Avatar
			result["name"] = v.Name
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateUpdateVerificationStatus(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.VerificationData == nil || reqBody.VerificationData["vTaskId"] == "" || reqBody.VerificationData["vTaskerId"] == "" || reqBody.VerificationData["status"] == nil {
		return &lib.ERROR_VERIFICATION_DATA_REQUIRED
	}
	return nil
}
