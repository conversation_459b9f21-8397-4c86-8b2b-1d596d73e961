package task

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUserComplaint "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComplaint"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func CreateUserComplaint(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateCreateUserComplaint(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"askerId": 1, "isoCode": 1, "phone": 1, "acceptedTasker.name": 1, "date": 1, "address": 1, "status": 1, "reported": 1}, &task)
	if task == nil {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}
	if task.Status != globalConstant.TASK_STATUS_DONE {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_DONE)
		return
	}
	if task.Reported {
		globalResponse.ResponseError(w, lib.ERROR_TASK_HAS_BEEN_REPORTED)
		return
	}

	var information *modelUserComplaint.UserComplaintReportData
	informationData, _ := json.Marshal(reqBody.ReportData)
	json.Unmarshal(informationData, &information)
	userComplaint := &modelUserComplaint.UserComplaint{
		XId:        globalLib.GenerateObjectId(),
		UserId:     task.AskerId,
		TaskId:     task.XId,
		Reason:     reqBody.Reason,
		ReportData: information,
		CreatedAt:  globalLib.GetCurrentTimestamp(local.TimeZone),
		Type:       reqBody.Type,
	}

	// Post to slack
	taskers := []string{}
	for _, t := range task.AcceptedTasker {
		taskers = append(taskers, t.Name)
	}
	taskerList := strings.Join(taskers, ", ")

	description := ""
	if reqBody.ReportData["description"] != nil {
		description, _ = reqBody.ReportData["description"].(string)
	}
	detailDescription := ""
	if reqBody.ReportData["detailDescription"] != nil {
		detailDescription, _ = reqBody.ReportData["detailDescription"].(string)
	}

	date := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone).Format("15:04:05 02-01-2006")
	message := fmt.Sprintf("Số điện thoại: %s\n", task.Phone)
	message += fmt.Sprintf("Taskers: %s\n", taskerList)
	message += fmt.Sprintf("Thời gian làm việc: %s\n", date)
	message += fmt.Sprintf("Địa chỉ: %s\n", task.Address)
	message += fmt.Sprintf("Lý do: %s\n", reqBody.Reason.Vi)
	if description != "" || detailDescription != "" {
		if description != "" {
			message += fmt.Sprintf("Mô tả: %s\n", description)
		}
		if detailDescription != "" {
			message += fmt.Sprintf("Mô tả chi tiết: %s\n", detailDescription)
		}
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.ASKER_COMPLAINT_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
	}
	go sendComplaintToMiDesk(reqBody, task)
	// insert to database
	globalDataAccess.InsertOne(globalCollection.COLLECTION_USER_COMPLAINT[local.ISO_CODE], userComplaint)
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"$set": bson.M{"reported": true}})
	globalResponse.ResponseSuccess(w, nil)
}

func validateCreateUserComplaint(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.Reason == nil {
		return &lib.ERROR_REASON_REQUIRED
	}
	if reqBody.ReportData == nil || len(reqBody.ReportData) == 0 {
		return &lib.ERROR_REPORT_DATA_REQUIRED
	}
	return nil
}

func sendComplaintToMiDesk(reqBody *model.ApiRequest, task *modelTask.Task) {
	token := globalLib.GetMiDeskAccessToken(cfg.MiDeskConfig.LoginUrl, cfg.MiDeskConfig.Email, cfg.MiDeskConfig.Password, cfg.SlackToken)
	if token != "" {
		user, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"emails": 1, "name": 1, "phone": 1})
		var email string
		if user != nil && len(user.Emails) > 0 {
			email = user.Emails[0].Address
		}
		description := ""
		if reqBody.ReportData["description"] != nil {
			description, _ = reqBody.ReportData["description"].(string)
		}
		detailDescription := ""
		if reqBody.ReportData["detailDescription"] != nil {
			detailDescription, _ = reqBody.ReportData["detailDescription"].(string)
		}
		message := fmt.Sprintf("Lý do: %s\n", reqBody.Reason.Vi)
		if description != "" || detailDescription != "" {
			if description != "" {
				message += fmt.Sprintf("Mô tả: %s\n", description)
			}
			if detailDescription != "" {
				message += fmt.Sprintf("Mô tả chi tiết: %s\n", detailDescription)
			}
		}
		globalLib.CallCreateMiDeskTicket(cfg.MiDeskConfig.CreateTicketUrl, token, cfg.SlackToken, map[string]interface{}{
			"title":    fmt.Sprintf("Complaint from Asker - %s - %s", user.Name, user.Phone),
			"content":  message,
			"channel":  "Asker complaint",
			"priority": "2", // 1: Khẩn cấp 2: Cao 3: Trung bình
			"contact": map[string]interface{}{
				"name":  user.Name,
				"email": email,
				"phone": user.Phone,
			},
		})
	}
}
