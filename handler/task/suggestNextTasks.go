/*
 * @File: suggestNextTasks.go
 * @Description: Handler function for suggestNextTasks api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Suggest Next Tasks by TaskId, UserId
 * @CreatedAt: 27/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func SuggestNextTasks(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateSuggestNextTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var result *modelTask.Task
	var doneTask *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"taskPlace": 1, "lat": 1, "lng": 1, "isoCode": 1}, &doneTask)
	if doneTask != nil {
		var serviceChannel []*modelServiceChannel.ServiceChannel
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"taskerList": reqBody.UserId}, bson.M{"serviceId": 1}, &serviceChannel)
		var serviceIds []string
		if len(serviceChannel) > 0 {
			for _, v := range serviceChannel {
				serviceIds = append(serviceIds, v.ServiceId)
			}
		}
		// Find tasks by status, date, task place and the services tasker provided.
		// Find base on district first, if don't have any task, we will find base on city.
		now := globalLib.GetCurrentTime(local.TimeZone)
		startOf := now.Add(30 * time.Minute)
		endOf := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 0, 0, now.Location())
		query := bson.M{
			"status":             globalConstant.TASK_STATUS_POSTED,
			"date":               bson.M{"$gte": startOf, "$lte": endOf},
			"taskPlace.district": doneTask.TaskPlace.District,
			"serviceId":          bson.M{"$in": serviceIds},
			"viewedTaskers":      reqBody.UserId,
		}
		fields := bson.M{"_id": 1, "lat": 1, "lng": 1, "serviceText": 1, "date": 1, "duration": 1}
		var tasks []*modelTask.Task
		globalDataAccess.GetAllByQueryLimitSort(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, fields, 50, bson.M{"date": 1}, &tasks)
		if len(tasks) == 0 {
			delete(query, "taskPlace.district")
			query["taskPlace.city"] = doneTask.TaskPlace.City
			globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, fields, &tasks)
		}
		if len(tasks) > 0 {
			var conflictTaskIds []string
			// Check Tasker accepted task at this time or not.
			var settings *modelSettings.Settings
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"timeInBetweenTask": 1}, &settings)
			var timeInBetweenTask int32 = 30
			if settings != nil && settings.TimeInBetweenTask > 0 {
				timeInBetweenTask = settings.TimeInBetweenTask
			}
			var listTasks []*modelTask.Task
			globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
				bson.M{
					"status":                  globalConstant.TASK_STATUS_CONFIRMED,
					"date":                    bson.M{"$gte": startOf, "$lte": endOf},
					"acceptedTasker.taskerId": reqBody.UserId,
				},
				bson.M{"lat": 1, "lng": 1, "date": 1, "duration": 1},
				&listTasks,
			)
			if len(listTasks) > 0 {
				for _, v := range listTasks {
					for _, t := range tasks {
						if lib.IsConflictTask(v, t, timeInBetweenTask) {
							conflictTaskIds = append(conflictTaskIds, t.XId)
						}
					}
				}
			}
			// Remove task conflict from list of posted tasks
			newTasks := tasks
			if len(conflictTaskIds) > 0 {
				for _, v := range tasks {
					if globalLib.FindStringInSlice(conflictTaskIds, v.XId) == -1 {
						newTasks = append(newTasks, v)
					}
				}
			}
			if len(newTasks) > 0 {
				lat1 := doneTask.Lat
				lng1 := doneTask.Lng
				// Get the task which has the nearest distance with done task
				result = newTasks[0]
				for i := 1; i < len(newTasks); i++ {
					if lib.CalcDistance(lat1, lng1, newTasks[i].Lat, newTasks[i].Lng) < lib.CalcDistance(lat1, lng1, result.Lat, result.Lng) {
						result = newTasks[i]
					}
				}
			}
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

func validateSuggestNextTasks(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
