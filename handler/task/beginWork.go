/*
 * @File: beginWork.go
 * @Description: Handler function for beginWork api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Begin Work by TaskId
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func BeginWork(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.TaskId == "" {
		local.Logger.Warn(lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_BOOKING_ID_REQUIRED)
		return
	}
	// Set data
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"status": 1, "startWorking": 1}, &task)
	if task == nil {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}
	if task.Status != globalConstant.TASK_STATUS_CONFIRMED {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_CONFIRMED)
		return
	}
	if task.StartWorking != nil && task.StartWorking.IsStart {
		globalResponse.ResponseError(w, lib.ERROR_TASK_STARTED)
		return
	}
	startWorking := map[string]interface{}{
		"isStart":   true,
		"startedAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	if reqBody.Lat > 0 {
		startWorking["lat"] = reqBody.Lat
	}
	if reqBody.Lng > 0 {
		startWorking["lng"] = reqBody.Lng
	}
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"$set": bson.M{"startWorking": startWorking}})
	globalResponse.ResponseSuccess(w, nil)
}
