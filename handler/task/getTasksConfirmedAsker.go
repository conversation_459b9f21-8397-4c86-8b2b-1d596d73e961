/*
 * @File: getTasksConfirmedAsker.go
 * @Description: Handler function for getTasksConfirmedAsker api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Tasks Confirmed by Asker (UserId)
 * @CreatedAt: 17/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTasksConfirmedAsker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get data
	d := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -7)
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"date":    bson.M{"$gte": d},
			"askerId": reqBody.UserId,
			"isoCode": reqBody.ISOCode,
			"status":  globalConstant.TASK_STATUS_CONFIRMED,
			"$or": []bson.M{
				{"detailLaundry": bson.M{"$exists": false}},
				{"detailLaundry.isReceived": true},
			},
		},
		bson.M{"_id": 1, "date": 1, "duration": 1, "fromPartner": 1},
		bson.M{"date": 1},
		&tasks,
	)

	var tasksResult []*modelTask.Task
	for _, task := range tasks {
		timeCheck := globalLib.GetCurrentTime(local.TimeZone).Add(-(time.Duration(task.Duration*60) + 30) * time.Minute)
		timeCheckTimestamp := globalLib.ParseTimestampFromDate(timeCheck)
		if task.Date.Seconds <= timeCheckTimestamp.Seconds {
			tasksResult = append(tasksResult, task)
		}
	}

	globalResponse.ResponseSuccess(w, tasksResult)
}
