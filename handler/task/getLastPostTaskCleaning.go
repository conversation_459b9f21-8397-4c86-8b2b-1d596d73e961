/*
 * @File: getLastPostTaskCleaning.go
 * @Description: Handler function for doneTaskByTasker api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Last Post Task Cleaning by UserId
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetLastPostTaskCleaning(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateGetLastPostTaskCleaning(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var service *modelService.Service
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1, "text": 1, "icon": 1, "tip.requirements": 1, "tetBookingDates": 1}, &service)
	var task map[string]interface{}
	globalDataAccess.GetOneByQuerySort(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"askerId":   reqBody.UserId,
			"serviceId": service.XId,
			"isoCode":   reqBody.ISOCode,
		},
		bson.M{"cost": 0, "costDetail": 0, "currency": 0, "pricing": 0, "promotion": 0, "viewedTaskers": 0, "visibility": 0},
		bson.M{"createdAt": -1},
		&task,
	)
	if task != nil {
		task["service"] = map[string]interface{}{
			"_id":  service.XId,
			"name": globalLib.GetServiceKeyName(service.Text.En),
			"text": service.Text,
			"icon": service.Icon,
		}
		if task["payment"] != nil {
			taskPayment := make(map[string]interface{})
			b, _ := json.Marshal(task["payment"])
			json.Unmarshal(b, &taskPayment)
			if taskPayment["method"] != nil && globalLib.FindStringInSlice(globalConstant.PAYMENT_METHOD_POST_TASK_BLACKLIST, taskPayment["method"].(string)) >= 0 {
				task["payment"] = map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CASH,
				}
			}
		}
		currentTime := globalLib.GetCurrentTimestamp(local.TimeZone)
		if service.TetBookingDates == nil || (service.TetBookingDates != nil && (currentTime.Seconds < service.TetBookingDates.FromDate.Seconds || currentTime.Seconds > service.TetBookingDates.ToDate.Seconds)) {
			delete(task, "isTetBooking")
		}
		// update requirement
		if task["requirements"] != nil && service.Tip != nil && len(service.Tip.Requirements) > 0 {
			// map service tip requirements
			mapRequirementByType := map[float64]*modelService.ServiceTipRequirements{}
			for _, v := range service.Tip.Requirements {
				mapRequirementByType[v.Type] = v
			}

			// Update to task requirements
			requirementData, _ := json.Marshal(task["requirements"])
			var requirements []map[string]interface{}
			json.Unmarshal(requirementData, &requirements)

			updatedRequirements := []interface{}{}
			for _, v := range requirements {
				t := v["type"].(float64)
				if value := mapRequirementByType[t]; value != nil {
					updatedRequirements = append(updatedRequirements, &modelService.ServiceTipRequirements{
						Type:     t,
						Duration: value.Duration,
						Text:     value.Text,
					})
				}
			}
			task["requirements"] = updatedRequirements
		}
	}
	globalResponse.ResponseSuccess(w, task)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetLastPostTaskCleaning(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
