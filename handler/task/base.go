/*
 * @File: base.go
 * @Description: Helper function for task api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package task

import (
	"encoding/json"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelOutstandingPayment "gitlab.com/btaskee/go-services-model-v2/grpcmodel/outStandingPayment"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

var cfg = config.GetConfig()

var TASK_FIELDS = bson.M{
	"_id":                      1,
	"isoCode":                  1,
	"serviceId":                1,
	"duration":                 1,
	"originCurrency":           1,
	"status":                   1,
	"acceptedTasker":           1,
	"costDetail":               1,
	"promotion":                1,
	"description":              1,
	"payment":                  1,
	"createdAt":                1,
	"taskNote":                 1,
	"date":                     1,
	"phone":                    1,
	"address":                  1,
	"homeType":                 1,
	"askerId":                  1,
	"cost":                     1,
	"contactName":              1,
	"autoChooseTasker":         1,
	"requirements":             1,
	"detail":                   1,
	"detailDeepCleaning":       1,
	"detailHostel":             1,
	"detailLaundry":            1,
	"cookingDetail":            1,
	"goMarketDetail":           1,
	"pet":                      1,
	"taskPlace":                1,
	"collectionDate":           1,
	"detailSofa":               1,
	"sharedPromotion":          1,
	"subscriptionId":           1,
	"countryCode":              1,
	"disinfectionDetail":       1,
	"isPrepayTask":             1,
	"isPremium":                1,
	"isTetBooking":             1,
	"fromPartner":              1,
	"detailChildCare":          1,
	"detailOfficeCleaning":     1,
	"detailWashingMachine":     1,
	"newCostDetail":            1,
	"detailWaterHeater":        1,
	"detailCarpet":             1,
	"detailHomeMoving":         1,
	"relatedTasks":             1,
	"sourceTaskId":             1,
	"startWorking":             1,
	"detailMassage":            1,
	"forceTasker":              1,
	"dateOptions":              1,
	"detailAirConditioner":     1,
	"scheduleId":               1,
	"isEco":                    1,
	"detailHousekeeping":       1,
	"detailIndustrialCleaning": 1,
	"detailBeautyCare":         1,
	"detailMakeup":             1,
	"detailHairStyling":        1,
	"detailNail":               1,
}

/*
 * @Description: Get List History Tasks by UserId
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func ValidateGetListTasks(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}

func UpdateFieldTaskDone(task map[string]interface{}) {
	if task["acceptedTasker"] != nil {
		var acceptedTasker []map[string]interface{}
		acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
		json.Unmarshal(acceptedTaskerData, &acceptedTasker)

		responseAcceptedTasker := []interface{}{}
		for _, tasker := range acceptedTasker {
			if tasker["taskDone"] == nil {
				tasker["taskDone"] = 0
			}
			responseAcceptedTasker = append(responseAcceptedTasker, tasker)
		}
		task["acceptedTasker"] = responseAcceptedTasker
	}
}

func getTaskNewOutstandingPaymentByUserId(taskIds []string, askerId string) map[string]*modelOutstandingPayment.OutstandingPayment {
	var oustandingPayments []*modelOutstandingPayment.OutstandingPayment
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_OUTSTANDING_PAYMENT[local.ISO_CODE],
		bson.M{
			"askerId":     askerId,
			"status":      globalConstant.OUTSTANDING_PAYMENT_STATUS_NEW,
			"data.taskId": bson.M{"$in": taskIds},
		},
		bson.M{
			"data.taskId": 1,
			"cardInfo":    1,
			"createdAt":   1,
		},
		bson.M{"createdAt": -1},
		&oustandingPayments,
	)

	oustandingPaymentByTaskId := make(map[string]*modelOutstandingPayment.OutstandingPayment)
	for _, op := range oustandingPayments {
		if oustandingPaymentByTaskId[op.Data.TaskId] == nil {
			oustandingPaymentByTaskId[op.Data.TaskId] = op
		}
	}
	return oustandingPaymentByTaskId
}

func GetHomeMovingStepInProgress(taskDetailHomeMoving *modelTask.TaskDetailHomeMoving, service *modelService.Service) *modelService.ServiceDetailServiceHomeMovingHomeMovingProcess {
	if service == nil || taskDetailHomeMoving == nil {
		return nil
	}
	if service.Name == globalConstant.SERVICE_KEY_NAME_HOME_MOVING && service.DetailService != nil && service.DetailService.HomeMoving != nil {
		if len(service.DetailService.HomeMoving.HomeMovingProcess) > 0 {
			completedStep := taskDetailHomeMoving.CompletedStep
			for _, item := range service.DetailService.HomeMoving.HomeMovingProcess {
				if item.Step == completedStep+1 {
					return item
				}
			}
		}
	}
	return nil
}

func RefactorListTaskHasRelatedTasks(mapTasks []map[string]interface{}, mapServiceById map[string]*modelService.Service, relatedTaskIds []string) []map[string]interface{} {
	// Get list relatedTasks
	mapRelatedTaskById := getRelatedTasksById(relatedTaskIds, mapServiceById)

	// Map data tasks. And check if task has relatedTasks
	for _, t := range mapTasks {
		// Check if the task has relatedTasks
		if t["relatedTasks"] == nil {
			continue
		}

		// NOTE: Không cần check này nữa vì đã không lấy task có field sourceTaskId từ đầu rồi
		// if t["sourceTaskId"] != nil && cast.ToString(t["sourceTaskId"]) != "" {
		// 	continue
		// }

		// Convert to relatedTasks
		relatedTasksData, _ := json.Marshal(t["relatedTasks"])
		var relatedTasks *modelTask.TaskRelatedTask
		json.Unmarshal(relatedTasksData, &relatedTasks)
		if relatedTasks == nil {
			continue
		}

		relatedTasksInResult := make(map[string]interface{})

		// HOME MOVING
		{
			// 1. Old home cleaning tasks
			oldHomeMovingSubTasksResult := []map[string]interface{}{}
			var oldHomeMovingSubTasksDate interface{}
			for _, v := range relatedTasks.OldHomeMovingSubTasks {
				task := mapRelatedTaskById[v.XId]
				if task == nil {
					continue
				}
				// Push to relatedTasks
				oldHomeMovingSubTasksResult = append(oldHomeMovingSubTasksResult, task)
				// Get the first date
				oldHomeMovingSubTasksDate = task["date"]
			}
			if len(oldHomeMovingSubTasksResult) > 0 {
				relatedTasksInResult["oldHomeMovingSubTasks"] = oldHomeMovingSubTasksResult
				relatedTasksInResult["oldHomeMovingSubTasksDate"] = oldHomeMovingSubTasksDate
			}

			// 2. New home cleaning tasks
			newHomeMovingSubTasksResult := []map[string]interface{}{}
			var newHomeMovingSubTasksDate interface{}
			for _, v := range relatedTasks.NewHomeMovingSubTasks {
				task := mapRelatedTaskById[v.XId]
				if task == nil {
					continue
				}
				// Push to relatedTasks
				newHomeMovingSubTasksResult = append(newHomeMovingSubTasksResult, task)
				// Get the first date
				newHomeMovingSubTasksDate = task["date"]
			}
			if len(newHomeMovingSubTasksResult) > 0 {
				relatedTasksInResult["newHomeMovingSubTasks"] = newHomeMovingSubTasksResult
				relatedTasksInResult["newHomeMovingSubTasksDate"] = newHomeMovingSubTasksDate
			}

			// 3. Push to task
			if len(relatedTasksInResult) > 0 {
				t["relatedTasks"] = relatedTasksInResult
			}
		}
	}

	return mapTasks
}

func getRelatedTasksById(relatedTaskIds []string, mapServiceById map[string]*modelService.Service) map[string]map[string]interface{} {
	tasks, _ := globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": bson.M{"$in": relatedTaskIds}}, TASK_FIELDS)
	if tasks == nil {
		return nil
	}

	mapRelatedTaskById := map[string]map[string]interface{}{}
	for _, t := range tasks {
		// Update service info
		serviceId := cast.ToString(t["serviceId"])
		if s, ok := mapServiceById[serviceId]; ok {
			t["service"] = map[string]interface{}{
				"_id":  s.XId,
				"name": s.Name,
				"text": s.Text,
				"icon": s.Icon,
			}
		}

		// Map task by id
		mapRelatedTaskById[cast.ToString(t["_id"])] = t
	}
	return mapRelatedTaskById
}

func MapRelatedTasksToTask(mapListTask map[string][]map[string]interface{}) []map[string]interface{} {
	newTasks := []map[string]interface{}{}
	for _, v := range mapListTask {
		task := v[0]
		task["relatedTasks"] = map[string]interface{}{
			"tasks": v[1:],
		}
		newTasks = append(newTasks, task)
	}
	return newTasks
}

func RefactorTaskPaymentStatus(task map[string]interface{}) {
	if task == nil {
		return
	}
	if task["payment"] == nil {
		return
	}
	payment := cast.ToStringMap(task["payment"])
	if payment == nil || payment["status"] == nil {
		return
	}
	if cast.ToBool(payment["isPaymentAttempted"]) && cast.ToString(payment["status"]) != globalConstant.TASK_PAYMENT_STATUS_PAID {
		payment["status"] = globalConstant.TASK_PAYMENT_STATUS_CHARGING
		return
	}
	if IsPaymentFailed(task) {
		payment["status"] = globalConstant.TASK_PAYMENT_STATUS_ERROR
	}
}

func RefactorIfTaskChangeToCashBecausePaymentFailed(task map[string]interface{}) {
	defer delete(task, "changesHistory")
	if task == nil {
		return
	}
	if task["payment"] == nil {
		return
	}
	payment := cast.ToStringMap(task["payment"])
	if payment == nil || payment["method"] == nil || cast.ToString(payment["method"]) != globalConstant.PAYMENT_METHOD_CASH {
		return
	}
	// Check changesHistory
	if task["changesHistory"] == nil {
		return
	}

	var changesHistory []map[string]interface{}
	changesHistoryData, _ := json.Marshal(task["changesHistory"])
	json.Unmarshal(changesHistoryData, &changesHistory)
	if len(changesHistory) == 0 {
		return
	}
	for t := len(changesHistory) - 1; t >= 0; t-- {
		item := changesHistory[t]
		if item == nil {
			continue
		}
		if cast.ToString(item["key"]) != globalConstant.CHANGES_HISTORY_KEY_UPDATE_PAYMENT_METHOD {
			continue
		}
		if cast.ToString(item["from"]) != globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON {
			break
		}
		content := cast.ToStringMap(item["content"])
		if content == nil {
			break
		}
		if cast.ToString(content["newPaymentMethod"]) != globalConstant.PAYMENT_METHOD_CASH {
			break
		}
		task["isChangedToCashBecauseOfPaymentFailed"] = true
	}
}

func IsPaymentFailed(task map[string]interface{}) bool {
	// Case not prepay task
	if isPrepayTask, err := cast.ToBoolE(task["isPrepayTask"]); err == nil && !isPrepayTask {
		return false
	}

	// Case prepay task
	payment := make(map[string]interface{})
	paymentData, _ := json.Marshal(task["payment"])
	json.Unmarshal(paymentData, &payment)
	if payment == nil {
		return false
	}

	// Case payment status is error
	paymentStatus := cast.ToString(payment["status"])
	if paymentStatus == globalConstant.TASK_PAYMENT_STATUS_ERROR {
		return true
	}

	// Case payment status is not paid and pass 15 minutes from last payment createdAt
	paymentCreatedAt := cast.ToTime(cast.ToString(payment["paymentCreatedAt"]))
	if paymentStatus != globalConstant.TASK_PAYMENT_STATUS_PAID && !paymentCreatedAt.IsZero() {
		return paymentCreatedAt.Before(globalLib.GetCurrentTime(local.TimeZone).Add(-15 * time.Minute))
	}

	return false
}
