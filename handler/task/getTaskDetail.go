/*
 * @File: getTaskDetail.go
 * @Description: Handler function for getTaskDetail api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package task

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime/debug"
	"sort"
	"sync"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelPaymentTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUserComplaint "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComplaint"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var paymentCollectionMap = map[string]string{
	globalConstant.PAYMENT_METHOD_SHOPEE_PAY:    globalCollection.COLLECTION_SHOPEEPAY_TRANSACTION[local.ISO_CODE],
	globalConstant.PAYMENT_METHOD_ZALO_PAY:      globalCollection.COLLECTION_ZALOPAY_TRANSACTION,
	globalConstant.PAYMENT_METHOD_MOMO:          globalCollection.COLLECTION_MOMO_TRANSACTION,
	globalConstant.PAYMENT_METHOD_VN_PAY:        globalCollection.COLLECTION_VN_PAY_TRANSACTION,
	globalConstant.PAYMENT_METHOD_TIKI_MINI_APP: globalCollection.COLLECTION_TIKI_MINI_APP_TRANSACTION,
}

/*
 * @Description: Get Task Detail by UserId, TaskId
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTaskDetail(w http.ResponseWriter, r *http.Request) {
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("UNKNOWN ERROR: %v. stack: %v", r, string(debug.Stack()))
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		}
	}()

	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetTaskDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	fields := bson.M{
		"_id":                      1,
		"isoCode":                  1,
		"requirements":             1,
		"serviceId":                1,
		"duration":                 1,
		"originCurrency":           1,
		"status":                   1,
		"acceptedTasker":           1,
		"costDetail":               1,
		"description":              1,
		"payment":                  1,
		"createdAt":                1,
		"taskNote":                 1,
		"date":                     1,
		"phone":                    1,
		"address":                  1,
		"homeType":                 1,
		"askerId":                  1,
		"cost":                     1,
		"contactName":              1,
		"promotion":                1,
		"autoChooseTasker":         1,
		"detail":                   1,
		"detailDeepCleaning":       1,
		"detailHostel":             1,
		"detailLaundry":            1,
		"cookingDetail":            1,
		"goMarketDetail":           1,
		"detailSofa":               1,
		"pet":                      1,
		"taskPlace":                1,
		"cancellationReason":       1,
		"cancelledAt":              1,
		"cancellationText":         1,
		"isCancelledByBackend":     1,
		"collectionDate":           1,
		"subscriptionId":           1,
		"countryCode":              1,
		"shortAddress":             1,
		"rated":                    1,
		"disinfectionDetail":       1,
		"detailSofaTH":             1,
		"isPrepayTask":             1,
		"covidInfos":               1,
		"isPremium":                1,
		"isTetBooking":             1,
		"fromPartner":              1,
		"detailChildCare":          1,
		"reported":                 1,
		"detailOfficeCleaning":     1,
		"requestVatInfo":           1,
		"detailWashingMachine":     1,
		"newCostDetail":            1,
		"detailWaterHeater":        1,
		"cancellationFee":          1,
		"gender":                   1,
		"detailCarpet":             1,
		"detailHomeMoving":         1,
		"startWorking.isStart":     1,
		"serviceName":              1,
		"detailMassage":            1,
		"forceTasker":              1,
		"dateOptions":              1,
		"detailAirConditioner":     1,
		"changesHistory":           1,
		"addons":                   1,
		"detailHousekeeping":       1,
		"detailIndustrialCleaning": 1,
		"source.from":              1,
		"source.taskId":            1,
		"detailBeautyCare":         1,
		"detailMakeup":             1,
		"detailHairStyling":        1,
		"detailNail":               1,
	}
	task, _ := globalDataAccess.GetOneByQueryMap(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"_id":     reqBody.TaskId,
			"askerId": reqBody.UserId,
		},
		fields,
	)
	if task == nil {
		globalResponse.ResponseSuccess(w, nil)
		return
	}

	if task["serviceId"] != nil {
		var service *modelService.Service
		globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], task["serviceId"].(string), bson.M{"name": 1, "text": 1, "icon": 1, "tip.requirements": 1, "durationByArea": 1, "detailService.homeMoving.homeMovingProcess": 1}, &service)
		if service != nil {
			task["service"] = map[string]interface{}{
				"_id":            service.XId,
				"name":           service.Name,
				"text":           service.Text,
				"icon":           service.Icon,
				"durationByArea": service.DurationByArea,
			}

			// update requirement
			if task["requirements"] != nil && service.Tip != nil && len(service.Tip.Requirements) > 0 {
				// map service tip requirements
				mapRequirementByType := map[float64]*modelService.ServiceTipRequirements{}
				for _, v := range service.Tip.Requirements {
					mapRequirementByType[v.Type] = v
				}

				// Update to task requirements
				requirementData, _ := json.Marshal(task["requirements"])
				var requirements []map[string]interface{}
				json.Unmarshal(requirementData, &requirements)

				updatedRequirements := []interface{}{}
				for _, v := range requirements {
					t := v["type"].(float64)

					if value := mapRequirementByType[t]; value != nil {
						updatedRequirements = append(updatedRequirements, &modelService.ServiceTipRequirements{
							Type:     t,
							Duration: value.Duration,
						})
					}
				}
				task["requirements"] = updatedRequirements
			}
		}
		// Get FA transactions
		if task["status"] == globalConstant.TASK_STATUS_CANCELED {
			findTaskCancelFee(task)
		}
		mapData.MapCostDetailForAsker(task)
		// Get rating
		var rate *modelRating.Rating
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskId": reqBody.TaskId}, bson.M{}, &rate)
		if rate != nil {
			task["rate"] = rate
		}

		// Update taskDone of acceptedTasker if it nil
		UpdateFieldTaskDone(task)

		// Comment covid dose
		// updateCovidDose(task)
		// Update detail for task home moving
		if service != nil && service.Name == globalConstant.SERVICE_KEY_NAME_HOME_MOVING {
			updateDetailHomeMovingTaskDetail(task, service)
		}
	}

	// Only check transaction payment for prepayTask
	if task["isPrepayTask"] != nil && task["isPrepayTask"].(bool) {
		var payment map[string]interface{}
		paymentData, _ := json.Marshal(task["payment"])
		json.Unmarshal(paymentData, &payment)

		// Only return if payment status PAID
		if payment != nil && payment["status"] != nil && payment["status"].(string) == globalConstant.TASK_PAYMENT_STATUS_PAID {
			successDate := getPaymentSuccessDate(cast.ToString(payment["method"]), cast.ToString(payment["transactionId"]))

			if successDate != nil {
				// add date to payment
				payment["date"] = successDate
				task["payment"] = payment
			}
		}
	}

	// Task subscription
	if cast.ToString(task["subscriptionId"]) != "" {
		payment := cast.ToStringMap(task["payment"])
		if payment != nil && cast.ToString(payment["method"]) == globalConstant.PAYMENT_METHOD_BANK_TRANSFER {
			task["payment"] = &modelTask.TaskPayment{
				Method: "",
				Status: globalConstant.TASK_PAYMENT_STATUS_PAID,
			}
		}
	}

	// Get lixi info if exists:
	wg := &sync.WaitGroup{}
	var lixiInfo map[string]interface{}
	if task["status"] == globalConstant.TASK_STATUS_DONE {
		taskerIds := []string{}
		for _, acceptedTasker := range cast.ToSlice(task["acceptedTasker"]) {
			tasker := cast.ToStringMap(acceptedTasker)
			taskerIds = append(taskerIds, cast.ToString(tasker["taskerId"]))
		}
		askerId := cast.ToString(task["askerId"])
		taskId := cast.ToString(task["_id"])

		if len(taskerIds) > 0 {
			wg.Add(1)
			go func() {
				defer wg.Done()
				lixiInfo = getTaskLixiInfo(taskId, askerId, taskerIds)
			}()
		}
	}

	// Task card PAID
	payment := cast.ToStringMap(task["payment"])
	if payment != nil && payment["method"] == globalConstant.PAYMENT_METHOD_CARD && cast.ToBool(payment["isPayOff"]) {
		payment["status"] = globalConstant.TASK_PAYMENT_STATUS_PAID
		task["payment"] = payment
	}
	RefactorTaskPaymentStatus(task)
	RefactorIfTaskChangeToCashBecausePaymentFailed(task)

	if task["detailMassage"] != nil {
		updateDetailMassage(task)
	}
	// Check outstanding payment
	outStandingPayment := map[string]interface{}{}
	outstandingPaymentByTaskId := getTaskNewOutstandingPaymentByUserId([]string{reqBody.TaskId}, reqBody.UserId)
	if outstandingPaymentByTaskId[reqBody.TaskId] != nil {
		// Add card info to outStandingPayment
		if outstandingPaymentByTaskId[reqBody.TaskId].CardInfo != nil {
			outStandingPayment["date"] = globalLib.ParseDateFromTimeStamp(outstandingPaymentByTaskId[reqBody.TaskId].CreatedAt, local.TimeZone)
			outStandingPayment["cardNumber"] = outstandingPaymentByTaskId[reqBody.TaskId].CardInfo.Number
			outStandingPayment["cardType"] = outstandingPaymentByTaskId[reqBody.TaskId].CardInfo.Type
			// append to task object
			task["outStandingPayment"] = outStandingPayment
		}
	}

	// Check if asker can see chat history of this task
	task["canViewChatHistory"] = false
	task["canSendChat"] = false

	taskDateInt, _ := task["date"].(primitive.DateTime)
	taskDate := time.Unix(int64(taskDateInt/1000), int64(taskDateInt%1000)*int64(time.Millisecond))
	taskDuration, _ := task["duration"].(float64)
	taskStatus, _ := task["status"].(string)
	taskServiceName, _ := task["serviceName"].(string)
	if !taskDate.IsZero() {
		if lib.IsAskerCanViewTaskChatHistory(taskStatus, taskDate, float64(taskDuration)) {
			task["canViewChatHistory"] = true
		}
		if lib.IsAskerCanSendChat(taskStatus, taskDate, float64(taskDuration), taskServiceName) {
			task["canSendChat"] = true
			task["canViewChatHistory"] = false
		}
		// If task not have acceptedTasker -> Cannot view chat
		acceptedTaskers := []map[string]interface{}{}
		acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
		json.Unmarshal(acceptedTaskerData, &acceptedTaskers)
		if len(acceptedTaskers) == 0 {
			task["canViewChatHistory"] = false
		}
	}

	// Get asker complaint of this task if exists
	if reported, _ := task["reported"].(bool); reported {
		var askerComplaint *modelUserComplaint.UserComplaint
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_COMPLAINT[local.ISO_CODE], bson.M{"taskId": task["_id"]}, bson.M{}, &askerComplaint)
		task["askerComplaint"] = askerComplaint
	}

	if task["acceptedTasker"] != nil {
		updateAcceptedTasker(task)
		updateFavouriteTasker(task)
		sortLeaderAcceptedTasker(task)
	}

	// get task source
	getTaskSource(task)

	wg.Wait()
	if lixiInfo != nil {
		task["lixiInfo"] = lixiInfo
	}

	globalResponse.ResponseSuccess(w, task)
}

func updateAcceptedTasker(task map[string]interface{}) {
	// Update isLeader
	acceptedTaskers := []map[string]interface{}{}
	acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
	json.Unmarshal(acceptedTaskerData, &acceptedTaskers)
	if len(acceptedTaskers) == 0 {
		return
	}
	if len(acceptedTaskers) == 1 {
		delete(acceptedTaskers[0], "isLeader")
	}
	// update to task
	task["acceptedTasker"] = acceptedTaskers
}

func updateDetailHomeMovingTaskDetail(task map[string]interface{}, service *modelService.Service) {
	homeMovingProcess := []map[string]interface{}{}
	var stepInProgress *modelService.ServiceDetailServiceHomeMovingHomeMovingProcess
	detailHomeMoving := cast.ToStringMap(task["detailHomeMoving"])
	taskDetailHomeMoving := &modelTask.TaskDetailHomeMoving{}
	taskDetailHomeMovingData, _ := json.Marshal(detailHomeMoving)
	json.Unmarshal(taskDetailHomeMovingData, &taskDetailHomeMoving)
	if taskDetailHomeMoving != nil {
		// Moving process
		if service.DetailService != nil && service.DetailService.HomeMoving != nil && len(service.DetailService.HomeMoving.HomeMovingProcess) > 0 {
			startWorking := cast.ToStringMap(task["startWorking"])
			isStart := cast.ToBool(startWorking["isStart"])
			completedStep := taskDetailHomeMoving.CompletedStep
			for _, item := range service.DetailService.HomeMoving.HomeMovingProcess {
				stepInfo := map[string]interface{}{
					"step":        item.Step,
					"title":       item.Title,
					"description": item.Description,
				}
				if isStart {
					if item.Step <= completedStep {
						stepInfo["status"] = globalConstant.HOME_MOVING_STEP_STATUS_COMPLETED
					} else if item.Step == completedStep+1 {
						stepInfo["status"] = globalConstant.HOME_MOVING_STEP_STATUS_IN_PROGRESS
						stepInProgress = item
					}
				}
				homeMovingProcess = append(homeMovingProcess, stepInfo)
			}
			sort.Slice(homeMovingProcess, func(i, j int) bool {
				return cast.ToInt32(homeMovingProcess[i]["step"]) < cast.ToInt32(homeMovingProcess[j]["step"])
			})
			detailHomeMoving["stepInProgress"] = stepInProgress
			detailHomeMoving["homeMovingProcess"] = homeMovingProcess
		}

		// Furniture
		if len(taskDetailHomeMoving.Furniture) > 0 {
			removableElectronic := []*modelTask.TaskDetailHomeMovingFurnitureOption{}
			for _, v := range taskDetailHomeMoving.Furniture {
				if len(v.Options) > 0 {
					for _, option := range v.Options {
						// Do cấu trúc tháo lắp máy lạnh có 1 option con để bên Asker dễ hiển thị nên để đoạn này là những option có nhiều option con mới xử lý lại
						if len(option.Options) == 0 {
							optionItem := &modelTask.TaskDetailHomeMovingFurnitureOption{
								Name:     option.Name,
								Text:     option.Text,
								Quantity: option.Quantity,
							}
							removableElectronic = append(removableElectronic, optionItem)
							continue
						}

						// Nếu có option thì list hết option con
						for _, optionOption := range option.Options {
							optionItem := &modelTask.TaskDetailHomeMovingFurnitureOption{
								Name:     optionOption.Name,
								Text:     optionOption.Text,
								Quantity: optionOption.Quantity,
							}
							removableElectronic = append(removableElectronic, optionItem)
						}
					}
				}
			}
			if len(removableElectronic) > 0 {
				detailHomeMoving["removableElectronic"] = removableElectronic
			}
		}

		task["detailHomeMoving"] = detailHomeMoving
	}
}

func sortLeaderAcceptedTasker(task map[string]interface{}) {
	acceptedTaskers := []map[string]interface{}{}
	acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
	json.Unmarshal(acceptedTaskerData, &acceptedTaskers)
	// Sort tasker leader
	sort.Slice(acceptedTaskers, func(i, j int) bool {
		return acceptedTaskers[i]["isLeader"] == true
	})
	task["acceptedTasker"] = acceptedTaskers
}

func getAskerFavouriteTasker(askerId string) map[string]bool {
	user, _ := modelUser.GetOneById(local.ISO_CODE, askerId, bson.M{"favouriteTasker": 1})
	if user != nil {
		result := make(map[string]bool)
		for _, v := range user.FavouriteTasker {
			result[v] = true
		}
		return result
	}
	return nil
}

func updateFavouriteTasker(task map[string]interface{}) {
	// get user to get list favourite taskers
	askerId := ""
	if v, ok := task["askerId"].(string); ok {
		askerId = v
	}
	favouriteTaskers := getAskerFavouriteTasker(askerId)
	if len(favouriteTaskers) == 0 {
		return
	}

	// Update isFavouriteTasker
	acceptedTaskers := []map[string]interface{}{}
	acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
	json.Unmarshal(acceptedTaskerData, &acceptedTaskers)
	for _, tasker := range acceptedTaskers {
		if tasker["taskerId"] != nil && favouriteTaskers[tasker["taskerId"].(string)] {
			tasker["isFavouriteTasker"] = true
		}
	}

	// update to task
	task["acceptedTasker"] = acceptedTaskers

}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/02/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetTaskDetail(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}

func getPaymentSuccessDate(paymentMethod string, transactionId string) interface{} {
	if paymentCollectionMap[paymentMethod] != "" {
		transactionMap, _ := globalDataAccess.GetOneByQueryMap(paymentCollectionMap[paymentMethod], bson.M{"_id": transactionId}, bson.M{"updatedAt": 1})
		if transactionMap != nil && transactionMap["updatedAt"] != nil {
			return transactionMap["updatedAt"]
		}
	}
	return nil
}

func findTaskCancelFee(task map[string]interface{}) {
	var taskPayment *modelTask.TaskPayment
	taskPaymentData, _ := json.Marshal(task["payment"])
	json.Unmarshal(taskPaymentData, &taskPayment)

	collectionFATransaction := globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE]

	// Case prepaytask. Get refund task info
	if taskPayment != nil && taskPayment.Status == globalConstant.PAYMENT_STATUS_PAID {
		var refundReq *modelRefundRequest.RefundRequest
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE],
			bson.M{"taskId": task["_id"]},
			bson.M{"amount": 1, "status": 1, "cancelTaskFee": 1},
			&refundReq,
		)
		if refundReq != nil {
			task["refund"] = map[string]interface{}{
				"status": refundReq.Status,
				"amount": refundReq.Amount,
			}
			task["cancellationFee"] = refundReq.CancelTaskFee
		} else {
			var transaction *modelFATransaction.FinancialAccountTransaction
			globalDataAccess.GetOneByQuery(collectionFATransaction,
				bson.M{"source.value": task["_id"], "source.name": bson.M{"$regex": primitive.Regex{Pattern: globalConstant.FA_TRANSACTION_SOURCE_NAME_REFUND_CANCEL_TASK, Options: "i"}}},
				bson.M{"source": 1},
				&transaction,
			)
			if transaction != nil {
				task["cancellationFee"] = transaction.Source.CancelTaskFee
			}
		}
		return
	}

	// Case task pay by card
	if taskPayment != nil && taskPayment.Method == globalConstant.PAYMENT_METHOD_CARD {
		reference := fmt.Sprintf("%s_%v", globalConstant.PAYMENT_REFERRENCE_CANCEL_TASK, task["_id"])

		// Case isoCode VN
		var paymentTransaction *modelPaymentTransaction.PaymentTransaction
		globalDataAccess.GetOneByQuerySort(globalCollection.COLLECTION_PAYMENT_TRANSACTION,
			bson.M{"reference": reference},
			bson.M{"cost": 1},
			bson.M{"createdAt": -1},
			&paymentTransaction,
		)
		if paymentTransaction != nil {
			task["cancellationFee"] = paymentTransaction.Cost
		}
		return
	}

	// Case task pay later
	var transactions []*modelFATransaction.FinancialAccountTransaction
	globalDataAccess.GetAllByQuery(collectionFATransaction,
		bson.M{"source.value": task["_id"], "source.name": globalConstant.FA_TRANSACTION_SOURCE_NAME_CANCEL_TASK},
		bson.M{"amount": 1, "source": 1},
		&transactions,
	)
	// Get cancellation fee
	if len(transactions) > 0 {
		for _, tr := range transactions {
			if tr.Source != nil && tr.Amount > 0 {
				task["cancellationFee"] = tr.Amount
				break
			}
		}
	}
}

func updateDetailMassage(task map[string]interface{}) {
	// Update isFavouriteTasker
	mapAcceptedTaskerInfo := make(map[string]interface{})
	acceptedTaskers := []map[string]interface{}{}
	acceptedTaskerData, _ := json.Marshal(task["acceptedTasker"])
	json.Unmarshal(acceptedTaskerData, &acceptedTaskers)
	for _, tasker := range acceptedTaskers {
		taskerId := cast.ToString(tasker["taskerId"])
		mapAcceptedTaskerInfo[taskerId] = tasker
	}
	taskDetailMassage := cast.ToStringMap(task["detailMassage"])
	packagesMassage, _ := taskDetailMassage["packages"].(primitive.A)
	for _, v := range packagesMassage {
		packageMassage := cast.ToStringMap(v)
		taskerId := cast.ToString(packageMassage["taskerId"])
		packageMassage["taskerInfo"] = mapAcceptedTaskerInfo[taskerId]
	}
}

// get task source
func getTaskSource(task map[string]interface{}) {
	var source *modelTask.TaskSource
	taskSouceData, _ := json.Marshal(task["source"])
	json.Unmarshal(taskSouceData, &source)
	if source != nil && source.TaskId != "" {
		var taskSource *modelTask.Task
		globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], source.TaskId, bson.M{"_id": 1, "serviceName": 1}, &taskSource)

		sourceData := map[string]interface{}{
			"from": source.From,
		}
		if taskSource != nil {
			sourceData["taskInfo"] = map[string]interface{}{
				"taskId":      taskSource.XId,
				"serviceName": taskSource.ServiceName,
			}
		}
		task["source"] = sourceData
	}
}

func getTaskLixiInfo(taskId, askerId string, taskerIds []string) map[string]interface{} {
	query := bson.M{
		"userId":        bson.M{"$in": taskerIds},
		"type":          globalConstant.FA_TRANSACTION_TYPE_DEPOSIT,
		"source.name":   globalConstant.FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER,
		"source.value":  askerId,
		"source.taskId": taskId,
	}
	var transactions []*modelFATransaction.FinancialAccountTransaction
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], query, bson.M{"amount": 1}, &transactions)
	if err != nil || len(transactions) == 0 {
		return nil
	}

	totalAmount := 0.0
	for _, tr := range transactions {
		totalAmount += tr.Amount
	}

	return map[string]interface{}{
		"totalAmount": totalAmount,
	}
}
