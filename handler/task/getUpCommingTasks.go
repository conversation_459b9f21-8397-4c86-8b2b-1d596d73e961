/*
- @File: getUpCommingTasks.go
- @Description: Handler function for getUpCommingTasks api
- @CreatedAt: 02/03/2020
- @Author: linhnh
- @UpdatedAt: 04/03/2021
- @UpdatedBy: ngoctb3
*/
package task

import (
	"encoding/json"
	"net/http"
	"sort"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get UpComming Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetUpCommingTasks(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = ValidateGetListTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get task
	date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -lib.MAX_RATING_DATE)
	query := bson.M{
		"askerId":      reqBody.UserId,
		"date":         bson.M{"$gte": date},
		"status":       bson.M{"$in": [3]string{globalConstant.TASK_STATUS_POSTED, globalConstant.TASK_STATUS_WAITING, globalConstant.TASK_STATUS_CONFIRMED}},
		"isoCode":      reqBody.ISOCode,
		"sourceTaskId": bson.M{"$exists": false},
	}
	tasks, _ := globalDataAccess.GetAllByQuerySortMap(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		TASK_FIELDS,
		bson.M{"createdAt": -1},
	)

	if len(tasks) == 0 {
		globalResponse.ResponseSuccess(w, tasks)
		return
	}
	newTasks := []map[string]interface{}{}
	mapTaskByScheduleId := make(map[string][]map[string]interface{})
	mapTaskBySubscriptionId := make(map[string][]map[string]interface{})
	// Get service
	for _, v := range tasks {
		// Update taskDone of acceptedTasker if it nil
		UpdateFieldTaskDone(v)
		mapData.MapCostDetailForAsker(v)
		// Map task by scheduleId
		scheduleId := cast.ToString(v["scheduleId"])
		if scheduleId != "" {
			mapTaskByScheduleId[scheduleId] = append(mapTaskByScheduleId[scheduleId], v)
		}
		// Map task by subscriptionId
		subscriptionId := cast.ToString(v["subscriptionId"])
		if subscriptionId != "" {
			mapTaskBySubscriptionId[subscriptionId] = append(mapTaskBySubscriptionId[subscriptionId], v)
		}
		if scheduleId == "" && subscriptionId == "" {
			newTasks = append(newTasks, v)
		}
	}
	// Set list task
	tasks = newTasks
	// Map task by scheduleId
	tasks = append(tasks, mapRelatedTasksToTask(mapTaskByScheduleId)...)
	// Map task by subscriptionId
	tasks = append(tasks, mapRelatedTasksToTask(mapTaskBySubscriptionId)...)

	var services []*modelService.Service
	serviceQuery := bson.M{
		"$or": []bson.M{
			{"status": globalConstant.SERVICE_STATUS_ACTIVE},
			{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
		},
	}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceQuery, bson.M{"_id": 1, "name": 1, "text": 1, "icon": 1, "detailService.homeMoving": 1}, &services)
	// Convert list to map
	mapServiceById := map[string]*modelService.Service{}
	for _, v := range services {
		mapServiceById[v.XId] = v
	}

	// Map data tasks. And check if task has relatedTasks
	relatedTaskIds := []string{}
	shouldRefactorListTask := false
	for _, t := range tasks {
		// Update service info
		serviceId := cast.ToString(t["serviceId"])
		if s, ok := mapServiceById[serviceId]; ok {
			t["service"] = map[string]interface{}{
				"_id":  s.XId,
				"name": s.Name,
				"text": s.Text,
				"icon": s.Icon,
			}
		}

		// Check if the task has relatedTasks
		if t["relatedTasks"] != nil || t["sourceTaskId"] != nil {
			shouldRefactorListTask = true
			relatedTaskIds = append(relatedTaskIds, GetRelatedTaskIdsOfTask(t)...)
		}
		service := mapServiceById[serviceId]
		if service != nil && service.Name == globalConstant.SERVICE_KEY_NAME_HOME_MOVING {
			updateDetailHomeMovingTaskUpComing(t, service)
		}
	}

	// NOTE: function này cần để cuối cùng trước khi return to client. Vì nó sẽ lấy các task con đưa vào task cha. Như vậy khi loop lại mapTasks sau bước này sẽ không lặp qua các task con nữa
	if shouldRefactorListTask {
		tasks = RefactorListTaskHasRelatedTasks(tasks, mapServiceById, relatedTaskIds)
	}

	sortFailedPaymentTask(tasks)

	globalResponse.ResponseSuccess(w, tasks)
}

func mapRelatedTasksToTask(mapListTask map[string][]map[string]interface{}) []map[string]interface{} {
	newTasks := []map[string]interface{}{}
	for _, v := range mapListTask {
		task := v[0]
		task["relatedTasks"] = map[string]interface{}{
			"tasks": v,
		}
	}
	return newTasks
}

func updateDetailHomeMovingTaskUpComing(task map[string]interface{}, service *modelService.Service) {
	startWorking := cast.ToStringMap(task["startWorking"])
	isStart := cast.ToBool(startWorking["isStart"])
	detailHomeMoving := cast.ToStringMap(task["detailHomeMoving"])
	taskDetailHomeMoving := &modelTask.TaskDetailHomeMoving{}
	taskDetailHomeMovingData, _ := json.Marshal(detailHomeMoving)
	json.Unmarshal(taskDetailHomeMovingData, &taskDetailHomeMoving)
	if taskDetailHomeMoving != nil {
		if isStart {
			detailHomeMoving["stepInProgress"] = GetHomeMovingStepInProgress(taskDetailHomeMoving, service)
		}
		if service.DetailService != nil && service.DetailService.HomeMoving != nil {
			taskPlace := cast.ToStringMap(task["taskPlace"])
			taskCity := cast.ToString(taskPlace["city"])
			for _, v := range service.DetailService.HomeMoving.City {
				if v.Name == taskCity {
					detailHomeMoving["waitingFeeSetting"] = v.WaitingFeeSetting
				}
			}
		}
		task["detailHomeMoving"] = detailHomeMoving
	}
}

// sort task has payment.status == error || payment.paymentUpdatedAt > 15 minute before to top
func sortFailedPaymentTask(tasks []map[string]interface{}) {
	// Set task payment status to error if payment failed or pass 15 minutes from last payment
	for _, task := range tasks {
		if IsPaymentFailed(task) {
			payment, _ := cast.ToStringMapE(task["payment"])
			if payment == nil {
				continue
			}

			payment["status"] = globalConstant.TASK_PAYMENT_STATUS_ERROR
		}
	}

	// Sort tasks
	sort.SliceStable(tasks, func(i, j int) bool {
		// Sort failed to top if one fail and one not fail
		iPaymentStatus := getTaskPaymentStatus(tasks[i])
		jPaymentStatus := getTaskPaymentStatus(tasks[j])
		if iPaymentStatus == globalConstant.TASK_PAYMENT_STATUS_ERROR && jPaymentStatus != globalConstant.TASK_PAYMENT_STATUS_ERROR {
			return true
		}
		if iPaymentStatus != globalConstant.TASK_PAYMENT_STATUS_ERROR && jPaymentStatus == globalConstant.TASK_PAYMENT_STATUS_ERROR {
			return false
		}

		// Sort createdAt -1 if both equal
		iCreatedAt := cast.ToTime(tasks[i]["createdAt"])
		jCreatedAt := cast.ToTime(tasks[j]["createdAt"])
		return iCreatedAt.After(jCreatedAt)
	})
}

func getTaskPaymentStatus(task map[string]interface{}) string {
	payment := cast.ToStringMap(task["payment"])
	if payment == nil {
		return ""
	}
	return cast.ToString(payment["status"])
}
