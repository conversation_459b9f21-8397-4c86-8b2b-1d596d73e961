package others

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func StringeeVoiceCall(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody := model.StringeeRequest{
		From:   r.URL.Query().Get("from"),
		To:     r.URL.Query().Get("to"),
		Custom: r.URL.Query().Get("custom"),
	}
	fromAlias := reqBody.From
	toAlias := reqBody.To
	fromCustomData := make(map[string]interface{})
	toCustomData := make(map[string]interface{})
	var taskId string
	if fromAlias != "" || toAlias != "" {
		users, _ := modelUser.GetAll(
			local.ISO_CODE,
			bson.M{"_id": bson.M{"$in": [2]string{fromAlias, toAlias}}},
			bson.M{"_id": 1, "name": 1, "avatar": 1, "phone": 1, "language": 1},
		)
		if len(users) > 0 {
			var fromUser, toUser *modelUser.Users
			for _, u := range users {
				if u.XId == fromAlias {
					fromUser = u
				} else if u.XId == toAlias {
					toUser = u
				}
			}
			if fromUser != nil {
				fromAlias = fromUser.Name
				fromCustomData = map[string]interface{}{
					"avatar":   fromUser.Avatar,
					"name":     fromUser.Name,
					"phone":    fromUser.Phone,
					"language": fromUser.Language,
				}
			}
			if toUser != nil {
				toAlias = toUser.Name
				toCustomData = map[string]interface{}{
					"avatar":   toUser.Avatar,
					"name":     toUser.Name,
					"phone":    toUser.Phone,
					"language": toUser.Language,
				}
			}
		}
	}
	if reqBody.Custom != "" {
		mapCustom := make(map[string]interface{})
		json.Unmarshal([]byte(reqBody.Custom), &mapCustom)
		if mapCustom["taskId"] != nil {
			taskId = mapCustom["taskId"].(string)
		}
	}
	result := []map[string]interface{}{
		{
			"action": "connect",
			"from": map[string]interface{}{
				"type":   "internal",
				"number": reqBody.From,
				"alias":  fromAlias,
			},
			"to": map[string]interface{}{
				"type":   "internal", //internal: app-to-app call type
				"number": reqBody.To, //make a call to user_2
				"alias":  toAlias,
			},
			"customData": map[string]interface{}{
				"fromUser": fromCustomData,
				"toUser":   toCustomData,
				"taskId":   taskId,
			},
			"maxConnectTime": 300, // maximum 5 minutes for a call
		},
	}
	globalResponse.ResponseSuccess(w, result)
}
