/*
 * @File: postToSlack.go
 * @Description: Handler function of post to slack api
 * @CreatedAt: 09/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
package others

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

/*
 * @Description: Handler function of post to slack api
 * @CreatedAt: 09/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func PostToSlack(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	var reqBody map[string]interface{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validatePostToSlack(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	globalLib.PostToSlack(cfg.SlackToken, reqBody["channel"].(string), reqBody["username"].(string), reqBody["message"].(string))
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request data
 * @CreatedAt: 09/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validatePostToSlack(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["channel"] == nil || reqBody["channel"].(string) == "" {
		return &lib.ERROR_CHANNEL_REQUIRED
	}
	if reqBody["username"] == nil || reqBody["username"].(string) == "" {
		return &lib.ERROR_USERNAME_REQUIRED
	}
	if reqBody["message"] == nil || reqBody["message"].(string) == "" {
		return &lib.ERROR_MESSAGE_REQUIRED
	}
	return nil
}
