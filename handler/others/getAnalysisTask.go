/*
 * @File: getAnalysisTask.go
 * @Description: Handler function of GetAnalysisTask api
 * @CreatedAt: 10/08/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
package others

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Get Analysis Task by UserId
 * @CreatedAt: 11/08/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetAnalysisTask(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	// Migrate user data
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"countryCode": 1, "phone": 1, "isoCode": 1, "isMigration": 1, "createdAt": 1, "lastOnline": 1, "lastDoneTask": 1, "updatedAt": 1, "fAccountId": 1})
	if user != nil && !user.IsMigration && user.IsoCode == "" {
		dataUserUpdate := bson.M{"isMigration": true, "isoCode": local.ISO_CODE}
		modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, dataUserUpdate)
	}
	// Log data to debug
	ip := globalLib.GetIPFromHttpRequest(r)
	local.Logger.Info("Tasker getAnalysisTask",
		zap.String("url", r.RequestURI),
		zap.String("taskerId", reqBody.UserId),
		zap.String("ipAddress", ip),
	)

	d := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -7)
	var ratings []*modelRating.Rating
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskerId": reqBody.UserId, "rate": bson.M{"$gte": 4}, "createdAt": bson.M{"$gte": d}},
		bson.M{"askerId": 1},
		&ratings,
	)
	var listAskersWorked []string
	if len(ratings) > 0 {
		for _, v := range ratings {
			listAskersWorked = append(listAskersWorked, v.AskerId)
		}
		listAskersWorked = globalLib.UniqString(listAskersWorked)
	}

	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"listAskersWorked": listAskersWorked,
		"listAskersOften":  []string{},
	})
}

// func SendLogger(w http.ResponseWriter, r *http.Request) {
// 	reqBody, errCode, logger := lib.ParseBodyParams(r)
// 	if errCode != nil {
// 		globalResponse.ResponseError(w, *errCode)
// 		return
// 	}
// 	if cfg.LogglyToken == "" {
// 		errCode := lib.ERROR_CUSTOM
// 		errCode.ErrorCode = "LOGGLY_NOT_CONFIG"
// 		errCode.Message = "Loggly is not config"
// 		logger.Info(errCode.ErrorCode,
// 			zap.Any("body", reqBody),
// 		)
// 		globalResponse.ResponseError(w, errCode)
// 		return
// 	}
// 	// Validate data
// 	if reqBody.Message == "" {
// 		logger.Info(lib.ERROR_MESSAGE_REQUIRED.ErrorCode,
// 			zap.Any("body", reqBody),
// 		)
// 		globalResponse.ResponseError(w, lib.ERROR_MESSAGE_REQUIRED)
// 		return
// 	}
// 	// Send log
// 	data := map[string]interface{}{
// 		"key":     "GO_SERVICE_LOGGLY",
// 		"message": reqBody.Message,
// 	}
// 	if reqBody.Data != nil {
// 		data["data"] = reqBody.Data
// 	}
// 	bData, err := json.Marshal(data)
// 	if err != nil {
// 		logger.Info(lib.ERROR_LOGGLY_FAILED.ErrorCode,
// 			zap.Error(err),
// 			zap.Any("body", reqBody),
// 		)
// 		globalResponse.ResponseError(w, lib.ERROR_LOGGLY_FAILED)
// 		return
// 	}
// 	req, err := http.NewRequest("POST", fmt.Sprintf("https://logs-01.loggly.com/inputs/%s/tag/http", cfg.LogglyToken), bytes.NewBuffer(bData))
// 	req.Header.Set("Content-Type", "application/json")
// 	if err != nil {
// 		logger.Info(lib.ERROR_LOGGLY_FAILED.ErrorCode,
// 			zap.Error(err),
// 			zap.Any("body", reqBody),
// 		)
// 		globalResponse.ResponseError(w, lib.ERROR_LOGGLY_FAILED)
// 		return
// 	}
// 	client := &http.Client{Timeout: 10 * time.Second}
// 	_, err = client.Do(req)
// 	if err != nil {
// 		logger.Info(lib.ERROR_LOGGLY_FAILED.ErrorCode,
// 			zap.Error(err),
// 			zap.Any("body", reqBody),
// 		)
// 		globalResponse.ResponseError(w, lib.ERROR_LOGGLY_FAILED)
// 		return
// 	}
// 	globalResponse.ResponseSuccess(w, nil)
// }
