/*
 * @File: initDataV2.go
 * @Description: Initial Data V2 api handler function
 * @CreatedAt: 10/08/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package others

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTaskerSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerSettings"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Initial Data V2
 * @CreatedAt: 10/08/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func InitialDataV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateInitData(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	result := make(map[string]interface{})
	// Get settings
	settings := getSettings(reqBody.ISOCode)
	if settings != nil {
		if settings.TaskerReferralValue != nil {
			d := globalLib.GetCurrentTime(local.TimeZone).AddDate(1, 0, 0)
			settings.TaskerReferralValue.ExpiredDate = globalLib.ParseTimestampFromDate(d)
		}
		result["settingSystem"] = settings
	}
	// Get services
	services := getServices(reqBody.ISOCode)
	if len(services) > 0 {
		result["services"] = services
	}
	// Get tasker settings
	taskerSettings := getTaskerSettings(reqBody.ISOCode)
	if taskerSettings != nil {
		result["taskerSettings"] = taskerSettings
	}

	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get setting by isoCode
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getSettings(isoCode string) *modelSetting.Settings {
	var settings *modelSetting.Settings
	fields := bson.M{
		"supportPhone":                1,
		"depositIntruction":           1,
		"minFAccountTasker":           1,
		"submitionAddressForTasker":   1,
		"referralSetting":             1,
		"taskerNotCommingFee":         1,
		"numOfTaskToCancel":           1,
		"numOfTaskToRating":           1,
		"taskerSupportPhone":          1,
		"limitNumberTaskerAcceptTask": 1,
		"workingSchedule":             1,
		"startWorkingSetting":         1,
		"voiceCall":                   1,
		"registerService":             1,
		"listOfToolsForTaskerLocal":   1,
		"taskerMonthlyAwardByHour":    1,
		"numOfDayNotification":        1,
		"taskerReferralValue":         1,
		"minDepositForTasker":         1,
		"moneyRewards":                1,
		"billOfMedicine":              1,
		"moneyOneWorkingHour":         1,
		"supportGasolineMoney":        1,
		"lockAccountTasker":           1,
		"limitNumberAcceptTaskInDay":  1,
	}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, fields, &settings)
	if settings != nil && settings.ReferralSetting != nil && settings.ReferralSetting.Voucher != nil && settings.ReferralSetting.Voucher.Value > 0 {
		settings.ReferralValue = settings.ReferralSetting.Voucher.Value
	}
	return settings
}

/*
 * @Description: Get list services by isoCode
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getServices(isoCode string) []*modelService.Service {
	var services []*modelService.Service
	query := bson.M{
		"onlyShowAsker": bson.M{"$ne": true},
		"$or": []bson.M{
			{"status": globalConstant.SERVICE_STATUS_ACTIVE},
			{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isTesting": true},
		},
	}
	fields := bson.M{
		"text":                 1,
		"workingProcess":       1,
		"listOfToolsForTasker": 1,
		"minTaskDone":          1,
		"minAvgRating":         1,
		"status":               1,
		"onlyShowTasker":       1,
		"imageCaching":         1,
		"linkContentInCar":     1,
		"adviceTips":           1,
		"vehicleType":          1,
		"city":                 1,
	}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &services)
	return services
}

/*
 * @Description: Get tasker setting by isoCode
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getTaskerSettings(isoCode string) *modelTaskerSettings.TaskerSettings {
	var taskerSettings *modelTaskerSettings.TaskerSettings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{}, &taskerSettings)
	return taskerSettings
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateInitData(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
