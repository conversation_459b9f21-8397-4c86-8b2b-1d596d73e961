/*
 * @File: addPlaceNotSupport.go
 * @Description: Handler function of Add place not support
 * @CreatedAt: 24/09/2021
 * @Author: vinhnt
 * @UpdatedAt:
 * @UpdatedBy:
 */
package others

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelNextExpansion "gitlab.com/btaskee/go-services-model-v2/grpcmodel/nextExpansion"
	"go.uber.org/zap"
)

/*
 * @Description: Handler function of Add place not support
 * @CreatedAt: 24/09/2021
 * @Author: vinhnt
 * @UpdatedAt:
 * @UpdatedBy:
 */
func AddPlaceNotSupport(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	errCode = validateAddPlaceNotSupport(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	var collectionName string
	if reqBody.Place.Country == local.ISO_CODE {
		collectionName = globalCollection.COLLECTION_NEXT_EXPANSION[local.ISO_CODE]
	} else {
		collectionName = globalCollection.COLLECTION_WORLD_NEXT_EXPANSION[local.ISO_CODE]
	}
	insertData := &modelNextExpansion.NextExpansion{
		XId:   globalLib.GenerateObjectId(),
		Place: reqBody.Place,
	}
	err := globalDataAccess.InsertOne(collectionName, insertData)
	if err != nil {
		local.Logger.Warn(lib.ERROR_INSERT_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_INSERT_FAILED)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request data
 * @CreatedAt: 24/09/2021
 * @Author: vinhnt
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateAddPlaceNotSupport(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.Place == nil {
		return &lib.ERROR_PLACE_REQUIRED
	}
	if reqBody.Place.Country == "" {
		return &lib.ERROR_PLACE_COUNTRY_REQUIRED
	}
	if reqBody.Place.City == "" {
		return &lib.ERROR_PLACE_CITY_REQUIRED
	}
	if reqBody.Place.District == "" {
		return &lib.ERROR_PLACE_DISTRICT_REQUIRED
	}
	return nil
}
