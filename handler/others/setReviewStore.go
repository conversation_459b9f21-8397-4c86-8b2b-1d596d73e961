package others

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func SetReviewStore(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// validate
	errCode := validateSetReviewStore(reqBody)
	if errCode != nil {
		return errCode
	}

	// Update database
	updateData := make(bson.M)
	if reqBody.Count > 0 {
		updateData["$inc"] = bson.M{"reviewStore.count": reqBody.Count}
	}
	if reqBody.IsReviewStore {
		updateData["$set"] = bson.M{"reviewStore.isReviewStore": reqBody.IsReviewStore}
	}
	modelUser.UpdateOneById(local.ISO_CODE, reqBody.UserId, updateData)
	return nil
}

func validateSetReviewStore(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Count == 0 && !reqBody.IsReviewStore {
		return &lib.ERROR_DATA_INVALID
	}
	return nil
}
