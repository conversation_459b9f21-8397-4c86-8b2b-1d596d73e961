package others

import (
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func GiveLixiTet2022(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}
	if reqBody.ISOCode == "" || reqBody.ISOCode != local.ISO_CODE {
		local.Logger.Warn(lib.ERROR_ISO_CODE_INCORRECT.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ISO_CODE_INCORRECT)
		return
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "language": 1, "phone": 1})
	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}

	now := globalLib.GetCurrentTime(local.TimeZone)
	startTime := time.Date(2022, 2, 1, 0, 0, 0, 0, local.TimeZone)
	endTime := time.Date(2022, 2, 3, 23, 59, 59, 0, local.TimeZone)
	if (now.Before(startTime) || now.After(endTime)) && !lib.CheckIsUserTester(user) {
		local.Logger.Warn(lib.ERROR_EVENT_NOT_RUNNING.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_EVENT_NOT_RUNNING)
		return
	}

	giftExist, _ := globalDataAccess.IsExistByQuery(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"userId":   user.XId,
			"title.en": "[LUCKY MONEY] Discount 22,000đ all services of bTaskee",
		},
	)
	if giftExist {
		local.Logger.Warn(lib.ERROR_VOUCHER_LIXI_EXIST.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_VOUCHER_LIXI_EXIST)
		return
	}
	// Tạo promotion
	now_ts := globalLib.ParseTimestampFromDate(now)
	endDate := time.Date(2022, 02, 28, 23, 59, 59, 0, local.TimeZone)
	endDate_ts := globalLib.ParseTimestampFromDate(endDate)

	code, err := promotioncode.GeneratePromotionCode(local.ISO_CODE, "", 7)
	if err != nil {
		local.Logger.Warn(lib.ERROR_INSERT_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Any("error", err),
		)
		globalResponse.ResponseError(w, lib.ERROR_INSERT_FAILED)
		return
	}

	userPromotionCode := &modelPromotionCode.PromotionCode{
		XId: globalLib.GenerateObjectId(),
		Value: &modelPromotionCode.PromotionCodeValue{
			Type:  globalConstant.PROMOTION_TYPE_MONEY,
			Value: 22000,
		},
		UserIds:         []string{reqBody.UserId},
		Limit:           1,
		IsoCode:         local.ISO_CODE,
		Code:            code,
		Target:          globalConstant.USER_TYPE_ASKER,
		Description:     "Discount 22,000 VND all services",
		CreatedAt:       now_ts,
		StartDate:       now_ts,
		EndDate:         endDate_ts,
		TypeOfPromotion: globalConstant.PROMOTION_APPLY_BOTH,
		Source:          globalConstant.PROMOTION_CODE_SOURCE_SYSTEM,
	}
	// Insert to database
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], userPromotionCode)
	if err != nil {
		local.Logger.Warn(lib.ERROR_INSERT_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_INSERT_FAILED)
		return
	}

	// Tạo gift
	userGift := &modelGift.Gift{
		XId:           globalLib.GenerateObjectId(),
		PromotionCode: userPromotionCode.Code,
		Source:        globalConstant.GIFT_SOURCE_SYSTEM,
		IsoCode:       local.ISO_CODE,
		UserId:        user.XId,
		PromotionId:   userPromotionCode.XId,
		Image:         "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/campaigns/vietnam/lixi2022/LixiTet2022.png",
		CreatedAt:     now_ts,
		Content: &service.ServiceText{
			Vi: "- Lựa chọn dịch vụ bạn muốn sử dụng.\n- Nhập thông tin chi tiết công việc.\n- Nhập mã khuyến mãi tại bước 'Xác nhận và thanh toán'.\n- Hoàn tất đặt lịch.",
			En: "- Select the service you want to use.\n- Enter detailed information\n- Enter the promo code at the 'Confirm and pay' step.\n- Complete the booking.",
			Th: "- Select the service you want to use.\n- Enter detailed information\n- Enter the promo code at the 'Confirm and pay' step.\n- Complete the booking.",
			Ko: "- 사용하려는 서비스를 선택합니다\n- 상세 정보를 입력합니다.\n- '확인 및 결제’ 단계에서 프로모션 코드를 입력합니다.\n- 예약을 완성합니다.",
		},
		Title: &service.ServiceText{
			Vi: "[LÌ XÌ TẾT] Voucher giảm 22,000đ cho tất cả dịch vụ của bTaskee",
			En: "[LUCKY MONEY] Discount 22,000đ all services of bTaskee",
			Th: "[LUCKY MONEY] Discount 22,000đ all services of bTaskee",
			Ko: "[Tet 선물] 모든 bTaskee 서비스에 대한 22,000동 할인 바우처",
		},
		Note: &service.ServiceText{
			Vi: "- Ưu đãi áp dụng đến hết ngày 28/02/2022.\n- Chỉ áp dụng cho dịch vụ trên ứng dụng bTaskee.\n- Mã khuyến mãi có giá trị sử dụng 01 lần duy nhất.\n- Không có giá trị quy đổi thành tiền mặt.\n- Không áp dụng đồng thời cùng các chương trình ưu đãi khác.",
			En: "- Valid until the end of February 28, 2022.\n- Only applicable to services on bTaskee app.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions\n- Not refundable and exchangeable.",
			Th: "- Valid until the end of February 28, 2022.\n- Only applicable to services on bTaskee app.\n- Promotion code only applied for 01 booking.\n- This promotion cannot be used together with other promotions\n- Not refundable and exchangeable.",
			Ko: "2022년 2월 28일까지 유효합니다.\n- bTaskee앱의 서비스에만 적용됩니다.\n- 프로모션 코드는 한 번만 사용할 수 있습니다\n- 현금으로 교환할 수 없습니다.\n- 다른 프로모션과 중복 적용되지 않습니다",
		},
		BrandInfo: &incentive.IncentiveBrandInfo{
			Name: "bTaskee",
			Text: &service.ServiceText{
				Vi: "bTaskee",
				En: "bTaskee",
				Th: "bTaskee",
				Ko: "bTaskee",
			},
			Image: "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/campaigns/vietnam/lixi2022/lixi-icon.png",
		},
		Expired: endDate_ts,
	}

	// Insert to db
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_GIFT[local.ISO_CODE], userGift)
	if err != nil {
		// Xoa ma khuyen mai da tao o tren
		globalDataAccess.DeleteOneById(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], userPromotionCode.XId)
		local.Logger.Warn(lib.ERROR_INSERT_FAILED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_INSERT_FAILED)
		return
	}
}
