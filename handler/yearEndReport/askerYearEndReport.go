/*
 * @File: askerYearEndReport.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 31/12/2021
 * @Author: vinhnt
 */
package yearEndReport

import (
	"fmt"
	"math"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelAskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerYearEndReport"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Asker Year End Report
 * @CreatedAt: 31/12/2021
 * @UpdatedBy: vinhnt
 */
func AskerYearEndReport(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetAskerReport(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get tasker
	asker, _ := modelUsers.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"createdAt": 1, "status": 1})
	if asker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	if globalLib.FindStringInSlice([]string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_DISABLED}, asker.Status) == -1 {
		local.Logger.Warn(lib.ERROR_USER_STATUS_INCORRECT.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_STATUS_INCORRECT)
		return
	}
	curentTime := globalLib.GetCurrentTime(local.TimeZone)
	taskerCreatedAt := globalLib.ParseDateFromTimeStamp(asker.CreatedAt, local.TimeZone)
	lastYear := curentTime.Year() - 1
	// Get tasker Report
	var askerReportData *modelAskerYearEndReport.AskerYearEndReport
	globalDataAccess.GetOneById(fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear), reqBody.UserId, bson.M{}, &askerReportData)
	var totalTaskDone int32
	var totalTaskDuration float64
	var mostUsedService *modelService.ServiceText
	if askerReportData != nil {
		totalTaskDone = askerReportData.TotalTaskDone
		totalTaskDuration = askerReportData.TotalTaskDuration
		mostUsedService = askerReportData.MostUsedService
	}
	// Return data
	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"mostUsedService":   mostUsedService,
		"totalTaskDone":     totalTaskDone,
		"totalTaskDuration": totalTaskDuration,
		"register":          math.Round(curentTime.Sub(taskerCreatedAt).Hours() / 24),
		"year":              lastYear,
	})
}

func validateGetAskerReport(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	return nil
}
