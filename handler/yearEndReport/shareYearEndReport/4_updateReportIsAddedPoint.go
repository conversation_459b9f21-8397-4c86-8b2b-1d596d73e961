package shareYearEndReport

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func updateReportIsAddedPoint(userId string, collectionName string) {
	updateReport(userId, collectionName, 0)
}

func updateReport(userId string, collectionName string, retry int32) {
	queryUpdate := bson.M{
		"$set": bson.M{
			"isAccumulatedPointBySharing": true,
		},
	}
	_, err := globalDataAccess.UpdateOneById(collectionName, userId, queryUpdate)
	if err != nil {
		if retry == 3 {
			msg := fmt.Sprintf("[%s] Added bPoint success but mark report is added failed: userId %s. Error: %s", local.ISO_CODE, userId, err)
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
			return
		}
		updateReport(userId, collectionName, retry+1)
	}
}
