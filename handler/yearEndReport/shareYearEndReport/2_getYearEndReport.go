package shareYearEndReport

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelAskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerYearEndReport"
	"go.mongodb.org/mongo-driver/bson"
)

func getYearEndReport(reqBody *model.ApiRequest, collectionName string) (*modelAskerYearEndReport.AskerYearEndReport, *globalResponse.ResponseErrorCode) {
	var askerYearEndReport *modelAskerYearEndReport.AskerYearEndReport
	globalDataAccess.GetOneById(collectionName, reqBody.UserId, bson.M{}, &askerYearEndReport)
	if askerYearEndReport == nil {
		return nil, &lib.ERROR_ASKER_REPORT_NOT_FOUND
	}
	return askerYearEndReport, nil
}
