package shareYearEndReport

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func ShareYearEndReport(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	lastYear := currentTime.Year() - 1
	collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)

	// 2. get year end report, check if already add bpoint via share
	askerYearEndReport, errCode := getYearEndReport(reqBody, collectionName)
	if errCode != nil {
		return nil, errCode, nil
	}
	// if already add bpoint via share, return
	if askerYearEndReport.IsAccumulatedPointBySharing {
		return nil, nil, nil
	}

	// 3. add point to user
	point, errCode, err := addUserPoint(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 4. set added point via share
	updateReportIsAddedPoint(reqBody.UserId, collectionName)

	return map[string]interface{}{
		"accumulatedPoint": point,
	}, nil, nil
}
