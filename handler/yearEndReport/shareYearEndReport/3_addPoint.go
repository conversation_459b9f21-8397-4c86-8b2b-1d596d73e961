package shareYearEndReport

import (
	"context"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	connectGRPCBPoint "gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcBPointVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBPointRequest "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointVN"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

// Cộng bPoint khi KH share trang cuối cùng dựa trên số bPoint đã thu thập được:
// trên 2000 bPoint: tặng 100 bPoint
// 1000 - 2000 bPoint: tặng 50 bPoint
// dưới 1000 bPoint: tặng 10 bPoint
func addUserPoint(userId string) (float64, *globalResponse.ResponseErrorCode, error) {
	pointSettings := map[string]float64{
		globalConstant.ASKER_RANK_NAME_MEMBER:   1,
		globalConstant.ASKER_RANK_NAME_SILVER:   5,
		globalConstant.ASKER_RANK_NAME_GOLD:     25,
		globalConstant.ASKER_RANK_NAME_PLATINUM: 50,
	}

	// get user rank
	user, err := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"rankInfo": 1})
	if err != nil || user == nil {
		return 0, &lib.ERROR_USER_NOT_FOUND, err
	}

	// get point by rankInfo
	rankName := user.GetRankInfo().GetRankName()
	if rankName == "" {
		rankName = globalConstant.ASKER_RANK_NAME_MEMBER
	}
	point := pointSettings[rankName]

	// connect grpc to bpoint service
	client, connect, err := connectGRPCBPoint.ConnectGRPCBPointVN(cfg.GRPC_BPoint_Port)
	if err != nil {
		return 0, &lib.ERROR_ADD_USER_BPOINT_FAILED, err
	}
	defer connect.Close()
	_, err = client.AddUserPoint(context.Background(), &modelBPointRequest.BPointRequest{
		UserId:     userId,
		Point:      point,
		SourceName: globalConstant.ADD_BPOINT_SOURCE_SHARE_YEAR_END_REPORT,
	})
	if err != nil {
		return 0, &lib.ERROR_ADD_USER_BPOINT_FAILED, err
	}
	return point, nil, nil
}
