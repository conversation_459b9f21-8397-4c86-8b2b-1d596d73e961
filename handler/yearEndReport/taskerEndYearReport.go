/*
 * @File: taskerEndYearReport.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 28/12/2021
 * @Author: vinhnt
 */
package yearEndReport

import (
	"fmt"
	"math"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTaskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerYearEndReport"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Tasker Year End Report
 * @CreatedAt: 28/12/2021
 * @UpdatedBy: vinhnt
 */
func TaskerYearEndReport(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetTaskerReport(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get tasker
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.TaskerId, bson.M{"createdAt": 1, "status": 1, "firstVerifyAt": 1})
	if tasker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	if globalLib.FindStringInSlice([]string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_DISABLED}, tasker.Status) == -1 {
		local.Logger.Warn(lib.ERROR_USER_STATUS_INCORRECT.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_STATUS_INCORRECT)
		return
	}
	curentTime := globalLib.GetCurrentTime(local.TimeZone)
	taskerRegister := globalLib.ParseDateFromTimeStamp(tasker.CreatedAt, local.TimeZone)
	if tasker.FirstVerifyAt != nil {
		taskerRegister = globalLib.ParseDateFromTimeStamp(tasker.FirstVerifyAt, local.TimeZone)
	}
	lastYear := curentTime.Year() - 1
	// Get tasker Report
	var taskerReportData *modelTaskerYearEndReport.TaskerYearEndReport
	globalDataAccess.GetOneById(fmt.Sprintf("%s%d", globalCollection.COLLECTION_TASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear), reqBody.TaskerId, bson.M{}, &taskerReportData)
	var badges, reviews []string
	var totalTaskDone int32
	var totalTaskCost, totalTaskDuration, avgRating float64
	var mostUsedService *modelService.ServiceText
	var isPremiumTasker bool
	if taskerReportData != nil {
		badges = taskerReportData.Badges
		totalTaskDone = taskerReportData.TotalTaskDone
		totalTaskCost = taskerReportData.TotalTaskCost
		totalTaskDuration = taskerReportData.TotalTaskDuration
		mostUsedService = taskerReportData.MostUsedService
		reviews = taskerReportData.Reviews
		isPremiumTasker = taskerReportData.IsPremiumTasker
		avgRating = taskerReportData.AvgRating
	}
	// Return data
	result := map[string]interface{}{
		"badges":            badges,
		"mostUsedService":   mostUsedService,
		"totalTaskDone":     totalTaskDone,
		"totalTaskCost":     totalTaskCost,
		"totalTaskDuration": totalTaskDuration,
		"register":          math.Round(curentTime.Sub(taskerRegister).Hours() / 24),
		"reviews":           reviews,
		"year":              lastYear,
		"isPremiumTasker":   isPremiumTasker,
		"avgRating":         math.Round(avgRating*100) / 100,
	}
	if taskerReportData != nil && taskerReportData.Achievement != nil && curentTime.After(globalLib.ParseDateFromString("2022-01-13T00:00:00Z07:00", local.TimeZone)) {
		result["achievement"] = taskerReportData.Achievement
	}
	globalResponse.ResponseSuccess(w, result)
}

func validateGetTaskerReport(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskerId == "" {
		return &lib.ERROR_TASKER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	return nil
}
