package askerYearEndReportVN

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelAskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerYearEndReport"
	"go.mongodb.org/mongo-driver/bson"
)

func getDataReport(askerId string, lastYear int) (*modelAskerYearEndReport.AskerYearEndReport, *globalResponse.ResponseErrorCode) {
	// Get tasker Report
	collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	var askerReportData *modelAskerYearEndReport.AskerYearEndReport
	err := globalDataAccess.GetOneById(collectionName, askerId, bson.M{}, &askerReportData)
	if askerReportData == nil || err != nil {
		return nil, &lib.ERROR_ASKER_REPORT_NOT_FOUND
	}
	return askerReportData, nil
}
