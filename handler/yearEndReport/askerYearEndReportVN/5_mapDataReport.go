package askerYearEndReportVN

import (
	"math"
	"sort"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerYearEndReport"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func mapDataReport(asker *modelUser.Users, dataReport *modelAskerYearEndReport.AskerYearEndReport, lastYear int) map[string]interface{} {
	var result map[string]interface{}
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	askerCreatedAt := globalLib.ParseDateFromTimeStamp(asker.CreatedAt, local.TimeZone)
	startLastYear := time.Date(lastYear, 1, 1, 0, 0, 0, 0, local.TimeZone)
	numberOfUsedOnService := dataReport.NumberOfUsedOnService
	sort.Slice(numberOfUsedOnService, func(i, j int) bool {
		return numberOfUsedOnService[i].NumberOfUsed > numberOfUsedOnService[j].NumberOfUsed || (numberOfUsedOnService[i].NumberOfUsed == numberOfUsedOnService[j].NumberOfUsed && numberOfUsedOnService[i].ServiceName > numberOfUsedOnService[j].ServiceName)
	})
	totalTaskDuration := dataReport.TotalTaskDuration
	for _, v := range numberOfUsedOnService {
		if globalConstant.SERVICE_NOT_HAVE_TASK_DURATION[v.ServiceName] {
			totalTaskDuration = totalTaskDuration + float64(v.NumberOfUsed)*3
		}
	}
	var listUsedService []string
	for _, v := range numberOfUsedOnService {
		listUsedService = append(listUsedService, v.ServiceName)
	}
	percentOfUsedbPoint := 0.0
	if dataReport.TotalUsedbPoint != 0 && dataReport.TotalAccumulatedbPoint != 0 {
		percentOfUsedbPoint = math.Round(float64(dataReport.TotalUsedbPoint) / float64(dataReport.TotalAccumulatedbPoint) * 100)
	}
	moneyByPoint := dataReport.TotalUsedbPoint * 5000
	// if percentOfUsedbPoint <= 50 {
	// 	moneyByPoint = dataReport.TotalAccumulatedbPoint * 5000
	// }
	moneyByAccumulatedbPoint := dataReport.TotalAccumulatedbPoint * 5000
	result = map[string]interface{}{
		"year":                        lastYear,
		"mostUsedService":             dataReport.MostUsedService,
		"totalTaskDone":               dataReport.TotalTaskDone,
		"totalTaskDuration":           roundUp(totalTaskDuration),
		"numberOfService":             len(dataReport.ListService),
		"numberOfUsedService":         len(numberOfUsedOnService),
		"numberOfUnusedService":       len(dataReport.ListUnusedService),
		"listService":                 dataReport.ListService,
		"listUsedService":             listUsedService,
		"listUnusedService":           dataReport.ListUnusedService,
		"totalAccumulatedbPoint":      dataReport.TotalAccumulatedbPoint,
		"totalUsedbPoint":             dataReport.TotalUsedbPoint,
		"numberOfReferred":            dataReport.NumberOfReferred,
		"totalFavouriteTasker":        dataReport.TotalFavouriteTasker,
		"totalVouchersReceived":       dataReport.TotalVouchersReceived,
		"totalVoucherValueReceived":   dataReport.TotalVoucherValueReceived,
		"totalVouchersUsed":           dataReport.TotalVouchersUsed,
		"totalVoucherValueUsed":       dataReport.TotalVoucherValueUsed,
		"isCreatedInLastYear":         askerCreatedAt.After(startLastYear),
		"register":                    math.Round(currentTime.Sub(askerCreatedAt).Hours() / 24 / 365),
		"percentOfUsedbPoint":         percentOfUsedbPoint,
		"bPointToMoney":               moneyByPoint,
		"accumulatedbPointToMoney":    moneyByAccumulatedbPoint,
		"isAccumulatedPointBySharing": dataReport.IsAccumulatedPointBySharing,
	}
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"rankSetting": 1}, &settings)

	var oldRankName, newRankName string = "MEMBER", "MEMBER"
	rankInfo := getRankInfo(asker, settings)
	if rankInfo != nil {
		newRankName = rankInfo.RankName
		result["rankInfo"] = rankInfo
	}
	lastRank := getLastRankInfo(asker, settings)
	if lastRank != nil {
		oldRankName = lastRank.RankName
		result["lastRankInfo"] = lastRank
	} else {
		oldRankName = "NEW"
	}
	result["compareRank"] = getCompareRank(oldRankName, newRankName, settings)

	return result
}

func getRankInfo(asker *modelUser.Users, settings *modelSettings.Settings) *modelUser.UserRankInfo {
	if asker.RankInfo != nil {
		return asker.RankInfo
	}
	if settings != nil && len(settings.RankSetting) > 0 {
		return &modelUser.UserRankInfo{
			RankName: settings.RankSetting[0].RankName,
			Text:     settings.RankSetting[0].Text,
		}
	}
	return nil
}

func getLastRankInfo(asker *modelUser.Users, settings *modelSettings.Settings) *modelUser.UserRankInfo {
	// if settings != nil && len(settings.RankSetting) > 0 {
	// 	return &modelUsers.UserRankInfo{
	// 		RankName: settings.RankSetting[0].RankName,
	// 		Text:     settings.RankSetting[0].Text,
	// 	}
	// }

	if len(asker.ResetRankHistory) >= 2 {
		return asker.ResetRankHistory[len(asker.ResetRankHistory)-2].RankInfo
	}

	return nil
}

func getCompareRank(oldRankName, newRankName string, settings *modelSettings.Settings) string {
	if oldRankName == "NEW" {
		return "NEW"
	}
	var indexOldRank, indexNewRank int
	if settings != nil && settings.RankSetting != nil {
		for i, rank := range settings.RankSetting {
			if rank.RankName == oldRankName {
				indexOldRank = i
			}
			if rank.RankName == newRankName {
				indexNewRank = i
			}
		}
	} else {
		listRankName := []string{"MEMBER", "SILVER", "GOLD", "PLATINUM"}
		for i, rankName := range listRankName {
			if rankName == oldRankName {
				indexOldRank = i
			}
			if rankName == newRankName {
				indexNewRank = i
			}
		}
	}
	if indexOldRank < indexNewRank {
		return "UP"
	} else if indexOldRank > indexNewRank {
		return "DOWN"
	} else {
		return "SAME"
	}
}

// roundUp rounds up a float64 number to the nearest integer.
func roundUp(num float64) int {
	return int(math.Ceil(num))
}
