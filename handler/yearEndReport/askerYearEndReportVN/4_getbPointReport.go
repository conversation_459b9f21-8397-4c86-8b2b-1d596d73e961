package askerYearEndReportVN

import (
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelAskerYearEndReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerYearEndReport"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

func getbPointReport(dataReport *modelAskerYearEndReport.AskerYearEndReport, lastYear int) {
	if dataReport.TotalAccumulatedbPoint > 0 || dataReport.TotalUsedbPoint > 0 {
		return
	}
	startYear := time.Date(lastYear, 1, 1, 0, 0, 0, 0, local.TimeZone)
	endYear := time.Date(lastYear, 12, 31, 23, 59, 59, 0, local.TimeZone)
	// Define the query to filter the point transactions.
	query := bson.M{
		"userId":    dataReport.XId,
		"createdAt": bson.M{"$gte": startYear, "$lte": endYear},
	}
	// Retrieve the point transactions that match the query.
	var pointTrans []*modelPointTransaction.PointTransaction
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], query, bson.M{"point": 1, "type": 1}, &pointTrans)

	// Calculate the total point earned by summing up the points from each transaction.
	var totalAccumulatedbPoint, totalUsedbPoint float64 = 0, 0
	for _, tran := range pointTrans {
		point := tran.Point
		if tran.Type == "C" {
			totalUsedbPoint += point
		} else {
			totalAccumulatedbPoint += point
		}
	}
	dataReport.TotalAccumulatedbPoint = totalAccumulatedbPoint
	dataReport.TotalUsedbPoint = totalUsedbPoint
	go updateReportPointToAskerReport(dataReport, lastYear)
}

func updateReportPointToAskerReport(dataReport *modelAskerYearEndReport.AskerYearEndReport, lastYear int) {
	collectionName := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.UpdateOneById(collectionName, dataReport.XId, bson.M{"$set": bson.M{"totalAccumulatedbPoint": dataReport.TotalAccumulatedbPoint, "totalUsedbPoint": dataReport.TotalUsedbPoint}})
}
