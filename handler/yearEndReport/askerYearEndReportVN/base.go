package askerYearEndReportVN

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func AskerYearEndReport(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	errCode := validation(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker, errCode := getAsker(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	lastYear := currentTime.Year() - 1

	dataReport, errCode := getDataReport(reqBody.UserId, lastYear)
	if errCode != nil {
		return nil, errCode
	}
	getbPointReport(dataReport, lastYear)

	result := mapDataReport(asker, dataReport, lastYear)
	return result, nil
}
