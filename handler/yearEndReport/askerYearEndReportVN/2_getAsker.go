package askerYearEndReportVN

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAsker(askerId string) (*modelUser.Users, *globalResponse.ResponseErrorCode) {
	asker, err := modelUser.GetOneById(local.ISO_CODE, askerId, bson.M{"createdAt": 1, "status": 1, "rankInfo": 1, "resetRankHistory": 1})
	if asker == nil || err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}
	return asker, nil
}
