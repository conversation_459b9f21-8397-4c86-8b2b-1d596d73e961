/*
 * @File: getIncentive.go
 * @Description: Handler function of get special incentive api
 * @CreatedAt: 27/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package incentive

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Special Incentive
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetSpecialIncentive(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetSpecialIncentive(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"phone": 1, "rankInfo": 1, "rankInfoByCountry": 1})
	//Set filter
	askerRank := lib.GetRankAsker(asker)
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := map[string]interface{}{
		"status":             globalConstant.INCENTIVE_STATUS_ACTIVE,
		"startDate":          bson.M{"$lte": now},
		"endDate":            bson.M{"$gte": now},
		"isTesting":          bson.M{"$ne": true},
		"isSpecialIncentive": true,
		"$or": []bson.M{
			{"rankRequire": bson.M{"$exists": false}},
			{"rankRequire": bson.M{"$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]}},
		},
	}
	isUserTester := lib.CheckIsUserTester(asker)
	if isUserTester {
		delete(query, "isTesting")
	}
	// Get data
	var incentives []*modelIncentive.Incentive
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		bson.M{"startDate": 1, "endDate": 1, "title": 1, "image": 1, "brandInfo": 1, "from": 1, "exchange": 1, "social": 1},
		bson.M{"startDate": -1},
		&incentives,
	)

	results := []map[string]interface{}{}

	for _, v := range incentives {
		result := map[string]interface{}{}
		b, _ := json.Marshal(v)
		json.Unmarshal(b, &result)
		result["point"] = v.Exchange.Point
		delete(result, "exchange")

		results = append(results, result)
	}

	//Return data to FE
	globalResponse.ResponseSuccess(w, results)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateGetSpecialIncentive(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
