/*
 * @File: getIncentive.go
 * @Description: Handler function of get incentive api
 * @CreatedAt: 27/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package incentive

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Incentive
 * @CreatedAt: 27/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetIncentive(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetIncentive(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	asker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"phone": 1, "rankInfo": 1, "rankInfoByCountry": 1})
	//Set filter
	askerRank := lib.GetRankAsker(asker)
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"status":    globalConstant.INCENTIVE_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"isTesting": bson.M{"$ne": true},
		"$or": []bson.M{
			{"rankRequire": bson.M{"$exists": false}},
			{"rankRequire": bson.M{"$lte": globalConstant.MAP_RANK_REQUIRE[askerRank]}},
		},
	}
	// Check user is tester or not
	isUserTester := lib.CheckIsUserTester(asker)
	if isUserTester {
		delete(query, "isTesting")
	}
	if reqBody.FilterBy != nil {
		categoryName, ok := reqBody.FilterBy["category"]
		if ok {
			query["categoryName"] = categoryName
		}
		exchangedPoint, ok := reqBody.FilterBy["exchangedPoint"]
		if ok {
			query["exchange.point"] = bson.M{"$lte": exchangedPoint}
		}
		from, ok := reqBody.FilterBy["from"]
		if ok {
			query["from"] = from
		}
	}

	//Set default sort
	sort := bson.M{"startDate": -1}
	if reqBody.SortBy != "" && reqBody.SortBy != "LASTEST" {
		if reqBody.SortBy == "POINT_DECREASE" {
			sort = bson.M{"exchange.point": -1}
		} else if reqBody.SortBy == "POINT_INCREASE" {
			sort = bson.M{"exchange.point": 1}
		}
	}

	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}

	// Get data
	var incentives []*modelIncentive.Incentive
	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		query,
		bson.M{"startDate": 1, "endDate": 1, "title": 1, "image": 1, "brandInfo": 1, "from": 1, "exchange": 1, "social": 1},
		int64(page),
		int64(limit),
		sort,
		&incentives,
	)

	results := []map[string]interface{}{}

	for _, v := range incentives {
		result := map[string]interface{}{}
		b, _ := json.Marshal(v)
		json.Unmarshal(b, &result)
		result["point"] = v.Exchange.Point
		delete(result, "exchange")

		results = append(results, result)
	}

	//Return data to FE
	globalResponse.ResponseSuccess(w, results)
}

/*
 * @Description: Get Incentive
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetIncentive(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
