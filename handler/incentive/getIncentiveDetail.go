/*
 * @File: getIncentive.go
 * @Description: Handler function of get incentive detail api
 * @CreatedAt: 27/10/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package incentive

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Detail Incentive
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/12/2020
 * @UpdatedBy: vinhnt
 */
func GetIncentiveDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.ID == "" {
		local.Logger.Warn(lib.ERROR_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ID_REQUIRED)
		return
	}
	// Get data
	var incentive *modelIncentive.Incentive
	globalDataAccess.GetOneById(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], reqBody.ID, bson.M{"codeList": 0}, &incentive)

	result := map[string]interface{}{}
	b, _ := json.Marshal(incentive)
	json.Unmarshal(b, &result)
	result["point"] = incentive.Exchange.Point
	delete(result, "exchange")

	globalResponse.ResponseSuccess(w, result)
}
