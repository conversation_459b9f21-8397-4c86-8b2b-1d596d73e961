/*
 * @File: getUsedGift.go
 * @Description: Handler function for get used gift api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package gift

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Used Gift by UserId
 * @CreatedAt: 04/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetUsedGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody *modelGift.Gift
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate request params
	errCode := validateGetUsedGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get list used gift
	var gifts []*modelGift.Gift
	now := globalLib.GetCurrentTime(local.TimeZone)
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"userId": reqBody.UserId,
			"$or": []bson.M{
				{"used": true},
				{"expired": bson.M{"$lt": now}},
			},
		},
		bson.M{},
		bson.M{"usedAt": -1},
		&gifts,
	)
	results := refactorGiftResult(gifts)
	globalResponse.ResponseSuccess(w, results)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetUsedGift(reqBody *modelGift.Gift) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
