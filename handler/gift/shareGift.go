package gift

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func ShareGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	decoder := json.NewDecoder(r.Body)
	reqBody := make(map[string]interface{})
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Validate request params
	errCode := validateShareGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Check newUser
	newUser, _ := modelUsers.GetOneById(local.ISO_CODE, reqBody["userId"].(string), bson.M{"status": 1, "isoCode": 1})
	if newUser == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	if newUser.Status != globalConstant.USER_STATUS_ACTIVE {
		local.Logger.Warn(lib.ERROR_USER_INACTIVE.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_INACTIVE)
		return
	}

	// Check shared user
	sharedUser, _ := modelUser.GetOneById(local.ISO_CODE, reqBody["sharedUserId"].(string), bson.M{"_id": 1, "name": 1})
	if sharedUser == nil {
		local.Logger.Warn(lib.ERROR_SHARED_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_SHARED_USER_NOT_FOUND)
		return
	}

	// Check gift userId
	var gift *modelGift.Gift
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"_id": reqBody["giftId"], "userId": reqBody["sharedUserId"]}, bson.M{"isoCode": 1, "applyFor": 1}, &gift)
	if gift == nil {
		local.Logger.Warn(lib.ERROR_GIFT_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_GIFT_NOT_FOUND)
		return
	}
	// Check gift ApplyFor
	if gift.ApplyFor == nil || !gift.ApplyFor.IsAllUsers {
		local.Logger.Warn(lib.ERROR_GIFT_CAN_NOT_SHARE.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_GIFT_CAN_NOT_SHARE)
		return
	}
	// Check gift with newUser
	if gift.IsoCode != newUser.IsoCode {
		local.Logger.Warn(lib.ERROR_USER_ISO_CODE_NOT_MATCH.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ISO_CODE_NOT_MATCH)
		return
	}

	// Change userId to newUserId
	_, err = globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], bson.M{"_id": reqBody["giftId"], "userId": reqBody["sharedUserId"]},
		bson.M{"$set": bson.M{"userId": reqBody["userId"]}},
	)
	if err != nil {
		local.Logger.Warn(lib.ERROR_GIFT_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_GIFT_NOT_FOUND)
		return
	}

	globalResponse.ResponseSuccess(w, map[string]interface{}{
		"sharedName": sharedUser.Name,
	})
}

func validateShareGift(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["userId"] == nil || reqBody["userId"].(string) == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody["sharedUserId"] == nil || reqBody["sharedUserId"].(string) == "" {
		return &lib.ERROR_SHARED_USER_ID_REQUIRED
	}
	if reqBody["giftId"] == nil || reqBody["giftId"].(string) == "" {
		return &lib.ERROR_GIFT_ID_REQUIRED
	}
	return nil
}
