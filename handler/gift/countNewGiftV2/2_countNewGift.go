package countNewGiftV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"go.mongodb.org/mongo-driver/bson"
)

func countNewGifts(reqBody *model.ApiRequest) int {
	// count gift has promotion invalid
	aggregateQuery := []bson.M{
		{
			"$match": gift.GetQueryGetNewGift(reqBody.UserId),
		},
		{
			"$lookup": bson.M{
				"from":         globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE],
				"localField":   "promotionCode",
				"foreignField": "code",
				"as":           "promotionCodes",
			},
		},
		{
			"$match": bson.M{
				"promotionCodes":        bson.M{"$exists": true, "$ne": []interface{}{}},
				"promotionCodes.locked": bson.M{"$ne": true},
			},
		},
	}
	var result []interface{}
	globalDataAccess.Aggregate(globalCollection.COLLECTION_GIFT[local.ISO_CODE], aggregateQuery, &result)
	return len(result)
}
