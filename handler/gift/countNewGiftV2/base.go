package countNewGiftV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func CountNewGift(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	// Validate request params
	errCode := validateCountNewGift(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// Get list new gift of user
	totalNewGifts := countNewGifts(reqBody)

	// Get list marketing campaign type promotion
	totalMarketingCampaignForUser := countMarketingCampaignForUser(reqBody)

	return map[string]interface{}{
		"totalNewGifts": totalNewGifts + totalMarketingCampaignForUser,
	}, nil
}
