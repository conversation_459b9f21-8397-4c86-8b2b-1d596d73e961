package redeem

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/bReward"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/urBox"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Update Redeem Gift Transaction
 * @CreatedAt: 29/10/2020
 * @Author: linhnh
 * @UpdatedAt: 13/11/2020
 * @UpdatedBy: vinhnt
 */
func UpdateRedeemGiftTransaction(redeemGiftTransactionId string, data map[string]interface{}) {
	if redeemGiftTransactionId == "" {
		return
	}
	objUpdate := bson.M{
		"status":    data["status"],
		"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	// when redeem success
	if data["giftId"] != nil {
		objUpdate["giftId"] = data["giftId"]
	}
	if data["pointTransactionId"] != nil {
		objUpdate["pointTransactionId"] = data["pointTransactionId"]
	}
	// when redeem success or error
	if data["data"] != nil {
		objUpdate["data"] = data["data"]
	}
	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], redeemGiftTransactionId, bson.M{"$set": objUpdate})
	// Post to Slack if has error
	if data["status"] != nil && data["status"].(string) == globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR && data["data"] != nil && data["data"].(string) != lib.ERROR_USER_NOT_ENOUGH_POINTS.ErrorCode {
		channel := "asker-error-tracking"
		message := fmt.Sprintf("Khách hàng %s đổi thưởng không thành công. Lỗi: %s", data["userId"].(string), data["data"].(string))
		globalLib.PostToSlack(cfg.SlackToken, channel, globalConstant.SLACK_USER_NAME, message)
	}
}

/*
 * @Description: Redeem Gift From System
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func RedeemGiftFromSystem(userId string, incentive *modelIncentive.Incentive) (map[string]interface{}, error) {
	if incentive.Promotion == nil || incentive.Promotion.Type == "" || incentive.Promotion.Value == 0 {
		return nil, errors.New("PROMOTION_MISSING_VALUE")
	}
	// Generate promotion code
	code, err := modelPromotionCode.GeneratePromotionCode(local.ISO_CODE, "", 7)
	if err != nil {
		return nil, err
	}
	// Create promotion
	objPromotion := &modelPromotionCode.PromotionCode{
		XId:             globalLib.GenerateObjectId(),
		Code:            code,
		TypeOfPromotion: globalConstant.PROMOTION_APPLY_BOTH,
		Value: &modelPromotionCode.PromotionCodeValue{
			Type:  incentive.Promotion.Type,
			Value: incentive.Promotion.Value,
		},
		Target:         globalConstant.USER_TYPE_ASKER,
		Limit:          1,
		UserIds:        []string{userId},
		IsoCode:        incentive.IsoCode,
		PaymentMethods: incentive.PaymentMethods,
	}
	if incentive.Title != nil && incentive.Title.En != "" {
		objPromotion.Description = incentive.Title.En
	}
	if incentive.Promotion.Target != "" {
		objPromotion.Target = incentive.Promotion.Target
	}
	if incentive.Promotion.MaxValue > 0 {
		objPromotion.Value.MaxValue = incentive.Promotion.MaxValue
	}
	if incentive.ApplyFor != nil {
		if len(incentive.ApplyFor.Service) > 0 {
			objPromotion.ServiceId = incentive.ApplyFor.Service[0]
		}
		if len(incentive.ApplyFor.City) > 0 {
			objPromotion.TaskPlace = &modelPromotionCode.PromotionCodeTaskPlace{
				City: incentive.ApplyFor.City,
			}
		}
		if incentive.ApplyFor.IsAllUsers || incentive.ApplyFor.IsSharePublic {
			objPromotion.UserIds = []string{}
		}
	}
	b, _ := json.Marshal(objPromotion)
	mapPromotionCode := make(map[string]interface{})
	json.Unmarshal(b, &mapPromotionCode)
	now := globalLib.GetCurrentTime(local.TimeZone)
	mapPromotionCode["startDate"] = now
	mapPromotionCode["createdAt"] = now
	if incentive.Promotion.ExpiredDate != nil {
		mapPromotionCode["endDate"] = globalLib.ParseDateFromTimeStamp(incentive.Promotion.ExpiredDate, local.TimeZone)
	}
	if incentive.Promotion.NumberOfDayDueDate > 0 {
		date := now.AddDate(0, 0, int(incentive.Promotion.NumberOfDayDueDate))
		mapPromotionCode["endDate"] = date
	}
	mapPromotionCode["source"] = globalConstant.PROMOTION_CODE_SOURCE_BREWARDS
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], mapPromotionCode)
	if err != nil {
		return nil, errors.New("CREATE_PROMOTION_FAIL")
	}
	return mapPromotionCode, nil
}

/*
 * @Description: Redeem Gift From System With Partner
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 29/10/2020
 * @UpdatedBy: linhnh
 */
func RedeemGiftFromSystemWithPartner(user *modelUser.Users, incentive *modelIncentive.Incentive, transactionId string) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	var err error
	if incentive.Partner == "" && incentive.CodeList != nil && len(incentive.CodeList) > 0 {
		result, err = getCodeFromPartnerBtaskee(incentive)
	} else if incentive.Partner == "" && incentive.CodeFromPartner != nil {
		result, err = getCodeFromPartnerBtaskeeOneCode(incentive)
	} else if incentive.Partner == "UR_BOX" { // with urBox
		result, err = getCodeFromUrBox(user, incentive, transactionId)
	} else if incentive.Type == globalConstant.INCENTIVE_TYPE_LIXI_TASKER {
		result, err = generateGiftLixiTasker(incentive)
	}
	return result, err
}

func generateGiftLixiTasker(incentive *modelIncentive.Incentive) (map[string]interface{}, error) {
	promotioCode, err := modelPromotionCode.GeneratePromotionCode(local.ISO_CODE, "", 7)
	if err != nil {
		return nil, err
	}

	// Generate promotion code, set default endDate is incentive.EndDate
	endDate := globalLib.ParseDateFromTimeStamp(incentive.EndDate, local.TimeZone)
	if incentive.GetPromotion().GetExpiredDate() != nil {
		endDate = globalLib.ParseDateFromTimeStamp(incentive.Promotion.ExpiredDate, local.TimeZone)
	}
	if incentive.GetPromotion().GetNumberOfDayDueDate() > 0 {
		endDate = globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, int(incentive.Promotion.NumberOfDayDueDate))
	}
	return map[string]interface{}{
		"code":    promotioCode,
		"endDate": endDate,
	}, nil
}

/*
 * @Description: Get Error Code Gift
 * @CreatedAt: 29/10/2020
 * @Author: linhnh
 * @UpdatedAt:
 * @UpdatedBy:
 */
func GetErrorCodeGift(errStr string) *globalResponse.ResponseErrorCode {
	errCode := &lib.SYSTEM_ERROR
	var findErrorCode *model.UrBoxErrorCode
	for _, v := range lib.ErrorCodeListUrBox {
		if errStr == v.Message {
			findErrorCode = v
			break
		}
	}
	if findErrorCode != nil {
		errCode.ErrorCode = findErrorCode.Code
		errCode.Message = errStr
	}
	return errCode
}

/*
 * @Description: Add Gift Collection
 * @CreatedAt: 29/10/2020
 * @Author: linhnh
 * @UpdatedAt: 12/11/2020
 * @UpdatedBy: vinhnt
 */
func AddGiftCollection(pointTransactionId string, userId string, respondRedeemGift map[string]interface{}, incentive *modelIncentive.Incentive, giftFrom string) string {
	data := &modelGift.Gift{
		XId:                globalLib.GenerateObjectId(),
		PointTransactionId: pointTransactionId,
		UserId:             userId,
		Source:             incentive.From,
		Title:              incentive.Title,
		Content:            incentive.Content,
		Note:               incentive.Note,
		Office:             incentive.Office,
		Image:              incentive.Image,
		BrandInfo:          incentive.BrandInfo,
		IncentiveId:        incentive.XId,
		IsoCode:            incentive.IsoCode,
		Social:             incentive.Social,
		CreatedAt:          globalLib.GetCurrentTimestamp(local.TimeZone),
		IncentiveType:      incentive.Type,
	}

	// This is for promotion lixi tet
	if incentive.Type == globalConstant.INCENTIVE_TYPE_LIXI_TASKER {
		data.ShareGiftInfo = &modelGift.GiftShareGiftInfo{
			Type:  incentive.Promotion.Type,
			Value: incentive.Promotion.Value,
		}
	}

	if respondRedeemGift["code"] != nil {
		data.PromotionCode = respondRedeemGift["code"].(string)
	}
	if incentive.Partner != "" {
		data.Partner = incentive.Partner
	}
	if incentive.ApplyFor != nil {
		data.ApplyFor = &modelGift.GiftApplyFor{
			City:          incentive.ApplyFor.City,
			Service:       incentive.ApplyFor.Service,
			IsAllUsers:    incentive.ApplyFor.IsAllUsers,
			IsSharePublic: incentive.ApplyFor.IsSharePublic,
		}
	}
	if giftFrom != "" {
		giftFromData := &modelGift.GiftFrom{
			From: giftFrom,
		}
		if respondRedeemGift["bundleVoucherTransactionId"] != nil {
			giftFromData.BundleVoucherTransactionId = cast.ToString(respondRedeemGift["bundleVoucherTransactionId"])
			delete(respondRedeemGift, "bundleVoucherTransactionId")
		}
		data.From = giftFromData
	}
	b, _ := json.Marshal(data)
	mapData := make(map[string]interface{})
	json.Unmarshal(b, &mapData)

	if incentive.Partner == "UR_BOX" {
		b, _ = json.Marshal(respondRedeemGift["data"])
		respondRedeemGiftData := make(map[string]interface{})
		json.Unmarshal(b, &respondRedeemGiftData)
		if respondRedeemGiftData["cart"] != nil {
			mapData["cart"] = respondRedeemGiftData["cart"]
			b, _ = json.Marshal(respondRedeemGiftData["cart"])
			respondRedeemGiftDataCart := make(map[string]interface{})
			json.Unmarshal(b, &respondRedeemGiftDataCart)
			if respondRedeemGiftDataCart["code_link_gift"] != nil {
				b, _ = json.Marshal(respondRedeemGiftDataCart["code_link_gift"])
				var arrayCodeLinkGift []map[string]interface{}
				json.Unmarshal(b, &arrayCodeLinkGift)
				if len(arrayCodeLinkGift) > 0 {
					if arrayCodeLinkGift[0]["expired"] != nil {
						// get expired time if is infinity
						if arrayCodeLinkGift[0]["expired"].(string) == "Vô hạn" {
							mapData["expiredIsInfinite"] = true
						} else { // get expired time
							t, err := time.Parse(globalConstant.LAYOUT_DATE, arrayCodeLinkGift[0]["expired"].(string))
							// t, _ := time.ParseInLocation(time.RFC3339, arrayCodeLinkGift[0]["expired"].(string), local.TimeZone)
							if err == nil {
								mapData["expired"] = t
							}
						}
					}
					if arrayCodeLinkGift[0]["link"] != nil {
						mapData["linkGift"] = arrayCodeLinkGift[0]["link"]
					}
					if arrayCodeLinkGift[0]["code"] != nil {
						mapData["promotionCode"] = arrayCodeLinkGift[0]["code"]
					}
				}
			}
		}
		// auto +1 yearn if expired not exist
		if mapData["expired"] == nil {
			t := globalLib.GetCurrentTime(local.TimeZone).AddDate(1, 0, 0)
			mapData["expired"] = t
		}
	}
	// from bTaskee
	if incentive.From == "SYSTEM" || incentive.From == "SYSTEM_WITH_PARTNER" {
		if respondRedeemGift["_id"] != nil {
			mapData["promotionId"] = respondRedeemGift["_id"]
		}
		if respondRedeemGift["endDate"] != nil {
			mapData["expired"] = respondRedeemGift["endDate"]
		}
	}
	if incentive.RedeemLink != "" {
		mapData["redeemLink"] = incentive.RedeemLink
	}
	mapData["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
	mapData["_id"] = data.XId
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_GIFT[local.ISO_CODE], mapData)
	if err != nil {
		return ""
	}
	return data.XId
}

// ================================================================================

/*
 * @Description: Get Code From Partner Btaskee
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 12/11/2020
 * @UpdatedBy: vinhnt
 */
func getCodeFromPartnerBtaskee(incentive *modelIncentive.Incentive) (map[string]interface{}, error) {
	codeList := []*modelIncentive.IncentiveCodeList{}
	var listCode []string
	if incentive.CodeList != nil && len(incentive.CodeList) > 0 {
		for _, v := range incentive.CodeList {
			var isExist bool
			for _, c := range codeList {
				if c.Code == v.Code {
					isExist = true
					break
				}
			}
			if !isExist {
				codeList = append(codeList, v)
				listCode = append(listCode, v.Code)
			}
		}
	}
	promotionCodes, _ := globalDataAccess.GetDistinctByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], "promotionCode", bson.M{"promotionCode": bson.M{"$in": listCode}})
	b, _ := json.Marshal(promotionCodes)
	var listCodeUsed []string
	json.Unmarshal(b, &listCodeUsed)

	var codeData *modelIncentive.IncentiveCodeList
	var countCodeLeft int
	if len(codeList) > 0 {
		for _, v := range codeList {
			if !v.IsUsed && globalLib.FindStringInSlice(listCodeUsed, v.Code) == -1 {
				codeData = v
				countCodeLeft++
			}
		}
	}

	if codeData == nil {
		deactivateIncentive(incentive, "The incentive has run out of codes - INACTIVE BY SYSTEM")
		message := fmt.Sprintf("Ưu đãi %s đã hết mã. Hãy thêm mã mới.", incentive.Title.Vi)
		slackChannel := "rewards-error-tracking"
		globalLib.PostToSlack(cfg.SlackToken, slackChannel, "bTaskee System", message)
		return nil, errors.New("CODE_IS_USED_UP")
	}
	// send slack if code list less than 10 codes
	if countCodeLeft <= 10 {
		message := fmt.Sprintf("Ưu đãi %s chỉ còn %d mã. Liên hệ với đối tác để gia hạn thêm.", incentive.Title.Vi, countCodeLeft)
		slackChannel := "rewards-error-tracking"
		globalLib.PostToSlack(cfg.SlackToken, slackChannel, "bTaskee System", message)
	}
	// update status code is used
	_, err := globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		bson.M{"_id": incentive.XId, "codeList.code": codeData.Code},
		bson.M{"$set": bson.M{
			"codeList.$.isUsed":    true,
			"codeList.$.updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}})

	if err != nil {
		return nil, errors.New("UPDATE_CODE_ERROR")
	}
	result := map[string]interface{}{
		"code": codeData.Code,
	}
	if incentive.Promotion.ExpiredDate != nil {
		result["endDate"] = globalLib.ParseDateFromTimeStamp(incentive.Promotion.ExpiredDate, local.TimeZone)
	}
	if incentive.Promotion.NumberOfDayDueDate > 0 {
		result["endDate"] = globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, int(incentive.Promotion.NumberOfDayDueDate))
	}
	return result, nil
}

/*
 * @Description: Get Code From UrBox
 * @CreatedAt: 28/10/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func getCodeFromUrBox(user *modelUser.Users, incentive *modelIncentive.Incentive, transactionId string) (map[string]interface{}, error) {
	options := map[string]interface{}{
		"app_secret":     cfg.UrBoxConfig.AppSecret,
		"app_id":         cfg.UrBoxConfig.AppId,
		"transaction_id": transactionId,
		"ttphone":        user.GetPhone(),
		"campaign_code":  incentive.GetGiftInfo().GetCampaignCode(),
		"site_user_id":   user.GetXId(),
		"isSendSms":      0,
		"dataBuy": []map[string]interface{}{
			{
				"priceId":  incentive.GiftInfo.Id,
				"quantity": 1,
			},
		},
	}
	//receiver email
	if user != nil && user.Emails != nil && len(user.Emails) > 0 && globalLib.IsEmailValid(user.Emails[0].Address) {
		options["ttemail"] = user.Emails[0].Address
	}
	//redeem
	reqBody, err := json.Marshal(options)
	if err != nil {
		local.Logger.Warn(err.Error(),
			zap.Error(err),
			zap.String("userId", user.XId),
			zap.Any("incentiveId", incentive.XId),
		)
		callUrBoxError(user, incentive, err.Error(), transactionId)
		return nil, err
	}
	req, err := http.NewRequest("POST", cfg.UrBoxConfig.CartPayVoucherUrl, bytes.NewBuffer(reqBody))
	if err != nil {
		local.Logger.Warn(err.Error(),
			zap.Error(err),
			zap.String("userId", user.XId),
			zap.Any("incentiveId", incentive.XId),
		)
		callUrBoxError(user, incentive, err.Error(), transactionId)
		return nil, err
	}

	signature, err := urBox.GenerateUrBoxSignature(options, []byte(cfg.UrBoxConfig.BTaskeePrivateKey))
	if err != nil {
		local.Logger.Warn(err.Error(),
			zap.Error(err),
			zap.String("userId", user.XId),
			zap.Any("incentiveId", incentive.XId),
		)
		callUrBoxError(user, incentive, err.Error(), transactionId)
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Signature", signature)

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		local.Logger.Warn(err.Error(),
			zap.Error(err),
			zap.String("userId", user.XId),
			zap.Any("incentiveId", incentive.XId),
		)
		callUrBoxError(user, incentive, err.Error(), transactionId)
		return nil, err
	}
	dec := json.NewDecoder(resp.Body)
	var result map[string]interface{}
	dec.Decode(&result)
	b, _ := json.Marshal(result)
	if result == nil || result["msg"] == nil || result["msg"].(string) != "success" {
		errStr := string(b)
		local.Logger.Warn("CODE_FROM_URBOX_ERROR",
			zap.String("userId", user.XId),
			zap.Any("incentiveId", incentive.XId),
			zap.String("message", errStr),
		)
		callUrBoxError(user, incentive, errStr, transactionId)
		return nil, errors.New(errStr)
	}
	// redeem success, check paid yet
	if result != nil && result["pay"] != nil && result["pay"].(int) != 2 {
		errData := map[string]interface{}{
			"reason":  "UNPAID",
			"message": "Unpaid, check money in UrBox account",
			"result":  result,
		}
		b, _ = json.Marshal(errData)
		errStr := string(b)
		local.Logger.Warn("CODE_FROM_URBOX_ERROR",
			zap.String("userId", user.XId),
			zap.Any("incentiveId", incentive.XId),
			zap.String("message", errStr),
		)
		callUrBoxError(user, incentive, errStr, transactionId)
		return nil, errors.New(errStr)
	}
	return result, nil
}

func callUrBoxError(user *modelUser.Users, incentive *modelIncentive.Incentive, err, transactionId string) {
	errItem := map[string]interface{}{
		"reason":    err,
		"createdAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	globalDataAccess.UpdateOneById(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		incentive.XId,
		bson.M{
			"$set": bson.M{
				"status":    globalConstant.INCENTIVE_STATUS_INACTIVE,
				"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
			},
			"$push": bson.M{
				"history.reasonInactive": bson.M{"$each": []map[string]interface{}{errItem}, "$slice": -5},
			},
		},
	)
	message := fmt.Sprintf("Khách hàng %s - %s đổi thưởng %s - %s qua UrBox không thành công.\nMã giao dịch: %s \nƯu đãi tạm thời sẽ INACTIVE. \nLỗi: %s", user.XId, user.Phone, incentive.Title.Vi, incentive.XId, transactionId, err)
	globalLib.PostToSlack(cfg.SlackToken, "rewards-error-tracking", "bTaskee System", message)
}

func getCodeFromPartnerBtaskeeOneCode(incentive *modelIncentive.Incentive) (map[string]interface{}, error) {
	codeLimit := incentive.CodeFromPartner.Limit
	codeNumberOfUsed := incentive.CodeFromPartner.NumberOfUsed

	if codeLimit != 0 && codeNumberOfUsed+1 > codeLimit {
		deactivateIncentive(incentive, "The incentive has run out of codes - INACTIVE BY SYSTEM")
		message := fmt.Sprintf("Ưu đãi %s đã hết mã. Hãy thêm mã mới.", incentive.Title.Vi)
		slackChannel := "rewards-error-tracking"
		globalLib.PostToSlack(cfg.SlackToken, slackChannel, "bTaskee System", message)
		return nil, errors.New("CODE_IS_USED_UP")
	}

	// send slack if code less than 10 number of use
	countCodeLeft := codeLimit - codeNumberOfUsed
	if countCodeLeft <= 10 {
		message := fmt.Sprintf("Ưu đãi %s chỉ còn %d mã. Liên hệ với đối tác để gia hạn thêm.", incentive.Title.Vi, countCodeLeft)
		slackChannel := "rewards-error-tracking"
		globalLib.PostToSlack(cfg.SlackToken, slackChannel, "bTaskee System", message)
	}

	// update status code is used
	_, err := globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		bson.M{"_id": incentive.XId},
		bson.M{"$inc": bson.M{"codeFromPartner.numberOfUsed": 1}},
	)
	if err != nil {
		return nil, errors.New("UPDATE_CODE_ERROR")
	}
	// return result
	result := map[string]interface{}{
		"code": incentive.CodeFromPartner.Code,
	}
	if incentive.Promotion.ExpiredDate != nil {
		result["endDate"] = globalLib.ParseDateFromTimeStamp(incentive.Promotion.ExpiredDate, local.TimeZone)
	}
	if incentive.Promotion.NumberOfDayDueDate > 0 {
		result["endDate"] = globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, int(incentive.Promotion.NumberOfDayDueDate))
	}
	return result, nil
}

func GetPointFlashSale(user *modelUser.Users, incentiveId string) (point float64) {
	isTester := lib.CheckIsUserTester(user)
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate":          bson.M{"$lte": currentTime},
		"endDate":            bson.M{"$gte": currentTime},
		"status":             globalConstant.FLASH_SALE_STATUS_ACTIVE,
		"isTesting":          bson.M{"$ne": true},
		"incentiveInfos._id": incentiveId,
	}
	if isTester {
		delete(query, "isTesting")
	}
	flashSale := bReward.GetDataFlashSale(query, bson.M{"_id": 1, "incentiveInfos": 1})
	if flashSale == nil {
		return
	}
	for _, v := range flashSale.IncentiveInfos {
		if incentiveId == v.XId {
			point = v.Point
		}
	}
	return
}

func deactivateIncentive(incentive *modelIncentive.Incentive, reason string) {
	errItem := map[string]interface{}{
		"reason":    reason,
		"createdAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	globalDataAccess.UpdateOneById(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		incentive.XId,
		bson.M{
			"$set": bson.M{
				"status":    globalConstant.INCENTIVE_STATUS_INACTIVE,
				"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
			},
			"$push": bson.M{
				"history.reasonInactive": bson.M{"$each": []map[string]interface{}{errItem}, "$slice": -5},
			},
		},
	)
}
