package redeemGiftVN

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/redeem"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelRedeemGiftTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func RedeemGiftVN(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	// Get user
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "name": 1, "phone": 1, "emails": 1, "point": 1, "language": 1, "rankInfo": 1, "isoCode": 1, "bPoint": 1, "rankInfoByCountry": 1})
	if user == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}

	// check Incentive exist
	var incentive *modelIncentive.Incentive
	globalDataAccess.GetOneById(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], reqBody.GiftId, bson.M{"createdAt": 0, "categoryName": 0, "history": 0}, &incentive)
	if incentive == nil {
		return nil, &lib.ERROR_INCENTIVE_NOT_FOUND
	}
	askerRank := lib.GetRankAsker(user)
	if incentive.RankRequire > globalConstant.MAP_RANK_REQUIRE[askerRank] {
		return nil, &lib.ERROR_YOUR_RANK_CAN_NOT_REDEEM_GIFT
	}

	// check redeem gif one time
	if incentive.IsRedeemOneTime {
		isRedeem, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], bson.M{"userId": reqBody.UserId, "incentiveInfo.incentiveId": incentive.XId})
		if isRedeem {
			return nil, &lib.ERROR_INCENTIVE_JUST_REDEEM_ONE_TIME
		}
	}

	// create RedeemGiftTransaction
	giftTransactionData := &modelRedeemGiftTransaction.RedeemGiftTransaction{
		XId:    globalLib.GenerateObjectId(),
		UserId: reqBody.UserId,
		Status: globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_NEW,
		IncentiveInfo: &modelRedeemGiftTransaction.RedeemGiftTransactionIncentiveInfo{
			IncentiveId: incentive.XId,
			Title:       incentive.Title,
			Exchange:    incentive.Exchange,
			Partner:     incentive.Partner,
			From:        incentive.From,
		},
		IsoCode: local.ISO_CODE,
	}
	b, _ := json.Marshal(giftTransactionData)
	mapData := make(map[string]interface{})
	json.Unmarshal(b, &mapData)
	mapData["dataFromClient"] = reqBody.Data
	mapData["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_REDEEM_GIFT_TRANSACTION[local.ISO_CODE], mapData)
	if err != nil {
		return nil, &lib.ERROR_CREATE_REDEEM_GIFT_TRANSACTION_FAILED
	}
	// check point
	var point float64
	if incentive.Exchange != nil && incentive.Exchange.Point > 0 {
		point = incentive.Exchange.Point
	}
	if v := redeem.GetPointFlashSale(user, incentive.XId); v > 0 {
		point = v
	}
	if reqBody.Point > 0 && reqBody.Point < point {
		return nil, &lib.ERROR_OFFER_HAS_ENDED
	}
	if point < 0 {
		redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
			"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
			"data":   lib.ERROR_POINT_GREATER_THAN_ZERO.ErrorCode,
			"userId": reqBody.UserId,
		})
		return nil, &lib.ERROR_POINT_GREATER_THAN_ZERO
	}
	// check point in user with point
	var currentPoint float64
	if user.Point > 0 {
		currentPoint = user.Point
	}
	if currentPoint < point {
		redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
			"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
			"data":   lib.ERROR_USER_NOT_ENOUGH_POINTS.ErrorCode,
			"userId": reqBody.UserId,
		})
		return nil, &lib.ERROR_USER_NOT_ENOUGH_POINTS
	}
	// check ACTIVE yet
	if incentive.Status != globalConstant.INCENTIVE_STATUS_ACTIVE {
		// skip is tester
		var setting *modelSettings.Settings
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &setting)
		if setting != nil && len(setting.Tester) > 0 && globalLib.FindStringInSlice(setting.Tester, user.Phone) == -1 {
			redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
				"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
				"data":   lib.ERROR_INCENTIVE_HAS_STOPPED.ErrorCode,
				"userId": reqBody.UserId,
			})
			return nil, &lib.ERROR_INCENTIVE_HAS_STOPPED
		}
	}
	// check expired
	if incentive.StartDate != nil && incentive.EndDate != nil {
		startDate := globalLib.ParseDateFromTimeStamp(incentive.StartDate, local.TimeZone)
		endDate := globalLib.ParseDateFromTimeStamp(incentive.EndDate, local.TimeZone)
		now := globalLib.GetCurrentTime(local.TimeZone)
		if now.Before(startDate) || now.After(endDate) {
			redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
				"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
				"data":   lib.ERROR_INCENTIVE_EXPIRED.ErrorCode,
				"userId": reqBody.UserId,
			})
			return nil, &lib.ERROR_INCENTIVE_EXPIRED
		}
	}
	// redeem gift
	respondRedeemGift := make(map[string]interface{})
	if incentive.From == "SYSTEM" {
		respondRedeemGift, err = redeem.RedeemGiftFromSystem(user.XId, incentive)
	} else if incentive.From == "SYSTEM_WITH_PARTNER" {
		respondRedeemGift, err = redeem.RedeemGiftFromSystemWithPartner(user, incentive, giftTransactionData.XId)
	}
	if err != nil {
		errCode := redeem.GetErrorCodeGift(err.Error())
		redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
			"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
			"data":   err.Error(),
			"userId": reqBody.UserId,
		})
		return nil, errCode
	}
	// Redeem Success
	// --------- Add PointTransaction --------------------------------------------
	pointTransactionId := addPointTransaction(point, user, respondRedeemGift, incentive)
	if pointTransactionId == "" {
		redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
			"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
			"data":   lib.ERROR_CREATE_POINT_TRANSACTION_FAILED.ErrorCode,
			"userId": reqBody.UserId,
		})
		return nil, &lib.ERROR_CREATE_POINT_TRANSACTION_FAILED
	}
	// update point in user
	modelUser.UpdateOneById(local.ISO_CODE, user.XId, bson.M{"$inc": bson.M{"point": -point}})
	// ----------- Insert gift to gift collection ----------------------------
	giftId := redeem.AddGiftCollection(pointTransactionId, user.XId, respondRedeemGift, incentive, "")
	if giftId == "" {
		redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
			"status": globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_ERROR,
			"data":   lib.ERROR_CREATE_GIFT_FAILED.ErrorCode,
			"userId": reqBody.UserId,
		})
		return nil, &lib.ERROR_CREATE_GIFT_FAILED
	}
	// update redeemGiftTransaction
	b, _ = json.Marshal(respondRedeemGift)
	redeem.UpdateRedeemGiftTransaction(giftTransactionData.XId, map[string]interface{}{
		"status":             globalConstant.REDEEM_GIFT_TRANSACTION_STATUS_SUCCESS,
		"giftId":             giftId,
		"pointTransactionId": pointTransactionId,
		"data":               string(b),
	})

	result := map[string]interface{}{
		"data": giftId,
	}
	if respondRedeemGift != nil && respondRedeemGift["code"] != nil {
		result["code"] = respondRedeemGift["code"]
		result["source"] = incentive.From
		if incentive.ApplyFor != nil {
			result["service"] = incentive.ApplyFor.Service
		}
		if len(incentive.PaymentMethods) > 0 {
			result["paymentMethods"] = incentive.PaymentMethods
		}
	}
	return result, nil
}

// =======================================================

/*
 * @Description: Add Point Transaction
 * @CreatedAt: 29/10/2020
 * @Author: linhnh
 * @UpdatedAt: 12/11/2020
 * @UpdatedBy: vinhnt
 */
func addPointTransaction(point float64, user *modelUser.Users, respondRedeemGift map[string]interface{}, incentive *modelIncentive.Incentive) string {
	b, _ := json.Marshal(respondRedeemGift)
	var oldPointRank float64
	if user.RankInfo != nil && user.RankInfo.Point > 0 {
		oldPointRank = user.RankInfo.Point
	}
	data := &modelPointTransaction.PointTransaction{
		XId:          globalLib.GenerateObjectId(),
		UserId:       user.XId,
		Data:         string(b),
		Point:        point,
		OldPoint:     user.Point,
		OldPointRank: oldPointRank,
		Type:         "C",
		IsoCode:      local.ISO_CODE,
		CreatedAt:    globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	lang := globalConstant.LANG_EN
	if user.Language != "" {
		lang = user.Language
	}
	sourceServiceText := &modelService.ServiceText{
		Vi: localization.T(globalConstant.LANG_VI, "GIFT_FROM_UR_BOX"),
		En: localization.T(globalConstant.LANG_EN, "GIFT_FROM_UR_BOX"),
		Ko: localization.T(globalConstant.LANG_KO, "GIFT_FROM_UR_BOX"),
		Th: localization.T(globalConstant.LANG_TH, "GIFT_FROM_UR_BOX"),
		Id: localization.T(globalConstant.LANG_ID, "GIFT_FROM_UR_BOX"),
	}
	if incentive.From == "SYSTEM" {
		data.ReferenceId = incentive.XId
		data.Source = &modelPointTransaction.PointTransactionSource{
			Name:            "SYSTEM",
			Text:            localization.T(lang, "GIFT_FROM_SYSTEM"),
			IncentiveId:     incentive.XId,
			PromotionCodeId: respondRedeemGift["_id"].(string),
			ServiceText:     sourceServiceText,
		}
		data.ReferenceCode = respondRedeemGift["code"].(string)
	} else if incentive.From == "SYSTEM_WITH_PARTNER" {
		data.ReferenceId = incentive.XId
		data.Source = &modelPointTransaction.PointTransactionSource{
			Name:        "SYSTEM_WITH_PARTNER",
			Text:        localization.T(lang, "GIFT_FROM_SYSTEM"),
			IncentiveId: incentive.XId,
			ServiceText: sourceServiceText,
		}
		if respondRedeemGift["code"] != nil {
			data.Source.PromotionCode = respondRedeemGift["code"].(string)
			data.ReferenceCode = respondRedeemGift["code"].(string)
		}
		if incentive.Partner == "UR_BOX" {
			temp := make(map[string]interface{})
			if respondRedeemGift["cart"] != nil {
				b, _ = json.Marshal(respondRedeemGift["cart"])
				json.Unmarshal(b, &temp)
				if temp["money_total"] != nil {
					data.Price = temp["money_total"].(float64)
				}
				if temp["id"] != nil {
					data.ReferenceId = temp["id"].(string)
				}
				// if temp["code_link_gift"] != nil {
				// 	var arrayTemp []map[string]interface{}
				// 	b, _ = json.Marshal(temp["code_link_gift"])
				// 	json.Unmarshal(b, &arrayTemp)
				// 	if len(arrayTemp) > 0 && arrayTemp[0]["code"] != nil {
				// 		data.ReferenceCode = arrayTemp[0]["code"].(string)
				// 	}
				// }
			}
			data.Source = &modelPointTransaction.PointTransactionSource{
				Name:        "UR_BOX",
				Text:        localization.T(lang, "GIFT_FROM_UR_BOX"),
				ServiceText: localization.GetLocalizeObject("GIFT_FROM_UR_BOX"),
			}
		}
	}
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], data)
	if err != nil {
		return ""
	}
	return data.XId
}
