/*
 * @File: redeemGift.go
 * @Description: Handler function for redeem gift api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package gift

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift/redeem/redeemGiftVN"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

/*
 * @Description: Redeem Gift
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 18/11/2020
 * @UpdatedBy: vinhnt
 */
func RedeemGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateRedeemGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// var setting *modelSettings.Settings
	// globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"giftSetting.rankingCycle.isResetbPointRunning": 1}, &setting)
	// if setting != nil && setting.GiftSetting != nil && setting.GiftSetting.RankingCycle != nil && setting.GiftSetting.RankingCycle.IsResetbPointRunning {
	// 	globalResponse.ResponseError(w, lib.ERROR_REDEEM_GIFT_IS_PAUSE)
	// 	return
	// }

	result, errCode := redeemGiftVN.RedeemGiftVN(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateRedeemGift(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.GiftId == "" {
		return &lib.ERROR_GIFT_ID_REQUIRED
	}
	if reqBody.Data == nil {
		return &lib.ERROR_DATA_INVALID
	}
	return nil
}
