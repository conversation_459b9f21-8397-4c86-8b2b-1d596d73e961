/*
 * @File: getUserGift.go
 * @Description: Handler function for get user gift api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package gift

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Gift by UserId
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetUserGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetUserGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	page := 1
	limit := lib.PAGING_LIMIT
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}

	var gifts []*modelGift.Gift
	globalDataAccess.GetAllByQueryPagingSort(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"userId": reqBody.UserId,
		},
		bson.M{"title": 1, "expired": 1, "promotionCode": 1, "brandInfo": 1, "createdAt": 1, "incentiveId": 1, "used": 1, "applyFor": 1, "cart.code_link_gift.code_image": 1},
		int64(page),
		int64(limit),
		bson.M{"createdAt": -1},
		&gifts,
	)
	result := refactorGiftResult(gifts)
	globalResponse.ResponseSuccess(w, result)
}

func refactorGiftResult(gifts []*modelGift.Gift) []map[string]interface{} {
	result := []map[string]interface{}{}
	for _, v := range gifts {
		vData, _ := json.Marshal(v)
		giftMap := make(map[string]interface{})
		json.Unmarshal(vData, &giftMap)

		if v.Cart != nil && len(v.Cart.CodeLinkGift) > 0 {
			giftMap["barCode"] = v.Cart.CodeLinkGift[0].CodeImage
		}

		delete(giftMap, "cart")
		result = append(result, giftMap)
	}
	return result
}

/*
 * @Description: validate get user gift
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetUserGift(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
