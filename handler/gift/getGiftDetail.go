/*
 * @File: getGiftDetail.go
 * @Description: Handler function for get gift detail api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package gift

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Gift Detail
 * @CreatedAt: 04/12/2020
 * @Author: ngoctb3
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetGiftDetail(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody *modelGift.Gift
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Validate request params
	errCode := validateGetGiftDetail(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get gift detail
	var gift *modelGift.Gift
	globalDataAccess.GetOneById(globalCollection.COLLECTION_GIFT[local.ISO_CODE], reqBody.XId, bson.M{}, &gift)
	if gift == nil {
		local.Logger.Warn(lib.ERROR_GIFT_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_GIFT_NOT_FOUND)
		return
	}

	query := bson.M{"code": gift.PromotionCode}
	if gift.PromotionId != "" {
		query = bson.M{"_id": gift.PromotionId}
	}
	var promotionCode *modelPromotionCode.PromotionCode
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], query, bson.M{"paymentMethods": 1}, &promotionCode)

	result := &model.GiftDetailWithPaymentMethod{
		Gift:           gift,
		PaymentMethods: promotionCode.GetPaymentMethods(),
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Get Gift Detail
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetGiftDetail(reqBody *modelGift.Gift) *globalResponse.ResponseErrorCode {
	if reqBody.XId == "" {
		return &lib.ERROR_GIFT_ID_REQUIRED
	}
	if reqBody.IsoCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.IsoCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
