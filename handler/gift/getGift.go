/*
 * @File: getGift.go
 * @Description: Handler function for get gift api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package gift

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Gift by UserId and ServiceId
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 11/11/2020
 * @UpdatedBy: vinhnt
 */
func GetGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Check input
	errCode = validateGetGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var result []*modelGift.Gift
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"userId": reqBody.UserId,
			"source": globalConstant.GIFT_SOURCE_SYSTEM,
			"$and": []bson.M{
				{
					"$or": []bson.M{
						{"applyFor.service": reqBody.ServiceId},
						{
							"$or": []bson.M{
								{"applyFor.service": bson.M{"$exists": false}},
								{"applyFor.service": []string{}},
							},
						},
					},
				},
				{
					"$or": []bson.M{
						{"used": bson.M{"$exists": false}},
						{"used": false},
					},
				},
			},
			"expired": bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
		},
		bson.M{"title": 1, "expired": 1, "promotionCode": 1, "brandInfo": 1, "createdAt": 1, "applyFor": 1, "social": 1},
		bson.M{"createdAt": -1},
		&result,
	)

	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate get gift api params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 */
func validateGetGift(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ServiceId == "" {
		return &lib.ERROR_SERVICE_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
