package getGiftV2

import (
	"encoding/json"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	"go.mongodb.org/mongo-driver/bson"
)

func getGifts(reqBody *model.ApiRequest) ([]map[string]interface{}, map[string]*modelPromotionCode.PromotionCode) {
	var gifts []*modelGift.Gift
	// Get gifts
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		bson.M{
			"userId": reqBody.UserId,
			"$or": []bson.M{
				{"used": bson.M{"$exists": false}},
				{"used": false},
			},
			"expired": bson.M{"$gte": globalLib.GetCurrentTime(local.TimeZone)},
			"source":  globalConstant.GIFT_SOURCE_SYSTEM,
		},
		bson.M{"title": 1, "expired": 1, "promotionCode": 1, "brandInfo": 1, "createdAt": 1, "applyFor": 1, "social": 1, "promotionId": 1, "from": 1},
		bson.M{"createdAt": -1},
		&gifts,
	)

	giftsMap := refactorGiftResult(gifts)

	// Get promotions
	promotionCodes := []string{}
	for _, gift := range giftsMap {
		if promotionCode := cast.ToString(gift["promotionCode"]); promotionCode != "" {
			promotionCodes = append(promotionCodes, promotionCode)
		}
	}
	query := bson.M{
		"code":   bson.M{"$in": promotionCodes},
		"locked": bson.M{"$ne": true},
	}
	taskPlace := reqBody.TaskPlace
	if taskPlace != nil {
		queryOr := []bson.M{}
		if taskPlace.City != "" {
			queryOr = append(queryOr, bson.M{
				"$or": []bson.M{
					{"taskPlace.city.0": bson.M{"$exists": false}},
					{"taskPlace.city": taskPlace.City},
				},
			})
		}
		if taskPlace.District != "" {
			queryOr = append(queryOr, bson.M{
				"$or": []bson.M{
					{"taskPlace.district.0": bson.M{"$exists": false}},
					{"taskPlace.district": taskPlace.District},
				},
			})
		}
		if len(queryOr) > 0 {
			query["$and"] = queryOr
		}
	}
	promotions, _ := handler.GetPromotions(
		query,
		bson.M{"_id": 1, "code": 1, "minOrderValue": 1, "taskStartDate": 1, "taskEndDate": 1, "serviceId": 1, "value": 1, "paymentMethods": 1, "hourRanges": 1},
	)
	mapPromotionCodeByCode := make(map[string]*modelPromotionCode.PromotionCode)
	for _, v := range promotions {
		mapPromotionCodeByCode[v.Code] = v
	}

	return giftsMap, mapPromotionCodeByCode
}

func refactorGiftResult(gifts []*modelGift.Gift) []map[string]interface{} {
	result := []map[string]interface{}{}
	// the gift has same userVoucherId will have same information
	quantityGiftByVoucherId := map[string]int{}
	for _, gift := range gifts {
		if gift.From != nil && gift.From.UserVoucherId != "" {
			quantityGiftByVoucherId[gift.From.UserVoucherId]++
		}
	}
	isVoucherReturn := map[string]bool{}

	for _, v := range gifts {
		// if gift info is returned with same userVoucherId, then continue
		if v.From != nil && v.From.UserVoucherId != "" && isVoucherReturn[v.From.UserVoucherId] {
			continue
		}

		quantity := 1
		if v.From != nil && v.From.UserVoucherId != "" && quantityGiftByVoucherId[v.From.UserVoucherId] > 0 {
			quantity = quantityGiftByVoucherId[v.From.UserVoucherId]
			isVoucherReturn[v.From.UserVoucherId] = true
		}

		vData, _ := json.Marshal(v)
		giftMap := make(map[string]interface{})
		json.Unmarshal(vData, &giftMap)

		giftMap["quantity"] = quantity

		giftMap["type"] = "GIFT"
		result = append(result, giftMap)
	}
	return result
}
