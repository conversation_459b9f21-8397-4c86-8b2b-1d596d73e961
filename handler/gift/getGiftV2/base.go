/*
 * @File: getGift.go
 * @Description: Handler function for get gift api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package getGiftV2

import (
	"sync"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Get List Gift by UserId and ServiceId
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 11/11/2020
 * @UpdatedBy: vinhnt
 */
func GetGift(reqBody *model.ApiRequest) ([]map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// Check input
	errCode := validateGetGift(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// get gift
	wg := &sync.WaitGroup{}

	wg.Add(1)
	var gifts []map[string]interface{}
	var mapPromotionCodeByCodeOfGift map[string]*modelPromotionCode.PromotionCode
	go func() {
		defer wg.Done()
		gifts, mapPromotionCodeByCodeOfGift = getGifts(reqBody)
		gifts = refactorGiftData(reqBody, gifts, mapPromotionCodeByCodeOfGift, "GIFT")
	}()

	wg.Add(1)
	var marketingCampaigns []map[string]interface{}
	var mapPromotionCodeByCodeOfMarketingCampaign map[string]*modelPromotionCode.PromotionCode
	go func() {
		defer wg.Done()
		marketingCampaigns, mapPromotionCodeByCodeOfMarketingCampaign = getMarketingCampaignForBookTask(reqBody)
		marketingCampaigns = refactorGiftData(reqBody, marketingCampaigns, mapPromotionCodeByCodeOfMarketingCampaign, "MARKETING_CAMPAIGN")
	}()

	wg.Wait()

	result := append(gifts, marketingCampaigns...)
	sortResult(result, mapPromotionCodeByCodeOfGift, mapPromotionCodeByCodeOfMarketingCampaign)
	return result, nil, nil
}

func refactorGiftData(reqBody *model.ApiRequest, result []map[string]interface{}, mapPromotionCodeByCode map[string]*modelPromotionCode.PromotionCode, giftType string) []map[string]interface{} {
	refactorResult := []map[string]interface{}{}
	for _, resultItem := range result {
		promotionCode := mapPromotionCodeByCode[cast.ToString(resultItem["promotionCode"])]
		if promotionCode == nil {
			continue
		}

		isDisabled, disableReason, disableSort := checkCondition(reqBody, promotionCode)
		if isDisabled {
			resultItem["isDisabled"] = true
			resultItem["disableReason"] = disableReason
			resultItem["disableSort"] = disableSort
		}

		resultItem["type"] = giftType
		refactorResult = append(refactorResult, resultItem)
	}
	return refactorResult
}

func checkCondition(reqBody *model.ApiRequest, promotionCode *modelPromotionCode.PromotionCode) (isDisable bool, disableReason *modelService.ServiceText, disableSort int) {
	// Check minOrderValue
	if promotionCode.MinOrderValue > 0 && reqBody.TaskCost < promotionCode.MinOrderValue {
		currency := globalConstant.CURRENCY_SIGN_VN.Sign
		return true, localization.GetLocalizeObject("PROMOTION_ERROR_MIN_ORDER_VALUE_NOT_REACHED", globalLib.FormatMoney(promotionCode.MinOrderValue)+currency), 1
	}
	// Check task date
	if promotionCode.TaskStartDate != nil {
		promotionCodeTaskStartDate := globalLib.ParseDateFromTimeStamp(promotionCode.TaskStartDate, local.TimeZone)
		if reqBody.TaskDate.Before(promotionCodeTaskStartDate) {
			return true, localization.GetLocalizeObject("PROMOTION_ERROR_NOT_APPLY_FOR_TASK_IN_THIS_TIME_FOR_STEP_4"), 2
		}
	}
	if promotionCode.TaskEndDate != nil {
		promotionCodeTaskEndDate := globalLib.ParseDateFromTimeStamp(promotionCode.TaskEndDate, local.TimeZone)
		if reqBody.TaskDate.After(promotionCodeTaskEndDate) {
			return true, localization.GetLocalizeObject("PROMOTION_ERROR_NOT_APPLY_FOR_TASK_IN_THIS_TIME_FOR_STEP_4"), 2
		}
	}
	// Check payment method
	if len(promotionCode.PaymentMethods) > 0 && reqBody.PaymentMethod != "" && globalLib.FindStringInSlice(promotionCode.PaymentMethods, reqBody.PaymentMethod) < 0 {
		return true, globalLib.GetPromotionCodeOnlyApplyPaymentMethodErrorText(promotionCode.PaymentMethods), 3
	}

	// Check service
	if promotionCode.ServiceId != "" && reqBody.ServiceId != promotionCode.ServiceId {
		errText := localization.GetLocalizeObject("PROMOTION_ERROR_NOT_APPLY_SERVICE", "")
		service := getService(promotionCode.ServiceId)
		if service != nil && service.Text != nil {
			errText = &modelService.ServiceText{
				Vi: localization.T(globalConstant.LANG_VI, "PROMOTION_ERROR_NOT_APPLY_SERVICE", service.Text.Vi),
				En: localization.T(globalConstant.LANG_EN, "PROMOTION_ERROR_NOT_APPLY_SERVICE", service.Text.En),
				Ko: localization.T(globalConstant.LANG_KO, "PROMOTION_ERROR_NOT_APPLY_SERVICE", service.Text.Ko),
				Th: localization.T(globalConstant.LANG_TH, "PROMOTION_ERROR_NOT_APPLY_SERVICE", service.Text.Th),
				Id: localization.T(globalConstant.LANG_ID, "PROMOTION_ERROR_NOT_APPLY_SERVICE", service.Text.Id),
			}
		}
		return true, errText, 4
	}
	//check hour ranges
	if len(promotionCode.HourRanges) > 0 && reqBody.TaskDate != nil && !isPromotionCodeInHourRanges(promotionCode, reqBody.TaskDate) {
		return true, localization.GetLocalizeObject("PROMOTION_NOT_VALID_AT_THIS_TIME"), 5
	}

	return false, nil, 0
}

func getService(serviceId string) (service *modelService.Service) {
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceId, bson.M{"text": 1}, &service)
	return
}

func isPromotionCodeInHourRanges(p *modelPromotionCode.PromotionCode, taskDate *time.Time) bool {
	for _, hourRange := range p.HourRanges {
		//The default value for hourRange.from is 00:00, and for hourRange.to, it is 23:59 if both are empty.
		from := "00:00"
		to := "23:59"

		if hourRange.From != "" {
			from = hourRange.From
		}

		if hourRange.To != "" {
			to = hourRange.To
		}

		taskTime := taskDate.Format(globalConstant.LAYOUT_HOUR_MINUTE)
		isInRange := taskTime > from && taskTime < to
		//if task time isn't in the range, check the next one
		if !isInRange {
			continue
		}

		//last step is check the weekDays, if weekdays is empty or the day of today contain in the weekdays array, then passed check
		if len(hourRange.WeekDays) == 0 || globalLib.FindInt32InSlice(hourRange.WeekDays, int32(taskDate.Weekday())) >= 0 {
			return true
		}
	}
	return false
}
