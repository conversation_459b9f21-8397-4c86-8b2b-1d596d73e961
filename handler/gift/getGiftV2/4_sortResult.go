package getGiftV2

import (
	"sort"

	"github.com/spf13/cast"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
)

func sortResult(result []map[string]interface{}, mapPromotionCodeByCodeOfGift, mapPromotionCodeByCodeOfMarketingCampaign map[string]*modelPromotionCode.PromotionCode) {
	sort.Slice(result, func(i, j int) (r bool) {
		iIsDisabled := isPromotionDisabled(result[i])
		jIsDisabled := isPromotionDisabled(result[j])
		// Sort isDisabled to the end
		if iIsDisabled != jIsDisabled {
			r = !iIsDisabled
			return
		}
		// Get promotion code value of i and j
		iCode := cast.ToString(result[i]["promotionCode"])
		iValue := getPromotionCodeValue(iCode, mapPromotionCodeByCodeOfGift, mapPromotionCodeByCodeOfMarketingCampaign)
		jCode := cast.ToString(result[j]["promotionCode"])
		jValue := getPromotionCodeValue(jCode, mapPromotionCodeByCodeOfGift, mapPromotionCodeByCodeOfMarketingCampaign)
		if iIsDisabled && jIsDisabled {
			// If both are disabled, sort by code
			iDisabledSort := cast.ToInt(result[i]["disableSort"])
			jDisabledSort := cast.ToInt(result[j]["disableSort"])
			r = iDisabledSort < jDisabledSort
			if iDisabledSort == jDisabledSort {
				r = iValue > jValue
			}
			return
		}
		// Sort by value desc
		r = iValue > jValue
		return
	})
}

func isPromotionDisabled(promotion map[string]interface{}) bool {
	return promotion["isDisabled"] != nil && cast.ToBool(promotion["isDisabled"])
}

func getPromotionCodeValue(code string, mapPromotionCodeByCodeOfGift, mapPromotionCodeByCodeOfMarketingCampaign map[string]*modelPromotionCode.PromotionCode) float64 {
	// Find code
	promotionCode := mapPromotionCodeByCodeOfGift[code]
	if promotionCode == nil {
		promotionCode = mapPromotionCodeByCodeOfMarketingCampaign[code]
	}
	// Return 0 if not found
	if promotionCode == nil || promotionCode.Value == nil {
		return 0
	}
	// Priority to use MaxValue
	if promotionCode.Value.MaxValue > 0 {
		return promotionCode.Value.MaxValue
	}
	// Use Value if MaxValue is not set
	return promotionCode.Value.Value
}
