package getGiftV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
)

func getMarketingCampaignForBookTask(reqBody *model.ApiRequest) ([]map[string]interface{}, map[string]*modelPromotionCode.PromotionCode) {
	return lib.GetAvailableMarketingCampaignForUser(reqBody, lib.FROM_GIFT)
}
