package countNewGift

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/gift"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
)

func countNewGifts(reqBody *model.ApiRequest) int64 {
	totalNewGifts, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_GIFT[local.ISO_CODE], gift.GetQueryGetNewGift(reqBody.UserId))
	return totalNewGifts
}
