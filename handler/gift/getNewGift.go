/*
 * @File: getNewGift.go
 * @Description: Handler function for get new gift api
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package gift

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get New Gift by UserId
 * @CreatedAt: 04/12/2020
 * @Author: ngoctb3
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetNewGift(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate request params
	errCode = validateGetNewGift(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get list new gift of user
	gifts := []*modelGift.Gift{}
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_GIFT[local.ISO_CODE],
		GetQueryGetNewGift(reqBody.UserId),
		bson.M{}, bson.M{"createdAt": -1}, &gifts)

	for _, v := range gifts {
		if v.Source == globalConstant.GIFT_SOURCE_SYSTEM && v.Expired == nil {
			expired := globalLib.ParseDateFromTimeStamp(v.CreatedAt, local.TimeZone).AddDate(0, 1, 0)
			v.Expired = globalLib.ParseTimestampFromDate(expired)
		}
	}

	results := refactorGiftResult(gifts)
	globalResponse.ResponseSuccess(w, results)
}

func GetQueryGetNewGift(userId string) bson.M {
	now := globalLib.GetCurrentTime(local.TimeZone)
	before1Month := now.AddDate(0, -1, 0)
	return bson.M{
		"userId": userId,
		"$and": []bson.M{
			{"$or": []bson.M{
				{"used": bson.M{"$exists": false}},
				{"used": false}},
			},
			{"$or": []bson.M{
				{"expiredIsInfinite": true},
				{"expired": bson.M{"$gte": now}},
				{
					"source":    globalConstant.GIFT_SOURCE_SYSTEM,
					"expired":   bson.M{"$exists": false},
					"createdAt": bson.M{"$gte": before1Month},
				}},
			},
		},
	}
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetNewGift(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
