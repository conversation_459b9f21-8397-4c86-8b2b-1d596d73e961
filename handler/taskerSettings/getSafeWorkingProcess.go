/*
 * @File: getSafeWorkingProcess.go
 * @Description: Handler function of getSafeWorkingProcess api
 * @CreatedAt: 16/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package taskerSettings

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskerSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerSettings"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Safe Working Process
 * @CreatedAt: 16/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetSafeWorkingProcess(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateGetSafeWorkingProcess(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var settings *modelTaskerSettings.TaskerSettings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"safeWorkingProcessLink": 1}, &settings)
	result := make(map[string]interface{})
	if settings != nil && settings.SafeWorkingProcessLink != "" {
		result["status"] = "success"
		result["link"] = settings.SafeWorkingProcessLink
	} else {
		result["status"] = "error"
		result["reason"] = "Link not found"
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetSafeWorkingProcess(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
