package getWeeklyRollCall

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getWeeklyRollCall(userId string, gameCampaign *modelGameCampaign.GameCampaign) ([]model.DailyRollCallResponse, *globalResponse.ResponseErrorCode) {
	var userGameCampaign *modelUserGameCampaign.UserGameCampaign
	//startAWeek, endAWeek := lib.GetStartEndOfWeek()
	currentDate := globalLib.GetCurrentTime(local.TimeZone)
	_, endAMonth := globalLib.GetStartEndOfMonth(local.TimeZone, currentDate)

	var weeklyRollCall []model.DailyRollCallResponse
	var startAWeek time.Time
	week := currentDate.Day() / 7 //cal week by divide by 7
	if currentDate.Day()%7 == 0 {
		week-- //if currentDate is 7,14,21,28, week number decrease by 1
	}
	startAWeek = time.Date(currentDate.Year(), currentDate.Month(), 7*week+1, 0, 0, 0, 0, local.TimeZone)
	endAWeek := startAWeek.AddDate(0, 0, 6)
	if week == 4 && currentDate.Day()%7 != 0 { //week 5th only has 3 days: 29,30,31
		endAWeek = endAMonth
	}
	numberOfDayAtThisWeek := endAWeek.Day() - startAWeek.Day() + 1
	for i := 0; i < numberOfDayAtThisWeek; i++ {
		var daily model.DailyRollCallResponse
		daily.Date = startAWeek.AddDate(0, 0, i)
		weeklyRollCall = append(weeklyRollCall, daily)
	}

	query := bson.M{
		"userId":         userId,
		"gameCampaignId": gameCampaign.XId,
	}
	fields := bson.M{
		"accumulateSpinHistory": 1,
	}
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], query, fields, &userGameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		errCode := &lib.SYSTEM_ERROR
		errCode.Message = err.Error()
		return nil, errCode
	}

	if userGameCampaign != nil {
		for _, spin := range userGameCampaign.AccumulateSpinHistory {
			createdAtDate := globalLib.StartADay(globalLib.ParseDateFromTimeStamp(spin.CreatedAt, local.TimeZone))
			if (createdAtDate.After(startAWeek) || createdAtDate.Equal(startAWeek)) && (createdAtDate.Before(endAWeek) || createdAtDate.Equal(endAWeek)) && (spin.Action == lib.USER_CAMPAIGN_ACTION_ROLL_CALL || spin.Action == lib.USER_CAMPAIGN_ACTION_ROLL_CALL_AT_NOEL) {
				weekDay := createdAtDate.Day() - startAWeek.Day()
				weeklyRollCall[weekDay].IsChecked = true
			}
		}
	}

	if gameCampaign.GetConfig().GetPresentUnboxing().GetCheckIn() != nil {
		current := globalLib.StartADay(currentDate)
		for i := 0; i < numberOfDayAtThisWeek; i++ {
			weeklyDate := globalLib.StartADay(weeklyRollCall[i].Date)
			if weeklyDate.Before(current) && !weeklyRollCall[i].IsChecked {
				if weeklyRollCall[i].Date.Day() != lib.NOEL_DATE {
					weeklyRollCall[i].BackgroundImage = gameCampaign.Config.PresentUnboxing.CheckIn.BackgroundNormalDayFail
				} else {
					weeklyRollCall[i].BackgroundImage = gameCampaign.Config.PresentUnboxing.CheckIn.BackgroundSpecialDayFail
				}
			} else {
				if weeklyRollCall[i].Date.Day() != lib.NOEL_DATE {
					weeklyRollCall[i].BackgroundImage = gameCampaign.Config.PresentUnboxing.CheckIn.BackgroundNormalDay
				} else {
					weeklyRollCall[i].BackgroundImage = gameCampaign.Config.PresentUnboxing.CheckIn.BackgroundSpecialDay
				}
			}
		}
	}
	return weeklyRollCall[:], nil
}
