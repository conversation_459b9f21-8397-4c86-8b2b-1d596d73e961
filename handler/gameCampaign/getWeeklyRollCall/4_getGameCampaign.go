package getWeeklyRollCall

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getGameCampaign(user *modelUser.Users) (*modelGameCampaign.GameCampaign, *globalResponse.ResponseErrorCode) {
	// Get gameCampaign
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.GAME_CAMPAIGN_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}
	fields := bson.M{"_id": 1, "config": 1, "minVersion": 1, "startDate": 1, "endDate": 1}
	if user != nil && lib.CheckIsUserTester(user) {
		query["isTesting"] = true
	}

	var gameCampaign *modelGameCampaign.GameCampaign
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE], query, fields, &gameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		errCode := &lib.ERROR_CAN_NOT_GET_GAME_CAMPAIGN
		errCode.Message = err.Error()
		return nil, errCode
	}
	if gameCampaign == nil {
		return nil, &lib.ERROR_GAME_CAMPAIGN_NOT_FOUND
	}

	return gameCampaign, nil

}
