package getWeeklyRollCall

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetWeeklyRollCall(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. validate
	errCode := validateData(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 2. get user
	user, errCode := getUser(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}

	// 3. get game campaign
	gameCampaign, errCode := getGameCampaign(user)
	if errCode != nil {
		return nil, errCode
	}

	// Return if gameCampaign == nil
	if gameCampaign == nil {
		return nil, nil
	}

	weeklyRollCall, errCode := getWeeklyRollCall(reqBody.UserId, gameCampaign)
	if errCode != nil {
		return nil, errCode
	}
	return weeklyRollCall, nil
}
