package dailyRollCall

import (
	"context"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcEventVN"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
)

var cfg = config.GetConfig()

func markRoll(userId string) (int, *globalResponse.ResponseErrorCode) {
	var err error
	numberOfSpin := 1
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	if currentTime.Day() == lib.NOEL_DATE && currentTime.Month() == time.Month(lib.NOEL_MONTH) {
		err = callGRPCEventPublishUserGameCampaignActionRollCallAtNoel(userId)
		numberOfSpin = 2
	} else {
		err = callGRPCEventPublishUserGameCampaignActionRollCall(userId)
	}
	if err != nil {
		errCode := &lib.SYSTEM_ERROR
		errCode.Message = err.Error()
		return 0, errCode
	}
	return numberOfSpin, nil
}

func callGRPCEventPublishUserGameCampaignActionRollCall(userId string) error {
	client, connect, err := grpcEventVN.ConnectGRPCEventVN(cfg.GRPC_Event_URL)
	if err != nil {
		return err
	}
	defer connect.Close()
	request := &eventMessage.EventCommonMessage{
		UserId: userId,
	}
	_, err = client.PublishUserGameCampaignActionRollCall(context.Background(), request)

	return err
}
func callGRPCEventPublishUserGameCampaignActionRollCallAtNoel(userId string) error {
	client, connect, err := grpcEventVN.ConnectGRPCEventVN(cfg.GRPC_Event_URL)
	if err != nil {
		return err
	}
	defer connect.Close()
	request := &eventMessage.EventCommonMessage{
		UserId: userId,
	}
	_, err = client.PublishUserGameCampaignActionRollCallAtNoel(context.Background(), request)

	return err
}
