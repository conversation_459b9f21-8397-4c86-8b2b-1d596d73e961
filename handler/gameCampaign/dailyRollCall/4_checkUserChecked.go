package dailyRollCall

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func checkUserChecked(userId string, gameCampaign *modelGameCampaign.GameCampaign) *globalResponse.ResponseErrorCode {
	currentDate := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"userId":         userId,
		"gameCampaignId": gameCampaign.XId,
	}
	fields := bson.M{
		"accumulateSpinHistory": 1,
	}
	var userGameCampaign *modelUserGameCampaign.UserGameCampaign
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], query, fields, &userGameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		errCode := &lib.SYSTEM_ERROR
		errCode.Message = err.Error()
		return errCode
	}
	if userGameCampaign == nil {
		return nil
	}
	current := globalLib.StartADay(currentDate)
	for _, spin := range userGameCampaign.AccumulateSpinHistory {
		createdAtDate := globalLib.StartADay(globalLib.ParseDateFromTimeStamp(spin.CreatedAt, local.TimeZone))
		if createdAtDate.Equal(current) && (spin.Action == lib.USER_CAMPAIGN_ACTION_ROLL_CALL || spin.Action == lib.USER_CAMPAIGN_ACTION_ROLL_CALL_AT_NOEL) {
			return &lib.ERROR_USER_CHECKED_TODAY
		}
	}
	return nil
}
