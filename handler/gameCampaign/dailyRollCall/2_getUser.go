package dailyRollCall

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUser(userId string) (*modelUser.Users, *globalResponse.ResponseErrorCode) {
	query := bson.M{"_id": userId, "type": globalConstant.USER_TYPE_ASKER}
	fields := bson.M{"_id": 1, "name": 1, "phone": 1, "language": 1}
	user, err := modelUser.GetOneByQuery(local.ISO_CODE, query, fields)
	if err != nil {
		errCode := &lib.ERROR_USER_NOT_FOUND
		errCode.Message = err.Error()
		return nil, errCode
	}
	if user == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}
	return user, nil
}
