package dailyRollCall

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func DailyRollCall(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// 1. validate
	errCode := validateData(reqBody)
	if errCode != nil {
		return nil, errCode
	}

	// 2. check user
	user, errCode := getUser(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}

	//3. get game campaign
	gameCampaign, errCode := getGameCampaign(reqBody.GameCampaignId, user)
	if errCode != nil {
		return nil, errCode
	}

	// 4. check if user has checked
	errCode = checkUserChecked(reqBody.UserId, gameCampaign)
	if errCode != nil {
		return nil, errCode
	}

	// 5. mark roll
	numberOfSpin, errCode := markRoll(reqBody.UserId)
	if errCode != nil {
		return nil, errCode
	}
	result := make(map[string]any)
	result["numberOfSpin"] = numberOfSpin
	return result, nil
}
