package getRunningGameCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getUser(userId string) (*modelUser.Users, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": userId, "type": globalConstant.USER_TYPE_ASKER}
	fields := bson.M{"_id": 1, "name": 1, "phone": 1, "isoCode": 1, "language": 1}
	user, err := modelUser.GetOneByQuery(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	if user == nil {
		return nil, nil, nil
	}
	return user, nil, nil
}
