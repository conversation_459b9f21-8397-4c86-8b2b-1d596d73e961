package getRunningGameCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
)

func GetRunningGameCampaign(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. get user
	user, errCode, err := getUser(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 2. get game campaign
	gameCampaign, errCode, err := getGameCampaign(user)
	if errCode != nil {
		return nil, errCode, err
	}

	// Return if gameCampaign == nil
	if gameCampaign == nil {
		return nil, nil, nil
	}

	// 3. get user spin for this game campaign
	var userGameCampaign *modelUserGameCampaign.UserGameCampaign
	if user != nil {
		userGameCampaign = getUserGameCampaign(user.XId, gameCampaign.XId)
	}

	// 4. reward for client show
	imagesForCache := []string{}
	rewards, rewardImagesForCache := getRewards(gameCampaign)

	// 5. Get config image to cache
	configImagesForCache := getConfigCacheImage(reqBody.Language, gameCampaign.Config)
	imagesForCache = append(imagesForCache, configImagesForCache...)
	imagesForCache = append(imagesForCache, rewardImagesForCache...)

	// xử lý backgroundItem cho mission
	if gameCampaign.GetConfig().GetGeneral().GetMissionList() != nil && gameCampaign.GetConfig().GetPresentUnboxing() != nil &&
		gameCampaign.GetConfig().GetPresentUnboxing().GetBackgroundItem() != "" && gameCampaign.GetConfig().GetGeneral().GetMissionList().GetBackgroundItem() == "" {
		gameCampaign.GetConfig().GetGeneral().GetMissionList().BackgroundItem = gameCampaign.GetConfig().GetPresentUnboxing().GetBackgroundItem()
	}

	// xử lý mission
	processMissionGameCampaign(user, gameCampaign)

	result := map[string]interface{}{
		"_id":             gameCampaign.XId,
		"name":            gameCampaign.Name,
		"showEventFrom":   gameCampaign.ShowEventFrom,
		"startDate":       gameCampaign.StartDate,
		"endDate":         gameCampaign.EndDate,
		"config":          gameCampaign.Config,
		"text":            gameCampaign.Text,
		"cacheImages":     globalLib.UniqString(imagesForCache),
		"rewards":         rewards,
		"giftCountToOpen": userGameCampaign.GetNumberOfSpin(),
		"gameType":        gameCampaign.GameType,
	}

	// xử lý quizGameChallenges
	if len(gameCampaign.GetChallenges()) > 0 {
		result["challenges"] = getQuizGameChallenges(gameCampaign, userGameCampaign)
	}

	// check app version with gameCampaign version
	if gameCampaign.MinVersion != "" && globalLib.CompareVersion(reqBody.Version, gameCampaign.MinVersion) < 0 {
		result["isRequiredUpdate"] = true
		return result, nil, nil
	}

	// Return
	return result, nil, nil
}
