package getRunningGameCampaign

import (
	"log"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"go.mongodb.org/mongo-driver/bson"
)

func getUserGameCampaign(userId, gameCampaignId string) *modelUserGameCampaign.UserGameCampaign {
	var userGameCampaign *modelUserGameCampaign.UserGameCampaign
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE],
		bson.M{
			"userId":         userId,
			"gameCampaignId": gameCampaignId,
		},
		bson.M{},
		&userGameCampaign,
	)
	if err != nil {
		log.Println("Error getUserGameCampaign", err)
		return nil
	}
	return userGameCampaign
}
