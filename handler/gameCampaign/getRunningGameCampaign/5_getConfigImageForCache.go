package getRunningGameCampaign

import (
	"encoding/json"

	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
)

func getConfigCacheImage(language string, config *modelGameCampaign.GameCampaignConfig) []string {
	if config == nil {
		return []string{}
	}

	result := []string{}
	if config.General != nil {
		if config.General.Banner != "" {
			result = append(result, config.General.Banner)
		}
		if config.General.FloatIcon != nil {
			result = append(result, getImageByLanguage(language, config.General.FloatIcon)...)
		}

		if config.General.MissionList != nil {
			if config.General.MissionList.TitleImage != nil {
				result = append(result, getImageByLanguage(language, config.General.MissionList.TitleImage)...)
			}
			if config.General.MissionList.Background != "" {
				result = append(result, config.General.MissionList.Background)
			}
			if config.General.MissionList.BackgroundTitle != "" {
				result = append(result, config.General.MissionList.BackgroundTitle)
			}
			if config.General.MissionList.BackgroundItem != "" {
				result = append(result, config.General.MissionList.BackgroundItem)
			}
		}

		if config.General.BannerByLocale != nil {
			if config.General.BannerByLocale != nil {
				result = append(result, getImageByLanguage(language, config.General.BannerByLocale)...)
			}
		}
	}

	if config.LuckyDraw != nil {
		if config.LuckyDraw.Header != nil {
			result = append(result, getRatioImageByLanguage(language, config.LuckyDraw.Header)...)
		}
		if config.LuckyDraw.Footer != nil {
			result = append(result, getRatioImageByLanguage(language, config.LuckyDraw.Footer)...)
		}
		if config.LuckyDraw.RewardList != nil {
			result = append(result, getImageByLanguage(language, config.LuckyDraw.RewardList)...)
		}
		if config.LuckyDraw.Wheel != nil {
			result = append(result, getImageByLanguage(language, config.LuckyDraw.Wheel)...)
		}
		if config.LuckyDraw.WheelFrame != nil {
			result = append(result, getImageByLanguage(language, config.LuckyDraw.WheelFrame)...)
		}
		if config.LuckyDraw.Needle != nil {
			result = append(result, getImageByLanguage(language, config.LuckyDraw.Needle)...)
		}
		if config.LuckyDraw.IconBack != "" {
			result = append(result, config.LuckyDraw.IconBack)
		}
		if config.LuckyDraw.IconNext != "" {
			result = append(result, config.LuckyDraw.IconNext)
		}
		if config.LuckyDraw.IconHistory != "" {
			result = append(result, config.LuckyDraw.IconHistory)
		}
		if config.LuckyDraw.IconReward != "" {
			result = append(result, config.LuckyDraw.IconReward)
		}
		if config.LuckyDraw.IconSpin != "" {
			result = append(result, config.LuckyDraw.IconSpin)
		}
		if config.LuckyDraw.IconShare != "" {
			result = append(result, config.LuckyDraw.IconShare)
		}
		if config.LuckyDraw.Introduction != nil && config.LuckyDraw.Introduction.Icon != "" {
			result = append(result, config.LuckyDraw.Introduction.Icon)
		}
		if config.LuckyDraw.PrimaryBackgroundButton != "" {
			result = append(result, config.LuckyDraw.PrimaryBackgroundButton)
		}
		if config.LuckyDraw.SecondaryBackgroundButton != "" {
			result = append(result, config.LuckyDraw.SecondaryBackgroundButton)
		}
		if config.LuckyDraw.IconClose != "" {
			result = append(result, config.LuckyDraw.IconClose)
		}
	}

	// noel campaign presentUnboxing
	if config.PresentUnboxing != nil {
		if config.PresentUnboxing.IconChecked != "" {
			result = append(result, config.PresentUnboxing.IconChecked)
		}

		if config.PresentUnboxing.Background != "" {
			result = append(result, config.PresentUnboxing.Background)
		}
		if config.PresentUnboxing.TreeImage != "" {
			result = append(result, config.PresentUnboxing.TreeImage)
		}
		if config.PresentUnboxing.FooterImage != "" {
			result = append(result, config.PresentUnboxing.FooterImage)
		}
		if config.PresentUnboxing.IconBack != "" {
			result = append(result, config.PresentUnboxing.IconBack)
		}
		if config.PresentUnboxing.IconNext != "" {
			result = append(result, config.PresentUnboxing.IconNext)
		}
		if config.PresentUnboxing.IconHistory != "" {
			result = append(result, config.PresentUnboxing.IconHistory)
		}
		if config.PresentUnboxing.IconReward != "" {
			result = append(result, config.PresentUnboxing.IconReward)
		}
		if config.PresentUnboxing.IconClose != "" {
			result = append(result, config.PresentUnboxing.IconClose)
		}
		if config.PresentUnboxing.IconMute != "" {
			result = append(result, config.PresentUnboxing.IconMute)
		}
		if config.PresentUnboxing.IconMuteOn != "" {
			result = append(result, config.PresentUnboxing.IconMuteOn)
		}
		if config.PresentUnboxing.IconMuteOff != "" {
			result = append(result, config.PresentUnboxing.IconMuteOff)
		}
		if config.PresentUnboxing.Introduction != nil {
			if config.PresentUnboxing.Introduction.Icon != "" {
				result = append(result, config.PresentUnboxing.Introduction.Icon)
			}
			if config.PresentUnboxing.Introduction.TitleImage != nil {
				result = append(result, getImageByLanguage(language, config.PresentUnboxing.Introduction.TitleImage)...)
			}
			if config.PresentUnboxing.Introduction.Background != "" {
				result = append(result, config.PresentUnboxing.Introduction.Background)
			}

			if config.PresentUnboxing.Introduction.Contents != nil {
				if config.PresentUnboxing.Introduction.Contents.Info != nil {
					if config.PresentUnboxing.Introduction.Contents.Info.TitleImage != nil {
						result = append(result, getImageByLanguage(language, config.PresentUnboxing.Introduction.Contents.Info.TitleImage)...)
					}

					for _, content := range config.PresentUnboxing.Introduction.Contents.Info.Contents {
						if content.Icon != "" {
							result = append(result, content.Icon)
						}
					}
				}

				if config.PresentUnboxing.Introduction.Contents.Rule != nil {
					if config.PresentUnboxing.Introduction.Contents.Rule.TitleImage != nil {
						result = append(result, getImageByLanguage(language, config.PresentUnboxing.Introduction.Contents.Rule.TitleImage)...)
					}

					for _, content := range config.PresentUnboxing.Introduction.Contents.Rule.Contents {
						if content.Icon != "" {
							result = append(result, content.Icon)
						}
					}
				}

				if config.PresentUnboxing.Introduction.Contents.Reward != nil {
					if config.PresentUnboxing.Introduction.Contents.Reward.TitleImage != nil {
						result = append(result, getImageByLanguage(language, config.PresentUnboxing.Introduction.Contents.Reward.TitleImage)...)
					}
				}

				if config.PresentUnboxing.Introduction.Contents.Note != nil {
					if config.PresentUnboxing.Introduction.Contents.Note.TitleImage != nil {
						result = append(result, getImageByLanguage(language, config.PresentUnboxing.Introduction.Contents.Note.TitleImage)...)
					}
				}
			}
		}
		if config.PresentUnboxing.PrimaryBackgroundButtonSmall != "" {
			result = append(result, config.PresentUnboxing.PrimaryBackgroundButtonSmall)
		}
		if config.PresentUnboxing.PrimaryBackgroundButtonBig != "" {
			result = append(result, config.PresentUnboxing.PrimaryBackgroundButtonBig)
		}
		if config.PresentUnboxing.PrimaryBackgroundButtonSmallDisabled != "" {
			result = append(result, config.PresentUnboxing.PrimaryBackgroundButtonSmallDisabled)
		}
		if config.PresentUnboxing.PrimaryBackgroundButtonBigDisabled != "" {
			result = append(result, config.PresentUnboxing.PrimaryBackgroundButtonBigDisabled)
		}
		if config.PresentUnboxing.Share != nil {
			if config.PresentUnboxing.Share.Icon != "" {
				result = append(result, config.PresentUnboxing.Share.Icon)
			}
		}
		if config.PresentUnboxing.History != nil {
			if config.PresentUnboxing.History.Background != "" {
				result = append(result, config.PresentUnboxing.History.Background)
			}
			if config.PresentUnboxing.History.TitleImage != nil {
				result = append(result, getImageByLanguage(language, config.PresentUnboxing.History.TitleImage)...)
			}
		}
		if config.PresentUnboxing.CheckIn != nil {
			if config.PresentUnboxing.CheckIn.TitleImage != nil {
				result = append(result, getImageByLanguage(language, config.PresentUnboxing.CheckIn.TitleImage)...)
			}
			if config.PresentUnboxing.CheckIn.Background != "" {
				result = append(result, config.PresentUnboxing.CheckIn.Background)
			}
			if config.PresentUnboxing.CheckIn.BackgroundNormalDay != "" {
				result = append(result, config.PresentUnboxing.CheckIn.BackgroundNormalDay)
			}
			if config.PresentUnboxing.CheckIn.BackgroundNormalDayFail != "" {
				result = append(result, config.PresentUnboxing.CheckIn.BackgroundNormalDayFail)
			}
			if config.PresentUnboxing.CheckIn.BackgroundSpecialDayFail != "" {
				result = append(result, config.PresentUnboxing.CheckIn.BackgroundSpecialDayFail)
			}
			if config.PresentUnboxing.CheckIn.BackgroundSpecialDay != "" {
				result = append(result, config.PresentUnboxing.CheckIn.BackgroundSpecialDay)
			}
		}
		if config.PresentUnboxing.BackgroundItem != "" {
			result = append(result, config.PresentUnboxing.BackgroundItem)
		}

		if config.PresentUnboxing.Boxes != nil {
			for _, box := range config.PresentUnboxing.Boxes {
				if box.Image != "" {
					result = append(result, box.Image)
				}
			}
		}

		if config.PresentUnboxing.ReceiveGift != nil {
			if config.PresentUnboxing.ReceiveGift.Background != "" {
				result = append(result, config.PresentUnboxing.ReceiveGift.Background)
			}
			if config.PresentUnboxing.ReceiveGift.BackgroundHighlight != "" {
				result = append(result, config.PresentUnboxing.ReceiveGift.BackgroundHighlight)
			}
		}

		if config.PresentUnboxing.SecondaryBackgroundButtonSmall != "" {
			result = append(result, config.PresentUnboxing.SecondaryBackgroundButtonSmall)
		}

		if config.PresentUnboxing.SecondaryBackgroundButtonBig != "" {
			result = append(result, config.PresentUnboxing.SecondaryBackgroundButtonBig)
		}

		if config.PresentUnboxing.TutorialImage != "" {
			result = append(result, config.PresentUnboxing.TutorialImage)
		}
	}

	if config.PuzzleGame != nil {
		if config.PuzzleGame.IconChevron != "" {
			result = append(result, config.PuzzleGame.IconChevron)
		}
		if config.PuzzleGame.IconBack != "" {
			result = append(result, config.PuzzleGame.IconBack)
		}
		if config.PuzzleGame.IconMuteOn != "" {
			result = append(result, config.PuzzleGame.IconMuteOn)
		}
		if config.PuzzleGame.IconMuteOff != "" {
			result = append(result, config.PuzzleGame.IconMuteOff)
		}
		if config.PuzzleGame.IconClose != "" {
			result = append(result, config.PuzzleGame.IconClose)
		}
		if config.PuzzleGame.IconNext != "" {
			result = append(result, config.PuzzleGame.IconNext)
		}
		if config.PuzzleGame.IconReward != "" {
			result = append(result, config.PuzzleGame.IconReward)
		}
		if config.PuzzleGame.IconLock != "" {
			result = append(result, config.PuzzleGame.IconLock)
		}
		if config.PuzzleGame.IconUnlock != "" {
			result = append(result, config.PuzzleGame.IconUnlock)
		}
		if config.PuzzleGame.Background != "" {
			result = append(result, config.PuzzleGame.Background)
		}
		if config.PuzzleGame.BackgroundEmptyTransaction != "" {
			result = append(result, config.PuzzleGame.BackgroundEmptyTransaction)
		}
		if config.PuzzleGame.BackgroundWinner != "" {
			result = append(result, config.PuzzleGame.BackgroundWinner)
		}
		if config.PuzzleGame.BackgroundTutorial != "" {
			result = append(result, config.PuzzleGame.BackgroundTutorial)
		}
		if config.PuzzleGame.BackgroundPuzzle != "" {
			result = append(result, config.PuzzleGame.BackgroundPuzzle)
		}
		if config.PuzzleGame.BackgroundModal != "" {
			result = append(result, config.PuzzleGame.BackgroundModal)
		}
		if config.PuzzleGame.BackgroundQuiz != "" {
			result = append(result, config.PuzzleGame.BackgroundQuiz)
		}
		if config.PuzzleGame.BackgroundAnswer != "" {
			result = append(result, config.PuzzleGame.BackgroundAnswer)
		}
		if config.PuzzleGame.BackgroundAnswerMultiple != "" {
			result = append(result, config.PuzzleGame.BackgroundAnswerMultiple)
		}
		if config.PuzzleGame.BackgroundQuestion != "" {
			result = append(result, config.PuzzleGame.BackgroundQuestion)
		}
		if config.PuzzleGame.BackgroundItem != "" {
			result = append(result, config.PuzzleGame.BackgroundItem)
		}
		if config.PuzzleGame.ButtonMediumDisable != "" {
			result = append(result, config.PuzzleGame.ButtonMediumDisable)
		}
		if config.PuzzleGame.ButtonLargeDisable != "" {
			result = append(result, config.PuzzleGame.ButtonLargeDisable)
		}
		if config.PuzzleGame.ButtonMediumActive != "" {
			result = append(result, config.PuzzleGame.ButtonMediumActive)
		}
		if config.PuzzleGame.ButtonLargeActive != "" {
			result = append(result, config.PuzzleGame.ButtonLargeActive)
		}
		if config.PuzzleGame.ButtonSmall != "" {
			result = append(result, config.PuzzleGame.ButtonSmall)
		}
		if config.PuzzleGame.ButtonNumberOfPlay != "" {
			result = append(result, config.PuzzleGame.ButtonNumberOfPlay)
		}
		if config.PuzzleGame.ButtonNumberOfPlayFull != "" {
			result = append(result, config.PuzzleGame.ButtonNumberOfPlayFull)
		}
		if len(config.PuzzleGame.CompletePuzzleImage) > 0 {
			result = append(result, config.PuzzleGame.CompletePuzzleImage...)
		}
		if config.PuzzleGame.Deeplink != nil {
			result = append(result, getImageByLanguage(language, config.PuzzleGame.Deeplink)...)
		}
		if config.PuzzleGame.ImageGiftBox != "" {
			result = append(result, config.PuzzleGame.ImageGiftBox)
		}
		if config.PuzzleGame.GetShare().GetShareImage() != nil {
			result = append(result, getImageByLanguage(language, config.PuzzleGame.GetShare().GetShareImage())...)
		}
		if config.PuzzleGame.GetShare().GetIcon() != "" {
			result = append(result, config.PuzzleGame.GetShare().GetIcon())
		}
		if config.PuzzleGame.Introduction != nil {
			if config.PuzzleGame.Introduction.Icon != "" {
				result = append(result, config.PuzzleGame.Introduction.Icon)
			}
			if config.PuzzleGame.Introduction.Background != "" {
				result = append(result, config.PuzzleGame.Introduction.Background)
			}
		}
		if config.GetPuzzleGame().GetIntroduction().GetBackground() != "" {
			result = append(result, config.GetPuzzleGame().GetIntroduction().GetBackground())
		}
		if config.GetPuzzleGame().GetCommunityPost().GetFirstChallengeComplete().GetBackground() != "" {
			result = append(result, config.GetPuzzleGame().GetCommunityPost().GetFirstChallengeComplete().GetBackground())
		}
		if config.GetPuzzleGame().GetCommunityPost().GetBackground() != "" {
			result = append(result, config.GetPuzzleGame().GetCommunityPost().GetBackground())
		}
		if config.GetPuzzleGame().GetCommunityPost().GetBackground() != "" {
			result = append(result, config.GetPuzzleGame().GetCommunityPost().GetBackground())
		}
		if config.PuzzleGame.CheckIn != nil {
			if config.PuzzleGame.CheckIn.Background != "" {
				result = append(result, config.PuzzleGame.CheckIn.Background)
			}
			if config.PuzzleGame.CheckIn.IconCheckPass != "" {
				result = append(result, config.PuzzleGame.CheckIn.IconCheckPass)
			}
			if config.PuzzleGame.CheckIn.IconCheckFail != "" {
				result = append(result, config.PuzzleGame.CheckIn.IconCheckFail)
			}
		}

	}
	return result
}

// image can be map[string]string or service.ServiceText here
/* Ex:
{
	"vi": "https://i.imgur.com/baxKD6O.png",
	"en": "https://i.imgur.com/baxKD6O.png",
	"ko": "https://i.imgur.com/baxKD6O.png",
	"th": "https://i.imgur.com/baxKD6O.png",
	"id": "https://i.imgur.com/baxKD6O.png",
}
*/
func getImageByLanguage(language string, image interface{}) []string {
	if image == nil {
		return nil
	}

	// Case app not send language
	result := []string{}
	textMap := make(map[string]string)
	textData, _ := json.Marshal(image)
	err := json.Unmarshal(textData, &textMap)
	if err != nil {
		return nil
	}
	if language == "" {
		for _, v := range textMap {
			if v != "" {
				result = append(result, v)
			}
		}
		return result
	}

	// Case app sent language to server
	textLanguage := textMap[language]
	if textLanguage != "" {
		result = append(result, textLanguage)
	}
	return result
}

// image can be map[string]map[string]string{} here
/* Ex:
{
	"ratio1x0_5": {
		"vi": "https://i.imgur.com/baxKD6O.png",
		"en": "https://i.imgur.com/baxKD6O.png",
		"ko": "https://i.imgur.com/baxKD6O.png",
		"th": "https://i.imgur.com/baxKD6O.png",
		"id": "https://i.imgur.com/baxKD6O.png",
	}
}
*/
func getRatioImageByLanguage(language string, ratioImage interface{}) []string {
	if ratioImage == nil {
		return nil
	}

	// Case app not send language
	result := []string{}
	ratioImageMap := make(map[string]map[string]string)
	ratioImageData, _ := json.Marshal(ratioImage)
	err := json.Unmarshal(ratioImageData, &ratioImageMap)
	if err != nil {
		return nil
	}
	for _, v := range ratioImageMap {
		result = append(result, getImageByLanguage(language, v)...)
	}
	return result
}
