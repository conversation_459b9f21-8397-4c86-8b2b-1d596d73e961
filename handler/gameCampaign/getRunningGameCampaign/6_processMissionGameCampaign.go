package getRunningGameCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func processMissionGameCampaign(user *modelUser.Users, gameCampaign *modelGameCampaign.GameCampaign) {
	if user != nil && gameCampaign.Config != nil && gameCampaign.Config.General != nil && gameCampaign.Config.General.MissionList != nil {
		var newMission []*modelGameCampaign.GameCampaignConfigGeneralMissionListMission
		// Kiểm tra có phải task done đầu tiên
		isExistTaskDone, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"askerId": user.XId, "status": globalConstant.TASK_STATUS_DONE})
		if isExistTaskDone {
			for _, mission := range gameCampaign.Config.General.MissionList.Mission {
				if mission.Name == lib.MISSION_NAME_WELLCOME {
					continue
				}
				newMission = append(newMission, mission)
			}
		}
		if len(newMission) > 0 {
			gameCampaign.Config.General.MissionList.Mission = newMission
		}
	}

}
