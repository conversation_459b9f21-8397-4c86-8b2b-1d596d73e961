package getRunningGameCampaign

import (
	"sort"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
)

func getQuizGameChallenges(gameCampaign *modelGameCampaign.GameCampaign, userGameCampaign *modelUserGameCampaign.UserGameCampaign) []map[string]interface{} {
	challengeStatusMap := getChallengeStatusHistoryMap(userGameCampaign)

	var result []map[string]interface{}
	for _, challenge := range gameCampaign.GetChallenges() {
		challengeStatus := globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_LOCKED
		// Check if challenge will be opened by condition
		isChallengeOpened := isChallengeOpened(challenge.GetConditions(), challengeStatusMap)
		if isChallengeOpened && userGameCampaign.GetNumberOfSpin() >= challenge.GetRequiredSpins() {
			challengeStatus = globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_OPEN
		}
		// Check if user pass this challenge
		if isUserCompletedChallenge(challenge.GetName(), challengeStatusMap) {
			challengeStatus = globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED
		}

		// Add to result
		result = append(result, map[string]interface{}{
			"_id":           challenge.GetXId(),
			"name":          challenge.GetName(),
			"type":          challenge.GetType(),
			"order":         challenge.GetOrder(),
			"status":        challengeStatus,
			"postData":      challenge.GetPostData(),
			"requiredSpins": challenge.GetRequiredSpins(),
		})
	}

	sort.Slice(result, func(i, j int) bool {
		return cast.ToInt(result[i]["order"]) < cast.ToInt(result[j]["order"])
	})

	return result
}

func getChallengeStatusHistoryMap(userGameCampaign *modelUserGameCampaign.UserGameCampaign) map[string]string {
	challengeStatusMap := make(map[string]string)
	for _, history := range userGameCampaign.GetChallengeHistories() {
		if history.GetStatus() == globalConstant.GAME_CAMPAIGN_CHALLENGE_QUIZ_STATUS_PASSED {
			challengeStatusMap[history.GetChallengeName()] = globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED
		}
	}
	return challengeStatusMap
}

func isChallengeOpened(conditions []*modelGameCampaign.GameCampaignChallengeCondition, challengeStatusMap map[string]string) bool {
	for _, condition := range conditions {
		// Check if challenge is started
		if condition.GetType() == "DATE" {
			now := globalLib.GetCurrentTime(local.TimeZone)
			startDateCondition := globalLib.ParseDateFromTimeStamp(condition.GetTargetDate(), local.TimeZone)
			if now.Before(startDateCondition) {
				return false
			}
		}
		// Check if user pass all challenges in target
		if condition.GetType() == "CHALLENGE_COMPLETED_REQUIRE" {
			for _, challengeName := range condition.GetTargetSArray() {
				if challengeStatus, ok := challengeStatusMap[challengeName]; !ok || challengeStatus != globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED {
					return false
				}
			}
		}
	}
	return true
}

func isUserCompletedChallenge(challengeName string, challengeStatusMap map[string]string) bool {
	if challengeStatus, ok := challengeStatusMap[challengeName]; !ok || challengeStatus != globalConstant.GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED {
		return false
	}
	return true
}
