package getRunningGameCampaign

import modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"

func getRewards(gameCampaign *modelGameCampaign.GameCampaign) ([]map[string]interface{}, []string) {
	rewards := []map[string]interface{}{}
	// get list images for cache
	imagesForCacheInApp := []string{}
	for _, v := range gameCampaign.Rewards {
		rewards = append(rewards, map[string]interface{}{
			"rewardKey": v.<PERSON><PERSON><PERSON>,
			"title":     v.Title,
			"image":     v.Image,
			"thumbnail": v.Thumbnail,
			"segment":   v.Segment,
		})
		if v.Image != "" {
			imagesForCacheInApp = append(imagesForCacheInApp, v.Image)
		}
		if v.Thumbnail != "" {
			imagesForCacheInApp = append(imagesForCacheInApp, v.Thumbnail)
		}
	}

	return rewards, imagesForCacheInApp
}
