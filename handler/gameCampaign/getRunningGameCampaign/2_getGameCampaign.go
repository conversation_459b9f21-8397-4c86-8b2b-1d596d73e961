package getRunningGameCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getGameCampaign(user *modelUser.Users) (*modelGameCampaign.GameCampaign, *globalResponse.ResponseErrorCode, error) {
	// Get gameCampaign
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"$or": []bson.M{
			{"showEventFrom": bson.M{"$lte": now}},
			{"startDate": bson.M{"$lte": now}},
		},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.GAME_CAMPAIGN_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}
	fields := bson.M{
		"_id":           1,
		"showEventFrom": 1,
		"startDate":     1,
		"endDate":       1,
		"rewards":       1,
		"isTesting":     1,
		"introduction":  1,
		"text":          1,
		"config":        1,
		"name":          1,
		"minVersion":    1,
		"gameType":      1,
		// New config for QUIZ_GAME
		"challenges._id":           1,
		"challenges.name":          1,
		"challenges.type":          1,
		"challenges.postData":      1,
		"challenges.order":         1,
		"challenges.conditions":    1,
		"challenges.requiredSpins": 1,
	}
	if user != nil && lib.CheckIsUserTester(user) {
		query["isTesting"] = true
	}

	var gameCampaign *modelGameCampaign.GameCampaign
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE], query, fields, &gameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.ERROR_CAN_NOT_GET_GAME_CAMPAIGN, err
	}
	return gameCampaign, nil, nil
}
