package getHistoryReceiveReward

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getUserGameCampaignRewards(userId, gameCampaignId string) ([]*modelUserGameCampaign.UserGameCampaignReward, *globalResponse.ResponseErrorCode, error) {
	// 3. get user game campaign info
	var userGameCampaign *modelUserGameCampaign.UserGameCampaign
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], bson.M{"userId": userId, "gameCampaignId": gameCampaignId}, bson.M{"rewards": 1}, &userGameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.ERROR_CAN_NOT_GET_USER_GAME_CAMPAIGN, err
	}

	if userGameCampaign == nil {
		return nil, nil, nil
	}

	return userGameCampaign.Rewards, nil, nil
}
