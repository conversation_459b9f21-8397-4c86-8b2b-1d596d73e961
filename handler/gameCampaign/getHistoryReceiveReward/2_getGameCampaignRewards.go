package getHistoryReceiveReward

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getGameCampaignRewards(gameCampaignId string) (map[int32]*modelGameCampaign.GameCampaignReward, *globalResponse.ResponseErrorCode, error) {
	var gameCampaign *modelGameCampaign.GameCampaign
	fields := bson.M{
		"rewards.rewardKey":      1,
		"rewards.thumbnail":      1,
		"rewards.title":          1,
		"rewards.giftInfo.title": 1,
		"rewards.navigateTo":     1,
		"rewards.screen":         1,
		"rewards.background":     1,
	}
	err := globalDataAccess.GetOneById(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE], gameCampaignId, fields, &gameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.ERROR_CAN_NOT_GET_GAME_CAMPAIGN, err
	}

	if gameCampaign == nil || len(gameCampaign.Rewards) == 0 {
		return nil, nil, nil
	}
	result := map[int32]*modelGameCampaign.GameCampaignReward{}
	for _, r := range gameCampaign.Rewards {
		result[r.RewardKey] = r
	}
	return result, nil, nil
}
