package getHistoryReceiveReward

import (
	"sort"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
)

func combineData(gameCampaignRewards map[int32]*modelGameCampaign.GameCampaignReward, userGameCampaignRewards []*modelUserGameCampaign.UserGameCampaignReward) []map[string]interface{} {
	if len(userGameCampaignRewards) == 0 || len(gameCampaignRewards) == 0 {
		return []map[string]interface{}{}
	}

	sort.Slice(userGameCampaignRewards, func(i, j int) bool {
		iCreatedAt := globalLib.ParseDateFromTimeStamp(userGameCampaignRewards[i].CreatedAt, local.TimeZone)
		jCreatedAt := globalLib.ParseDateFromTimeStamp(userGameCampaignRewards[j].CreatedAt, local.TimeZone)
		return iCreatedAt.After(jCreatedAt)
	})

	// Process if both of them have data
	result := []map[string]interface{}{}
	for _, v := range userGameCampaignRewards {
		if gameCampaignRewards[v.RewardKey] == nil {
			continue
		}
		rewardTitle := gameCampaignRewards[v.RewardKey].Title
		title := &modelService.ServiceText{
			Vi: rewardTitle.GetVi(),
			En: rewardTitle.GetEn(),
			Id: rewardTitle.GetId(),
			Th: rewardTitle.GetTh(),
			Ko: rewardTitle.GetKo(),
		}
		item := map[string]interface{}{
			"title":     title,
			"createdAt": v.CreatedAt,
		}
		if gameCampaignRewards[v.RewardKey].GiftInfo != nil && gameCampaignRewards[v.RewardKey].GiftInfo.Title != nil {
			rewardTitle := gameCampaignRewards[v.RewardKey].GiftInfo.Title
			title = &modelService.ServiceText{
				Vi: rewardTitle.GetVi(),
				En: rewardTitle.GetEn(),
				Id: rewardTitle.GetId(),
				Th: rewardTitle.GetTh(),
				Ko: rewardTitle.GetKo(),
			}
			item["title"] = title
		}

		if gameCampaignRewards[v.RewardKey].Thumbnail != "" {
			item["icon"] = gameCampaignRewards[v.RewardKey].Thumbnail
		}

		if gameCampaignRewards[v.RewardKey].Image != "" {
			item["image"] = gameCampaignRewards[v.RewardKey].Image
		}

		if gameCampaignRewards[v.RewardKey].NavigateTo != "" {
			item["navigateTo"] = gameCampaignRewards[v.RewardKey].NavigateTo
		}

		if gameCampaignRewards[v.RewardKey].Screen != "" {
			item["screen"] = gameCampaignRewards[v.RewardKey].Screen
		}

		if gameCampaignRewards[v.RewardKey].Background != "" {
			item["background"] = gameCampaignRewards[v.RewardKey].Background
		}

		result = append(result, item)
	}
	return result
}
