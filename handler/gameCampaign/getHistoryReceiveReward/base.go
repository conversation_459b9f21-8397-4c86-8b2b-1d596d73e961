package getHistoryReceiveReward

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetHistoryReceiveReward(reqBody *model.ApiRequest) ([]map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get game campaign to get reward detail
	gameCampaignRewards, errCode, err := getGameCampaignRewards(reqBody.GameCampaignId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. get user game campaign
	userGameCampaign, errCode, err := getUserGameCampaignRewards(reqBody.UserId, reqBody.GameCampaignId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 4. combine data
	rewards := combineData(gameCampaignRewards, userGameCampaign)
	return rewards, nil, nil
}
