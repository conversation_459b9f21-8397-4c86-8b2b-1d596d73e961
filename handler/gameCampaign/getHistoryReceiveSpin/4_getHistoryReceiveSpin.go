package getHistoryReceiveSpin

import (
	"fmt"
	"sort"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func getHistoryReceiveSpin(userGameCampaign *modelUserGameCampaign.UserGameCampaign, gameCampaign *modelGameCampaign.GameCampaign) []map[string]interface{} {
	config := gameCampaign.GetConfig()
	if userGameCampaign == nil || config == nil {
		return nil
	}

	var historyGiftOpening []map[string]interface{}
	// sort data by created At
	sort.Slice(userGameCampaign.AccumulateSpinHistory, func(i, j int) bool {
		iCreatedAt := globalLib.ParseDateFromTimeStamp(userGameCampaign.AccumulateSpinHistory[i].CreatedAt, local.TimeZone)
		jCreatedAt := globalLib.ParseDateFromTimeStamp(userGameCampaign.AccumulateSpinHistory[j].CreatedAt, local.TimeZone)
		return iCreatedAt.After(jCreatedAt)
	})

	for _, u := range userGameCampaign.AccumulateSpinHistory {
		title := &modelService.ServiceText{}

		// Key này dùng cho những game mới. Không hard code nữa
		// TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_ROLL_CALL
		// TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_DONE_TASK
		// TEXT_HISTORY_GIFT_OPENING_BTASKEE_9TH_BIRTHDAY_GAME_CAMPAIGN_REFERRAL
		newKeyLocalize := fmt.Sprintf("TEXT_HISTORY_GIFT_OPENING_%s_GAME_CAMPAIGN_%s", gameCampaign.Name, u.Action)
		if localization.VI[newKeyLocalize] != "" {
			title = localization.GetLocalizeObject(newKeyLocalize, u.Spin)
		} else {
			switch u.Action {
			case lib.USER_CAMPAIGN_ACTION_ROLL_CALL, lib.USER_CAMPAIGN_ACTION_ROLL_CALL_AT_NOEL:
				title = localization.GetLocalizeObject("TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_ROLL_CALL", u.Spin)
			case lib.USER_CAMPAIGN_ACTION_REFERRAL:
				title = localization.GetLocalizeObject("TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_SIGNUP_BY_REFERRAL", u.Spin)
			case lib.USER_CAMPAIGN_ACTION_DONE_TASK:
				title = localization.GetLocalizeObject("TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK", u.Spin)
			case lib.USER_CAMPAIGN_ACTION_DONE_TASK_REFERRAL:
				title = localization.GetLocalizeObject("TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK_REFERRAL", u.Spin)
			case lib.USER_CAMPAIGN_ACTION_DONE_TASK_FIRST:
				title = localization.GetLocalizeObject("TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_DONE_TASK_FIRST", u.Spin)
			case lib.USER_CAMPAIGN_ACTION_NEW_SIGN_UP:
				title = localization.GetLocalizeObject("TEXT_HISTORY_GIFT_OPENING_NOEL_GAME_CAMPAIGN_NEW_SIGN_UP", u.Spin)
			}
		}
		historyItem := map[string]interface{}{
			"createdAt":  u.CreatedAt,
			"title":      title,
			"icon":       config.GetPresentUnboxing().GetIconReward(),
			"background": config.GetPresentUnboxing().GetBackgroundItem(),
		}
		if config.GetPuzzleGame().GetIconReward() != "" {
			historyItem["icon"] = config.GetPuzzleGame().GetIconReward()
		}
		if config.GetPuzzleGame().GetBackgroundItem() != "" {
			historyItem["background"] = config.GetPuzzleGame().GetBackgroundItem()
		}
		historyGiftOpening = append(historyGiftOpening, historyItem)
	}

	return historyGiftOpening
}
