package getHistoryReceiveSpin

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetHistoryReceiveSpin(reqBody *model.ApiRequest) ([]map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate the request
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get game campaign gameCampaign
	gameCampaign, errCode, err := getConfigGameCampaign(reqBody.GameCampaignId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. get user game campaign
	userGameCampaign, errCode, err := getUserGameCampaign(reqBody.UserId, reqBody.GameCampaignId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 4. get history receive spin
	result := getHistoryReceiveSpin(userGameCampaign, gameCampaign)
	return result, nil, nil
}
