package getHistoryReceiveSpin

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getConfigGameCampaign(gameCampaignId string) (*modelGameCampaign.GameCampaign, *globalResponse.ResponseErrorCode, error) {
	var gameCampaign *modelGameCampaign.GameCampaign
	err := globalDataAccess.GetOneById(globalCollection.COLLECTION_GAME_CAMPAIGN[local.ISO_CODE],
		gameCampaignId,
		bson.M{
			"name":                                  1,
			"config.presentUnboxing.iconReward":     1,
			"config.presentUnboxing.backgroundItem": 1,
			"config.puzzleGame.iconReward":          1,
			"config.puzzleGame.backgroundItem":      1,
		},
		&gameCampaign,
	)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.ERROR_CAN_NOT_GET_GAME_CAMPAIGN, err
	}

	if gameCampaign == nil || gameCampaign.Config == nil {
		return nil, nil, nil
	}

	return gameCampaign, nil, nil
}
