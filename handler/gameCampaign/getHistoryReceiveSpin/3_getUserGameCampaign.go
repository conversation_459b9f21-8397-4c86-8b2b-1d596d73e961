package getHistoryReceiveSpin

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getUserGameCampaign(userId, gameCampaignId string) (*modelUserGameCampaign.UserGameCampaign, *globalResponse.ResponseErrorCode, error) {
	// 3. get user game campaign info
	var userGameCampaign *modelUserGameCampaign.UserGameCampaign
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USER_GAME_CAMPAIGN[local.ISO_CODE], bson.M{"userId": userId, "gameCampaignId": gameCampaignId}, bson.M{"accumulateSpinHistory": 1}, &userGameCampaign)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.ERROR_CAN_NOT_GET_USER_GAME_CAMPAIGN, err
	}

	if userGameCampaign == nil {
		return nil, nil, nil
	}

	return userGameCampaign, nil, nil
}
