/*
 * @File: getAllSettings.go
 * @Description: Handler function of get all settings v2 api
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package v2

import (
	"encoding/json"
	"net/http"
	"sort"
	"sync"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelCardPaymentConfig "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cardPaymentConfig"
	modelCommunitySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communitySetting"
	modelPaymentMethodCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMethodCampaign"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceGroup "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceGroup"
	modelServicePackage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/servicePackage"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSettingRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingRating"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscriptionSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionSetting"
	modelTaskerSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerSettings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get All Settings by UserId
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetAllSettings(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// 2. Check input
	errCode = validateGetAllSettings(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get iso code by lat,lng or ip
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"countryCode": 1, "phone": 1, "isoCode": 1, "isUsedZaloPay": 1, "favouriteServiceByCountry": 1, "isBlacklistByOps": 1})
	if user == nil || user.CountryCode == "" || user.Phone == "" {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	wg := &sync.WaitGroup{}
	wg.Add(1)
	var settingSubscription *modelSubscriptionSetting.SubscriptionSetting
	go func() {
		defer wg.Done()
		settingSubscription = getSubscriptionSetting()
	}()
	// Get setting rating
	var settingRating *modelSettingRating.SettingRating
	wg.Add(1)
	go func() {
		defer wg.Done()
		settingRating = getSettingRating()
	}()
	// Get asker setting
	var askerSetting map[string]interface{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		askerSetting = getAskerSetting()
	}()
	// Get card payment config
	var cardConfig *modelCardPaymentConfig.CardPaymentConfig
	wg.Add(1)
	go func() {
		defer wg.Done()
		cardConfig = getCardConfig()
	}()
	// Get tasker setting
	var taskerSetting map[string]interface{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		taskerSetting = getTaskerSetting()
	}()
	// Get settingCountry
	wsc := &sync.WaitGroup{}
	var settingCountry *modelSettingCountry.SettingCountry
	wsc.Add(1)
	go func() {
		defer wsc.Done()
		settingCountry = getSettingCountry()
	}()

	var communitySetting *modelCommunitySetting.CommunitySetting
	wsc.Add(1)
	go func() {
		defer wsc.Done()
		communitySetting = getCommunitySetting()
	}()

	// Get setting system
	isUserTester, mapSettings := getSettingSystem(user, reqBody.AppVersion)

	var paymentMethodCampaign map[string]*modelPaymentMethodCampaign.PaymentMethodCampaign
	wsc.Add(1)
	go func() {
		defer wsc.Done()
		paymentMethodCampaign = getPaymentMethodCampaign(isUserTester)
	}()

	// Get Services
	mapServices, mapServiceById, listServices := getServices(isUserTester, reqBody.AppVersion)

	wsc.Add(1)
	var mapServicePackageById map[string]*modelServicePackage.ServicePackage
	go func() {
		defer wsc.Done()
		mapServicePackageById = getServicePackage(isUserTester, reqBody.AppVersion)
	}()
	// Get paymentMethods
	wsc.Wait()
	paymentMethods := make(map[string]interface{})
	if settingCountry != nil && settingCountry.PaymentMethods != nil {
		paymentMethods = getPaymentMethods(settingCountry.PaymentMethods, reqBody.AppVersion, isUserTester, user.IsBlacklistByOps, paymentMethodCampaign, user.XId)
	}
	mapSettings["paymentMethods"] = paymentMethods
	wg.Wait()
	mapServices = refactorMapService(mapServices, mapServicePackageById)
	data := map[string]interface{}{
		"services":               mapServices,
		"serviceGroup":           getServiceGroup(mapServiceById, mapServicePackageById),
		"favouriteServices":      getFavouriteServices(user, mapServiceById, listServices, mapServicePackageById),
		"googleMapWebServiceKey": cfg.GoogleMapApiKey,
		"subscriptionSetting":    settingSubscription,
		"settingRating":          settingRating,
		"settingSystem":          mapSettings,
		"location": map[string]interface{}{
			"countryCode": user.CountryCode,
			"isoCode":     local.ISO_CODE,
		},
		"isTester":          isUserTester,
		"cardPaymentConfig": cardConfig,
		"AWS3Config":        cfg.AWS3,
		"askerSetting":      askerSetting,
		"communitySetting":  communitySetting,
		"isHaveEventConfig": checkExistRunningEventConfig(reqBody.AppVersion),
	}
	if settingCountry != nil && settingCountry.Currency != nil {
		data["currency"] = settingCountry.Currency
	}
	if taskerSetting != nil {
		data["taskerSettings"] = taskerSetting
	}
	globalResponse.ResponseSuccess(w, data)
}

func getSubscriptionSetting() *modelSubscriptionSetting.SubscriptionSetting {
	// Get setting subscription
	var settingSubscription *modelSubscriptionSetting.SubscriptionSetting
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"_id": 1, "numberFavTaskers": 1, "numberOfDaysPostTaskFromNow": 1, "defaultTaskTime": 1, "renewBefore": 1, "discount": 1}, &settingSubscription)
	if settingSubscription != nil {
		var subsDiscount []*modelSubscriptionSetting.SubscriptionSettingDiscount
		currentTime := globalLib.GetCurrentTimestamp(local.TimeZone)
		for _, discount := range settingSubscription.Discount {
			if discount.StartDate != nil && currentTime.Seconds >= discount.StartDate.Seconds &&
				discount.EndDate != nil && currentTime.Seconds <= discount.EndDate.Seconds {
				subsDiscount = append(subsDiscount, discount)
			}
		}
		settingSubscription.Discount = subsDiscount
	}
	return settingSubscription
}

func getSettingRating() *modelSettingRating.SettingRating {
	var settingRating *modelSettingRating.SettingRating
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_RATING[local.ISO_CODE], bson.M{}, bson.M{"_id": 0}, &settingRating)
	return settingRating
}

func getAskerSetting() map[string]interface{} {
	var askerSetting map[string]interface{}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_ASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{}, &askerSetting)
	return askerSetting
}

func getCardConfig() *modelCardPaymentConfig.CardPaymentConfig {
	var cardConfig *modelCardPaymentConfig.CardPaymentConfig
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_CARD_PAYMENT_CONFIG[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{}, &cardConfig)
	return cardConfig
}

func getSettingCountry() *modelSettingCountry.SettingCountry {
	var settingCountry *modelSettingCountry.SettingCountry
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"currency": 1, "paymentMethods": 1}, &settingCountry)
	return settingCountry
}

func getCommunitySetting() *modelCommunitySetting.CommunitySetting {
	var communitySetting *modelCommunitySetting.CommunitySetting
	fields := bson.M{
		"isTesting":         1,
		"postThemes":        1,
		"isDisablePostNews": 1,
	}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_COMMUNITY_SETTING[local.ISO_CODE], bson.M{}, fields, &communitySetting)

	if communitySetting == nil {
		return nil
	}

	if communitySetting.PostThemes != nil && len(communitySetting.PostThemes) > 0 {
		// Filter postThemes based on current date
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		var filteredThemes []*modelCommunitySetting.CommunitySettingPostTheme

		for _, theme := range communitySetting.PostThemes {
			// Include themes without date range
			if theme.StartDate == nil && theme.EndDate == nil {
				filteredThemes = append(filteredThemes, theme)
				continue
			}

			// Check if current date is within range
			startTime := globalLib.ParseDateFromTimeStamp(theme.StartDate, local.TimeZone)
			endTime := globalLib.ParseDateFromTimeStamp(theme.EndDate, local.TimeZone)

			if currentTime.After(startTime) && currentTime.Before(endTime) {
				filteredThemes = append(filteredThemes, theme)
			}
		}

		communitySetting.PostThemes = filteredThemes
	}
	return communitySetting
}

func getServiceGroup(mapServiceById map[string]*modelService.Service, mapServicePackageById map[string]*modelServicePackage.ServicePackage) []map[string]interface{} {
	var serviceGroup []*modelServiceGroup.ServiceGroup
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_SERVICE_GROUP[local.ISO_CODE], bson.M{}, bson.M{}, bson.M{"weight": 1}, &serviceGroup)
	var mapServiceGroup []map[string]interface{}
	for _, sg := range serviceGroup {
		group := map[string]interface{}{
			"_id":  sg.XId,
			"name": sg.Name,
			"text": sg.Text,
		}
		services := []map[string]interface{}{}
		for _, s := range sg.ServiceIds {
			if v, ok := mapServiceById[s]; ok {
				services = append(services, mapServiceData(v))
			}
		}

		for _, s := range sg.ServicePackageIds {
			if v, ok := mapServicePackageById[s]; ok {
				services = append(services, mapServicePackageData(v))
			}
		}
		group["services"] = services
		mapServiceGroup = append(mapServiceGroup, group)
	}
	return mapServiceGroup
}

func getFavouriteServices(user *modelUser.Users, mapServiceById map[string]*modelService.Service, listServices []string, mapServicePackageById map[string]*modelServicePackage.ServicePackage) []map[string]interface{} {
	var mapFavouriteServices []map[string]interface{}
	var favouriteServiceIds []string
	if user.FavouriteServiceByCountry != nil && len(user.FavouriteServiceByCountry.VN) > 0 {
		favouriteServiceIds = user.FavouriteServiceByCountry.VN
	}
	if len(favouriteServiceIds) > 0 {
		for _, fs := range favouriteServiceIds {
			if v, ok := mapServiceById[fs]; ok {
				mapFavouriteServices = append(mapFavouriteServices, mapServiceData(v))
			}

			if sp, ok := mapServicePackageById[fs]; ok {
				mapFavouriteServices = append(mapFavouriteServices, mapServicePackageData(sp))
			}
		}
		return mapFavouriteServices
	}
	for _, s := range listServices {
		if len(mapFavouriteServices) == lib.MAX_LEN_FAVOURITE_SERVICES {
			break
		}
		if v, ok := mapServiceById[s]; ok {
			if v.IsSubscription && v.Name != globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION {
				continue
			}
			mapFavouriteServices = append(mapFavouriteServices, mapServiceData(v))
		}
	}
	return mapFavouriteServices
}

func mapServiceData(service *modelService.Service) map[string]interface{} {
	serviceText := service.ShortText
	if serviceText == nil {
		serviceText = service.Text
	}
	return map[string]interface{}{
		"_id":          service.XId,
		"name":         service.Name,
		"text":         serviceText,
		"icon":         service.Icon,
		"isNewService": service.IsNewService,
	}
}

func mapServicePackageData(service *modelServicePackage.ServicePackage) map[string]interface{} {
	serviceText := service.ShortText
	if serviceText == nil {
		serviceText = service.Text
	}
	return map[string]interface{}{
		"_id":          service.XId,
		"name":         service.Name,
		"text":         serviceText,
		"icon":         service.Icon,
		"isNewService": service.IsNewService,
	}
}

/*
 * @Description: validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetAllSettings(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.AppVersion == "" {
		return &lib.ERROR_APP_VERSION_REQUIRED
	}
	return nil
}

/*
 * @Description: get setting system by isoCode
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getSettingSystem(user *modelUser.Users, appVersion string) (bool, map[string]interface{}) {
	var settings *modelSettings.Settings
	fields := bson.M{
		"paymentMethod":                   1,
		"cardPaymentConfig":               1,
		"subscriptionPaymentInstruction":  1,
		"supportPhone":                    1,
		"giftSetting":                     1,
		"rankSetting":                     1,
		"defaultTaskTime":                 1,
		"minPostTaskTime":                 1,
		"postTaskMinHours":                1,
		"postTaskMaxHours":                1,
		"timeInBetweenTask":               1,
		"taskerNotCommingFee":             1,
		"costForChooseTasker":             1,
		"cancelTaskFeeInPercentage":       1,
		"cancelTaskFee":                   1,
		"defaultDuration":                 1,
		"maxDatePostTask":                 1,
		"limitNumberCancelTask":           1,
		"enableVerifyTaskerByQR":          1,
		"listOfToolsForTaskerLocal":       1,
		"numberRatingSuggestReviewApp":    1,
		"inviteDeeplink":                  1,
		"communityGroup":                  1,
		"enableMoMo":                      1,
		"enableVtcPay":                    1,
		"zaloPay":                         1,
		"reportTaskDone":                  1,
		"tester":                          1,
		"minTopUp":                        1,
		"numberOfTaskCanSeeRatingTasker":  1,
		"cancelPrepayTaskFeeInPercentage": 1,
		"cancelPrepayTaskFeeMinimum":      1,
		"isShowCovidInfo":                 1,
		"isShowCovidOption":               1,
		"loginWith":                       1,
		"referralSetting":                 1,
		"campaignSetting":                 1,
		"headerSetting":                   1,
	}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, fields, &settings)
	mapSettings := make(map[string]interface{})
	isUserTester := false
	if settings != nil {
		if len(settings.Tester) > 0 {
			for _, v := range settings.Tester {
				if user.Phone == v {
					isUserTester = true
					break
				}
			}
		}

		// Map categories
		if settings.GiftSetting != nil && settings.GiftSetting.Category != nil && len(settings.GiftSetting.Category) > 0 {
			var newCategories []*modelSettings.SettingsGiftSettingCategory
			for _, v := range settings.GiftSetting.Category {
				if v.Status == "ACTIVE" {
					newCategories = append(newCategories, v)
				}
			}
			// Sort by weight
			if len(newCategories) > 0 {
				sort.SliceStable(newCategories, func(i, j int) bool { return newCategories[i].Weight < newCategories[j].Weight })
				settings.GiftSetting.Category = newCategories
			}
		}

		b, _ := json.Marshal(settings)
		json.Unmarshal(b, &mapSettings)
		delete(mapSettings, "tester")

		settingPayment := map[string]interface{}{
			"momo": map[string]interface{}{
				"isUsed": settings.EnableMoMo,
			},
			"atmAndInternetBanking": map[string]interface{}{
				"isUsed": settings.EnableVtcPay,
			},
		}
		if settings.ZaloPay != nil && (settings.ZaloPay.IsUsed || user.IsUsedZaloPay) && settings.ZaloPay.AskerVersion != "" && globalLib.CompareVersion(appVersion, settings.ZaloPay.AskerVersion) == 1 {
			settingPayment["zaloPay"] = map[string]interface{}{
				"isUsed": true,
			}
		}
		mapSettings["paymentMethod"] = settingPayment

		if settings.LoginWith != nil && len(settings.LoginWith) > 0 {
			var settingLoginWith []string
			for _, v := range settings.LoginWith {
				if v.Status == "ACTIVE" {
					settingLoginWith = append(settingLoginWith, v.Name)
				}
			}
			if len(settingLoginWith) > 0 {
				mapSettings["loginWith"] = settingLoginWith
			}
		}

		// Process referral setting
		if settings.ReferralSetting != nil && settings.ReferralSetting.Voucher != nil && settings.ReferralSetting.Voucher.Value > 0 {
			mapSettings["referralValue"] = settings.ReferralSetting.Voucher.Value
		}

		// Overwrite headerSetting
		mapSettings["headerSetting"] = getCurrentHeaderImage(settings)
	}
	return isUserTester, mapSettings
}

/*
 * @Description: get service list by isoCode
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getServices(isUserTester bool, appVersion string) ([]map[string]interface{}, map[string]*modelService.Service, []string) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	var services []*modelService.Service
	queryStatus := []bson.M{
		{"status": globalConstant.SERVICE_STATUS_ACTIVE},
		{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
	}
	query := bson.M{
		"$and": []bson.M{
			{
				"$or": queryStatus,
			},
			{
				"$or": []bson.M{
					{"serviceBeginAt": bson.M{"$exists": false}},
					{"serviceBeginAt": bson.M{"$lte": now}},
				},
			},
		},
		"onlyShowTasker":   bson.M{"$ne": true},
		"isTesting":        bson.M{"$ne": true},
		"isServicePackage": bson.M{"$ne": true},
	}
	if isUserTester {
		delete(query, "$and")
		query["$or"] = queryStatus
		delete(query, "isTesting")
	}

	fields := bson.M{
		"_id":                          1,
		"icon":                         1,
		"name":                         1,
		"text":                         1,
		"city":                         1,
		"status":                       1,
		"weight":                       1,
		"tip":                          1,
		"postingLimits":                1,
		"requirements":                 1,
		"detail":                       1,
		"detailAreaV3":                 1,
		"detailLaundry":                1,
		"detailCooking":                1,
		"defaultTaskTime":              1,
		"shortText":                    1,
		"workingProcessV2":             1,
		"detailSofa":                   1,
		"linkForm":                     1,
		"isNewService":                 1,
		"maximumPSI":                   1,
		"estimatedPriceDefault":        1,
		"tetBookingDates":              1,
		"minTaskOfSubscription":        1,
		"requireAskerVersion":          1,
		"disinfectionDetail":           1,
		"detailSofaTH":                 1,
		"isOpenGoMarketDefault":        1,
		"goMarketWithStore":            1,
		"estimatedAmountConfig":        1,
		"premiumOptions":               1,
		"taskServiceId":                1,
		"optional":                     1,
		"thumbnail":                    1,
		"detailService":                1,
		"relatedServiceIds":            1,
		"isSubscription":               1,
		"durationByArea":               1,
		"priceSetting.minPostTaskTime": 1,
		"ecoOptions":                   1,
		"addons":                       1,
		"monthlyOptions":               1,

		"priceSetting.costForChooseTasker": 1,
	}
	sort := bson.M{"weight": 1}
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, sort, &services)
	mapServiceInfo := make(map[string]*model.RelatedServiceInfo)
	mapServiceById := make(map[string]*modelService.Service)
	var listServices []string
	for _, service := range services {
		serviceText := service.ShortText
		if serviceText == nil {
			serviceText = service.Text
		}
		mapServiceInfo[service.XId] = &model.RelatedServiceInfo{
			XId:          service.XId,
			Name:         service.Name,
			Thumbnail:    service.Thumbnail,
			Text:         serviceText,
			IsNewService: service.IsNewService,
		}
	}
	var mapServices []map[string]interface{}
	if len(services) > 0 {
		for _, v := range services {
			if v.RequireAskerVersion != "" && globalLib.CompareVersion(appVersion, v.RequireAskerVersion) < 0 {
				continue
			}
			b, _ := json.Marshal(v)
			item := make(map[string]interface{})
			json.Unmarshal(b, &item)
			if v.City != nil && len(v.City) > 0 {
				var arrayCity []string
				var arrayCityInfo []*modelService.ServiceCity
				var limitBookTaskDates []map[string]interface{}
				for _, c := range v.City {
					arrayCity = append(arrayCity, c.Name)
					arrayCityInfo = append(arrayCityInfo, c)
					if c.LimitBookTaskDate != 0 {
						limitBookTaskDates = append(limitBookTaskDates, map[string]interface{}{
							"city":              c.Name,
							"limitBookTaskDate": c.LimitBookTaskDate,
						})
					}
				}
				if len(limitBookTaskDates) > 0 {
					item["limitBookTaskDates"] = limitBookTaskDates
				}
				if v.Name == globalConstant.SERVICE_KEY_NAME_CHILD_CARE || v.Name == globalConstant.SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION ||
					v.Name == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION || v.Name == globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING ||
					v.Name == globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE {
					item["city"] = arrayCityInfo
				} else {
					item["city"] = arrayCity
				}
			}
			if v.Tip != nil {
				item["tip"] = v.Tip.Tip
				if v.Tip.Requirements != nil && len(v.Tip.Requirements) > 0 {
					// Covid options stop working type 4,5
					var requirements []*modelService.ServiceTipRequirements
					for _, req := range v.Tip.Requirements {
						if req.Type != 4 && req.Type != 5 {
							requirements = append(requirements, req)
						}
					}
					if len(requirements) > 0 {
						item["requirements"] = requirements
					}
				}
			}
			if v.Text.En == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT {
				if v.GoMarketWithStore != nil {
					if (isUserTester && v.GoMarketWithStore.IsTesting) || v.GoMarketWithStore.Status == globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE {
						item["goMarketWithStore"] = &modelService.ServiceGoMarketWithStore{
							Status: globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE,
						}
					} else {
						delete(item, "goMarketWithStore")
					}
				}
			}
			if v.Text.En == globalConstant.SERVICE_NAME_DISINFECTION {
				if v.DisinfectionDetail != nil {
					// cities := []*modelService.ServiceDisinfectionDetailCity{}
					for _, city := range v.DisinfectionDetail.City {
						areaToShowInAppAsker := []*modelService.ServiceDisinfectionDetailCityArea{}
						for _, area := range city.Area {
							if area.IsShowInAppAsker {
								areaToShowInAppAsker = append(areaToShowInAppAsker, area)
							}
						}
						// Update new area list in each city
						// Update city.Area cũng đã update trong DisinfectionDetail. Vì city là con trỏ tới v.DisinfectionDetail.City
						city.Area = areaToShowInAppAsker
					}
				}
				item["disinfectionDetail"] = v.DisinfectionDetail
			}
			if v.Text.En == globalConstant.SERVICE_NAME_CARPET_CLEANING {
				if v.DetailService != nil && v.DetailService.CarpetCleaning != nil {
					// cities := []*modelService.ServiceDisinfectionDetailCity{}
					for _, city := range v.DetailService.CarpetCleaning.City {
						areaToShowInAppAsker := []*modelService.ServiceDetailServiceCarpetCleaningCityArea{}
						for _, area := range city.Area {
							if area.IsShowInAppAsker {
								areaToShowInAppAsker = append(areaToShowInAppAsker, area)
							}
						}
						// Update new area list in each city
						// Update city.Area cũng đã update trong CarpetCleaning. Vì city là con trỏ tới v.CarpetCleaning.City
						city.Area = areaToShowInAppAsker
					}
				}
				item["detailService"] = v.DetailService
			}

			if globalLib.IsHomeMovingServiceByKeyName(v.Name) {
				RefactorHomeMovingService(v)
				item["detailService"] = v.DetailService
			}

			if globalLib.IsBeautyCareServiceByKeyName(v.Name) {
				RefactorBeautyCareService(v, appVersion)
				item["detailService"] = v.DetailService
			}

			if v.PremiumOptions != nil && ((isUserTester && v.PremiumOptions.IsTesting) || v.PremiumOptions.Status == globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE) {
				item["premiumOptions"] = &modelService.ServicePremiumOptions{
					Status:         globalConstant.SERVICE_PREMIUM_OPTIONS_STATUS_ACTIVE,
					ApplyForCities: v.PremiumOptions.ApplyForCities,
				}
			} else {
				delete(item, "premiumOptions")
			}
			if v.TetBookingDates != nil {
				if v.TetBookingDates.MinVersion != "" && globalLib.CompareVersion(appVersion, v.TetBookingDates.MinVersion) < 0 {
					delete(item, "tetBookingDates")
				} else {
					fromDate := globalLib.ParseDateFromTimeStamp(v.TetBookingDates.FromDate, local.TimeZone)
					toDate := globalLib.ParseDateFromTimeStamp(v.TetBookingDates.ToDate, local.TimeZone)
					if !isUserTester && (v.TetBookingDates.IsTesting || now.Before(fromDate) || now.After(toDate)) {
						delete(item, "tetBookingDates")
					}
				}
			}
			relatedServices := []*model.RelatedServiceInfo{}
			for _, serviceId := range v.RelatedServiceIds {
				if relatedService, ok := mapServiceInfo[serviceId]; ok {
					relatedServices = append(relatedServices, relatedService)
				}
			}
			delete(item, "relatedServiceIds")
			if len(relatedServices) > 0 {
				item["relatedServices"] = relatedServices
			}

			// Không xoá priceSetting nữa vì có dùng field costForChooseTasker trong field này
			// delete(item, "priceSetting")
			if v.PriceSetting != nil && v.PriceSetting.MinPostTaskTime > 0 {
				item["minPostTaskTime"] = v.PriceSetting.MinPostTaskTime
			}

			if globalLib.IsHomeCleaningServiceByKeyName(v.Name) {
				item["workingProcessV2"] = v.WorkingProcessV2
			}
			listServices = append(listServices, v.XId)
			mapServiceById[v.XId] = v
			mapServices = append(mapServices, item)
		}
	}

	return mapServices, mapServiceById, listServices
}

func getPaymentMethods(paymentMethods *modelSettingCountry.SettingCountryPaymentMethods, appVersion string, isUserTester bool, isBlacklistByOps bool, paymentMethodCampaign map[string]*modelPaymentMethodCampaign.PaymentMethodCampaign, userId string) map[string]interface{} {
	paymentMethodsMap := make(map[string]interface{})
	paymentMethodsMap["topUp"] = getPaymentInfo(paymentMethods.TopUp, appVersion, isUserTester, paymentMethodCampaign, userId)
	paymentMethodsMap["subscription"] = getPaymentInfo(paymentMethods.Subscription, appVersion, isUserTester, paymentMethodCampaign, userId)

	// Only allow asker book task by CASH if user is blacklist by ops (Cannot send task to tasker)
	paymentMethodsMap["bookTask"] = []map[string]interface{}{
		{
			"name":      globalConstant.PAYMENT_METHOD_CASH,
			"status":    globalConstant.PAYMENT_METHOD_STATUS_ACTIVE,
			"isDefault": true,
		},
	}
	if !isBlacklistByOps {
		paymentMethodsMap["bookTask"] = getPaymentInfo(paymentMethods.BookTask, appVersion, isUserTester, paymentMethodCampaign, userId)
	}

	paymentMethodsMap["recharge"] = getPaymentInfo(paymentMethods.Recharge, appVersion, isUserTester, paymentMethodCampaign, userId)
	paymentMethodsMap["comboVoucher"] = getPaymentInfo(paymentMethods.ComboVoucher, appVersion, isUserTester, paymentMethodCampaign, userId)
	return paymentMethodsMap
}

func getPaymentInfo(paymentMethod []*modelSettingCountry.SettingCountryPaymentInfo, appVersion string, isUserTester bool, paymentMethodCampaign map[string]*modelPaymentMethodCampaign.PaymentMethodCampaign, userId string) []map[string]interface{} {
	var paymentMaps []map[string]interface{}
	for _, payment := range paymentMethod {
		paymentMap := make(map[string]interface{})
		if payment.IsTesting && isUserTester {
			paymentMap["name"] = payment.Name
			paymentMap["status"] = globalConstant.PAYMENT_METHOD_STATUS_ACTIVE
			paymentMap["isDefault"] = payment.IsDefault
		} else if globalLib.CompareVersion(appVersion, payment.MinVersion) > -1 && payment.Status != globalConstant.PAYMENT_METHOD_STATUS_INACTIVE {
			paymentMap["name"] = payment.Name
			paymentMap["status"] = payment.Status
			paymentMap["isDefault"] = payment.IsDefault
		}

		// If paymentMap is not exist (mean not have any fields)
		if len(paymentMap) == 0 {
			continue
		}
		if len(payment.NotApplyForServices) > 0 {
			paymentMap["notApplyForServices"] = payment.NotApplyForServices
		}
		if paymentMethodCampaign != nil && paymentMethodCampaign[payment.Name] != nil {
			paymentMap["campaign"] = paymentMethodCampaign[payment.Name]
		}

		if payment.Name == globalConstant.PAYMENT_METHOD_BPAY_BUSINESS {
			isExist, _ := modelBusinessMember.IsExist(local.ISO_CODE, bson.M{"userId": userId, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE})
			if !isExist {
				continue
			}
		}
		// Push to list to return
		paymentMaps = append(paymentMaps, paymentMap)

	}
	return paymentMaps
}

func getCurrentHeaderImage(settings *modelSettings.Settings) map[string]interface{} {
	if settings.HeaderSetting != nil && len(settings.HeaderSetting.Images) > 0 {
		now := globalLib.GetCurrentTime(local.TimeZone)
		for _, v := range settings.HeaderSetting.Images {
			if v.Date != nil && v.Date.From != nil && v.Date.To != nil {
				from := globalLib.ParseDateFromTimeStamp(v.Date.From, local.TimeZone)
				to := globalLib.ParseDateFromTimeStamp(v.Date.To, local.TimeZone)

				// from < now < to
				if now.After(from) && now.Before(to) {
					return map[string]interface{}{
						"imageHeader": v.Image,
					}
				}
			}
		}
	}
	return nil
}

func getTaskerSetting() map[string]interface{} {
	var taskerSetting *modelTaskerSettings.TaskerSettings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"avatarFrame": 1}, &taskerSetting)
	if taskerSetting == nil {
		return nil
	}

	result := make(map[string]interface{})

	// get avatarFrame if date is ok
	if taskerSetting.AvatarFrame != nil {
		now := globalLib.GetCurrentTime(local.TimeZone)
		from := globalLib.ParseDateFromTimeStamp(taskerSetting.AvatarFrame.From, local.TimeZone)
		to := globalLib.ParseDateFromTimeStamp(taskerSetting.AvatarFrame.To, local.TimeZone)
		if now.After(from) && now.Before(to) {
			result["avatarFrame"] = map[string]interface{}{
				"default": taskerSetting.AvatarFrame.Default,
				"premium": taskerSetting.AvatarFrame.Premium,
			}
		}
	}
	return result
}

func getServicePackage(isUserTester bool, appVersion string) map[string]*modelServicePackage.ServicePackage {
	mapServicePackageById := make(map[string]*modelServicePackage.ServicePackage)
	var servicePackage []*modelServicePackage.ServicePackage
	fields := bson.M{
		"_id":                  1,
		"icon":                 1,
		"name":                 1,
		"text":                 1,
		"shortText":            1,
		"services":             1,
		"isNewService":         1,
		"requireAskerVersion":  1,
		"requireTaskerVersion": 1,
		"introService":         1,
		"weight":               1,
	}
	query := bson.M{
		"status":    globalConstant.SERVICE_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}

	if isUserTester {
		delete(query, "isTesting")
	}

	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_PACKAGE[local.ISO_CODE], query, fields, &servicePackage)

	if len(servicePackage) == 0 {
		return mapServicePackageById
	}
	for _, sp := range servicePackage {
		if sp.RequireAskerVersion != "" && globalLib.CompareVersion(appVersion, sp.RequireAskerVersion) < 0 {
			continue
		}
		mapServicePackageById[sp.XId] = sp
	}
	return mapServicePackageById
}

func refactorMapService(mapServices []map[string]interface{}, mapServicePackageById map[string]*modelServicePackage.ServicePackage) []map[string]interface{} {
	newMapServices := mapServices
	for _, sp := range mapServicePackageById {
		b, _ := json.Marshal(sp)
		item := make(map[string]interface{})
		json.Unmarshal(b, &item)
		newMapServices = append(newMapServices, item)
	}

	if len(newMapServices) != len(mapServices) {
		sort.Slice(newMapServices, func(i, j int) bool {
			weightI := cast.ToFloat64(newMapServices[i]["weight"])
			weightJ := cast.ToFloat64(newMapServices[j]["weight"])
			return weightI < weightJ
		})
	}

	return newMapServices
}
