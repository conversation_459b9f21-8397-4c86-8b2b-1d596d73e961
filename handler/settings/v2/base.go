package v2

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelEventConfig"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMethodCampaign"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

var cfg = config.GetConfig()

func RefactorHomeMovingService(service *modelService.Service) {
	if service.DetailService != nil && service.DetailService.HomeMoving != nil {
		for _, city := range service.DetailService.HomeMoving.City {
			if city == nil || city.HomeType == nil {
				continue
			}
			for _, homeType := range city.HomeType {
				if homeType == nil || homeType.Type == nil {
					continue
				}
				for _, homeTypeType := range homeType.Type {
					if homeTypeType == nil {
						continue
					}
					homeTypeType.Price = 0
					homeTypeType.VehicleQuantity = 0
					homeTypeType.VehicleType = ""
					homeTypeType.PriceInBuildingByType = 0
					if homeTypeType.Options == nil {
						continue
					}
					for _, option := range homeTypeType.Options {
						if option == nil {
							continue
						}
						option.Price = 0
						option.VehicleQuantity = 0
						option.VehicleType = ""
					}
				}
			}
			if city.Furniture != nil {
				for _, furnitureType := range city.Furniture.Type {
					if furnitureType == nil || furnitureType.Options == nil {
						continue
					}
					for _, option := range furnitureType.Options {
						if option != nil && option.Options != nil {
							for _, optionOption := range option.Options {
								optionOption.Price = 0
							}
						}
					}
				}
			}
			// city.Options = nil
			for _, v1 := range city.Options {
				if v1 == nil {
					continue
				}
				v1.Prices = nil
				for _, v2 := range v1.Options {
					if v2 == nil {
						continue
					}
					v2.Prices = nil
				}
			}
			city.ExtraKm = nil
		}
	}
}

func getPaymentMethodCampaign(isTester bool) map[string]*paymentMethodCampaign.PaymentMethodCampaign {
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE,
		"isTesting": bson.M{"$ne": true},
	}
	if isTester {
		delete(query, "isTesting")
	}
	fields := bson.M{
		"_id":           1,
		"paymentMethod": 1,
		"navigateTo":    1,
		"description":   1,
	}

	paymentMethodCampaigns, err := handler.GetPaymentMethodCampaigns(query, fields)
	if err != nil {
		msg := fmt.Sprintf("[go-api-asker-vn-v3] GetPaymentMethodCampaigns error: %v", err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		return nil
	}

	result := make(map[string]*paymentMethodCampaign.PaymentMethodCampaign)
	for _, paymentMethodCampaign := range paymentMethodCampaigns {
		result[paymentMethodCampaign.PaymentMethod] = paymentMethodCampaign
	}
	return result
}

func checkExistRunningEventConfig(appVersion string) bool {
	var eventConfigs []*modelEventConfig.EventConfig
	now := globalLib.GetCurrentTime(local.TimeZone)
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_EVENT_CONFIG[local.ISO_CODE], bson.M{
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
	}, bson.M{"appVersion": 1}, &eventConfigs)

	isExist := false
	for _, eventConfig := range eventConfigs {
		if eventConfig.AppVersion == "" ||
			(eventConfig.AppVersion != "" && globalLib.CompareVersion(appVersion, eventConfig.AppVersion) >= 0) {
			isExist = true
			break
		}
	}
	return isExist
}

func RefactorBeautyCareService(service *modelService.Service, appVersion string) {
	if service.GetDetailService().GetBeautyCare() == nil {
		return
	}

	for _, v := range service.GetDetailService().GetBeautyCare().GetCity() {
		activePackages := []*modelService.ServiceDetailServiceBeautyCareCityPackage{}
		for _, p := range v.GetPackages() {
			if p.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
				// Check min asker version in package
				if p.GetMinAskerVersion() != "" && globalLib.CompareVersion(appVersion, p.GetMinAskerVersion()) < 0 {
					continue
				}
				p.MainServices = refactorBeautyCareServicePackageServices(p.GetMainServices())
				p.ExtraServices = refactorBeautyCareServicePackageServices(p.GetExtraServices())
				activePackages = append(activePackages, p)
			}
		}
		v.Packages = activePackages
	}
}

func refactorBeautyCareServicePackageServices(services []*modelService.ServiceDetailServiceBeautyCareCityPackageServices) []*modelService.ServiceDetailServiceBeautyCareCityPackageServices {
	activeServices := []*modelService.ServiceDetailServiceBeautyCareCityPackageServices{}
	for _, v := range services {
		if v.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
			activeSubOptions := []*modelService.ServiceDetailServiceBeautyCareCityPackageServicesOptions{}
			for _, sub := range v.GetOptions() {
				if sub.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
					activeSubOptions = append(activeSubOptions, sub)
				}
			}
			v.Options = activeSubOptions
			if v.GetCategories() != nil {
				var minPrice, maxPrice float64 = 0.0, 0.0
				activeStyles := []*modelService.ServiceDetailServiceBeautyCareCityPackageServicesCategoriesListStyle{}
				for _, style := range v.GetCategories().GetListStyle() {
					if style.GetStatus() == globalConstant.SERVICE_STATUS_ACTIVE {
						if minPrice == 0 || style.GetPrice() <= minPrice {
							minPrice = style.GetPrice()
						}
						if maxPrice == 0 || style.GetPrice() >= maxPrice {
							maxPrice = style.GetPrice()
						}
						activeStyles = append(activeStyles, style)
					}
				}
				v.PriceRange = &modelService.ServiceDetailServiceBeautyCareCityPackageServicesPriceRange{
					FromPrice: minPrice,
					ToPrice:   maxPrice,
				}
				v.Categories.ListStyle = activeStyles
			}
			activeServices = append(activeServices, v)
		}
	}

	return activeServices
}
