/*
 * @File: getAllSettings.go
 * @Description: Handler function of get all settings v2 api
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package v2

import (
	"encoding/json"
	"net/http"
	"sort"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelCardPaymentConfig "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cardPaymentConfig"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/communitySetting"
	modelPaymentMethodCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMethodCampaign"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServicePackage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/servicePackage"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSettingRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingRating"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscriptionSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionSetting"

	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get All Settings
 * @CreatedAt: 05/07/2021
 * @Author: ngoctb
 * @UpdatedAt:
 * @UpdatedBy:
 */
func GetAllSettingsWithoutLogin(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// 2. Check input
	errCode = validateGetAllSettingsWithoutLogin(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	wg := &sync.WaitGroup{}
	// Get setting subscription
	wg.Add(1)
	var settingSubscription *modelSubscriptionSetting.SubscriptionSetting
	go func() {
		defer wg.Done()
		settingSubscription = getSubscriptionSetting()
	}()
	// Get setting rating
	var settingRating *modelSettingRating.SettingRating
	wg.Add(1)
	go func() {
		defer wg.Done()
		settingRating = getSettingRating()
	}()
	// Get asker setting
	var askerSetting map[string]interface{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		askerSetting = getAskerSetting()
	}()
	// Get card payment config
	var cardConfig *modelCardPaymentConfig.CardPaymentConfig
	wg.Add(1)
	go func() {
		defer wg.Done()
		cardConfig = getCardConfig()
	}()
	// Get settingCountry
	var settingCountry *modelSettingCountry.SettingCountry
	wg.Add(1)
	go func() {
		defer wg.Done()
		settingCountry = getSettingCountry()
	}()
	// Get setting system
	mapSettings := make(map[string]interface{})
	wg.Add(1)
	go func() {
		defer wg.Done()
		mapSettings = getSettingSystemWithoutLogin(reqBody.AppVersion)
	}()

	var paymentMethodCampaign map[string]*modelPaymentMethodCampaign.PaymentMethodCampaign
	wg.Add(1)
	go func() {
		defer wg.Done()
		paymentMethodCampaign = getPaymentMethodCampaign(false)
	}()

	// Get Services
	var mapServices []map[string]interface{}
	mapServiceById := make(map[string]*modelService.Service)
	wg.Add(1)
	go func() {
		defer wg.Done()
		mapServices, mapServiceById = getServicesWithoutLogin(reqBody.AppVersion)
	}()

	var communitySetting *communitySetting.CommunitySetting
	wg.Add(1)
	go func() {
		defer wg.Done()
		communitySetting = getCommunitySetting()
	}()

	wg.Add(1)
	var mapServicePackageById map[string]*modelServicePackage.ServicePackage
	go func() {
		defer wg.Done()
		mapServicePackageById = getServicePackage(false, reqBody.AppVersion)
	}()
	wg.Wait()
	// Get paymentMethods
	paymentMethods := make(map[string]interface{})
	if settingCountry != nil && settingCountry.PaymentMethods != nil {
		paymentMethods = getPaymentMethodsWithoutLogin(settingCountry.PaymentMethods, reqBody.AppVersion, paymentMethodCampaign)
	}
	mapSettings["paymentMethods"] = paymentMethods
	mapServices = refactorMapService(mapServices, mapServicePackageById)
	data := map[string]interface{}{
		"services":               mapServices,
		"serviceGroup":           getServiceGroup(mapServiceById, mapServicePackageById),
		"googleMapWebServiceKey": cfg.GoogleMapApiKey,
		"subscriptionSetting":    settingSubscription,
		"settingRating":          settingRating,
		"settingSystem":          mapSettings,
		"location": map[string]interface{}{
			"isoCode": local.ISO_CODE,
		},
		"cardPaymentConfig": cardConfig,
		"AWS3Config":        cfg.AWS3,
		"askerSetting":      askerSetting,
		"communitySetting":  communitySetting,
		"isHaveEventConfig": checkExistRunningEventConfig(reqBody.AppVersion),
	}

	if settingCountry != nil && settingCountry.Currency != nil {
		data["currency"] = settingCountry.Currency
	}

	globalResponse.ResponseSuccess(w, data)
}

/*
 * @Description: validate request params
 * @CreatedAt: 05/07/2021
 * @Author: ngoctb
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetAllSettingsWithoutLogin(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.AppVersion == "" {
		return &lib.ERROR_APP_VERSION_REQUIRED
	}
	return nil
}

/*
 * @Description: get setting system
 * @CreatedAt: 05/07/2021
 * @Author: ngoctb
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getSettingSystemWithoutLogin(appVersion string) map[string]interface{} {
	var settings *modelSettings.Settings
	fields := bson.M{
		"paymentMethod":                  1,
		"cardPaymentConfig":              1,
		"subscriptionPaymentInstruction": 1,
		"supportPhone":                   1,
		"giftSetting":                    1,
		"rankSetting":                    1,
		"defaultTaskTime":                1,
		"minPostTaskTime":                1,
		"postTaskMinHours":               1,
		"postTaskMaxHours":               1,
		"timeInBetweenTask":              1,
		"taskerNotCommingFee":            1,
		"costForChooseTasker":            1,
		"cancelTaskFeeInPercentage":      1,
		"cancelTaskFee":                  1,
		"defaultDuration":                1,
		"maxDatePostTask":                1,
		"limitNumberCancelTask":          1,
		"enableVerifyTaskerByQR":         1,
		"listOfToolsForTaskerLocal":      1,
		"numberRatingSuggestReviewApp":   1,
		"inviteDeeplink":                 1,
		"communityGroup":                 1,
		"enableMoMo":                     1,
		"enableVtcPay":                   1,
		"zaloPay":                        1,
		"reportTaskDone":                 1,
		"minTopUp":                       1,
		"numberOfTaskCanSeeRatingTasker": 1,
		"isShowCovidInfo":                1,
		"isShowCovidOption":              1,
		"loginWith":                      1,
		"referralSetting":                1,
		"campaignSetting":                1,
		"headerSetting":                  1,
	}
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, fields, &settings)
	mapSettings := make(map[string]interface{})
	if settings != nil {
		// Map categories
		if settings.GiftSetting != nil && settings.GiftSetting.Category != nil && len(settings.GiftSetting.Category) > 0 {
			var newCategories []*modelSettings.SettingsGiftSettingCategory
			for _, v := range settings.GiftSetting.Category {
				if v.Status == "ACTIVE" {
					newCategories = append(newCategories, v)
				}
			}
			// Sort by weight
			if len(newCategories) > 0 {
				sort.SliceStable(newCategories, func(i, j int) bool { return newCategories[i].Weight < newCategories[j].Weight })
				settings.GiftSetting.Category = newCategories
			}
		}

		b, _ := json.Marshal(settings)
		json.Unmarshal(b, &mapSettings)
		delete(mapSettings, "tester")

		settingPayment := map[string]interface{}{
			"momo": map[string]interface{}{
				"isUsed": settings.EnableMoMo,
			},
			"atmAndInternetBanking": map[string]interface{}{
				"isUsed": settings.EnableVtcPay,
			},
		}
		if settings.ZaloPay != nil && settings.ZaloPay.IsUsed && settings.ZaloPay.AskerVersion != "" && globalLib.CompareVersion(appVersion, settings.ZaloPay.AskerVersion) == 1 {
			settingPayment["zaloPay"] = map[string]interface{}{
				"isUsed": true,
			}
		}
		mapSettings["paymentMethod"] = settingPayment

		if settings.LoginWith != nil && len(settings.LoginWith) > 0 {
			var settingLoginWith []string
			for _, v := range settings.LoginWith {
				if v.Status == "ACTIVE" {
					settingLoginWith = append(settingLoginWith, v.Name)
				}
			}
			if len(settingLoginWith) > 0 {
				mapSettings["loginWith"] = settingLoginWith
			}
		}

		// Overwrite headerSetting
		mapSettings["headerSetting"] = getCurrentHeaderImage(settings)
	}

	if settings != nil && settings.ReferralSetting != nil && settings.ReferralSetting.Voucher != nil && settings.ReferralSetting.Voucher.Value > 0 {
		mapSettings["referralValue"] = settings.ReferralSetting.Voucher.Value
	}

	return mapSettings
}

/*
 * @Description: get service list
 * @CreatedAt: 05/07/2021
 * @Author: ngoctb
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getServicesWithoutLogin(appVersion string) ([]map[string]interface{}, map[string]*modelService.Service) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	var services []*modelService.Service
	query := bson.M{
		"$and": []bson.M{
			{
				"$or": []bson.M{
					{"status": globalConstant.SERVICE_STATUS_ACTIVE},
					{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isSubscription": true},
				},
			},
			{
				"$or": []bson.M{
					{"serviceBeginAt": bson.M{"$exists": false}},
					{"serviceBeginAt": bson.M{"$lte": now}},
				},
			},
		},
		"onlyShowTasker":   bson.M{"$ne": true},
		"isTesting":        bson.M{"$ne": true},
		"isServicePackage": bson.M{"$ne": true},
	}

	fields := bson.M{
		"_id":                              1,
		"icon":                             1,
		"name":                             1,
		"text":                             1,
		"city":                             1,
		"status":                           1,
		"weight":                           1,
		"tip":                              1,
		"postingLimits":                    1,
		"requirements":                     1,
		"detail":                           1,
		"detailAreaV3":                     1,
		"detailLaundry":                    1,
		"detailCooking":                    1,
		"defaultTaskTime":                  1,
		"shortText":                        1,
		"workingProcessV2":                 1,
		"detailSofa":                       1,
		"linkForm":                         1,
		"isNewService":                     1,
		"maximumPSI":                       1,
		"estimatedPriceDefault":            1,
		"tetBookingDates":                  1,
		"minTaskOfSubscription":            1,
		"requireAskerVersion":              1,
		"disinfectionDetail":               1,
		"detailSofaTH":                     1,
		"isOpenGoMarketDefault":            1,
		"goMarketWithStore":                1,
		"estimatedAmountConfig":            1,
		"premiumOptions":                   1,
		"taskServiceId":                    1,
		"detailChildCare":                  1,
		"detailService":                    1,
		"optional":                         1,
		"isSubscription":                   1,
		"durationByArea":                   1,
		"priceSetting.minPostTaskTime":     1,
		"addons":                           1,
		"monthlyOptions":                   1,
		"priceSetting.costForChooseTasker": 1,
	}

	sort := bson.M{"weight": 1}
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, sort, &services)
	var mapServices []map[string]interface{}
	mapServiceById := make(map[string]*modelService.Service)
	if len(services) > 0 {
		for _, v := range services {
			if v.RequireAskerVersion != "" && globalLib.CompareVersion(appVersion, v.RequireAskerVersion) < 0 {
				continue
			}
			b, _ := json.Marshal(v)
			item := make(map[string]interface{})
			json.Unmarshal(b, &item)
			if v.City != nil && len(v.City) > 0 {
				var arrayCity []string
				var arrayCityInfo []*modelService.ServiceCity
				var limitBookTaskDates []map[string]interface{}
				for _, c := range v.City {
					arrayCity = append(arrayCity, c.Name)
					arrayCityInfo = append(arrayCityInfo, c)
					if c.LimitBookTaskDate != 0 {
						limitBookTaskDates = append(limitBookTaskDates, map[string]interface{}{
							"city":              c.Name,
							"limitBookTaskDate": c.LimitBookTaskDate,
						})
					}
				}
				if len(limitBookTaskDates) > 0 {
					item["limitBookTaskDates"] = limitBookTaskDates
				}
				if v.Name == globalConstant.SERVICE_KEY_NAME_CHILD_CARE || v.Name == globalConstant.SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION ||
					v.Name == globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING || v.Name == globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE {
					item["city"] = arrayCityInfo
				} else {
					item["city"] = arrayCity
				}
			}
			if v.Tip != nil {
				item["tip"] = v.Tip.Tip
				if v.Tip.Requirements != nil && len(v.Tip.Requirements) > 0 {
					// Covid options stop working type 4,5
					var requirements []*modelService.ServiceTipRequirements
					for _, req := range v.Tip.Requirements {
						if req.Type != 4 && req.Type != 5 {
							requirements = append(requirements, req)
						}
					}
					if len(requirements) > 0 {
						item["requirements"] = requirements
					}
				}
			}
			if v.Text.En == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT {
				if v.GoMarketWithStore != nil {
					if v.GoMarketWithStore.Status == globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE {
						item["goMarketWithStore"] = &modelService.ServiceGoMarketWithStore{
							Status: globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE,
						}
					} else {
						delete(item, "goMarketWithStore")
					}
				}
			}
			if v.Text.En == globalConstant.SERVICE_NAME_DISINFECTION {
				if v.DisinfectionDetail != nil {
					// cities := []*modelService.ServiceDisinfectionDetailCity{}
					for _, city := range v.DisinfectionDetail.City {
						areaToShowInAppAsker := []*modelService.ServiceDisinfectionDetailCityArea{}
						for _, area := range city.Area {
							if area.IsShowInAppAsker {
								areaToShowInAppAsker = append(areaToShowInAppAsker, area)
							}
						}
						// Update new area list in each city
						// Update city.Area cũng đã update trong DisinfectionDetail. Vì city là con trỏ tới v.DisinfectionDetail.City
						city.Area = areaToShowInAppAsker
					}
				}
				item["disinfectionDetail"] = v.DisinfectionDetail
			}
			if v.Text.En == globalConstant.SERVICE_NAME_CARPET_CLEANING {
				if v.DetailService != nil && v.DetailService.CarpetCleaning != nil {
					// cities := []*modelService.ServiceDisinfectionDetailCity{}
					for _, city := range v.DetailService.CarpetCleaning.City {
						areaToShowInAppAsker := []*modelService.ServiceDetailServiceCarpetCleaningCityArea{}
						for _, area := range city.Area {
							if area.IsShowInAppAsker {
								areaToShowInAppAsker = append(areaToShowInAppAsker, area)
							}
						}
						// Update new area list in each city
						// Update city.Area cũng đã update trong CarpetCleaning. Vì city là con trỏ tới v.CarpetCleaning.City
						city.Area = areaToShowInAppAsker
					}
				}
				item["detailService"] = v.DetailService
			}

			// Home moving
			if globalLib.IsHomeMovingServiceByKeyName(v.Name) {
				RefactorHomeMovingService(v)
				item["detailService"] = v.DetailService
			}

			if globalLib.IsBeautyCareServiceByKeyName(v.Name) {
				RefactorBeautyCareService(v, appVersion)
				item["detailService"] = v.DetailService
			}

			if v.PremiumOptions != nil && v.PremiumOptions.Status == globalConstant.GO_MARKET_WITH_STORE_STATUS_ACTIVE {
				item["premiumOptions"] = &modelService.ServicePremiumOptions{
					Status:         globalConstant.SERVICE_PREMIUM_OPTIONS_STATUS_ACTIVE,
					ApplyForCities: v.PremiumOptions.ApplyForCities,
				}
			} else {
				delete(item, "premiumOptions")
			}

			// Không xoá priceSetting nữa vì có dùng field costForChooseTasker trong field này
			// delete(item, "priceSetting")
			if v.PriceSetting != nil && v.PriceSetting.MinPostTaskTime > 0 {
				item["minPostTaskTime"] = v.PriceSetting.MinPostTaskTime
			}
			// if v.TetBookingDates != nil && v.TetBookingDates.MinVersion != "" && globalLib.CompareVersion(appVersion, v.TetBookingDates.MinVersion) < 0 {
			// 	delete(item, "tetBookingDates")
			// }
			if v.TetBookingDates != nil {
				if v.TetBookingDates.MinVersion != "" && globalLib.CompareVersion(appVersion, v.TetBookingDates.MinVersion) < 0 {
					delete(item, "tetBookingDates")
				} else {
					fromDate := globalLib.ParseDateFromTimeStamp(v.TetBookingDates.FromDate, local.TimeZone)
					toDate := globalLib.ParseDateFromTimeStamp(v.TetBookingDates.ToDate, local.TimeZone)
					if v.TetBookingDates.IsTesting || now.Before(fromDate) || now.After(toDate) {
						delete(item, "tetBookingDates")
					}
				}
			}
			if globalLib.IsHomeCleaningServiceByKeyName(v.Name) {
				item["workingProcessV2"] = v.WorkingProcessV2
			}
			mapServiceById[v.XId] = v
			mapServices = append(mapServices, item)
		}
	}
	return mapServices, mapServiceById
}

func getPaymentMethodsWithoutLogin(paymentMethods *modelSettingCountry.SettingCountryPaymentMethods, appVersion string, paymentMethodCampaign map[string]*modelPaymentMethodCampaign.PaymentMethodCampaign) map[string]interface{} {
	paymentMethodsMap := make(map[string]interface{})
	paymentMethodsMap["topUp"] = getPaymentInfoWithoutLogin(paymentMethods.TopUp, appVersion, paymentMethodCampaign)
	paymentMethodsMap["subscription"] = getPaymentInfoWithoutLogin(paymentMethods.Subscription, appVersion, paymentMethodCampaign)
	paymentMethodsMap["bookTask"] = getPaymentInfoWithoutLogin(paymentMethods.BookTask, appVersion, paymentMethodCampaign)
	paymentMethodsMap["recharge"] = getPaymentInfoWithoutLogin(paymentMethods.Recharge, appVersion, paymentMethodCampaign)
	paymentMethodsMap["comboVoucher"] = getPaymentInfoWithoutLogin(paymentMethods.ComboVoucher, appVersion, paymentMethodCampaign)
	return paymentMethodsMap
}

func getPaymentInfoWithoutLogin(paymentMethod []*modelSettingCountry.SettingCountryPaymentInfo, appVersion string, paymentMethodCampaign map[string]*modelPaymentMethodCampaign.PaymentMethodCampaign) []map[string]interface{} {
	var paymentMaps []map[string]interface{}
	for _, payment := range paymentMethod {
		paymentMap := make(map[string]interface{})
		if globalLib.CompareVersion(appVersion, payment.MinVersion) > -1 && payment.Status != globalConstant.PAYMENT_METHOD_STATUS_INACTIVE && !payment.IsTesting {
			paymentMap["name"] = payment.Name
			paymentMap["status"] = payment.Status
			paymentMap["isDefault"] = payment.IsDefault

			// Check if payment method has campaign in this time
			if paymentMethodCampaign != nil && paymentMethodCampaign[payment.Name] != nil {
				paymentMap["campaign"] = paymentMethodCampaign[payment.Name]
			}
			if len(payment.NotApplyForServices) > 0 {
				paymentMap["notApplyForServices"] = payment.NotApplyForServices
			}
			// Append to result list
			paymentMaps = append(paymentMaps, paymentMap)
		}
	}
	return paymentMaps
}
