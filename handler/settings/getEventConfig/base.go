package getEventConfig

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetEventConfig(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get  event config
	eventConfig, errCode, err := getEventConfig(reqBody)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. return
	return map[string]interface{}{
		"name":           eventConfig.Name,
		"activeServices": eventConfig.ActiveServices,
		"appConfig":      eventConfig.AppConfig,
		"paymentMethods": eventConfig.PaymentMethods,
	}, nil, nil
}
