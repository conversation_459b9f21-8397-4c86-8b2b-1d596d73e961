package getEventConfig

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelEventConfig"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"

	"go.mongodb.org/mongo-driver/bson"
)

func getEventConfig(reqBody *model.ApiRequest) (*modelEventConfig.EventConfig, *globalResponse.ResponseErrorCode, error) {
	var eventConfigs []*modelEventConfig.EventConfig
	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
		"status":    globalConstant.EVENT_CONFIG_STATUS_ACTIVE,
	}
	if reqBody.EventId != "" {
		query = bson.M{
			"_id": reqBody.EventId,
		}
	}
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_EVENT_CONFIG[local.ISO_CODE], query, bson.M{"name": 1, "appVersion": 1, "activeServices": 1, "appConfig": 1, "paymentMethods": 1}, bson.M{"createdAt": -1}, &eventConfigs)

	var usingEventConfig *modelEventConfig.EventConfig
	for _, eventConfig := range eventConfigs {
		if eventConfig.AppVersion == "" ||
			(eventConfig.AppVersion != "" && globalLib.CompareVersion(reqBody.AppVersion, eventConfig.AppVersion) >= 0) {
			usingEventConfig = eventConfig
			break
		}
	}
	return usingEventConfig, nil, nil
}
