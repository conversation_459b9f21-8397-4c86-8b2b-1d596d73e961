/*
 * @File: getAllSettings.go
 * @Description: Handler function of get all settings v2 api
 * @CreatedAt: 23/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package getPartnerMiniAppSettings

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetPartnerMiniAppSettings(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. Check input
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get partner setting
	partnerSetting, err := getPartnerSetting(reqBody.PartnerCode)
	if partnerSetting == nil || err != nil {
		return nil, &lib.ERROR_PARTNER_NOT_FOUND, err
	}

	// 3. get list service for partner
	services, err := getServices(partnerSetting.IsoCode, partnerSetting.ServiceIds)
	if err != nil {
		return nil, &lib.ERROR_SERVICE_NOT_FOUND, err
	}

	// 4. get settingCountry
	settingCountry, err := getSettingCountry(partnerSetting.IsoCode)
	if err != nil {
		return nil, &lib.ERROR_SETTING_COUNTRY_NOT_FOUND, err
	}

	// 5. get settingSystem
	settingSystem, _ := getSettingSystem(partnerSetting.IsoCode)

	// Return to client
	result := map[string]interface{}{
		"countryCode":    partnerSetting.CountryCode,
		"isoCode":        partnerSetting.IsoCode,
		"language":       partnerSetting.Language,
		"services":       services,
		"settingCountry": settingCountry,
		"settingSystem":  settingSystem,
	}
	return result, nil, nil
}
