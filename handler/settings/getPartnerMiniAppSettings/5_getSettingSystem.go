package getPartnerMiniAppSettings

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	"go.mongodb.org/mongo-driver/bson"
)

func getSettingSystem(isoCode string) (*modelSettings.Settings, error) {
	var settings *modelSettings.Settings
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"minPostTaskTime": 1}, &settings)
	return settings, err
}
