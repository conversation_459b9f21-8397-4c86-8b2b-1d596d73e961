package getPartnerMiniAppSettings

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	"go.mongodb.org/mongo-driver/bson"
)

func getSettingCountry(isoCode string) (*modelSettingCountry.SettingCountry, error) {
	var settingCountry *modelSettingCountry.SettingCountry
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": isoCode}, bson.M{"city": 1, "currency": 1}, &settingCountry)
	return settingCountry, err
}
