package getPartnerMiniAppSettings

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

func getServices(isoCode string, serviceIds []string) ([]*modelService.Service, error) {
	query := bson.M{
		"_id":    bson.M{"$in": serviceIds},
		"status": globalConstant.SERVICE_STATUS_ACTIVE,
	}
	fields := bson.M{
		"_id":              1,
		"name":             1,
		"text":             1,
		"detail":           1,
		"city":             1,
		"icon":             1,
		"shortText":        1,
		"postingLimits":    1,
		"detailAreaV3":     1,
		"workingProcessV2": 1,
		"detailSofa":       1,
		"detailSofaTH":     1,
	}
	var services []*modelService.Service
	err := globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, bson.M{"weight": 1}, &services)

	for _, service := range services {
		if service.ShortText != nil {
			service.Text = service.ShortText
			service.ShortText = nil
		}
	}

	return services, err
}
