package getPartnerMiniAppSettings

import (
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/partnerMiniAppSettings"
	"go.mongodb.org/mongo-driver/bson"
)

func getPartnerSetting(partnerCode string) (*partnerMiniAppSettings.PartnerMiniAppSettings, error) {
	var partnerSetting *partnerMiniAppSettings.PartnerMiniAppSettings
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PARTNER_MINI_APP_SETTINGS,
		bson.M{"partnerCode": partnerCode},
		bson.M{"isoCode": 1, "countryCode": 1, "language": 1, "serviceIds": 1},
		&partnerSetting,
	)
	return partnerSetting, err
}
