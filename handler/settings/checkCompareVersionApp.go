/*
 * @File: checkCompareVersionApp.go
 * @Description: <PERSON>le, parse api params for CheckCompareVersionApp
 * @CreatedAt: 02/03/2021
 * @Author: vinhnt
 */
package settings

import (
	"encoding/json"
	"math"
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Check version app
 * @CreatedAt: 02/03/2021
 * @Author: vinhnt
 */
func CheckCompareVersionApp(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	validData := validateDataCheckCompareVersionApp(reqBody)
	if validData != nil {
		local.Logger.Warn(validData.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *validData)
		return
	}
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"versionAsker": 1}, &settings)
	//Get platform Info
	platformInfo := getPlatformInfo(reqBody.Platform, settings)
	//Get current version
	currentVersion := platformInfo["version"].(string)
	forcedVersion, hasForcedVersion := platformInfo["forcedVersion"].(string)
	isGreaterVersion := versionCompare(reqBody.Version, currentVersion)
	if hasForcedVersion {
		// Check forced version
		isGreaterForcedVersion := versionCompare(reqBody.Version, forcedVersion)
		if isGreaterForcedVersion == -1 {
			platformInfo["isForce"] = true
			delete(platformInfo, "forcedVersion")
			globalResponse.ResponseSuccess(w, platformInfo)
			return
		} else if (isGreaterForcedVersion == 0 || isGreaterForcedVersion == 1) && isGreaterVersion == -1 {
			platformInfo["isForce"] = false
			delete(platformInfo, "forcedVersion")
			globalResponse.ResponseSuccess(w, platformInfo)
			return
		}
	} else {
		if isGreaterVersion == -1 {
			globalResponse.ResponseSuccess(w, platformInfo)
			return
		}
	}

	globalResponse.ResponseSuccess(w, nil)
}

func validateDataCheckCompareVersionApp(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.Version == "" {
		return &lib.ERROR_VERSION_REQUIRED
	}
	if reqBody.Platform == "" {
		return &lib.ERROR_PLATFORM_REQUIRED
	}
	if reqBody.Platform != globalConstant.SETTINGS_PLATFORM_ANDROID && reqBody.Platform != globalConstant.SETTINGS_PLATFORM_IOS {
		return &lib.ERROR_PLATFORM_INVALID
	}
	return nil
}

func getPlatformInfo(platform string, settings *modelSettings.Settings) map[string]interface{} {
	platformInfoMap := make(map[string]interface{})
	if platform == globalConstant.SETTINGS_PLATFORM_IOS {
		platformInfo := settings.VersionAsker.Ios
		b, _ := json.Marshal(platformInfo)
		json.Unmarshal(b, &platformInfoMap)
	} else {
		platformInfo := settings.VersionAsker.Android
		b, _ := json.Marshal(platformInfo)
		json.Unmarshal(b, &platformInfoMap)
	}
	return platformInfoMap
}

/*
Returns:

	-1 = left is LOWER than right
	0 = they are equal
	1 = left is GREATER = right is LOWER
*/
func versionCompare(oldVersion, currentVersion string) int {
	a := strings.Split(oldVersion, ".")
	b := strings.Split(currentVersion, ".")
	length := int(math.Max(float64(len(a)), float64(len(b))))
	for i := 0; i < length; i++ {
		aToInt, _ := strconv.Atoi(a[i])
		bToInt, _ := strconv.Atoi(b[i])
		if (a[i] != "" && b[i] == "" && aToInt > 0) || (aToInt > bToInt) {
			return 1
		} else if (b[i] != "" && a[i] == "" && bToInt > 0) || (aToInt < bToInt) {
			return -1
		}
	}
	return 0
}
