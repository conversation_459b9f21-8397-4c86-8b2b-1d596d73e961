/*
 * @File: getSupportCity.go
 * @Description: <PERSON><PERSON>, parse api params for GetSupportCity
 * @CreatedAt: 13/07/2021
 * @Author: vinhnt
 */
package settings

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
* @Description: GetSupportCity
* @CreatedAt: 13/07/2021
* @Author: vinhnt
 */
func GetSupportCity(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.ISOCode == "" {
		local.Logger.Warn(lib.ERROR_ISO_CODE_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ISO_CODE_REQUIRED)
		return
	}

	// Get settingCountry
	var settingCountry *modelSettingCountry.SettingCountry
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": reqBody.ISOCode}, bson.M{"city": 1}, &settingCountry)
	result := []*modelSettingCountry.SettingCountryCity{}
	for _, city := range settingCountry.City {
		if city.Status != "INACTIVE" {
			result = append(result, city)
		}
	}
	globalResponse.ResponseSuccess(w, result)
}
