/*
 * @File: getAllSettings.go
 * @Description: Handler function of get all settings api version 1
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package settings

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSettingRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingRating"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	subscriptionSettingModel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionSetting"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Get All Settings by UserId
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetAllSettings(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// 2. Check input
	errCode = validateGetAllSettings(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get iso code by lat,lng or ip
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"countryCode": 1, "phone": 1, "isoCode": 1})
	if user == nil || user.CountryCode == "" || user.Phone == "" {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	isoCode := user.IsoCode

	// Get Services
	services := getServices(isoCode)

	// Get setting header
	// settingHeader, _ := repoSettingHeader.New().GetOneByQuery(bson.M{"isoCode": isoCode}, bson.M{"_id": 0, "headerImageLink": 1})

	// Get setting subscription
	var settingSubscription *subscriptionSettingModel.SubscriptionSetting
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"_id": 0, "numberFavTaskers": 1}, &settingSubscription)

	// Get setting rating
	var settingRating *modelSettingRating.SettingRating
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_RATING[local.ISO_CODE], bson.M{}, bson.M{"_id": 0}, &settingRating)

	// Get setting system
	isUserTester, settings := getSettingSystems(isoCode, user)

	// // 3. Get setting languages, country codes
	// settingApp, _ := repoSettingApp.New().Get(bson.M{"languages": 1, "countryCodes": 1})
	// var newLanguages []*modelSettingApp.SettingAppLanguages
	// var newCountryCodes []*modelSettingApp.SettingAppCountryCodes
	// if settingApp != nil && settingApp.Languages != nil {
	// 	for _, v := range settingApp.Languages {
	// 		if v.MinVersion == "" || globalLib.CompareVersion(v.MinVersion, reqBody.AppVersion) >= 0 {
	// 			newLanguages = append(newLanguages, v)
	// 		}
	// 	}
	// }

	// if settingApp != nil && settingApp.CountryCodes != nil {
	// 	for _, v := range settingApp.CountryCodes {
	// 		if v.MinVersion == "" || globalLib.CompareVersion(v.MinVersion, reqBody.AppVersion) >= 0 {
	// 			newCountryCodes = append(newCountryCodes, v)
	// 		}
	// 	}
	// }

	// Get currency
	var settingCountry *modelSettingCountry.SettingCountry
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": isoCode}, bson.M{}, &settingCountry)

	// cardPaymentConfig, err := repoCardPaymentConfig.New().GetCardPaymentConfig()

	data := map[string]interface{}{
		"services":               services,
		"googleMapWebServiceKey": cfg.GoogleMapApiKey,
		// "settingHeader":          settingHeader,
		"settingSubscription": settingSubscription,
		"settingRating":       settingRating,
		"settingSystem":       settings,
		// "languages":              newLanguages,
		// "countryCodes":           newCountryCodes,
		// "cardPaymentConfig":   cardPaymentConfig,
		"location": map[string]interface{}{
			"countryCode": user.CountryCode,
			"isoCode":     isoCode,
		},
		"isTester": isUserTester,
	}
	if settingCountry != nil && settingCountry.Currency != nil {
		data["currency"] = settingCountry.Currency
	}

	globalResponse.ResponseSuccess(w, data)
}

/*
 * @Description: validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetAllSettings(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.AppVersion == "" {
		return &lib.ERROR_APP_VERSION_REQUIRED
	}
	return nil
}

/*
 * @Description: get list service by iso code
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getServices(isoCode string) []*modelService.Service {
	var services []*modelService.Service
	query := bson.M{
		"$or": []bson.M{
			{"status": globalConstant.SERVICE_STATUS_ACTIVE},
			{"status": globalConstant.SERVICE_STATUS_INACTIVE, "isTesting": true},
		},
		"onlyShowTasker": bson.M{"$ne": true},
	}
	fields := bson.M{"_id": 1, "minTaskOfSubscription": 1, "icon": 1, "name": 1, "text": 1, "city": 1, "postingLimits": 1, "minutesPostTaskAfterNow": 1, "listOfToolsForTasker": 1, "workingProcess": 1, "isTesting": 1, "isNewService": 1, "tip": 1, "priceSetting": 1, "detail": 1, "detailAreaV3": 1, "detailLaundry": 1, "detailCooking": 1, "limitTimePostTask": 1, "tetBookingDates": 1, "pauseSetting": 1, "linkForm": 1, "limitDateOfBooking": 1, "addons": 1}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &services)
	return services
}

/*
 * @Description: get setting systems by isoCode and check if user is tester
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func getSettingSystems(isoCode string, user *modelUser.Users) (bool, *modelSettings.Settings) {
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"_id": 0}, &settings)
	isUserTester := false
	if settings != nil && len(settings.Tester) > 0 {
		for _, v := range settings.Tester {
			if user.Phone == v {
				isUserTester = true
				break
			}
		}
	}
	return isUserTester, settings
}
