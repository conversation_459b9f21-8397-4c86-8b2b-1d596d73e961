/*
 * @File: getChatListChatMessage.go
 * @Description: Handler function of get chat list chat message api
 * @CreatedAt: 02/03/2021
 * @Author: ngoctb3
 * @UpdatedAt: 23/03/2021
 * @UpdatedBy: ngoctb3
 */

package chatMessage

import (
	"sort"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

/*
 * @Description: Get List Message in Chat Message
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func GetChatListChatMessage(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// Validate data
	resErr := validateGetChatListChatMessage(reqBody)
	if resErr != nil {
		return nil, resErr, nil
	}

	// Get chat by chatId
	if reqBody.ChatId != "" {
		return getChatById(reqBody.ChatId)
	}

	fileds := bson.M{
		"_id":                  1,
		"date":                 1,
		"address":              1,
		"acceptedTasker":       1,
		"status":               1,
		"duration":             1,
		"contactName":          1,
		"serviceText":          1,
		"serviceName":          1,
		"collectionDate":       1,
		"askerId":              1,
		"detailHomeMoving":     1,
		"phone":                1,
		"startWorking.isStart": 1,
		"serviceId":            1,
		"isoCode":              1,
	}
	// Get chat by taskId, userId
	taskData, err := handler.GetTask(bson.M{"_id": reqBody.TaskId, "askerId": reqBody.UserId}, fileds)
	// Check permission get list chat
	if !hasPermissionGetListChat(taskData) {
		return nil, &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT, err
	}
	// Check task status
	// if taskData.Status != globalConstant.TASK_STATUS_CONFIRMED {
	// 	globalResponse.ResponseError(w, lib.ERROR_CONVERSATION_DONE)
	// 	return
	// }
	// Get tasker info
	taskerInfo, resErr := GetTaskerInfo(taskData)
	if resErr != nil {
		return nil, resErr, nil
	}
	// Get chat messages
	chatMessage, err := GetChatMessages(taskData, reqBody.UserId, taskerInfo)
	if chatMessage == nil || err != nil {
		return nil, &lib.SYSTEM_ERROR, err
	}
	result := map[string]interface{}{
		"_id":        chatMessage.XId,
		"taskerInfo": taskerInfo,
		"task": map[string]interface{}{
			"_id":              taskData.XId,
			"date":             taskData.Date,
			"address":          taskData.Address,
			"serviceText":      taskData.ServiceText,
			"duration":         taskData.Duration,
			"status":           taskData.Status,
			"serviceName":      taskData.ServiceName,
			"homeMovingDetail": GetHomeMovingDetail(taskData),
		},
		"messages": chatMessage.Messages,
	}
	return result, nil, nil
}

/*
 * @Description: validateGetChatListChatMessage
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateGetChatListChatMessage(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ChatId != "" {
		return nil
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	return nil
}

/*
 * @Description: GetChatMessages or create if not exists
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetChatMessages(task *modelTask.Task, askerId string, tasker map[string]interface{}) (*modelChatMessage.ChatMessage, error) {
	query := bson.M{"taskId": task.XId, "askerId": askerId}
	fields := bson.M{"_id": 1, "messages": 1}
	chatMessage, err := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, query, fields)
	if chatMessage == nil && err == mongo.ErrNoDocuments {
		now_ts := globalLib.GetCurrentTimestamp(local.TimeZone)
		message := &modelChatMessage.ChatMessageMessages{
			XId:  globalLib.GenerateObjectId(),
			From: globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
			MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
				Title:  localization.GetLocalizeObject("CHAT_MESSAGE_PREPARED_MESSAGE_TITLE", task.ContactName),
				Text:   localization.GetLocalizeObject("CHAT_MESSAGE_PREPARED_MESSAGE"),
				SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER,
			},
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		}

		chatMessage = &modelChatMessage.ChatMessage{
			XId:        globalLib.GenerateObjectId(),
			TaskerName: tasker["name"].(string),
			TaskerId:   tasker["_id"].(string),
			TaskId:     task.XId,
			AskerName:  task.ContactName,
			AskerId:    askerId,
			Messages:   []*modelChatMessage.ChatMessageMessages{message},
			CreatedAt:  now_ts,
		}

		err := pkgChatMessage.CreateChatConversation(local.ISO_CODE, chatMessage)
		if err != nil {
			return nil, err
		}

		err = pkgChatMessage.SendMessageToConversation(local.ISO_CODE, chatMessage.XId, []*modelChatMessage.ChatMessageMessages{message})
		if err != nil {
			return nil, err
		}
	}
	if chatMessage == nil {
		return nil, err
	}
	chatMessage.Messages = getMessagesByChatId(chatMessage.XId)
	return chatMessage, nil
}

func getMessagesByChatId(chatId string) []*modelChatMessage.ChatMessageMessages {
	messages, _ := pkgChatMessage.GetChatMessages(
		local.ISO_CODE,
		bson.M{
			"chatId": chatId,
			"$or": []bson.M{
				{"messageBySystem.sendTo": bson.M{"$in": []string{globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER, globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_BOTH}}},
				{"messageBySystem": bson.M{"$exists": false}},
			},
		},
		bson.M{},
	)
	return messages
}

/*
 * @Description: get tasker info
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetTaskerInfo(taskData *modelTask.Task) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	taskerInfo := make(map[string]interface{})
	//Set takerInfo default
	taskerInfo["_id"] = taskData.AcceptedTasker[0].TaskerId
	taskerInfo["avatar"] = taskData.AcceptedTasker[0].Avatar
	taskerInfo["name"] = taskData.AcceptedTasker[0].Name
	taskerInfo["language"] = globalConstant.LANG_EN
	// Case multiple AcceptedTasker
	for _, v := range taskData.AcceptedTasker {
		if v.IsLeader {
			taskerInfo["_id"] = v.TaskerId
			taskerInfo["avatar"] = v.Avatar
			taskerInfo["name"] = v.Name
			break
		}
	}
	//Set data taskerInfo CompanyId
	if taskData.AcceptedTasker[0].CompanyId != "" {
		companyData, _ := modelUser.GetOneById(local.ISO_CODE, taskData.AcceptedTasker[0].CompanyId, bson.M{"name": 1, "avatar": 1, "language": 1, "gender": 1})
		if companyData == nil {
			return nil, &lib.ERROR_COMPANY_NOT_FOUND
		}
		taskerInfo["_id"] = companyData.XId
		taskerInfo["name"] = companyData.Name
		taskerInfo["avatar"] = companyData.Avatar
		taskerInfo["employeeName"] = taskData.AcceptedTasker[0].Name
		taskerInfo["employeeAvatar"] = taskData.AcceptedTasker[0].Avatar
		taskerInfo["language"] = companyData.Language
		taskerInfo["gender"] = companyData.Gender
	} else {
		tasker, _ := modelUser.GetOneById(local.ISO_CODE, taskerInfo["_id"].(string), bson.M{"language": 1, "gender": 1})
		if tasker != nil && tasker.Language != "" {
			taskerInfo["language"] = tasker.Language
			taskerInfo["gender"] = tasker.Gender
		}
	}

	return taskerInfo, nil
}

/*
 * @Description: Check if user can get list chat
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func hasPermissionGetListChat(taskData *modelTask.Task) bool {
	if taskData == nil {
		return false
	}
	taskDate := globalLib.ParseDateFromTimeStamp(taskData.Date, local.TimeZone)
	return lib.IsAskerCanSendChat(taskData.Status, taskDate, float64(taskData.Duration), taskData.ServiceName)
}

/*
 * @Description: Get chat by _id
 * @CreatedAt: 15/04/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */

func getChatById(chatId string) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	chatMessage, err := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, bson.M{"_id": chatId}, bson.M{"_id": 1, "taskId": 1, "members": 1})
	if chatMessage == nil {
		return nil, &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, err
	}

	var taskData *modelTask.Task
	globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"_id": chatMessage.TaskId,
		},
		bson.M{"_id": 1, "date": 1, "address": 1, "acceptedTasker": 1},
		&taskData,
	)

	taskerInfo, resErr := GetTaskerInfo(taskData)
	if resErr != nil {
		return nil, resErr, nil
	}

	result := map[string]interface{}{
		"_id":        chatMessage.XId,
		"taskerInfo": taskerInfo,
		"task": map[string]interface{}{
			"_id":     taskData.XId,
			"date":    taskData.Date,
			"address": taskData.Address,
		},
		"messages": getMessagesByChatId(chatId),
	}
	return result, nil, nil
}

func GetHomeMovingDetail(task *modelTask.Task) map[string]interface{} {
	if task.ServiceName != globalConstant.SERVICE_KEY_NAME_HOME_MOVING {
		return nil
	}
	var fromAddress, toAddress string
	var stepInProgress *modelService.ServiceDetailServiceHomeMovingHomeMovingProcess
	var isStart bool
	if task.StartWorking != nil {
		isStart = task.StartWorking.IsStart
	}
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], task.ServiceId, bson.M{"name": 1, "detailService.homeMoving.homeMovingProcess": 1}, &service)
	homeMovingProcess := []map[string]interface{}{}
	if service != nil {
		if service.Name == globalConstant.SERVICE_KEY_NAME_HOME_MOVING && service.DetailService != nil && service.DetailService.HomeMoving != nil {
			if len(service.DetailService.HomeMoving.HomeMovingProcess) > 0 && task.DetailHomeMoving != nil {
				if task.DetailHomeMoving.OldHomeDetail != nil && task.DetailHomeMoving.OldHomeDetail.AddressDetail != nil {
					fromAddress = task.DetailHomeMoving.OldHomeDetail.AddressDetail.Address
				}
				if task.DetailHomeMoving.NewHomeDetail != nil && task.DetailHomeMoving.NewHomeDetail.AddressDetail != nil {
					toAddress = task.DetailHomeMoving.NewHomeDetail.AddressDetail.Address
				}
				completedStep := task.DetailHomeMoving.CompletedStep
				for _, item := range service.DetailService.HomeMoving.HomeMovingProcess {
					stepInfo := map[string]interface{}{
						"step":        item.Step,
						"title":       item.Title,
						"description": item.Description,
					}
					if isStart {
						if item.Step <= completedStep {
							stepInfo["status"] = globalConstant.HOME_MOVING_STEP_STATUS_COMPLETED
						} else if item.Step == completedStep+1 {
							stepInfo["status"] = globalConstant.HOME_MOVING_STEP_STATUS_IN_PROGRESS
							stepInProgress = item
						}
					}
					homeMovingProcess = append(homeMovingProcess, stepInfo)
				}
				sort.Slice(homeMovingProcess, func(i, j int) bool {
					return cast.ToInt32(homeMovingProcess[i]["step"]) < cast.ToInt32(homeMovingProcess[j]["step"])
				})
			}
		}
	}
	return map[string]interface{}{
		"homeMovingProcess": homeMovingProcess,
		"fromAddress":       fromAddress,
		"toAddress":         toAddress,
		"stepInProgress":    stepInProgress,
	}
}
