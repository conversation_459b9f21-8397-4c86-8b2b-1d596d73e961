package getListChatV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListChatV2(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. Validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	asker, errCode, err := getFavTasker(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, err
	}
	// 2. Get task comfirmed by User
	taskMap, taskIds, taskerIds, serviceNames := getTasks(reqBody.UserId)

	//  3. Get list chat by taskId
	listChat, _ := getListChat(reqBody.UserId, taskIds, int64(reqBody.Page), int64(reqBody.Limit))
	if len(listChat) == 0 {
		return nil, nil, nil
	}
	mapMessageByChatId, _ := getListMessagesByChatIds(listChat)

	// 4. Get Taskers
	mapTaskerInfoById := getTaskers(taskerIds)

	// 5. Combine task + chat info by taskId
	result := combineChatInformation(asker, taskMap, listChat, mapTaskerInfoById, mapMessageByChatId, serviceNames)
	return result, nil, nil
}
