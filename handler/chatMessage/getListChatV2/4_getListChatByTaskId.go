package getListChatV2

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func getListChat(userId string, taskIds []string, page, limit int64) ([]*modelChatMessage.ChatMessage, error) {
	orConditions := []bson.M{
		{
			"taskId":      bson.M{"$exists": false},
			"members._id": userId,
			"lastMessage": bson.M{"$exists": true},
		},
		{
			"taskId":      "",
			"members._id": userId,
			"lastMessage": bson.M{"$exists": true},
		},
	}
	if len(taskIds) > 0 {
		orConditions = append(orConditions, bson.M{
			"taskId": bson.M{"$in": taskIds},
		})
	}

	query := bson.M{
		"$or":                                  orConditions,
		fmt.Sprintf("archivedData.%s", userId): bson.M{"$ne": true},
	}
	fields := bson.M{
		"_id":         1,
		"taskId":      1,
		"askerName":   1,
		"taskerId":    1,
		"taskerName":  1,
		"createdAt":   1,
		"members":     1,
		"messages":    1,
		"lastMessage": 1,
		"version":     1,
	}
	return pkgChatMessage.GetChatMessagesPagingSort(
		local.ISO_CODE,
		query,
		fields,
		page,
		limit,
		bson.M{"lastMessage.createdAt": -1},
	)
}
