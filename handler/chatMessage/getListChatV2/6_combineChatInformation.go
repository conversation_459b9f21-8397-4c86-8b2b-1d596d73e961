package getListChatV2

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func combineChatInformation(asker *modelUser.Users, taskMap map[string]*modelTask.Task, listChat []*modelChatMessage.ChatMessage, mapTaskerInfoById map[string]TaskerInfo, mapMessageByChatId map[string][]*modelChatMessage.ChatMessageMessages, serviceNames []string) []map[string]interface{} {
	mapFavTasker := make(map[string]struct{})
	for _, id := range asker.FavouriteTasker {
		mapFavTasker[id] = struct{}{}
	}
	result := []map[string]interface{}{}
	mapIconByServiceName := mapIconByServiceName(serviceNames)
	for _, v := range listChat {
		if v == nil {
			continue
		}
		v.Messages = mapMessageByChatId[v.XId]
		isRead, countUnreadMessage := CountUnreadMessage(asker.XId, v)
		_, isFavouriteTasker := mapFavTasker[v.TaskerId]
		chatInfo := map[string]interface{}{
			"taskId":             v.TaskId,
			"chatId":             v.XId,
			"askerName":          v.AskerName,
			"taskerId":           v.TaskerId,
			"taskerName":         v.TaskerName,
			"taskerAvatar":       mapTaskerInfoById[v.TaskerId].Avatar,
			"isPremiumTasker":    mapTaskerInfoById[v.TaskerId].IsPremiumTasker,
			"isFavouriteTasker":  isFavouriteTasker,
			"isRead":             isRead,
			"countUnreadMessage": countUnreadMessage,
			"createdAt":          globalLib.ParseDateFromTimeStamp(v.CreatedAt, local.TimeZone),
			"members":            v.Members,
		}
		// Add task info to response
		dataTask, ok := taskMap[v.TaskId]
		if ok {
			chatInfo["serviceText"] = dataTask.ServiceText
			chatInfo["date"] = globalLib.ParseDateFromTimeStamp(dataTask.Date, local.TimeZone)
			chatInfo["duration"] = dataTask.Duration
			chatInfo["serviceName"] = dataTask.ServiceName
			chatInfo["taskPlace"] = dataTask.TaskPlace
			chatInfo["serviceIcon"] = mapIconByServiceName[dataTask.GetServiceName()]
		}
		lang := globalConstant.LANG_EN
		if asker.Language != "" {
			lang = asker.Language
		}
		var lastMessage *modelChatMessage.ChatMessageMessages
		if v.LastMessage != nil {
			lastMessage = v.LastMessage
		}
		if lastMessage != nil {
			lastChatMessageInfo := map[string]interface{}{
				"userName":  lastMessage.UserName,
				"from":      lastMessage.From,
				"message":   lastMessage.Message,
				"createdAt": lastMessage.CreatedAt,
			}
			if lastMessage.VideoSummary != nil {
				lastChatMessageInfo["videoSummary"] = lastMessage.VideoSummary
			}
			if lastMessage.Message == "" {
				if lastMessage.Image != "" {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_IMAGE")
				} else if lastMessage.Video != "" || lastMessage.VideoSummary != nil {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_VIDEO")
				} else if lastMessage.Location != nil {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_LOCATION")
				} else if lastMessage.From == globalConstant.CHAT_MESSAGE_FROM_SYSTEM {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_BY_SYSTEM")
				} else if lastMessage.Type == globalConstant.CHAT_MESSAGE_REQUEST_TYPE_INCREASE_DURATION {
					lastChatMessageInfo["message"] = localization.T(lang, "REQUEST_INCREASE_DURATION")
				} else if lastMessage.Type == globalConstant.CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DATE_TIME {
					lastChatMessageInfo["message"] = localization.T(lang, "REQUEST_UPDATE_DATE_TIME")
				} else if lastMessage.Type == globalConstant.CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DETAIL {
					lastChatMessageInfo["message"] = localization.T(lang, "REQUEST_UPDATE_DETAIL")
				} else if lastMessage.CallData != nil {
					if lastMessage.CallData.Status == globalConstant.CALL_STATUS_SUCCESS {
						lastChatMessageInfo["message"] = localization.T(lang, "VOICE_CALL")
					} else if lastMessage.CallData.Status == globalConstant.CALL_STATUS_MISSED {
						lastChatMessageInfo["message"] = localization.T(lang, "MISSED_CALL")
					}
				}
			}
			chatInfo["lastChatMessage"] = lastChatMessageInfo
			chatInfo["lastMessageAt"] = globalLib.ParseDateFromTimeStamp(v.LastMessage.CreatedAt, local.TimeZone)
		}
		result = append(result, chatInfo)
	}
	return result
}

func CountUnreadMessage(userId string, chatMessage *modelChatMessage.ChatMessage) (bool, int) {
	query := bson.M{
		"chatId":                         chatMessage.XId,
		"userId":                         bson.M{"$ne": userId},
		fmt.Sprintf("status.%s", userId): bson.M{"$exists": false},
		"isRead":                         bson.M{"$ne": true},
	}
	countUnreadMessage, _ := pkgChatMessage.CountMessagesByQuery(local.ISO_CODE, query)
	return countUnreadMessage == 0, int(countUnreadMessage)
}

func mapIconByServiceName(serviceNames []string) map[string]string {
	services, _ := handler.GetServices(bson.M{"name": bson.M{"$in": serviceNames}}, bson.M{"name": 1, "icon": 1})
	mapIconByServiceName := make(map[string]string)
	for _, service := range services {
		mapIconByServiceName[service.GetName()] = service.GetIcon()
	}
	return mapIconByServiceName
}
