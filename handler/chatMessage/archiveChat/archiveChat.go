package archiveChat

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func archiveChat(userId, chatId string) (*globalResponse.ResponseErrorCode, error) {
	pkgChatMessage.UpdateChatConversation(
		local.ISO_CODE,
		bson.M{"_id": chatId},
		bson.M{
			"$set": bson.M{
				fmt.Sprintf("archivedData.%s", userId): true,
			},
		},
	)
	return nil, nil
}
