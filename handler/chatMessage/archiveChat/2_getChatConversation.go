package archiveChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getChatConversation(chatId string) (*chatMessage.ChatMessage, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": chatId}
	fields := bson.M{"_id": 1, "members": 1, "taskId": 1}
	chatMessage, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if chatMessage == nil {
		return nil, &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, nil
	}
	return chatMessage, nil, nil
}
