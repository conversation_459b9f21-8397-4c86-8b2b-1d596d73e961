package archiveChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

// Allow to archive chat by chatId
func ArchiveChat(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. Get chat
	chat, errCode, err := getChatConversation(reqBody.ChatId)
	if errCode != nil {
		return errCode, err
	}

	// 3. Check permission
	errCode = checkPermission(reqBody.UserId, chat)
	if errCode != nil {
		return errCode, nil
	}

	// 4. Archive chat
	return archiveChat(reqBody.UserId, reqBody.ChatId)
}
