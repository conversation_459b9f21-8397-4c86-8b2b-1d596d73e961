package deleteChat

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func deleteChat(userId, chatId string) (*globalResponse.ResponseErrorCode, error) {
	pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{"chatId": chatId},
		bson.M{
			"$set": bson.M{
				fmt.Sprintf("status.%s", userId): "deleted", // To get message from this time for user
			},
		},
	)
	pkgChatMessage.UpdateChatConversation(
		local.ISO_CODE,
		bson.M{"_id": chatId},
		bson.M{
			"$set": bson.M{
				fmt.Sprintf("archivedData.%s", userId): true, // To hide chat for user in list chat messages
			},
		},
	)
	return nil, nil
}
