package deleteChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
)

func checkPermission(userId string, chatMessage *modelChatMessage.ChatMessage) *globalResponse.ResponseErrorCode {
	// Only can remove chat message of 2 user (NOT TASK'S CHAT)
	if chatMessage.TaskId != "" {
		return &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT
	}
	for _, v := range chatMessage.Members {
		if v.XId == userId {
			return nil
		}
	}
	return &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT
}
