package deleteChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

// Allow to archive chat by chatId
func DeleteChat(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. Get chat
	chatMessage, errCode, err := getChatMessage(reqBody.ChatId)
	if errCode != nil {
		return errCode, err
	}

	// 3. Check permission
	errCode = checkPermission(reqBody.UserId, chatMessage)
	if errCode != nil {
		return errCode, nil
	}

	// 4. Archive chat
	return deleteChat(reqBody.UserId, reqBody.ChatId)
}
