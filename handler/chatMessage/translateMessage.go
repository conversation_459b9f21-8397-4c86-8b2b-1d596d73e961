/*
 * @File: translateMessage.go
 * @Description: Handler function for api translate message
 * @CreatedAt: 02/03/2021
 * @Author: ngoctb3
 */
package chatMessage

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Translate Message
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func TranslateMessage(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	resErr := validateTranslateMessage(reqBody)
	if resErr != nil {
		local.Logger.Warn(resErr.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *resErr)
		return
	}

	// get translate from google
	result, err := globalLib.TranslateFromGoogle(reqBody.Text, reqBody.Language, cfg.CloudTranslationAPI)
	if err != nil {
		message := fmt.Sprintf("Error TranslateFromGoogle. api: /translate-message. err: %s", err.Error())
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], "bTaskee System", message)
	}

	if result != nil && result["translatedText"] != nil && result["translatedText"].(string) != "" {
		updateTranslatedTestToMessage(reqBody.ChatId, reqBody.MessageId, result)
	}
	response := map[string]interface{}{
		"translatedText": result["translatedText"],
		"messageId":      reqBody.MessageId,
	}
	globalResponse.ResponseSuccess(w, response)
}

func updateTranslatedTestToMessage(chatId string, messageId string, result map[string]interface{}) {
	// update chat message
	pkgChatMessage.UpdateChatMessage(
		local.ISO_CODE,
		bson.M{"chatId": chatId, "_id": messageId},
		bson.M{
			"$set": bson.M{
				"translatedText": result["translatedText"].(string),
				"translatedAt":   globalLib.GetCurrentTime(local.TimeZone),
			},
		},
	)
}

/*
 * @Description: Validate api params
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateTranslateMessage(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ChatId == "" {
		return &lib.ERROR_CHAT_ID_REQUIRED
	}
	if reqBody.Text == "" {
		return &lib.ERROR_TEXT_REQUIRED
	}
	if reqBody.MessageId == "" {
		return &lib.ERROR_MESSAGE_ID_REQUIRED
	}
	return nil
}
