package setIsReadChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func SetIsReadChatMessage(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode
	}

	// Set data
	errCode = updateChatMessage(reqBody.UserId, reqBody.ChatId)
	if errCode != nil {
		return errCode
	}
	return nil
}
