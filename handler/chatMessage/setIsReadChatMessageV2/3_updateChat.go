package setIsReadChatMessageV2

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func updateChatMessage(userId string, chatId string) *globalResponse.ResponseErrorCode {
	_, err := pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{
			"chatId":                         chatId,
			fmt.Sprintf("status.%s", userId): bson.M{"$exists": false},
		},
		bson.M{
			"$set": bson.M{fmt.Sprintf("status.%s", userId): "seen"},
		},
	)
	if err != nil {
		return &lib.SYSTEM_ERROR
	}

	_, err = pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{
			"chatId":                      chatId,
			"from":                        bson.M{"$in": []string{globalConstant.CHAT_MESSAGE_FROM_SYSTEM, globalConstant.CHAT_MESSAGE_FROM_ASKER}},
			"messageBySystem":             bson.M{"$exists": true},
			"messageBySystem.isAskerRead": bson.M{"$ne": true},
		},
		bson.M{"$set": bson.M{"messageBySystem.isAskerRead": true}},
	)
	if err != nil {
		return &lib.SYSTEM_ERROR
	}
	return nil
}
