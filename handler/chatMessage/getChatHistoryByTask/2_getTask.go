package getChatHistoryByTask

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTask(taskId string) (*modelTask.Task, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": taskId}
	fields := bson.M{
		"_id":                      1,
		"address":                  1,
		"status":                   1,
		"date":                     1,
		"duration":                 1,
		"acceptedTasker.taskerId":  1,
		"acceptedTasker.avatar":    1,
		"acceptedTasker.name":      1,
		"acceptedTasker.companyId": 1,
		"askerId":                  1,
		"detailHomeMoving":         1,
		"startWorking.isStart":     1,
		"serviceName":              1,
		"serviceId":                1,
		"isoCode":                  1,
	}
	task, err := handler.GetTask(query, fields)
	if task == nil || err != nil {
		return nil, &lib.ERROR_TASK_NOT_FOUND, err
	}
	return task, nil, nil
}
