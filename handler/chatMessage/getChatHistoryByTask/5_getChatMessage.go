package getChatHistoryByTask

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

func getChatMessage(task *modelTask.Task, tasker map[string]interface{}) (*modelChatMessage.ChatMessage, error) {
	return chatMessage.GetChatMessages(task, task.AskerId, tasker)
}
