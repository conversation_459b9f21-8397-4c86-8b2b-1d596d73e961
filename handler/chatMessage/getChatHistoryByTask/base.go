package getChatHistoryByTask

import (
	chatMessagePackage "gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetChatHistoryByTask(reqBody *model.ApiRequest) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 2. get task
	task, errCode, err := getTask(reqBody.TaskId)
	if errCode != nil {
		return nil, errCode, err
	}

	// 3. check permission
	if !isAskerHasPermission(task) {
		return nil, &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT, nil
	}

	// 4. get tasker info
	taskerInfo, errCode := getTaskerInfo(task)
	if errCode != nil {
		return nil, errCode, nil
	}

	// 5. get chatMessage
	chatMessage, err := getChatMessage(task, taskerInfo)
	if chatMessage == nil {
		return nil, &lib.SYSTEM_ERROR, err
	}

	// Remap chat message for asker
	language := lib.GetUserLanguage(reqBody.UserId)
	pkgChatMessage.MapListMessageForAsker(local.ISO_CODE, language, chatMessage.Messages)

	// 6. return chat message
	result := map[string]interface{}{
		"_id":        chatMessage.XId,
		"taskerInfo": taskerInfo,
		"task": map[string]interface{}{
			"_id":              task.XId,
			"date":             task.Date,
			"address":          task.Address,
			"homeMovingDetail": chatMessagePackage.GetHomeMovingDetail(task),
			"serviceName":      task.ServiceName,
		},
		"messages": chatMessage.Messages,
	}
	return result, nil, nil
}
