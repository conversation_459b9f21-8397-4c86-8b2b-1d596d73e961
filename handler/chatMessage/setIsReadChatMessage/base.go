package setIsReadChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func SetIsReadChatMessage(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode
	}

	// Set data
	err := updateChatMessage(reqBody.ChatId)
	if err != nil {
		return &lib.SYSTEM_ERROR
	}
	return nil
}
