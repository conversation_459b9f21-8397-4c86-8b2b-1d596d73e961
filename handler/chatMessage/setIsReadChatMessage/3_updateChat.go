package setIsReadChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func updateChatMessage(chatId string) error {
	_, err := pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{
			"chatId": chatId,
			"isRead": bson.M{"$ne": true},
			"from":   globalConstant.USER_TYPE_TASKER,
		},
		bson.M{"$set": bson.M{"isRead": true}},
	)
	if err != nil {
		return err
	}

	_, err = pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{
			"chatId":                      chatId,
			"from":                        bson.M{"$in": []string{globalConstant.CHAT_MESSAGE_FROM_SYSTEM, globalConstant.CHAT_MESSAGE_FROM_ASKER}},
			"messageBySystem":             bson.M{"$exists": true},
			"messageBySystem.isAskerRead": bson.M{"$ne": true},
		},
		bson.M{"$set": bson.M{"messageBySystem.isAskerRead": true, "isRead": true}},
	)
	return err
}
