package sendChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func SendChatMessageV2(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. get chat message
	chatMessage, receivers, errCode, err := getChatMessage(reqBody.ChatId, reqBody.UserId)
	if errCode != nil {
		return &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, err
	}

	if chatMessage.TaskId != "" {
		// 3. getTask
		task, err := getTask(chatMessage.TaskId)
		if task == nil || err != nil {
			return &lib.ERROR_TASK_NOT_FOUND, err
		}

		// 4. check if user can send chat message
		if !isTimeValid(task) {
			return &lib.ERROR_NOT_HAVE_PERMISSION_SEND_CHAT, nil
		}
	} else {
		// Check with chat create for members
		isInMembers := false
		for _, v := range chatMessage.Members {
			if v.XId == reqBody.UserId {
				isInMembers = true
				break
			}
		}
		if !isInMembers {
			return &lib.ERROR_NOT_HAVE_PERMISSION_SEND_CHAT, nil
		}
	}

	// 4. update chat message
	errCode, err = updateChatMessage(reqBody)
	if errCode != nil {
		return errCode, err
	}

	// 5. send notification
	go sendNotification(reqBody, chatMessage, receivers)

	// 6. send socket
	go sendWebsocket(reqBody, receivers)

	return nil, nil
}
