package sendChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getChatMessage(chatId, userId string) (*modelChatMessage.ChatMessage, []*kafkaEvent.ConversationMessageReceiver, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": chatId}
	fields := bson.M{"_id": 1, "taskId": 1, "members": 1, "taskerId": 1}
	chat, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, nil, &lib.SYSTEM_ERROR, err
	}
	if chat == nil {
		return nil, nil, &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, nil
	}
	receivers := []*kafkaEvent.ConversationMessageReceiver{}
	if len(chat.Members) > 0 {
		for _, v := range chat.Members {
			if v.XId == userId {
				continue
			}
			receivers = append(receivers, &kafkaEvent.ConversationMessageReceiver{
				UserId: v.XId,
				Type:   v.Type,
			})
		}
	} else {
		receivers = append(receivers, &kafkaEvent.ConversationMessageReceiver{
			UserId: chat.TaskerId,
			Type:   globalConstant.USER_TYPE_TASKER,
		})
	}
	return chat, receivers, nil, nil
}
