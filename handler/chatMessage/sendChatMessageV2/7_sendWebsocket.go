package sendChatMessageV2

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/grpcProxy/grpcChatServer"
	modelChatMesage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
)

func sendWebsocket(reqBody *model.ApiRequest, receivers []*kafkaEvent.ConversationMessageReceiver) error {
	var message *modelChatMesage.ChatMessageMessages
	data, _ := json.Marshal(reqBody.Data)
	json.Unmarshal(data, &message)
	grpcChatServer.SendSocketChatMessage(local.ISO_CODE, "", cfg.GRPC_Chat_Server_VN_V3_URL, reqBody.ChatId, receivers, message, nil)
	return nil
}
