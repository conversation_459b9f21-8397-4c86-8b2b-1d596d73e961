package sendChatMessageV2

import (
	"encoding/json"
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func sendNotification(reqBody *model.ApiRequest, chatMessage *modelChatMessage.ChatMessage, receivers []*kafkaEvent.ConversationMessageReceiver) {
	title := &modelService.ServiceText{}
	body := localization.GetLocalizeObject("CHAT_NEW_MESSAGE")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       28,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
	}
	if chatMessage != nil {
		payload.ChatId = chatMessage.XId
		payload.TaskId = chatMessage.TaskId
	}

	receiverIds := []string{}
	for _, v := range receivers {
		receiverIds = append(receiverIds, v.UserId)
	}

	users, err := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": receiverIds}}, bson.M{"_id": 1, "language": 1})
	if err != nil && len(users) == 0 {
		data, _ := json.Marshal(reqBody.Data)
		message := fmt.Sprintf("Send chat message ERROR: Cannot get user from database. data:%s, error:%v", string(data), err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{}
	for _, user := range users {
		userIds = append(userIds, &modelPushNotificationRequest.PushNotificationRequestUserIds{
			UserId: user.XId, Language: user.Language,
		})
	}
	lib.SendNotification([]interface{}{}, userIds, title, body, payload, "")
}
