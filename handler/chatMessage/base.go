/*
 * @File: base.go
 * @Description: base function for chat message api
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
package chatMessage

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

var cfg = config.GetConfig()

// =======================================
/*
 * @Description: Add Message To CHAT_MESSAGE
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 17/12/2020
 * @UpdatedBy: vinhnt
 */
func addToChat(chatId, message, userId, sendFrom string) {
	// Update the exists chat
	query := bson.M{"_id": chatId}
	fields := bson.M{"askerId": 1, "taskerId": 1, "taskId": 1}
	chatMessage, _ := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if chatMessage != nil {
		tasker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"_id": 1, "name": 1, "language": 1})
		if tasker == nil {
			return
		}
		asker, _ := modelUser.GetOneById(local.ISO_CODE, chatMessage.AskerId, bson.M{"_id": 1, "name": 1, "language": 1})
		if asker == nil {
			return
		}
		userName := tasker.Name
		userId := tasker.XId
		if sendFrom == globalConstant.USER_TYPE_ASKER {
			userId = asker.XId
			userName = asker.Name
		}

		messageData := &modelChatMessage.ChatMessageMessages{
			From:      globalConstant.USER_TYPE_TASKER,
			Message:   message,
			UserId:    userId,
			UserName:  userName,
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		}
		pkgChatMessage.SendMessageToConversation(local.ISO_CODE, chatId, []*modelChatMessage.ChatMessageMessages{messageData})
		// Send notification to received user.
		sendNewMessageNotification(tasker, asker, chatMessage.TaskId, chatId)
	}
}

/*
 * @Description: Send Message Notification
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 02/02/2021
 * @UpdatedBy: ngoctb3
 */
func sendNewMessageNotification(tasker, asker *modelUser.Users, taskId, chatId string) {
	lang := globalConstant.LANG_EN
	if asker.Language != "" {
		lang = asker.Language
	}
	var listNotification []interface{}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      asker.XId,
		TaskId:      taskId,
		Type:        28,
		Description: localization.T(lang, "CHAT_NEW_MESSAGE"),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		Url:         fmt.Sprintf("/asker/chatConversation?taskId=%s&chatToUserId=%s&type=ASKER", taskId, tasker.XId),
	}
	listNotification = append(listNotification, notify)
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "CHAT_NEW_MESSAGE_FROM", tasker.Name),
		En: localization.T("en", "CHAT_NEW_MESSAGE_FROM", tasker.Name),
		Ko: localization.T("ko", "CHAT_NEW_MESSAGE_FROM", tasker.Name),
		Th: localization.T("th", "CHAT_NEW_MESSAGE_FROM", tasker.Name),
	}
	body := &modelService.ServiceText{
		Vi: localization.T("vi", "CHAT_NEW_MESSAGE"),
		En: localization.T("en", "CHAT_NEW_MESSAGE"),
		Ko: localization.T("ko", "CHAT_NEW_MESSAGE"),
		Th: localization.T("th", "CHAT_NEW_MESSAGE"),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       28,
		ChatId:     chatId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: asker.XId, Language: lang},
	}
	lib.SendNotification(listNotification, userIds, title, body, payload, "")
}
