/*
 * @File: getChatHistory.go
 * @Description: handler function of getChatHistory api
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
package chatMessage

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

/*
 * @Description: GetChatHistory (CHAT_MESSAGE)
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func GetChatHistory(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetChatHistory(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get chat data
	results := []map[string]interface{}{}
	chat, errCode, err := getChatHistory(reqBody.TaskId)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	if chat == nil {
		globalResponse.ResponseSuccess(w, results)
		return
	}
	result := map[string]interface{}{
		"messages":   chat.Messages,
		"taskerName": chat.TaskerName,
		"askerName":  chat.AskerName,
	}
	results = append(results, result)
	if len(chat.ChangedHistory) > 0 {
		for i := range chat.ChangedHistory {
			result := map[string]interface{}{
				"messages":   chat.ChangedHistory[len(chat.ChangedHistory)-(i+1)].Messages,
				"taskerName": chat.ChangedHistory[len(chat.ChangedHistory)-(i+1)].OldTaskerName,
				"askerName":  chat.AskerName,
			}
			results = append(results, result)
		}
	}
	globalResponse.ResponseSuccess(w, results)
}

/*
 * @Description: validate GetChatHistory
 * @CreatedAt: 29/10/2021
 * @Author: vinhnt
 */
func validateGetChatHistory(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	// Minhle confirmed that this field is always required
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	return nil
}

func getChatHistory(taskId string) (*modelChatMessage.ChatMessage, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"taskId": taskId}
	fields := bson.M{}
	chat, err := pkgChatMessage.GetChatConversationWithMessages(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if chat != nil {
		return chat, nil, nil
	}

	// Case chat == nil -> Find in history
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_HISTORY_CHAT_MESSAGES[local.ISO_CODE], bson.M{"taskId": taskId}, bson.M{}, &chat)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if chat != nil {
		return chat, nil, nil
	}

	return nil, nil, nil
}
