package loadMoreMessages

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: validate
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ChatId == "" {
		return &lib.ERROR_CHAT_ID_REQUIRED
	}
	return nil
}
