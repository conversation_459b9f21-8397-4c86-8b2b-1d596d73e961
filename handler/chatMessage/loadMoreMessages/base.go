/*
 * @File: getChatListChatMessage.go
 * @Description: Handler function of get chat list chat message api
 * @CreatedAt: 02/03/2021
 * @Author: ngoctb3
 * @UpdatedAt: 23/03/2021
 * @UpdatedBy: ngoctb3
 */

package loadMoreMessages

import (
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: Get List Message in Chat Message
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func LoadMoreMessages(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// Validate data
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}

	if reqBody.Limit == 0 {
		reqBody.Limit = 10
	}

	language := globalConstant.LANG_VI
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		language = lib.GetUserLanguage(reqBody.UserId)
	}()

	// Get chat
	chatMessage, errCode, err := getChatMessage(reqBody.ChatId)
	if errCode != nil {
		return nil, errCode, err
	}

	// Check permission
	errCode = checkPermission(reqBody.UserId, chatMessage)
	if errCode != nil {
		return nil, errCode, nil
	}

	// Get message of chat
	wg.Wait()
	messages := getMessages(language, reqBody.UserId, chatMessage, int64(reqBody.Skip), int64(reqBody.Limit))

	// Init data for a chat info
	result := map[string]interface{}{
		"_id":      chatMessage.XId,
		"messages": messages,
	}
	return result, nil, nil
}
