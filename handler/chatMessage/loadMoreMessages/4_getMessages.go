package loadMoreMessages

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func getMessages(language, userId string, chatMessage *modelChatMessage.ChatMessage, skip, limit int64) []*modelChatMessage.ChatMessageMessages {
	query := bson.M{
		"chatId":                         chatMessage.XId,
		fmt.Sprintf("status.%s", userId): bson.M{"$ne": "deleted"},
		"$or": []bson.M{
			{"messageBySystem": bson.M{"$exists": false}},
			{"messageBySystem.sendTo": bson.M{"$in": []string{globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER, globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_BOTH}}},
		},
	}
	fields := bson.M{}
	sort := bson.M{"createdAt": -1}
	messages, _ := pkgChatMessage.GetChatMessagesSkipLimitSort(local.ISO_CODE, query, fields, skip, limit, sort)
	pkgChatMessage.MapListMessageForAsker(local.ISO_CODE, language, messages)
	return messages
}
