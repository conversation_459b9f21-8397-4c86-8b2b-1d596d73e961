package loadMoreMessages

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getChatMessage(chatId string) (*modelChatMessage.ChatMessage, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": chatId}
	fields := bson.M{"_id": 1, "taskId": 1, "members": 1, "version": 1, "askerId": 1, "taskerId": 1}
	chatMessage, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if chatMessage == nil {
		return nil, &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, err
	}
	return chatMessage, nil, nil
}
