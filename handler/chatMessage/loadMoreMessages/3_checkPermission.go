package loadMoreMessages

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
)

func checkPermission(userId string, chatMessage *modelChatMessage.ChatMessage) *globalResponse.ResponseErrorCode {
	// Old task chat
	if chatMessage.TaskId != "" {
		isUserBelongToChat := userId == chatMessage.TaskerId || userId == chatMessage.AskerId
		if !isUserBelongToChat {
			return &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT
		}
		return nil
	}

	// New task has members
	for _, v := range chatMessage.Members {
		if v.XId == userId {
			return nil
		}
	}
	return &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT
}
