/*
 * @File: initConversation.go
 * @Description: handler function of init conversation api
 * @CreatedAt: 02/03/2021
 * @Author: ngoctb3
 */
package chatMessage

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Init Conversation (CHAT_MESSAGE)
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func InitConversation(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	resErr := validateInitConversation(reqBody)
	if resErr != nil {
		local.Logger.Warn(resErr.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *resErr)
		return
	}
	// Get task data
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"askerId": 1, "acceptedTasker": 1, "status": 1, "serviceText": 1, "contactName": 1, "serviceName": 1, "isoCode": 1}, &task)
	if task == nil {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}
	if task.Status != globalConstant.TASK_STATUS_CONFIRMED {
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_CONFIRMED)
		return
	}
	// Get chat data
	existsChat, _ := pkgChatMessage.GetChatConversation(local.ISO_CODE, bson.M{"taskId": reqBody.TaskId}, bson.M{"_id": 1})
	if existsChat != nil {
		// Update the exists chat
		addToChat(existsChat.XId, reqBody.Message, reqBody.UserId, reqBody.SendFrom)
	} else {
		// New chat will be initialized here
		initNewConversation(task, reqBody)
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: validate Init Conversation
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateInitConversation(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Message == "" {
		return &lib.ERROR_MESSAGE_REQUIRED
	}
	return nil
}

/*
 * @Description: Init new conversation and send notification to asker
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func initNewConversation(task *modelTask.Task, reqBody *model.ApiRequest) {
	// New chat will be initialized here
	var taskerId string
	if task.AcceptedTasker != nil && len(task.AcceptedTasker) > 0 {
		// Check isLeader === true (deepCleaning, officeCleaning service)
		if globalLib.IsDeepCleaningServiceByKeyName(task.ServiceName) || globalLib.IsOfficeCleaningServiceByKeyName(task.ServiceName) {
			for _, v := range task.AcceptedTasker {
				if v.IsLeader {
					taskerId = v.TaskerId
					break
				}
			}
		} else {
			// Check number of acceptedTasker
			taskerId = task.AcceptedTasker[0].TaskerId
			if task.AcceptedTasker[0].CompanyId != "" {
				taskerId = task.AcceptedTasker[0].CompanyId
			}
		}
	}

	tasker, _ := modelUser.GetOneById(local.ISO_CODE, taskerId, bson.M{"_id": 1, "name": 1, "language": 1})
	if tasker == nil {
		return
	}
	asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"_id": 1, "name": 1, "language": 1})
	if asker == nil {
		return
	}

	userName := tasker.Name
	userId := tasker.XId
	if reqBody.SendFrom == globalConstant.USER_TYPE_ASKER {
		userId = asker.XId
		userName = asker.Name
	}

	// Insert chat message to database
	chatMessage := &modelChatMessage.ChatMessage{
		XId:        globalLib.GenerateObjectId(),
		TaskId:     reqBody.TaskId,
		AskerId:    asker.XId,
		AskerName:  task.ContactName,
		TaskerId:   tasker.XId,
		TaskerName: tasker.Name,
		// AskerLanguage:  asker.Language,
		// TaskerLanguage: tasker.Language,
		// Messages:  []*modelChatMessage.ChatMessageMessages{message},
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	err := pkgChatMessage.CreateChatConversation(local.ISO_CODE, chatMessage)
	if err != nil {
		return
	}

	message := &modelChatMessage.ChatMessageMessages{
		From:      globalConstant.USER_TYPE_TASKER,
		UserId:    userId,
		Message:   reqBody.Message,
		UserName:  userName,
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	err = pkgChatMessage.SendMessageToConversation(local.ISO_CODE, chatMessage.XId, []*modelChatMessage.ChatMessageMessages{message})
	if err != nil {
		return
	}
	// Send notification to received user.
	sendNewMessageNotification(tasker, asker, reqBody.TaskId, chatMessage.XId)
}
