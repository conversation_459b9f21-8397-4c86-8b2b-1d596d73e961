/*
 * @File: messageNewTasker.go
 * @Description: Handler function for message new tasker api
 * @CreatedAt: 02/03/2021
 * @Author: ngoctb3
 */
package chatMessage

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

/*
 * @Description: Add Message New Tasker
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func MessageNewTasker(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	resErr := validateMessageNewTasker(reqBody)
	if resErr != nil {
		local.Logger.Warn(resErr.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *resErr)
		return
	}

	// Set data
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "name": 1, "language": 1})
	if tasker == nil {
		globalResponse.ResponseSuccess(w, nil)
		return
	}

	var task *modelTask.Task
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"_id": reqBody.TaskId, "status": globalConstant.TASK_STATUS_CONFIRMED}, bson.M{"acceptedTasker": 1, "isoCode": 1}, &task)
	if task == nil {
		globalResponse.ResponseSuccess(w, nil)
		return
	}

	query := bson.M{"_id": reqBody.ChatId}
	fields := bson.M{"_id": 1, "taskerId": 1, "taskerName": 1}
	chat, _ := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if chat == nil {
		globalResponse.ResponseSuccess(w, nil)
		return
	}

	acceptedTaskerId := task.AcceptedTasker[0].TaskerId
	if task.AcceptedTasker[0].CompanyId != "" {
		acceptedTaskerId = task.AcceptedTasker[0].CompanyId
	}
	if acceptedTaskerId == reqBody.UserId {
		lang := globalConstant.LANG_VI
		if tasker.Language != "" {
			lang = tasker.Language
		}

		oldMessages, err := pkgChatMessage.GetChatMessages(local.ISO_CODE, bson.M{"chatId": chat.XId}, bson.M{})
		if err != nil && err != mongo.ErrNoDocuments {
			msg := fmt.Sprintf("[btaskee-go/go-api-asker-vn-v3] Get Chat Messages failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
			globalResponse.ResponseSuccess(w, nil)
			return
		}

		now_ts := globalLib.GetCurrentTimestamp(local.TimeZone)
		changedHistory := &modelChatMessage.ChatMessageChangedHistory{
			OldTaskerId:   chat.TaskerId,
			OldTaskerName: chat.TaskerName,
			NewTaskerId:   tasker.XId,
			Messages:      oldMessages,
			ChangedAt:     now_ts,
		}

		_, err = pkgChatMessage.UpdateChatConversation(
			local.ISO_CODE,
			bson.M{"_id": chat.XId},
			bson.M{
				"$set": bson.M{
					"taskerId":       tasker.XId,
					"taskerName":     tasker.Name,
					"taskerLanguage": lang,
					"messages":       []*modelChatMessage.ChatMessageMessages{},
					"updatedAt":      now_ts,
				},
				"$push": bson.M{"changedHistory": changedHistory},
			},
		)
		if err != nil {
			msg := fmt.Sprintf("[go-accept-booking-service-v2] Update exists Chat Message for new tasker failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
			globalResponse.ResponseSuccess(w, nil)
			return
		}

		// Remove old messages if move to history success
		go func() {
			oldMessageIds := []string{}
			for _, v := range oldMessages {
				oldMessageIds = append(oldMessageIds, v.XId)
			}
			err = pkgChatMessage.DeleteChatMessage(local.ISO_CODE, bson.M{"_id": bson.M{"$in": oldMessageIds}})
			if err != nil {
				err = pkgChatMessage.DeleteChatMessage(local.ISO_CODE, bson.M{"_id": bson.M{"$in": oldMessageIds}})
				if err != nil {
					msg := fmt.Sprintf("[go-accept-booking-service-v2] Delete Chat Message after move to history failed: taskId %s. Error: %s", task.XId, err.Error())
					globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
				}
			}
		}()
		addToChat(reqBody.ChatId, reqBody.Message, reqBody.UserId, reqBody.SendFrom)
	}

	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: validate messageNewTasker api
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateMessageNewTasker(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ChatId == "" {
		return &lib.ERROR_CHAT_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	return nil
}
