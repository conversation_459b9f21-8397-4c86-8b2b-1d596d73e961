package sendCallChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getChatConversation(taskId string, userId string) (*modelChatMessage.ChatMessage, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{
		"taskId":  taskId,
		"askerId": userId,
	}
	chatConversation, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, bson.M{"_id": 1})
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if chatConversation == nil {
		return nil, &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, nil
	}
	return chatConversation, nil, nil
}
