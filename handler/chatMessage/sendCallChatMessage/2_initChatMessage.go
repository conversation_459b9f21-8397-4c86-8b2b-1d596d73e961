package sendCallChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
)

func initChatMessage(reqBody *model.ApiRequest) map[string]interface{} {
	return map[string]interface{}{
		"from": globalConstant.USER_TYPE_ASKER,
		"callData": &modelChatMessage.ChatMessageMessagesCallData{
			Status:   reqBody.Status,
			Duration: reqBody.Duration,
		},
		"userId": reqBody.UserId,
	}
}
