package sendCallChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/sendChatMessageV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var cfg = config.GetConfig()

func SendCallChatMessage(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. Init chat message for call data type
	if reqBody != nil {
		reqBody.Data = initChatMessage(reqBody)
	}

	// 3. Get chat conversation
	if reqBody.ChatId == "" {
		chatConversation, errCode, err := getChatConversation(reqBody.TaskId, reqBody.UserId)
		if errCode != nil {
			return errCode, err
		}
		reqBody.ChatId = chatConversation.XId
	}

	// 4. Call function send-chat-message-v2
	errCode, err := sendChatMessageV2.SendChatMessageV2(reqBody)
	if errCode != nil {
		return errCode, err
	}

	go sendWebsocket(reqBody)

	return nil, nil
}
