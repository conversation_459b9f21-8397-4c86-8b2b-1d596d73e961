package sendCallChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ChatId == "" && reqBody.TaskId == "" {
		return &lib.ERROR_CHAT_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.From == "" || reqBody.Status == "" {
		return &lib.ERROR_MISSING_CALL_INFORMATION
	}
	return nil
}
