package sendChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

// user can send message within 30 minutes after task done
func isTimeValid(task *modelTask.Task) bool {
	taskDate := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	return lib.IsAskerCanSendChat(task.Status, taskDate, task.Duration, task.ServiceName)
}
