package sendChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func getChatMessage(chatId string) (*modelChatMessage.ChatMessage, error) {
	query := bson.M{"_id": chatId}
	fields := bson.M{"_id": 1, "taskId": 1}
	chat, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	return chat, err
}
