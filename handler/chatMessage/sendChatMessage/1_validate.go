package sendChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func validate(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.ChatId == "" {
		return &lib.ERROR_CHAT_ID_REQUIRED
	}
	if reqBody.Data == nil || len(reqBody.Data) == 0 {
		return &lib.ERROR_DATA_UPDATE_REQUIRED
	}
	if len(reqBody.MessageTo) == 0 {
		return &lib.ERROR_MESSAGE_TO_REQUIRED
	}
	return nil
}
