package sendChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTask(taskId string) (*modelTask.Task, error) {
	var task *modelTask.Task
	err := globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"date": 1, "duration": 1, "status": 1, "serviceName": 1}, &task)
	return task, err
}
