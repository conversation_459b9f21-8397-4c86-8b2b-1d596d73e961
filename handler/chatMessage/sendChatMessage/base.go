package sendChatMessage

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

func SendChatMessage(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.<PERSON>questURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// if reqBody.Version == globalConstant.API_VERSION_V2 {
	// 	errCode, err := sendChatMessageV2.SendChatMessageV2(reqBody)
	// 	if errCode != nil {
	// 		local.Logger.Warn(errCode.ErrorCode,
	// 			zap.String("url", r.RequestURI),
	// 			zap.Any("body", reqBody),
	// 			zap.Error(err),
	// 		)
	// 		globalResponse.ResponseError(w, *errCode)
	// 		return
	// 	}
	// 	globalResponse.ResponseSuccess(w, nil)
	// 	return
	// }

	errCode, err := process(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	globalResponse.ResponseSuccess(w, nil)
}

func process(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	// 1. validate
	errCode := validate(reqBody)
	if errCode != nil {
		return errCode, nil
	}

	// 2. get chat message
	chatMessage, err := getChatMessage(reqBody.ChatId)
	if chatMessage == nil || err != nil {
		return &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, err
	}

	// 3. getTask
	task, err := getTask(chatMessage.TaskId)
	if task == nil || err != nil {
		return &lib.ERROR_TASK_NOT_FOUND, err
	}

	// 4. check if user can send chat message
	if !isTimeValid(task) {
		return &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT, nil
	}

	// 4. update chat message
	errCode, err = updateChatMessage(reqBody)
	if errCode != nil {
		return errCode, err
	}

	// 5. send notification
	go sendNotification(reqBody, chatMessage)

	// 6. send socket
	go sendWebsocket(reqBody)

	return nil, nil
}
