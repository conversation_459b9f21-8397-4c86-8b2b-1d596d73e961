package sendChatMessage

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
)

func updateChatMessage(reqBody *model.ApiRequest) (*globalResponse.ResponseErrorCode, error) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	reqBody.Data["_id"] = globalLib.GenerateObjectId()
	reqBody.Data["chatId"] = reqBody.ChatId
	reqBody.Data["createdAt"] = now
	err := pkgChatMessage.InsertChatMessage(local.ISO_CODE, reqBody.Data)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}

	_, err = pkgChatMessage.UpdateChatConversation(
		local.ISO_CODE,
		bson.M{"_id": reqBody.ChatId},
		bson.M{
			"$set": bson.M{
				"lastMessage": reqBody.Data,
			},
			"$unset": bson.M{
				"archivedData": 1,
			},
		},
	)
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED, err
	}

	return nil, nil
}
