package sendChatMessage

import (
	"encoding/json"
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func sendNotification(reqBody *model.ApiRequest, chatMessage *modelChatMessage.ChatMessage) {
	title := &modelService.ServiceText{
		Vi: "",
		En: "",
		Ko: "",
		Th: "",
		Id: "",
	}
	body := &modelService.ServiceText{
		Vi: localization.T(globalConstant.LANG_VI, "CHAT_NEW_MESSAGE"),
		En: localization.T(globalConstant.LANG_EN, "CHAT_NEW_MESSAGE"),
		Ko: localization.T(globalConstant.LANG_KO, "CHAT_NEW_MESSAGE"),
		Th: localization.T(globalConstant.LANG_TH, "CHAT_NEW_MESSAGE"),
		Id: localization.T(globalConstant.LANG_ID, "CHAT_NEW_MESSAGE"),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       28,
		ChatId:     reqBody.ChatId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
	}
	if chatMessage != nil && chatMessage.TaskId != "" {
		payload.TaskId = chatMessage.TaskId
	}
	user, err := modelUser.GetOneById(local.ISO_CODE, reqBody.MessageTo[0], bson.M{"_id": 1, "language": 1})
	if err != nil && user == nil {
		data, _ := json.Marshal(reqBody.Data)
		message := fmt.Sprintf("Send chat message ERROR: Cannot get user from database. data:%s, error:%v", string(data), err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{
		UserId: user.XId, Language: user.Language},
	}
	lib.SendNotification([]interface{}{}, userIds, title, body, payload, "")
}
