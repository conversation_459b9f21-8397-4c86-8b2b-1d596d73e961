package getChatListChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getChatById(language, userId string, chatId string, skip, limit int64) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	query := bson.M{"_id": chatId}
	fields := bson.M{"_id": 1, "taskId": 1, "members": 1}
	chatMessage, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if chatMessage == nil {
		return nil, &lib.ERROR_CHAT_MESSAGE_NOT_FOUND, err
	}

	var taskData *modelTask.Task
	var taskerInfo map[string]interface{}
	if chatMessage.TaskId != "" {
		globalDataAccess.GetOneByQuery(
			globalCollection.COLLECTION_TASK[local.ISO_CODE],
			bson.M{"_id": chatMessage.TaskId},
			TASK_FIELDS_TO_UPDATE_TO_CHAT_INFO,
			&taskData,
		)

		var resErr *globalResponse.ResponseErrorCode
		taskerInfo, resErr = GetTaskerInfo(taskData)
		if resErr != nil {
			return nil, resErr, nil
		}
	}

	// Get message for new version
	chatMessage.Messages = getChatMessageNewVersion(language, userId, chatMessage.XId, skip, limit)
	return getChatInfoResult(userId, chatMessage, taskData, taskerInfo), nil, nil
}
