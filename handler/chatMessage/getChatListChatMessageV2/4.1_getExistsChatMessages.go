package getChatListChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getExistsChatByMemberIds(language, userId string, chatMemberIds []string, skip, limit int64) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	memberIds := globalLib.UniqString(append(chatMemberIds, userId))
	// $all will get the document if all the elements in the array in query are matched with document array elements
	// in this ex: get chat that all id in memberIds are in chat.Members
	/// -> Must add query to check chat.Members length is equal to memberIds length
	query := bson.M{
		"members._id": bson.M{"$all": memberIds},
		"members":     bson.M{"$size": len(memberIds)},
	}
	fields := bson.M{"_id": 1, "members": 1, "version": 1}
	chat, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err.Error() != mongo.ErrNoDocuments.Error() {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if chat == nil {
		return nil, nil, nil
	}

	// Get message for new version
	chat.Messages = getChatMessageNewVersion(language, userId, chat.XId, skip, limit)
	return getChatInfoResult(userId, chat, nil, nil), nil, nil
}

func checkIfIsPrivateChatWithOneTasker(askerId string, members []*modelChatMessage.ChatMessageMember) (taskerId string, check bool) {
	if len(members) != 2 {
		return "", false
	}

	for _, v := range members {
		if v.XId != askerId && v.Type == globalConstant.USER_TYPE_TASKER {
			return v.XId, true
		}
	}
	return "", false
}
