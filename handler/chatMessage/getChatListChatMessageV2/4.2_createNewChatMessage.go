package getChatListChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func createConversation(userId string, chatMemberIds []string) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. Check user
	memberIds := globalLib.UniqString(append(chatMemberIds, userId))
	if len(memberIds) < 2 {
		return nil, &lib.SYSTEM_ERROR, nil
	}

	// 2. Get users
	memberInfoMap, err := getChatMembersInfo(memberIds)
	if err != nil {
		return nil, &lib.SYSTEM_ERROR, err
	}

	// 3. init data chat
	chat, errCode, err := initConversationData(userId, memberIds, memberInfoMap)
	if errCode != nil {
		return nil, errCode, err
	}

	// 4. Save to db
	err = pkgChatMessage.CreateChatConversation(local.ISO_CODE, chat)
	if err != nil {
		return nil, &lib.SYSTEM_ERROR, err
	}

	return getChatInfoResult(userId, chat, nil, nil), nil, nil
}

func getChatMembersInfo(memberIds []string) (map[string]*modelUser.Users, error) {
	members, err := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": memberIds}}, bson.M{"_id": 1, "avatar": 1, "type": 1, "name": 1, "language": 1, "isPremiumTasker": 1, "cleaningOption": 1})
	if err != nil {
		return nil, err
	}

	// convert to map
	userMap := map[string]*modelUser.Users{}
	for _, user := range members {
		userMap[user.XId] = user
	}

	return userMap, nil
}

func initConversationData(userId string, memberIds []string, memberInfoMap map[string]*modelUser.Users) (*modelChatMessage.ChatMessage, *globalResponse.ResponseErrorCode, error) {
	members := []*modelChatMessage.ChatMessageMember{}
	for _, memberId := range memberIds {
		if memberInfoMap[memberId] == nil {
			return nil, &lib.ERROR_USER_NOT_FOUND, nil
		}

		role := ""
		if memberId == userId {
			role = "ADMIN"
		}

		members = append(members, &modelChatMessage.ChatMessageMember{
			XId:      memberId,
			Avatar:   memberInfoMap[memberId].Avatar,
			Type:     memberInfoMap[memberId].Type,
			Name:     memberInfoMap[memberId].Name,
			Role:     role,
			JoinedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		})
	}
	return &modelChatMessage.ChatMessage{
		XId:       globalLib.GenerateObjectId(),
		Members:   members,
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}, nil, nil
}
