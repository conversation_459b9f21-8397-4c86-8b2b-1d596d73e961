package getChatListChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getChatMessagesByUserIdAndTaskId(language, userId, taskId string, skip int64, limit int64) (map[string]interface{}, *globalResponse.ResponseErrorCode, error) {
	// Get chat by taskId, userId
	taskData, err := handler.GetTask(bson.M{"_id": taskId, "askerId": userId}, TASK_FIELDS_TO_UPDATE_TO_CHAT_INFO)
	if err != nil && err != mongo.ErrNoDocuments {
		return nil, &lib.SYSTEM_ERROR, err
	}
	if taskData == nil {
		return nil, &lib.ERROR_TASK_NOT_FOUND, nil
	}
	// Check permission get list chat
	if !hasPermissionGetListChat(taskData) {
		return nil, &lib.ERROR_NOT_HAVE_PERMISSION_GET_LIST_CHAT, nil
	}
	// Check task status
	// if taskData.Status != globalConstant.TASK_STATUS_CONFIRMED {
	// 	globalResponse.ResponseError(w, lib.ERROR_CONVERSATION_DONE)
	// 	return
	// }
	// Get tasker info
	taskerInfo, resErr := GetTaskerInfo(taskData)
	if resErr != nil {
		return nil, resErr, nil
	}
	// Get chat messages
	chatMessage, err := getChatConversation(language, taskData, userId, taskerInfo, skip, limit)
	if chatMessage == nil || err != nil {
		return nil, &lib.SYSTEM_ERROR, err
	}

	return getChatInfoResult(userId, chatMessage, taskData, taskerInfo), nil, nil
}

func getChatInfoResult(userId string, chatMessage *modelChatMessage.ChatMessage, task *modelTask.Task, taskerInfo map[string]interface{}) map[string]interface{} {
	// Init data for a chat info
	result := map[string]interface{}{
		"_id":        chatMessage.XId,
		"taskerInfo": taskerInfo,
		"messages":   chatMessage.Messages,
	}

	// Add task info if this chat is created for a task
	if task != nil {
		// get service by task.ServiceId
		service, _ := handler.GetService(bson.M{"_id": task.GetServiceId()}, bson.M{"icon": 1})
		result["task"] = map[string]interface{}{
			"_id":              task.XId,
			"date":             task.Date,
			"address":          task.Address,
			"serviceText":      task.ServiceText,
			"duration":         task.Duration,
			"status":           task.Status,
			"serviceName":      task.ServiceName,
			"homeMovingDetail": GetHomeMovingDetail(task),
			"originCurrency":   task.OriginCurrency,
			"serviceIcon":      service.GetIcon(),
		}
	}

	// Add list members if this chat is created for a group of members
	if len(chatMessage.Members) > 0 {
		memberIds := []string{}
		for _, member := range chatMessage.Members {
			memberIds = append(memberIds, member.XId)
		}

		memberInfoMap, _ := getChatMembersInfo(memberIds)

		members := []map[string]interface{}{}
		for _, v := range chatMessage.Members {
			m := map[string]interface{}{
				"_id":      v.XId,
				"avatar":   v.Avatar,
				"type":     v.Type,
				"name":     v.Name,
				"role":     v.Role,
				"language": globalConstant.LANG_EN,
			}
			if memberInfoMap[v.XId] != nil {
				m["language"] = memberInfoMap[v.XId].Language
				m["avatar"] = memberInfoMap[v.XId].Avatar
				m["name"] = memberInfoMap[v.XId].Name
				if memberInfoMap[v.XId].CleaningOption != nil && memberInfoMap[v.XId].CleaningOption.HasCleaningKit {
					m["hasCleaningKit"] = true
				}
				if memberInfoMap[v.XId].IsPremiumTasker {
					m["isPremiumTasker"] = true
				}
			}
			members = append(members, m)
		}
		result["members"] = members
	}

	// Add list confirmed tasks with tasker if this chat is created for a private chat with one tasker
	if confirmedTasksWithTasker := getConfirmedTaskWithTasker(userId, chatMessage.Members); confirmedTasksWithTasker != nil {
		result["confirmedTasksWithTasker"] = confirmedTasksWithTasker
	}
	// Add list waiting tasks with tasker if this chat is created for a private chat with one tasker
	waitingTasksWithTasker := getWaitingTaskWithTasker(userId, chatMessage.Members)
	if waitingTasksWithTasker != nil {
		result["waitingTasksWithTasker"] = waitingTasksWithTasker
	}

	return result
}

func getConfirmedTaskWithTasker(userId string, members []*modelChatMessage.ChatMessageMember) []*modelTask.Task {
	taskerId, isPrivateChatWithOneTasker := checkIfIsPrivateChatWithOneTasker(userId, members)
	if !isPrivateChatWithOneTasker {
		return nil
	}
	var tasks []*modelTask.Task
	query := bson.M{
		"askerId":                 userId,
		"status":                  globalConstant.TASK_STATUS_CONFIRMED,
		"acceptedTasker.taskerId": taskerId,
	}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, bson.M{"_id": 1, "date": 1, "serviceText": 1, "duration": 1, "taskPlace": 1, "forceTasker": 1}, &tasks)
	return tasks
}

func getWaitingTaskWithTasker(userId string, members []*modelChatMessage.ChatMessageMember) []*modelTask.Task {
	taskerId, isPrivateChatWithOneTasker := checkIfIsPrivateChatWithOneTasker(userId, members)
	if !isPrivateChatWithOneTasker {
		return nil
	}
	var tasks []*modelTask.Task
	query := bson.M{
		"askerId":              userId,
		"status":               globalConstant.TASK_STATUS_POSTED,
		"forceTasker.taskerId": taskerId,
	}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, bson.M{"_id": 1, "date": 1, "serviceText": 1, "duration": 1, "taskPlace": 1, "forceTasker": 1}, &tasks)
	return tasks
}
