package getChatListChatMessageV2

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

/*
 * @Description: validateGetChatListChatMessage
 * @CreatedAt: 19/11/2020
 * @Author: vinhnt
 * @UpdatedAt: 02/03/2021
 * @UpdatedBy: ngoctb3
 */
func validateGetChatListChatMessage(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ChatId != "" {
		return nil
	}
	if reqBody.TaskId == "" && len(reqBody.MemberIds) == 0 {
		return &lib.ERROR_BOOKING_ID_OR_MEMBER_IDS_REQUIRED
	}
	return nil
}
