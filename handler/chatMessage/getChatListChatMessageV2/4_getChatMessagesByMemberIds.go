package getChatListChatMessageV2

import (
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func getChatMessagesByMemberIds(language, userId string, memberIds []string, skip, limit int64) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// Get chat by memberIds and userId (if exists)
	chat, errCode, err := getExistsChatByMemberIds(language, userId, memberIds, skip, limit)
	if errCode != nil {
		return nil, errCode, err
	}
	if chat != nil {
		return chat, nil, nil
	}

	// Create new chat if not exists
	chat, errCode, err = createConversation(userId, memberIds)
	if errCode != nil {
		return nil, errCode, err
	}

	return chat, nil, nil
}
