package getListChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func getListChatByTaskId(taskIds []string) ([]*modelChatMessage.ChatMessage, error) {
	query := bson.M{"taskId": bson.M{"$in": taskIds}}
	return pkgChatMessage.GetChatConversations(local.ISO_CODE, query, bson.M{})
}
