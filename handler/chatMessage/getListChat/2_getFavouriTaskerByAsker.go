package getListChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getFavTasker(askerId string) (*modelUser.Users, *globalResponse.ResponseErrorCode, error) {
	fields := bson.M{"_id": 1, "avatar": 1, "favouriteTasker": 1, "language": 1}
	asker, err := modelUser.GetOneById(local.ISO_CODE, askerId, fields)
	if asker == nil && err != nil {
		return nil, &lib.ERROR_USER_NOT_FOUND, err
	}
	return asker, nil, nil
}
