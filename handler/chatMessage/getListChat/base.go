package getListChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetListChat(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode, error) {
	// 1. Validate
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode, nil
	}
	asker, errCode, err := getFavTasker(reqBody.UserId)
	if errCode != nil {
		return nil, errCode, err
	}
	// 2. Get task comfirmed by User
	taskMap, taskIds, taskerIds := getTasks(reqBody.UserId)
	if len(taskIds) == 0 {
		return nil, nil, nil
	}
	//  3. Get list chat by taskId
	listChat, _ := getListChatByTaskId(taskIds)
	if len(listChat) == 0 {
		return nil, nil, nil
	}

	mapMessageByChatId, _ := getListMessagesByChatIds(listChat)

	// 4. Get Taskers
	mapTaskerInfoById := getTaskers(taskerIds)
	// 5. Combine task + chat info by taskId
	result := combineChatInformation(asker, taskMap, listChat, mapTaskerInfoById, mapMessageByChatId)
	return result, nil, nil
}
