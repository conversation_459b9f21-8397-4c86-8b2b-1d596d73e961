package getListChat

import (
	"sort"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler/chatMessage/getListChatV2"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func combineChatInformation(asker *modelUser.Users, taskMap map[string]*modelTask.Task, listChat []*modelChatMessage.ChatMessage, mapTaskerInfoById map[string]TaskerInfo, mapMessageByChatId map[string][]*modelChatMessage.ChatMessageMessages) []map[string]interface{} {
	mapFavTasker := make(map[string]struct{})
	for _, id := range asker.FavouriteTasker {
		mapFavTasker[id] = struct{}{}
	}
	result := []map[string]interface{}{}
	for _, v := range listChat {
		if v == nil {
			continue
		}
		v.Messages = mapMessageByChatId[v.XId]
		isRead, countUnreadMessage := getListChatV2.CountUnreadMessage(asker.XId, v)
		_, isFavouriteTasker := mapFavTasker[v.TaskerId]
		chatInfo := map[string]interface{}{
			"taskId":             v.TaskId,
			"chatId":             v.XId,
			"askerName":          v.AskerName,
			"taskerId":           v.TaskerId,
			"taskerName":         v.TaskerName,
			"taskerAvatar":       mapTaskerInfoById[v.TaskerId].Avatar,
			"isPremiumTasker":    mapTaskerInfoById[v.TaskerId].IsPremiumTasker,
			"isFavouriteTasker":  isFavouriteTasker,
			"isRead":             isRead,
			"countUnreadMessage": countUnreadMessage,
		}
		// Add task info to response
		dataTask, ok := taskMap[v.TaskId]
		if ok {
			chatInfo["serviceText"] = dataTask.ServiceText
			chatInfo["date"] = globalLib.ParseDateFromTimeStamp(dataTask.Date, local.TimeZone)
			chatInfo["duration"] = dataTask.Duration
		}
		lang := globalConstant.LANG_EN
		if asker.Language != "" {
			lang = asker.Language
		}
		if len(v.Messages) > 0 {
			lastMessage := v.Messages[len(v.Messages)-1]
			lastChatMessageInfo := map[string]interface{}{
				"userName":  lastMessage.UserName,
				"from":      lastMessage.From,
				"message":   lastMessage.Message,
				"createdAt": lastMessage.CreatedAt,
			}
			if lastMessage.Message == "" {
				if lastMessage.Image != "" {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_IMAGE")
				} else if lastMessage.Video != "" {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_VIDEO")
				} else if lastMessage.Location != nil {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_LOCATION")
				} else if lastMessage.From == globalConstant.CHAT_MESSAGE_FROM_SYSTEM {
					lastChatMessageInfo["message"] = localization.T(lang, "CHAT_MESSAGE_BY_SYSTEM")
				} else if lastMessage.Type == globalConstant.CHAT_MESSAGE_REQUEST_TYPE_INCREASE_DURATION {
					lastChatMessageInfo["message"] = localization.T(lang, "REQUEST_INCREASE_DURATION")
				} else if lastMessage.Type == globalConstant.CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DATE_TIME {
					lastChatMessageInfo["message"] = localization.T(lang, "REQUEST_UPDATE_DATE_TIME")
				} else if lastMessage.Type == globalConstant.CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DETAIL {
					lastChatMessageInfo["message"] = localization.T(lang, "REQUEST_UPDATE_DETAIL")
				}
			}
			chatInfo["lastChatMessage"] = lastChatMessageInfo
		}
		result = append(result, chatInfo)
	}
	result = sortDataByTaskDate(result)
	return result
}

// Sort data list task because list task canceled by tasker not sort yet
func sortDataByTaskDate(tasks []map[string]interface{}) []map[string]interface{} {
	sort.SliceStable(tasks, func(i, j int) bool {
		firstDate := tasks[i]["date"].(time.Time)
		secondDate := tasks[j]["date"].(time.Time)
		return firstDate.Before(secondDate)
	})
	return tasks
}
