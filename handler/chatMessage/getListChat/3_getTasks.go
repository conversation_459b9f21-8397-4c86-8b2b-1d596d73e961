package getListChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/handler"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasks(askerId string) (map[string]*modelTask.Task, []string, []string) {
	taskMap := make(map[string]*modelTask.Task)
	var taskIds, taskerIds []string
	// Get list task from taskIds
	// Lấy dư ra 1 ngày để tính thời gian done của các task do db hiện tại không lưu endTime của task
	startTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
	query := bson.M{
		"date":    bson.M{"$gte": startTime},
		"status":  bson.M{"$in": lib.TASK_STATUS_ALLOW_ASKER_SEND_CHAT},
		"isoCode": local.ISO_CODE,
		"askerId": askerId,
	}
	fields := bson.M{
		"_id":                      1,
		"serviceText":              1,
		"serviceName":              1,
		"date":                     1,
		"collectionDate":           1,
		"detailLaundry.isReceived": 1,
		"duration":                 1,
		"askerId":                  1,
		"acceptedTasker.taskerId":  1,
		"acceptedTasker.isLeader":  1,
		"acceptedTasker.companyId": 1,
		"status":                   1,
	}
	tasks, _ := handler.GetTasks(query, fields)
	for _, task := range tasks {
		// Check can send chat view
		taskDate := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
		if !lib.IsAskerCanSendChat(task.Status, taskDate, float64(task.Duration), task.ServiceName) {
			continue
		}
		// Add data to get chat
		taskMap[task.XId] = task
		taskIds = append(taskIds, task.XId)
		for _, tasker := range task.AcceptedTasker {
			taskerIds = append(taskerIds, tasker.TaskerId)
			if tasker.CompanyId != "" {
				taskerIds = append(taskerIds, tasker.CompanyId)
			}
		}
	}
	return taskMap, taskIds, taskerIds
}
