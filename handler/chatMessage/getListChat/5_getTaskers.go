package getListChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskers(taskerIds []string) map[string]TaskerInfo {
	mapTaskerInfoById := make(map[string]TaskerInfo)
	query := bson.M{"_id": bson.M{"$in": taskerIds}}
	fields := bson.M{"_id": 1, "avatar": 1, "isPremiumTasker": 1}
	taskers, _ := modelUser.GetAll(local.ISO_CODE, query, fields)
	for _, tasker := range taskers {
		mapTaskerInfoById[tasker.XId] = TaskerInfo{
			Avatar:          tasker.Avatar,
			IsPremiumTasker: tasker.IsPremiumTasker,
		}
	}
	return mapTaskerInfoById
}

type TaskerInfo struct {
	Avatar          string
	IsPremiumTasker bool
}
