package getListChat

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func getListMessagesByChatIds(listChat []*modelChatMessage.ChatMessage) (map[string][]*modelChatMessage.ChatMessageMessages, error) {
	chatIds := make([]string, 0)
	for _, chat := range listChat {
		chatIds = append(chatIds, chat.XId)
	}
	query := bson.M{"chatId": bson.M{"$in": chatIds}}
	messages, _ := pkgChatMessage.GetChatMessages(local.ISO_CODE, query, bson.M{})
	mapMessageByChatId := map[string][]*modelChatMessage.ChatMessageMessages{}
	for _, v := range messages {
		mapMessageByChatId[v.ChatId] = append(mapMessageByChatId[v.ChatId], v)
	}
	return mapMessageByChatId, nil
}
