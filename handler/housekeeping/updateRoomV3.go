/*
 * @File: updateRoomV3.go
 * @Description: Handler function of udpate Room api
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
package housekeeping

import (
	"encoding/json"
	"net/http"
	"reflect"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Room Housekeeping V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func UpdateRoomV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	reqBody := make(map[string]interface{})
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate data
	errCode := validateUpdateRoomV3(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Check if hostel is not exists in database
	var dataHostel *modelHousekeeping.Housekeeping
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{"_id": reqBody["housekeepingId"].(string), "userId": reqBody["userId"].(string), "rooms.id": reqBody["roomId"].(string)}, bson.M{"rooms.$": 1}, &dataHostel)
	if dataHostel == nil || dataHostel.Rooms[0] == nil {
		local.Logger.Warn(lib.ERROR_ROOM_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ROOM_NOT_FOUND)
		return
	}

	// Update room
	errCode, err = updateRoomV3(reqBody, dataHostel)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request params V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func validateUpdateRoomV3(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["housekeepingId"] == nil {
		return &lib.ERROR_ID_REQUIRED
	}
	if reqBody["userId"] == nil {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody["roomId"] == nil {
		return &lib.ERROR_ROOM_ID_REQUIRED
	}
	if reqBody["data"] == nil {
		return &lib.ERROR_DATA_UPDATE_REQUIRED
	}
	return nil
}

/*
 * @Description: Update room in database V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func updateRoomV3(reqBody map[string]interface{}, dataHostel *modelHousekeeping.Housekeeping) (*globalResponse.ResponseErrorCode, error) {
	isUpdate := false
	dataUpdate := dataHostel.Rooms[0]
	roomData := make(map[string]interface{})
	bytes, _ := json.Marshal(reqBody["data"])
	json.Unmarshal(bytes, &roomData)

	if roomData["name"] != nil {
		dataUpdate.Name = roomData["name"].(string)
		isUpdate = true
	}
	if roomData["area"] != nil {
		dataUpdate.Area = roomData["area"].(float64)
		isUpdate = true
	}
	if roomData["note"] != nil {
		dataUpdate.Note = roomData["note"].(string)
		isUpdate = true
	}
	if roomData["images"] != nil {
		typeOf := reflect.ValueOf(roomData["images"])
		if typeOf.Kind() == reflect.Slice {
			objImages := roomData["images"].([]interface{})
			images := make([]string, len(objImages))
			for k, v := range objImages {
				images[k] = v.(string)
			}
			dataUpdate.Images = images
			isUpdate = true
		}
	}

	if isUpdate {
		dataUpdate.UpdatedAt = globalLib.GetCurrentTimestamp(local.TimeZone)
		_, err := globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE],
			bson.M{"_id": reqBody["housekeepingId"].(string), "rooms.id": reqBody["roomId"].(string)},
			bson.M{"$set": bson.M{"rooms.$": dataUpdate}},
		)
		if err != nil {
			return &globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "UPDATE_FAILED", Message: err.Error()}, err
		}
		return nil, nil
	} else {
		return &globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "NOTHING_CHANGE"}, nil
	}
}
