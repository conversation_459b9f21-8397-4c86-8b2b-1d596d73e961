/*
 * @File: removeRoom.go
 * @Description: Handler function of remove room api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package housekeeping

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Remove Room Housekeeping
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func RemoveRoom(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	reqBody := make(map[string]interface{})
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate data
	errCode := validateRemoveRoom(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Check room exists or not
	var dataHostel *modelHousekeeping.Housekeeping
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{"_id": reqBody["housekeepingId"].(string), "userId": reqBody["userId"].(string), "rooms.id": reqBody["roomId"].(string)}, bson.M{"rooms.$": 1}, &dataHostel)
	if dataHostel == nil || dataHostel.Rooms[0] == nil {
		local.Logger.Warn(lib.ERROR_ROOM_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ROOM_NOT_FOUND)
		return
	}

	// Update housekeeping in database
	_, err = globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE],
		bson.M{"_id": reqBody["housekeepingId"].(string), "rooms.id": reqBody["roomId"].(string)},
		bson.M{"$pull": bson.M{
			"rooms": bson.M{"id": reqBody["roomId"].(string)},
		}},
	)
	if err != nil {
		local.Logger.Warn("REMOVE_FAILED",
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "REMOVE_FAILED", Message: err.Error()})
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: validate request data
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateRemoveRoom(reqBody map[string]interface{}) *globalResponse.ResponseErrorCode {
	if reqBody["housekeepingId"] == nil {
		return &lib.ERROR_ID_REQUIRED
	}
	if reqBody["userId"] == nil {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody["roomId"] == nil {
		return &lib.ERROR_ROOM_ID_REQUIRED
	}
	return nil
}
