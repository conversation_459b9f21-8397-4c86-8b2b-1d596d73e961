/*
 * @File: getHouseKeepingList.go
 * @Description: Handler function of get housekeeping list api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package housekeeping

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Housekeeping by UserId
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetHousekeepingList(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	var reqBody modelHousekeeping.Housekeeping
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate request params
	errCode := validateGetHouseKeepingList(&reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var result []*modelHousekeeping.Housekeeping
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{"userId": reqBody.UserId, "status": globalConstant.HOUSEKEEPING_STATUS_ACTIVE, "isoCode": reqBody.IsoCode}, bson.M{"createdAt": 0, "updatedAt": 0}, &result)

	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate get house keeping list
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetHouseKeepingList(reqBody *modelHousekeeping.Housekeeping) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.IsoCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.IsoCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
