/*
 * @File: removeHousekeeping.go
 * @Description: Handler function of remove housekeeping api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package housekeeping

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Remove Housekeeping by ID
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func RemoveHousekeeping(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	var reqBody modelHousekeeping.Housekeeping
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate request params
	errCode := validateRemoveHousekeeping(&reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Update data in database
	_, err = globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{"_id": reqBody.XId, "userId": reqBody.UserId}, bson.M{"$set": bson.M{"status": globalConstant.HOUSEKEEPING_STATUS_DELETED, "updatedAt": globalLib.GetCurrentTime(local.TimeZone)}})
	if err != nil {
		local.Logger.Warn("UPDATE_FAILED",
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "UPDATE_FAILED", Message: err.Error()})
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request data
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateRemoveHousekeeping(reqBody *modelHousekeeping.Housekeeping) *globalResponse.ResponseErrorCode {
	if reqBody.XId == "" {
		return &lib.ERROR_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}
