/*
 * @File: updateTaskRooms.go
 * @Description: Handler function of udpate task rooms api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package housekeeping

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPricingVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/pushNotificationNewTask"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPricingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	modelPricingResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	modelPushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Update Task Room Housekeeping
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func UpdateTaskRooms(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	reqBody := model.ApiRequest{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate data
	errCode := validateUpdateTaskRooms(&reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, bson.M{"viewedTaskers": 0, "voiceCallHistory.createdAt": 0, "costDetail.currency": 0}, &task)
	if task != nil && task.AskerId == reqBody.UserId {
		// check 2h before task begin
		next2Hours := globalLib.GetCurrentTime(local.TimeZone).Add(2 * time.Hour)
		taskDate := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
		if task.Status == globalConstant.TASK_STATUS_CONFIRMED && next2Hours.After(taskDate) {
			local.Logger.Warn(lib.ERROR_TIME_NOT_ALLOW.ErrorCode,
				zap.String("url", r.RequestURI),
				zap.Any("body", reqBody),
			)
			globalResponse.ResponseError(w, lib.ERROR_TIME_NOT_ALLOW)
			return
		}

		// Get rooms info
		var hostel *modelHousekeeping.Housekeeping
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{"_id": task.DetailHostel.HostelId}, bson.M{"rooms": 1}, &hostel)

		if hostel != nil && hostel.Rooms != nil && len(hostel.Rooms) > 0 {
			wg := &sync.WaitGroup{}
			var totalArea float64
			var newRooms []*modelHousekeeping.HousekeepingRooms
			for _, v := range hostel.Rooms {
				isContain := false
				for _, rId := range reqBody.RoomIds {
					if rId == v.Id {
						isContain = true
						break
					}
				}
				if isContain {
					totalArea += v.Area
					newRooms = append(newRooms, v)
				}
			}
			task.DetailHostel.Rooms = newRooms

			// Calculate new price
			expectCost, err := getPrice(task)
			if err != nil {
				local.Logger.Warn(lib.ERROR_CAN_NOT_GET_PRICE.ErrorCode,
					zap.String("url", r.RequestURI),
					zap.Error(err),
					zap.Any("body", reqBody),
				)
				globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_GET_PRICE)
				return
			}

			// Update to DB
			dataSet := map[string]interface{}{
				"pricing": map[string]interface{}{
					"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
					"costDetail": &modelTask.TaskPricingCostDetail{
						BaseCost:   expectCost.BaseCost,
						Cost:       expectCost.Cost,
						Duration:   expectCost.Duration,
						IsIncrease: expectCost.Cost > expectCost.FinalCost,
						Currency:   expectCost.Currency.Code,
					},
				},
				"duration":               expectCost.Duration,
				"cost":                   expectCost.Cost,
				"status":                 globalConstant.TASK_STATUS_POSTED,
				"detailHostel.rooms":     newRooms,
				"detailHostel.totalArea": totalArea,
				"updatedAt":              globalLib.GetCurrentTime(local.TimeZone),
			}

			// Re-calculate decreasedCost with new cost
			if task.Promotion != nil {
				task.Promotion.DecreasedCost = expectCost.FinalCost
				dataSet["promotion"] = task.Promotion
			}

			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task.XId, bson.M{"$set": dataSet, "$unset": bson.M{"expiredNotifications": 1, "acceptedTasker": 1, "viewedTaskers": 1, "visibility": 1, "intervalForAccept": 1}})
			// Update changes history
			wg.Add(1)
			go func() {
				defer wg.Done()
				historyData := map[string]interface{}{
					"key": "updateHostelRooms",
					"data": map[string]interface{}{
						"status": globalConstant.TASK_STATUS_POSTED,
						"rooms":  newRooms,
						"cost":   expectCost.Cost,
					},
					"createdBy": reqBody.UserId,
				}
				updateTaskChangesHistory(bson.M{"_id": task.XId}, historyData)
			}()

			// Resend updated task notification to the Taskers
			wg.Add(1)
			go func() {
				defer wg.Done()
				// Remove all old notificaions of this task.
				globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"taskId": task.XId})
				// TODO:
				// appLogger('info', 'Update task date time before expire.', {
				// 	taskId: task._id,
				// 	phone: task.phone,
				// 	newDate: newDate,
				// 	oldCost: task.cost,
				// 	newCost: expectCost
				// });
				// removeJobResendNotification(task._id);
				asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"isBlacklist": 1})
				if asker != nil && !asker.IsBlacklist {
					// Check task is emergency
					timeInBetween := taskDate.Sub(globalLib.GetCurrentTime(local.TimeZone)).Minutes()
					var emergencyTaskWithin int32 = 180
					var setting *modelSettings.Settings
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"emergencyTaskWithin": 1}, &setting)
					if setting != nil && setting.EmergencyTaskWithin > 0 {
						emergencyTaskWithin = setting.EmergencyTaskWithin
					}
					isEmergency := timeInBetween <= float64(emergencyTaskWithin)
					// Connect to push notification new task
					client, connect, err := pushNotificationNewTask.ConnectGRPCPushNotificationNewTask(cfg.GRPC_Send_Task_URL)
					if err != nil {
						local.Logger.Warn("Connect GRPC Push Notification New Task error",
							zap.String("url", r.RequestURI),
							zap.Error(err),
						)
					}
					defer connect.Close()
					req := &modelPushNotificationNewTask.NewTaskRequest{
						Service: &modelPushNotificationNewTask.NewTaskRequestService{
							XId: task.ServiceId,
						},
						Booking: &modelPushNotificationNewTask.NewTaskRequestBooking{
							XId:     task.XId,
							IsoCode: task.IsoCode,
						},
					}
					if isEmergency {
						// Send to all Taskers
						_, err = client.Normal(context.Background(), req)
						if err != nil {
							local.Logger.Warn("Push Notification New Task Normal error",
								zap.String("url", r.RequestURI),
								zap.Error(err),
							)
						}
					} else {
						_, err = client.NewTaskToDistrict(context.Background(), req)
						if err != nil {
							local.Logger.Warn("Push Notification New Task District error",
								zap.String("url", r.RequestURI),
								zap.Error(err),
							)
						}
					}
				}
			}()
			wg.Wait()
			// With CONFIRMED task send notfify about changing to Tasker
			sendNotificationChangeTaskToTasker(task)
			globalResponse.ResponseSuccess(w, nil)
		} else {
			local.Logger.Warn(lib.ERROR_HOUSEKEEPING_NOT_FOUND.ErrorCode,
				zap.String("url", r.RequestURI),
				zap.Any("body", reqBody),
			)
			globalResponse.ResponseError(w, lib.ERROR_HOUSEKEEPING_NOT_FOUND)
			return
		}
	} else {
		local.Logger.Warn(lib.ERROR_TASK_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_TASK_NOT_FOUND)
		return
	}
}

/*
 * @Description: Get Price
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
func getPrice(booking *modelTask.Task) (*modelPricingResponse.CostResult, error) {
	pricingRequest := initDataToGetPrice(booking)

	client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(cfg.GRPCPricingPort)
	if err != nil {
		return nil, err
	}
	defer connect.Close()

	response, err := client.GetPricingHousekeeping(context.Background(), pricingRequest)
	if err != nil {
		return nil, err
	}
	return response, nil
}

/*
 * @Description: Init Data To Get Price
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: ngoctb
 */
func initDataToGetPrice(booking *modelTask.Task) *modelPricingRequest.PricingRequest {
	service := modelService.Service{
		XId: booking.ServiceId,
	}
	task := modelTask.Task{
		TaskPlace: &modelTask.TaskPlace{
			City:     booking.TaskPlace.City,
			District: booking.TaskPlace.District,
		},
		Date:             booking.Date,
		Duration:         booking.Duration,
		HomeType:         booking.HomeType,
		AutoChooseTasker: booking.AutoChooseTasker,
		DetailHostel:     booking.DetailHostel,
	}

	// Add promotion code if exist
	if booking.Promotion != nil && booking.Promotion.Code != "" {
		task.Promotion = &modelTask.TaskPromotion{
			Code: booking.Promotion.Code,
		}
	}

	return &modelPricingRequest.PricingRequest{Service: &service, Task: &task, IsoCode: booking.IsoCode}
}

/*
 * @Description: Update Task Changes History
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 20/11/2020
 * @UpdatedBy: ngoctb
 */
func updateTaskChangesHistory(condition interface{}, data map[string]interface{}) {
	updateData := map[string]interface{}{
		"key":       data["key"],
		"content":   data["data"],
		"createdBy": data["createdBy"],
		"createdAt": globalLib.GetCurrentTime(local.TimeZone),
	}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], condition, bson.M{"$push": bson.M{"changesHistory": updateData}})
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateUpdateTaskRooms(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.TaskId == "" {
		return &lib.ERROR_BOOKING_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.RoomIds == nil || len(reqBody.RoomIds) == 0 {
		return &lib.ERROR_ROOM_ID_REQUIRED
	}
	return nil
}

func sendNotificationChangeTaskToTasker(task *modelTask.Task) {
	var arrayNotification []interface{}
	var arrayUserIds []*modelPushNotificationRequest.PushNotificationRequestUserIds
	for _, v := range task.AcceptedTasker {
		taskerId := v.TaskerId
		if v.CompanyId != "" {
			taskerId = v.CompanyId
		}
		tasker, _ := modelUser.GetOneById(local.ISO_CODE, taskerId, bson.M{"language": 1})
		lang := globalConstant.LANG_EN
		if tasker != nil && tasker.Language != "" {
			lang = tasker.Language
		}
		notify := &modelNotification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      v.TaskerId,
			Type:        25,
			TaskId:      task.XId,
			Description: localization.T(lang, "NOTIFY_TASK_CHANGE_ROOMS_TEXT"),
			NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
			CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		}
		arrayNotification = append(arrayNotification, notify)
		arrayUserIds = append(arrayUserIds, &modelPushNotificationRequest.PushNotificationRequestUserIds{UserId: v.TaskerId, Language: lang})
	}
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFY_TASK_CHANGE_ROOMS_TITLE"),
		En: localization.T("en", "NOTIFY_TASK_CHANGE_ROOMS_TITLE"),
		Ko: localization.T("ko", "NOTIFY_TASK_CHANGE_ROOMS_TITLE"),
		Th: localization.T("th", "NOTIFY_TASK_CHANGE_ROOMS_TITLE"),
	}
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s-%s", task.ServiceText.Vi, localization.T("vi", "NOTIFY_TASK_CHANGE_ROOMS_TEXT")),
		En: fmt.Sprintf("%s-%s", task.ServiceText.En, localization.T("en", "NOTIFY_TASK_CHANGE_ROOMS_TEXT")),
		Ko: fmt.Sprintf("%s-%s", task.ServiceText.Ko, localization.T("ko", "NOTIFY_TASK_CHANGE_ROOMS_TEXT")),
		Th: fmt.Sprintf("%s-%s", task.ServiceText.Th, localization.T("th", "NOTIFY_TASK_CHANGE_ROOMS_TEXT")),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       25,
		TaskId:     task.XId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	lib.SendNotification(arrayNotification, arrayUserIds, title, body, payload, "")
}
