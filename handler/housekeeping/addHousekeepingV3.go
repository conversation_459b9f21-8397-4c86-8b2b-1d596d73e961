/*
 * @File: addHousekeepingV3.go
 * @Description: Handler function of add house keeping api
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
package housekeeping

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	"go.uber.org/zap"
)

/*
 * @Description: Insert Housekeeping V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func AddHousekeepingV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	var reqBody modelHousekeeping.Housekeeping
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate data
	validData := validateDataAddHousekeepingV3(&reqBody)
	if validData != nil {
		local.Logger.Warn(validData.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, *validData)
		return
	}
	if len(reqBody.Rooms) > 0 {
		for _, room := range reqBody.Rooms {
			room.Id = globalLib.GenerateObjectId()
			room.CreatedAt = globalLib.GetCurrentTimestamp(local.TimeZone)
		}
	}
	// Insert data to db
	objHostel, errCode, err := insertHostelV3(&reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, objHostel)
}

/*
 * @Description: Validate Params Housekeeping V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func validateDataAddHousekeepingV3(req *modelHousekeeping.Housekeeping) *globalResponse.ResponseErrorCode {
	if req.Name == "" {
		return &lib.ERROR_NAME_REQUIRED
	}
	if req.Address == "" {
		return &lib.ERROR_ADDRESS_REQUIRED
	}
	if req.TaskPlace == nil {
		return &lib.ERROR_TASK_PLACE_REQUIRED
	}
	if req.HomeType == "" {
		return &lib.ERROR_HOME_TYPE_REQUIRED
	}
	if req.HouseNumber == "" {
		return &lib.ERROR_HOUSE_NUMBER_REQUIRED
	}
	if req.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if req.IsoCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if req.IsoCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}

/*
 * @Description: insert hostel to database V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func insertHostelV3(reqBody *modelHousekeeping.Housekeeping) (*modelHousekeeping.Housekeeping, *globalResponse.ResponseErrorCode, error) {
	objHostel := &modelHousekeeping.Housekeeping{
		XId:         globalLib.GenerateObjectId(),
		Name:        reqBody.Name,
		UserId:      reqBody.UserId,
		Address:     reqBody.Address,
		HomeType:    reqBody.HomeType,
		TaskPlace:   reqBody.TaskPlace,
		HouseNumber: reqBody.HouseNumber,
		ContactName: reqBody.ContactName,
		IsoCode:     reqBody.IsoCode,
		Status:      globalConstant.HOUSEKEEPING_STATUS_ACTIVE,
		Rooms:       reqBody.Rooms,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	if reqBody.PhoneNumber != "" {
		objHostel.PhoneNumber = reqBody.PhoneNumber
	}
	if reqBody.CountryCode != "" {
		objHostel.CountryCode = reqBody.CountryCode
	}
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], objHostel)
	if err != nil {
		return nil, &globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "INSERT_FAILED", Message: err.Error()}, err
	}
	return objHostel, nil, nil
}
