/*
 * @File: addRoomV3.go
 * @Description: Handler function of add room api
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
package housekeeping

import (
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelHousekeeping "gitlab.com/btaskee/go-services-model-v2/grpcmodel/housekeeping"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Add Room Housekeeping V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func AddRoomV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Decode request body
	reqBody := make(map[string]interface{})
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate data
	validData := validateDataAddRoomV3(reqBody)
	if validData != nil {
		local.Logger.Warn(validData.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *validData)
		return
	}

	// Check if housekeeping is exists
	isHousekeepingExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], bson.M{"_id": reqBody["housekeepingId"].(string), "userId": reqBody["userId"].(string)})
	if !isHousekeepingExist {
		local.Logger.Warn(lib.ERROR_HOUSEKEEPING_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_HOUSEKEEPING_NOT_FOUND)
		return
	}

	// Update housekeeping add room
	objRoom, errCode, err := addRoomToHousekeepingV3(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
			zap.Error(err),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, objRoom)
}

/*
 * @Description: Validate Params Room Housekeeping V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func validateDataAddRoomV3(req map[string]interface{}) *globalResponse.ResponseErrorCode {
	if req["housekeepingId"] == nil {
		return &lib.ERROR_ID_REQUIRED
	}
	if req["userId"] == nil {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if req["name"] == nil {
		return &lib.ERROR_NAME_REQUIRED
	}
	if req["area"] == nil {
		return &lib.ERROR_AREA_REQUIRED
	}
	if req["images"] == nil {
		return &lib.ERROR_IMAGES_NOT_VALID
	} else {
		typeOf := reflect.ValueOf(req["images"])
		if typeOf.Kind() != reflect.Slice {
			return &lib.ERROR_IMAGES_NOT_VALID
		}
	}
	return nil
}

/*
 * @Description: Add room to housekeeping V3
 * @CreatedAt: 18/05/2021
 * @Author: vinhnt
 */
func addRoomToHousekeepingV3(reqBody map[string]interface{}) (*modelHousekeeping.HousekeepingRooms, *globalResponse.ResponseErrorCode, error) {
	var note string
	if reqBody["note"] != nil {
		note = reqBody["note"].(string)
	}
	objImages := reqBody["images"].([]interface{})
	images := make([]string, len(objImages))
	for k, v := range objImages {
		images[k] = v.(string)
	}
	objRoom := &modelHousekeeping.HousekeepingRooms{
		Id:        fmt.Sprintf("room%s", globalLib.GenerateObjectId()),
		Name:      reqBody["name"].(string),
		Area:      reqBody["area"].(float64),
		Note:      note,
		Images:    images,
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}

	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], reqBody["housekeepingId"].(string), bson.M{"$push": bson.M{"rooms": objRoom}})
	if err != nil {
		return nil, &globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "ADD_FAILED", Message: err.Error()}, err
	}
	return objRoom, nil, nil
}
