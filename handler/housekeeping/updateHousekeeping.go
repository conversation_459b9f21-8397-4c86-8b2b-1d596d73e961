/*
 * @File: updateHousekeeping.go
 * @Description: Handler function of udpate Housekeeping api
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package housekeeping

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update Housekeeping
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func UpdateHousekeeping(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	var reqBody map[string]interface{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	// Validate request params
	if reqBody["_id"] == nil {
		local.Logger.Warn(lib.ERROR_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_ID_REQUIRED)
		return
	}

	// Update housekeeping in db
	id := reqBody["_id"].(string)
	delete(reqBody, "_id")
	reqBody["updatedAt"] = globalLib.GetCurrentTime(local.TimeZone)

	_, err = globalDataAccess.UpdateOneById(globalCollection.COLLECTION_HOUSEKEEPING[local.ISO_CODE], id, bson.M{"$set": reqBody})
	if err != nil {
		local.Logger.Warn("UPDATE_FAILED",
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, globalResponse.ResponseErrorCode{StatusCode: lib.ERROR_CODE_SERVER, ErrorCode: "UPDATE_FAILED", Message: err.Error()})
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}
