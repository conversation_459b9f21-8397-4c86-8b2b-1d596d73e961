/*
 * @File: updateScheduleTime.go
 * @Description: Handler function of update schedule time api
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package taskSchedule

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTaskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update schedule time by scheduleId
 * @CreatedAt: 08/10/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func UpdateScheduleTime(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateUpdateScheduleTime(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	status := globalConstant.TASK_SCHEDULE_STATUS_INACTIVE
	if len(reqBody.Weekday) > 0 {
		status = globalConstant.TASK_SCHEDULE_STATUS_ACTIVE
	}
	scheduleTime := reqBody.ScheduleTime.In(local.TimeZone)
	updateData := bson.M{
		"status":         status,
		"weeklyRepeater": reqBody.Weekday,
		"scheduleTime":   scheduleTime,
		"updatedAt":      globalLib.GetCurrentTime(local.TimeZone),
	}
	var schedule *modelTaskSchedule.TaskSchedule
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], reqBody.ScheduleId, bson.M{"serviceText": 1, "serviceId": 1}, &schedule)
	if schedule == nil {
		globalResponse.ResponseError(w, lib.ERROR_DATA_NOT_FOUND)
		return
	}
	// Check time is in postting limit
	errCode = checkTimeInPostingLimit(schedule, reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	if !globalLib.IsHomeCookingService(schedule.ServiceText.En) {
		updateData["scheduleDuration"] = reqBody.ScheduleDuration
	}
	globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE],
		bson.M{"_id": reqBody.ScheduleId, "askerId": reqBody.UserId},
		bson.M{"$set": updateData},
	)
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateUpdateScheduleTime(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ScheduleId == "" {
		return &lib.ERROR_SCHEDULE_ID_REQUIRED
	}
	if reqBody.ScheduleTime == nil {
		return &lib.ERROR_SCHEDULE_TIME_REQUIRED
	}
	if reqBody.ScheduleDuration == 0 {
		return &lib.ERROR_SCHEDULE_DURATION_REQUIRED
	}
	return nil
}

/*
 * @Description: Check time is in posting limit
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func checkTimeInPostingLimit(schedule *modelTaskSchedule.TaskSchedule, reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if schedule.ServiceId != "" {
		var service *modelService.Service
		globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], schedule.ServiceId, bson.M{"postingLimits": 1}, &service)
		if service != nil && service.PostingLimits != nil && service.PostingLimits.From != "" && service.PostingLimits.To != "" {
			from := strings.Split(service.PostingLimits.From, ":")
			fromHour, _ := strconv.Atoi(from[0])
			fromMinute, _ := strconv.Atoi(from[1])

			to := strings.Split(service.PostingLimits.To, ":")
			toHour, _ := strconv.Atoi(to[0])
			toMinute, _ := strconv.Atoi(to[1])

			newDate := reqBody.ScheduleTime.In(local.TimeZone)
			beginTimeInADay := time.Date(newDate.Year(), newDate.Month(), newDate.Day(), int(fromHour), int(fromMinute), 0, 0, local.TimeZone)
			endTimeInADay := time.Date(newDate.Year(), newDate.Month(), newDate.Day(), int(toHour), int(toMinute), 0, 0, local.TimeZone)
			newDateWithDuration := newDate.Add(time.Minute * time.Duration(reqBody.ScheduleDuration*60))
			if newDate.After(endTimeInADay) || newDate.Before(beginTimeInADay) || newDateWithDuration.After(endTimeInADay) || newDateWithDuration.Before(beginTimeInADay) {
				return &lib.ERROR_TIME_NOT_ALLOW
			}
		}
	}
	return nil
}
