/*
 * @File: activeTaskSchedule.go
 * @Description: Handler function of get schedule tasks by userId api
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package taskSchedule

import (
	"encoding/json"
	"net/http"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelTaskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Schedule Tasks by UserId
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetScheduleTasks(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateGetScheduleTasks(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Get list tasks
	listTaskSchedule := getListTaskSchedule(reqBody)
	// Get data from list tasks
	var mapTasks []map[string]interface{}
	// Get data
	if len(listTaskSchedule) > 0 {
		var serviceIds, taskIds, taskerIds []string
		for _, t := range listTaskSchedule {
			if t.ServiceId != "" {
				serviceIds = append(serviceIds, t.ServiceId)
			}
			if t.TaskId != "" {
				taskIds = append(taskIds, t.TaskId)
			}
			if t.ForceAcceptTaskerId != "" {
				taskerIds = append(taskerIds, t.ForceAcceptTaskerId)
			}
		}
		w := sync.WaitGroup{}
		// get services
		var mapServiceById map[string]*modelService.Service
		w.Add(1)
		go func() {
			defer w.Done()
			mapServiceById = getMapServiceById(serviceIds)
		}()
		// get tasks
		var mapTaskById map[string]*modelTask.Task
		w.Add(1)
		go func() {
			defer w.Done()
			mapTaskById = getMapTaskById(taskIds)
		}()
		// get tasker
		var mapTaskerById map[string]interface{}
		w.Add(1)
		go func() {
			defer w.Done()
			mapTaskerById = getMapTaskerById(taskerIds)
		}()
		w.Wait()
		for _, t := range listTaskSchedule {
			item := make(map[string]interface{})
			b, _ := json.Marshal(t)
			json.Unmarshal(b, &item)
			// map data service
			mapDataService(item, t.ServiceId, mapServiceById)
			// map data task
			mapDataTask(item, t.TaskId, mapTaskById)
			// map data tasker
			if tasker, ok := mapTaskerById[t.ForceAcceptTaskerId]; ok && tasker != nil && t.ForceAcceptTaskerId != "" {
				item["forceAcceptTasker"] = tasker
			}
			// map data schedule
			mapTasks = append(mapTasks, item)
		}
	}
	if len(mapTasks) == 0 {
		for _, t := range listTaskSchedule {
			item := make(map[string]interface{})
			b, _ := json.Marshal(t)
			json.Unmarshal(b, &item)
			mapTasks = append(mapTasks, item)
		}
	}
	globalResponse.ResponseSuccess(w, mapTasks)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetScheduleTasks(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}

func getListTaskSchedule(reqBody *model.ApiRequest) []*modelTaskSchedule.TaskSchedule {
	var listTaskSchedule []*modelTaskSchedule.TaskSchedule
	globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE],
		bson.M{
			"askerId": reqBody.UserId,
			"status":  bson.M{"$in": [2]string{globalConstant.TASK_SCHEDULE_STATUS_ACTIVE, globalConstant.TASK_SCHEDULE_STATUS_INACTIVE}},
		},
		bson.M{"_id": 1, "serviceId": 1, "status": 1, "weeklyRepeater": 1, "beginAt": 1, "duration": 1, "payment": 1, "acceptedTasker": 1, "isoCode": 1, "taskId": 1, "scheduleTime": 1, "scheduleDuration": 1, "serviceName": 1, "serviceText": 1, "forceAcceptTaskerId": 1, "createdAt": 1},
		bson.D{{Key: "status", Value: 1}, {Key: "createdAt", Value: -1}},
		&listTaskSchedule,
	)
	return listTaskSchedule
}

func getMapServiceById(serviceIds []string) map[string]*modelService.Service {
	mapServiceById := make(map[string]*modelService.Service)
	if len(serviceIds) == 0 {
		return mapServiceById
	}
	serviceIds = globalLib.UniqString(serviceIds)
	var services []*modelService.Service
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": serviceIds}},
		bson.M{"_id": 1, "text": 1, "icon": 1},
		&services,
	)
	for _, service := range services {
		mapServiceById[service.XId] = service
	}
	return mapServiceById
}

func getMapTaskById(taskIds []string) map[string]*modelTask.Task {
	mapTaskById := make(map[string]*modelTask.Task)
	if len(taskIds) == 0 {
		return mapTaskById
	}
	taskIds = globalLib.UniqString(taskIds)
	var listTasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": taskIds}},
		bson.M{"_id": 1, "date": 1, "duration": 1, "address": 1, "status": 1, "createdAt": 1, "serviceId": 1, "detailChildCare": 1, "detailOfficeCleaning": 1, "taskPlace": 1},
		&listTasks,
	)
	for _, t := range listTasks {
		mapTaskById[t.XId] = t
	}
	return mapTaskById
}

func getMapTaskerById(taskerIds []string) map[string]interface{} {
	mapTaskerById := make(map[string]interface{})
	if len(taskerIds) == 0 {
		return mapTaskerById
	}
	taskerIds = globalLib.UniqString(taskerIds)
	taskers, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": taskerIds}}, bson.M{"_id": 1, "name": 1, "avatar": 1, "gender": 1, "isPremiumTasker": 1})
	for _, tasker := range taskers {
		mapTaskerById[tasker.XId] = tasker
	}
	return mapTaskerById
}

func mapDataService(item map[string]interface{}, serviceId string, mapServiceById map[string]*modelService.Service) {
	if s, ok := mapServiceById[serviceId]; s != nil && ok {
		serviceName := ""
		if s.Text != nil {
			serviceName = globalLib.GetServiceKeyName(s.Text.En)
		}
		item["service"] = map[string]interface{}{
			"_id":  s.XId,
			"name": serviceName,
			"text": s.Text,
			"icon": s.Icon,
		}
	}
}

func mapDataTask(item map[string]interface{}, taskId string, mapTaskById map[string]*modelTask.Task) {
	if task, ok := mapTaskById[taskId]; ok && task != nil {
		item["task"] = map[string]interface{}{
			"_id":                  task.XId,
			"date":                 task.Date,
			"duration":             task.Duration,
			"address":              task.Address,
			"status":               task.Status,
			"serviceId":            task.ServiceId,
			"createdAt":            task.CreatedAt,
			"detailChildCare":      task.DetailChildCare,
			"detailOfficeCleaning": task.DetailOfficeCleaning,
		}
		item["taskPlace"] = task.TaskPlace
		item["address"] = task.Address
		item["date"] = task.Date
	}
}
