/*
 * @File: activeTaskSchedule.go
 * @Description: Handler function of active task schedule api
 * @CreatedAt: 08/09/2020
 * @Author: linhnh
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
package taskSchedule

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Active Task Schedule by ScheduleId
 * @CreatedAt: 08/10/2020
 * @Author: ngoctb3
 * @UpdatedAt: 04/03/2021
 * @UpdatedBy: ngoctb3
 */
func ActiveTaskSchedule(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data request
	errCode = validateActiveTaskSchedule(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	newStatus := globalConstant.TASK_SCHEDULE_STATUS_INACTIVE
	if reqBody.IsActive {
		newStatus = globalConstant.TASK_SCHEDULE_STATUS_ACTIVE
	}
	globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE],
		bson.M{"_id": reqBody.ScheduleId, "askerId": reqBody.UserId},
		bson.M{"$set": bson.M{"status": newStatus, "updatedAt": globalLib.GetCurrentTime(local.TimeZone)}},
	)
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 04/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateActiveTaskSchedule(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ScheduleId == "" {
		return &lib.ERROR_SCHEDULE_ID_REQUIRED
	}
	return nil
}
