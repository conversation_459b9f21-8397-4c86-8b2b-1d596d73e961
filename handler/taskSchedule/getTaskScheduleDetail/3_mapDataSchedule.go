package getTaskScheduleDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

// Map data Service
func mapDataService(item map[string]interface{}, serviceId string) {
	service := getServiceById(serviceId)
	if service == nil {
		return
	}
	serviceName := ""
	if service.Text != nil {
		serviceName = globalLib.GetServiceKeyName(service.Text.En)
	}
	// map service to item
	item["service"] = map[string]interface{}{
		"_id":  service.XId,
		"name": serviceName,
		"text": service.Text,
		"icon": service.Icon,
	}
}

// Get data Service by Id
func getServiceById(serviceId string) *modelService.Service {
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceId, bson.M{"_id": 1, "text": 1, "icon": 1}, &service)
	return service
}

// Map data Task
func mapDataTask(item map[string]interface{}, taskId string) {
	task := getTaskById(taskId)
	if task == nil {
		return
	}
	// map task to item
	item["task"] = map[string]interface{}{
		"_id":                  task.XId,
		"date":                 task.Date,
		"duration":             task.Duration,
		"address":              task.Address,
		"status":               task.Status,
		"serviceId":            task.ServiceId,
		"createdAt":            task.CreatedAt,
		"detailChildCare":      task.DetailChildCare,
		"detailOfficeCleaning": task.DetailOfficeCleaning,
	}
	item["address"] = task.Address
	item["date"] = task.Date
}

// Get data Task by Id
func getTaskById(taskId string) *modelTask.Task {
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"_id": 1, "date": 1, "duration": 1, "address": 1, "status": 1, "createdAt": 1, "serviceId": 1, "detailChildCare": 1, "detailOfficeCleaning": 1}, &task)
	return task
}
