package getTaskScheduleDetail

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelTaskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	"go.mongodb.org/mongo-driver/bson"
)

// GetTaskSchedule by Id
func getTaskSchedule(scheduleId string) (*modelTaskSchedule.TaskSchedule, *globalResponse.ResponseErrorCode) {
	var taskSchedule *modelTaskSchedule.TaskSchedule
	globalDataAccess.GetOneById(
		globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE],
		scheduleId,
		bson.M{"_id": 1, "serviceId": 1, "status": 1, "weeklyRepeater": 1, "beginAt": 1, "duration": 1, "payment": 1, "acceptedTasker": 1, "isoCode": 1, "taskId": 1, "scheduleTime": 1, "scheduleDuration": 1, "serviceName": 1, "serviceText": 1, "forceAcceptTaskerId": 1, "createdAt": 1},
		&taskSchedule,
	)
	if taskSchedule == nil {
		return nil, &lib.ERROR_TASK_SCHEDULE_NOT_FOUND
	}
	return taskSchedule, nil
}
