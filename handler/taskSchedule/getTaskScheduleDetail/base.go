package getTaskScheduleDetail

import (
	"encoding/json"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetTaskScheduleDetail(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Validate data request
	errCode := validateGetScheduleTasks(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	// Get list tasks
	taskSchedule, errCode := getTaskSchedule(reqBody.ScheduleId)
	if errCode != nil {
		return nil, errCode
	}
	// map data
	mapSchedule := make(map[string]interface{})
	b, _ := json.Marshal(taskSchedule)
	json.Unmarshal(b, &mapSchedule)
	// Get data Service
	if taskSchedule.ServiceId != "" {
		mapDataService(mapSchedule, taskSchedule.ServiceId)
	}
	// Get data Task
	if taskSchedule.TaskId != "" {
		mapDataTask(mapSchedule, taskSchedule.TaskId)
	}
	// Return data
	return mapSchedule, nil
}
