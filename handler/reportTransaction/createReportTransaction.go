/*
 * @File: createReportTransaction.go
 * @Description: Bussiness function to process api createReportTransaction
 * @CreatedAt: 25/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package reportTransaction

import (
	"fmt"
	"net/http"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelFinancialAccountTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelReportTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reportTransaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: CreateReportTransaction
 * @CreatedAt: 25/12/2020
 * @Author: vinhnt
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func CreateReportTransaction(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateCreateReportTransaction(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE], bson.M{"transactionId": reqBody.TransactionId})
	if isExist {
		globalResponse.ResponseError(w, lib.ERROR_TRANSACTION_HAS_BEEN_REPORT)
		return
	}
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "name": 1, "phone": 1})
	if user == nil {
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	// Insert Data collection ReportTransaction
	insertReportTransaction(reqBody)
	// Post to Slack
	postReportTransactionToSlack(user, reqBody)

	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: post report to slack
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func postReportTransactionToSlack(user *modelUser.Users, reqBody *model.ApiRequest) {
	message := fmt.Sprintf("-------------------------------------\nKhách hàng %s, SĐT: %s báo cáo sự cố giao dịch. TransactionId %s. TransactionInfo %s", user.Name, user.Phone, reqBody.TransactionId, getTransactionInfo(reqBody.TransactionId))
	if reqBody.ReasonReport != nil && len(reqBody.ReasonReport) > 0 {
		for i, v := range reqBody.ReasonReport {
			if ok := v.Vi != ""; ok {
				message = fmt.Sprintf("%s\n%d: %v", message, i+1, v.Vi)
			} else {
				message = fmt.Sprintf("%s\n%d: %v", message, i+1, v.En)
			}
		}
	}
	if reqBody.OtherReport != "" {
		message = fmt.Sprintf("%s\nLý do khác: %s", message, reqBody.OtherReport)
	}
	message = fmt.Sprintf("%s\n-------------------------------------", message)
	globalLib.PostToSlack(cfg.SlackToken, globalConstant.REPORT_TRANSACTION_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
}

func getTransactionInfo(transactionId string) (info string) {
	var transaction *modelFinancialAccountTransaction.FinancialAccountTransaction
	globalDataAccess.GetOneById(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], transactionId, bson.M{"source": 1}, &transaction)

	infoDatas := []string{}
	if transaction.GetSource().GetTaskId() != "" {
		infoDatas = append(infoDatas, fmt.Sprintf("taskId: %s", transaction.GetSource().GetTaskId()))
	}
	if transaction.GetSource().GetSubscriptionCode() != "" {
		infoDatas = append(infoDatas, fmt.Sprintf("subscriptionCode: %s", transaction.GetSource().GetSubscriptionCode()))
	}
	info = strings.Join(infoDatas, ", ")
	return
}

/*
 * @Description: Insert report transaction to db
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func insertReportTransaction(reqBody *model.ApiRequest) {
	insertData := &modelReportTransaction.ReportTransaction{
		XId:           globalLib.GenerateObjectId(),
		UserId:        reqBody.UserId,
		TransactionId: reqBody.TransactionId,
		Other:         reqBody.OtherReport,
		IsoCode:       reqBody.ISOCode,
		PaymentMethod: reqBody.PaymentMethod,
		CreatedAt:     globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	if reqBody.ReasonReport != nil {
		insertData.Reason = reqBody.ReasonReport
	}
	globalDataAccess.InsertOne(globalCollection.COLLECTION_REPORT_TRANSACTION[local.ISO_CODE], insertData)
}

/*
 * @Description: CreateReportTransaction
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateCreateReportTransaction(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.TransactionId == "" {
		return &lib.ERROR_TRANSACTION_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	if reqBody.ReasonReport == nil && reqBody.OtherReport == "" {
		return &lib.ERROR_REASON_REPORT_REQUIRED
	}
	return nil
}
