/*
 * @File: addUserIntoServiceChannel-v2.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package serviceChannel

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Add User Into Service Channel
 * @CreatedAt: 25/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func AddUserIntoServiceChannelV2(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateAddUserIntoServiceChannelV2(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Update data
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "phone": 1, "name": 1, "workingPlaces": 1})
	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_NOT_FOUND)
		return
	}
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], reqBody.ServiceId, bson.M{"text": 1}, &service)
	if service != nil {
		globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": reqBody.ServiceId}, bson.M{"$addToSet": bson.M{"taskerList": user.XId}})
		// Post to slack for Tasker team handle
		var city string
		if user.WorkingPlaces != nil && len(user.WorkingPlaces) > 0 {
			city = user.WorkingPlaces[0].City
		}
		message := fmt.Sprintf("[%s] - Tasker đã đăng ký dịch vụ %s trên app: %s - %s", city, service.Text.Vi, user.Name, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, "new-cooking-tasker", "bTaskee System", message)
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateAddUserIntoServiceChannelV2(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ServiceId == "" {
		return &lib.ERROR_SERVICE_ID_REQUIRED
	}
	return nil
}
