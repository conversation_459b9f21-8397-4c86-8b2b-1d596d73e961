/*
 * @File: getServicesChannel.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 24/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package serviceChannel

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get Services Channel by UserId
 * @CreatedAt: 17/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func GetServicesChannel(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse request params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetServicesChannel(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"phone": 1})
	var settings *modelSetting.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &settings)
	var isTesting bool
	if user != nil && user.Phone != "" && settings != nil && len(settings.Tester) > 0 && globalLib.FindStringInSlice(settings.Tester, user.Phone) >= 0 {
		isTesting = true
	}
	serviceCondition := []bson.M{
		{"status": globalConstant.SERVICE_STATUS_ACTIVE},
	}
	if isTesting {
		serviceCondition = append(serviceCondition, bson.M{"status": globalConstant.SERVICE_STATUS_INACTIVE})
	}
	var services, result []*modelService.Service
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"$or": serviceCondition}, bson.M{"text": 1, "minMoneyDeposite": 1}, &services)
	var serviceChannel []*modelServiceChannel.ServiceChannel
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"taskerList": reqBody.UserId}, bson.M{"serviceId": 1}, &serviceChannel)
	var serviceIds []string
	if len(serviceChannel) > 0 {
		for _, v := range serviceChannel {
			serviceIds = append(serviceIds, v.ServiceId)
		}
	}
	if len(serviceIds) == 0 {
		result = services
	} else {
		for _, v := range services {
			if globalLib.FindStringInSlice(serviceIds, v.XId) >= 0 {
				result = append(result, v)
			}
		}
	}
	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetServicesChannel(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
