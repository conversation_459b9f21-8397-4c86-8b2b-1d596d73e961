package paymentV3

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelCardPaymentConfig "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cardPaymentConfig"
	modelPaymentCard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentCard"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelWalletTrueMoney "gitlab.com/btaskee/go-services-model-v2/grpcmodel/walletTrueMoney"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func GetListPaymentV3(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetListPayments(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	cards := getListCards(reqBody)
	trueMoneyWallet := getTrueMoneyWallet(reqBody)
	bPayBalance := getbPayBalance(reqBody)
	result := map[string]interface{}{
		"cards":           cards,
		"trueMoneyWallet": trueMoneyWallet,
		"bPayBalance":     bPayBalance,
	}
	globalResponse.ResponseSuccess(w, result)
}

func validateGetListPayments(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}

func getListCards(reqBody *model.ApiRequest) []*modelPaymentCard.PaymentCard {
	var cardConfig *modelCardPaymentConfig.CardPaymentConfig
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_CARD_PAYMENT_CONFIG[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"enableCardPayment": 1}, &cardConfig)
	// TODO: Dùng field khác để check tích hợp thẻ có đang enable hay không
	//if cardConfig == nil || !cardConfig.EnableCardPayment {
	if cardConfig == nil {
		user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"phone": 1})
		if user != nil && user.Phone != "" {
			var settings *modelSettings.Settings
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &settings)
			if settings != nil && len(settings.Tester) > 0 && globalLib.FindStringInSlice(settings.Tester, user.Phone) < 0 {
				return nil
			}
		}
	}
	query := bson.M{
		"userId": reqBody.UserId,
	}
	var paymentCards []*modelPaymentCard.PaymentCard
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE],
		query,
		bson.M{"shopperIP": 0, "userId": 0},
		&paymentCards)

	// migrate card status
	result := migrateCardStatus(paymentCards)
	return result
}

func migrateCardStatus(paymentCards []*modelPaymentCard.PaymentCard) []*modelPaymentCard.PaymentCard {
	result := []*modelPaymentCard.PaymentCard{}
	for _, v := range paymentCards {
		// Add card status to
		if globalLib.CheckCardExpiry(v) {
			v.Status = globalConstant.PAYMENT_CARD_STATUS_EXPIRED
		} else if v.Disabled {
			v.Status = globalConstant.PAYMENT_CARD_STATUS_DISABLED
		} else {
			v.Status = globalConstant.PAYMENT_CARD_STATUS_ACTIVE
		}

		// Not return cards disable to client
		if v.Status != globalConstant.PAYMENT_CARD_STATUS_DISABLED {
			result = append(result, v)
		}
	}

	return result
}

func getTrueMoneyWallet(reqBody *model.ApiRequest) *modelWalletTrueMoney.WalletTrueMoney {
	var result *modelWalletTrueMoney.WalletTrueMoney
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_WALLET_TRUE_MONEY, bson.M{"userId": reqBody.UserId}, bson.M{}, &result)
	return result
}

func getbPayBalance(reqBody *model.ApiRequest) float64 {
	// Get user by id
	user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"_id": 1, "fAccountId": 1})
	if user == nil {
		return 0
	}

	// Get fAccount if user is exists
	balance, _ := globalLib.GetFAccountByIsoCode(user.FAccountId, local.ISO_CODE)
	if balance == 0 {
		return 0
	}

	// Get task POSTED, WAITING, CONFIRM payment method credit by userId
	date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -globalConstant.LIMIT_TASK_DATE)
	status := [3]string{globalConstant.TASK_STATUS_POSTED, globalConstant.TASK_STATUS_WAITING, globalConstant.TASK_STATUS_CONFIRMED}
	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{
			"date":           bson.M{"$gte": date},
			"askerId":        reqBody.UserId,
			"status":         bson.M{"$in": status},
			"payment.method": globalConstant.PAYMENT_METHOD_CREDIT,
			"isoCode":        local.ISO_CODE,
		},
		bson.M{"cost": 1, "costDetail": 1},
		&tasks,
	)

	// Get money used in task post before
	var creditMoneyUsed float64
	for _, vTask := range tasks {
		cost := vTask.Cost
		if vTask.CostDetail != nil && vTask.CostDetail.FinalCost > 0 {
			cost = vTask.CostDetail.FinalCost
		}
		creditMoneyUsed += cost
	}

	// Calc money can use to book new task
	balance -= creditMoneyUsed
	if balance < 0 {
		return 0
	}
	return balance
}
