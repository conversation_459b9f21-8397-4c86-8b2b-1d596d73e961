package getSurveyById

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func GetSurveyById(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Check input
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker, errCode := checkAskerSentSurvey(reqBody.UserId, reqBody.SurveyId)
	if errCode != nil {
		return nil, errCode
	}
	result, errCode := getSurveySetting(reqBody.SurveyId, asker)
	if errCode != nil {
		return nil, errCode
	}

	return result, nil
}
