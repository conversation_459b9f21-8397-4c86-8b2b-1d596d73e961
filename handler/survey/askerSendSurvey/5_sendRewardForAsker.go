package askerSendSurvey

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGift "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gift"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	modelPromotionCode "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSurveySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/surveySetting"
)

var cfg = config.GetConfig()

func sendRewardForAsker(reqBody *model.ApiRequest, surveySetting *modelSurveySetting.SurveySetting) string {
	if surveySetting.Reward != nil && surveySetting.Reward.PromotionValue > 0 {
		promotionCode, err := insertPromotionCode(surveySetting.Reward, reqBody)
		if err == nil && promotionCode != nil {
			return insertGift(surveySetting.Reward, promotionCode, reqBody)
		}
	}
	return ""
}

func insertPromotionCode(surveyReward *modelSurveySetting.SurveySettingReward, reqBody *model.ApiRequest) (*modelPromotionCode.PromotionCode, error) {
	// Get the current time
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	currentTime_ts := globalLib.ParseTimestampFromDate(currentTime)
	// Set endDate
	promotionPeriod := 30
	if surveyReward.PromotionPeriod > 0 {
		promotionPeriod = int(surveyReward.PromotionPeriod)
	}
	expiredDateGift_ts := globalLib.ParseTimestampFromDate(currentTime.AddDate(0, 0, promotionPeriod))
	// Set value promotion
	promotionType := globalConstant.PROMOTION_TYPE_MONEY
	if surveyReward.PromotionType != "" {
		promotionType = surveyReward.PromotionType
	}

	// Generate promotion code
	code, err := modelPromotionCode.GeneratePromotionCode(local.ISO_CODE, surveyReward.PromotionPrefix, 12)
	if err != nil {
		return nil, err
	}

	// Init data promotion
	promotionCode := &modelPromotionCode.PromotionCode{
		XId: globalLib.GenerateObjectId(),
		Value: &modelPromotionCode.PromotionCodeValue{
			Type:  promotionType,
			Value: surveyReward.PromotionValue,
		},
		UserIds:         []string{reqBody.UserId},
		IsoCode:         local.ISO_CODE,
		Target:          globalConstant.USER_TYPE_ASKER,
		CreatedAt:       currentTime_ts,
		StartDate:       currentTime_ts,
		EndDate:         expiredDateGift_ts,
		TypeOfPromotion: globalConstant.PROMOTION_APPLY_BOTH,
		Description:     "Promotion for asker send Survey report",
		Code:            code,
		CreatedBy:       "SYSTEM",
		Source:          globalConstant.PROMOTION_CODE_SOURCE_SURVEY,
	}
	if surveyReward.PromotionServiceId != "" {
		promotionCode.ServiceId = surveyReward.PromotionServiceId
	}

	// Insert promotion codes into the database
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], promotionCode)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("Error create promotionCode for asker send survey report: promotionCode: %v - err: %v", promotionCode, err))
	}
	return promotionCode, err
}

// NOTE: Remove to use the function GeneratePromotionCode in the modelPromotionCode package
// // getPromotionCodeByPrefix generates a promotion code based on the given prefix and ISO code.
// // It returns the generated promotion code.
// func getPromotionCodeByPrefix(prefix string) string {
// 	// Generate a random string to append to the prefix
// 	randomString := globalLib.RandomString(12 - len(prefix))

// 	// Keep generating codes until a unique one is found
// 	for {
// 		// Create the code by combining the prefix and random string
// 		code := fmt.Sprintf("%s%s", prefix, randomString)

// 		// Check if the generated code already exists in the collection
// 		isUsed, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{
// 			"code": bson.M{
// 				"$regex": primitive.Regex{
// 					Pattern: fmt.Sprintf("^%s$", code),
// 					Options: "i",
// 				},
// 			},
// 		})

// 		// If the code is not used, return it
// 		if !isUsed {
// 			return code
// 		}
// 	}
// }

func insertGift(surveyReward *modelSurveySetting.SurveySettingReward, promotionCode *modelPromotionCode.PromotionCode, reqBody *model.ApiRequest) string {
	gift := &modelGift.Gift{
		XId:     globalLib.GenerateObjectId(),
		UserId:  reqBody.UserId,
		IsoCode: reqBody.ISOCode,
		Image:   "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/riJTXLDjWDm7ymzud",
		BrandInfo: &modelIncentive.IncentiveBrandInfo{
			Name: "bTaskee",
			Text: &modelService.ServiceText{
				En: "bTaskee",
				Vi: "bTaskee",
				Ko: "bTaskee",
				Th: "bTaskee",
				Id: "bTaskee",
			},
			Image: "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/sEFHixKrmjNcp9jaw",
		},
		PromotionCode: promotionCode.Code,
		Expired:       promotionCode.EndDate,
		Source:        globalConstant.GIFT_SOURCE_SYSTEM,
		CreatedAt:     globalLib.GetCurrentTimestamp(local.TimeZone),
		PromotionId:   promotionCode.XId,
	}
	// Set the gift image if provided in the request body
	if surveyReward.Image != "" {
		gift.Image = surveyReward.Image
	}
	if surveyReward.Title != nil {
		gift.Title = surveyReward.Title
	}
	if surveyReward.Content != nil {
		gift.Content = surveyReward.Content
	}
	if surveyReward.Note != nil {
		gift.Content = surveyReward.Note
	}
	if promotionCode.ServiceId != "" {
		gift.ApplyFor = &modelGift.GiftApplyFor{
			Service: []string{promotionCode.ServiceId},
		}
	}
	// Insert all the gifts into the database
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_GIFT[local.ISO_CODE], gift)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("Error create gift for asker send survey report: Gift: %v - err: %v", gift, err))
		return ""
	}
	return gift.XId
}
