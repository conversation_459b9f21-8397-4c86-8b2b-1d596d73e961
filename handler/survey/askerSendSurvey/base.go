package askerSendSurvey

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

func AskerSendSurvey(reqBody *model.ApiRequest) (interface{}, *globalResponse.ResponseErrorCode) {
	// Check input
	errCode := validate(reqBody)
	if errCode != nil {
		return nil, errCode
	}
	asker, errCode := checkAskerSentSurvey(reqBody.ISOCode, reqBody.UserId, reqBody.SurveyId)
	if errCode != nil {
		return nil, errCode
	}
	surveySetting, errCode := getSurveySetting(reqBody.SurveyId, asker)
	if errCode != nil {
		return nil, errCode
	}
	// Insert survey report
	errCode = insertSurveyReport(reqBody, surveySetting)
	if errCode != nil {
		return nil, errCode
	}
	giftId := sendRewardForAsker(reqBody, surveySetting)
	return map[string]interface{}{"giftId": giftId}, nil
}
