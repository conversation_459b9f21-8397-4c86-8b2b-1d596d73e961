package askerSendSurvey

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func checkAskerSentSurvey(isoCode, userId, surveyId string) (*modelUser.Users, *globalResponse.ResponseErrorCode) {
	// Get asker
	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"phone": 1})
	if asker == nil {
		return nil, &lib.ERROR_USER_NOT_FOUND
	}

	isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_SURVEY_REPORT[isoCode], bson.M{"surveyId": surveyId, "userId": userId})
	if isExist {
		return nil, &lib.ERROR_ASKER_HAVE_SENT_SURVEY
	}
	return asker, nil
}
