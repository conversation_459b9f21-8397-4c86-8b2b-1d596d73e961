package askerSendSurvey

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSurveyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/surveyReport"
	modelSurveySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/surveySetting"
)

func insertSurveyReport(reqBody *model.ApiRequest, surveySetting *modelSurveySetting.SurveySetting) *globalResponse.ResponseErrorCode {
	surveyReport := &modelSurveyReport.SurveyReport{
		XId:        globalLib.GenerateObjectId(),
		SurveyId:   reqBody.SurveyId,
		SurveyName: surveySetting.Name,
		UserId:     reqBody.UserId,
		Answers:    reqBody.SurveyAnswers,
		CreatedAt:  globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_SURVEY_REPORT[reqBody.ISOCode], surveyReport)
	if err != nil {
		return &lib.ERROR_INSERT_FAILED
	}
	return nil
}
