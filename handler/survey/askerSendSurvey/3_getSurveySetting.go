package askerSendSurvey

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelSurveySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/surveySetting"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getSurveySetting(surveyId string, asker *modelUser.Users) (*modelSurveySetting.SurveySetting, *globalResponse.ResponseErrorCode) {
	query := bson.M{"_id": surveyId, "status": globalConstant.SURVEY_SETTING_STATUS_ACTIVE, "isTesting": bson.M{"$ne": true}}
	if lib.CheckIsUserTester(asker) {
		delete(query, "isTesting")
	}
	var surveySetting *modelSurveySetting.SurveySetting
	err := globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_SURVEY_SETTING[local.ISO_CODE],
		query,
		bson.M{},
		&surveySetting,
	)

	if err != nil || surveySetting == nil {
		return nil, &lib.ERROR_SURVEY_SETTING_NOT_FOUND
	}
	return surveySetting, nil
}
