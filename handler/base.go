package handler

import (
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcProxy/grpcChatServer"
	modelAskerReferralCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerReferralCampaign"
	modelAskerReferralSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerReferralSetting"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	modelPaymentMethodCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMethodCampaign"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotioncode"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionhistory"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

var cfg = config.GetConfig()

func GetTask(query, fields bson.M) (result *modelTask.Task, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, fields, &result)
	return
}

func GetAskerReferralSetting(query, fields bson.M) (result *modelAskerReferralSetting.AskerReferralSetting, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_ASKER_REFERRAL_SETTING[local.ISO_CODE], query, fields, &result)
	return
}

func GetAskerReferralCampaign(query, fields bson.M) (result *modelAskerReferralCampaign.AskerReferralCampaign, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_ASKER_REFERRAL_CAMPAIGN[local.ISO_CODE], query, fields, &result)
	return
}

func GetIncentives(query, fields bson.M) (result []*modelIncentive.Incentive, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE], query, fields, &result)
	return
}

func GetTasks(query, fields bson.M) (tasks []*modelTask.Task, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, fields, &tasks)
	return tasks, err
}

func GetPromotions(query bson.M, fields bson.M) (result []*promotioncode.PromotionCode, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], query, fields, &result)
	return
}

func GetPromotionHistories(query bson.M, fields bson.M) (result []*promotionhistory.PromotionHistory, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], query, fields, &result)
	return
}

func GetServiceChannels(query bson.M, fields bson.M) (result []*modelServiceChannel.ServiceChannel, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], query, fields, &result)
	return
}

func GetServices(query bson.M, fields bson.M) (result []*modelService.Service, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &result)
	return
}

func GetPaymentMethodCampaigns(query bson.M, fields bson.M) (result []*modelPaymentMethodCampaign.PaymentMethodCampaign, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_PAYMENT_METHOD_CAMPAIGN[local.ISO_CODE], query, fields, &result)
	return
}
func SendChatMessageViaSocket(task *modelTask.Task, chatId string, sendTo []string, message *modelChatMessage.ChatMessageMessages) error {
	receivers := []*kafkaEvent.ConversationMessageReceiver{}
	for _, v := range sendTo {
		receivers = append(receivers, &kafkaEvent.ConversationMessageReceiver{
			UserId: v,
		})
	}
	_, err := grpcChatServer.SendSocketChatMessage(local.ISO_CODE, "", cfg.GRPC_Chat_Server_VN_V3_URL, chatId, receivers, message, task)
	return err
}

func GetService(query, fields bson.M) (result *modelService.Service, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &result)
	return
}
