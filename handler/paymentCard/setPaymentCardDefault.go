/*
 * @File: setPaymentCardDefault.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package paymentCard

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Set Default Payment Card
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func SetPaymentCardDefault(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateSetPaymentCardDefault(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	errCode = updatePaymentCard(reqBody.UserId, reqBody.CardId, reqBody.IsDefault)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Validate request data
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateSetPaymentCardDefault(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.CardId == "" {
		return &lib.ERROR_CARD_ID_REQUIRED
	}
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	return nil
}

/*
 * @Description: Update payment card default in database
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func updatePaymentCard(userId string, cardId string, isDefault bool) *globalResponse.ResponseErrorCode {
	var err error
	if isDefault {
		_, err = globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], bson.M{"userId": userId}, bson.M{"$set": bson.M{"isDefault": false}})
		if err != nil {
			return &lib.ERROR_UPDATE_FAILED
		}
	}

	_, err = globalDataAccess.UpdateOneById(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], cardId, bson.M{"$set": bson.M{"isDefault": isDefault}})
	if err != nil {
		return &lib.ERROR_UPDATE_FAILED
	}
	return nil
}
