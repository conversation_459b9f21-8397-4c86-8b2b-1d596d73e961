/*
 * @File: getListCards.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 28/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package paymentCard

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelCardPaymentConfig "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cardPaymentConfig"
	modelPaymentCard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentCard"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Get List Payment Card
 * @CreatedAt: 15/10/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func GetListCards(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	errCode = validateGetListCards(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Get data
	var cardConfig *modelCardPaymentConfig.CardPaymentConfig
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_CARD_PAYMENT_CONFIG[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"enableCardPayment": 1}, &cardConfig)
	// TODO: Dùng field khác để check tích hợp thẻ có đang enable hay không
	// if cardConfig == nil || !cardConfig.EnableCardPayment {
	if cardConfig == nil {
		user, _ := modelUser.GetOneById(local.ISO_CODE, reqBody.UserId, bson.M{"phone": 1})
		if user != nil && user.Phone != "" {
			var settings *modelSettings.Settings
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"tester": 1}, &settings)
			if settings != nil && len(settings.Tester) > 0 && globalLib.FindStringInSlice(settings.Tester, user.Phone) < 0 {
				globalResponse.ResponseSuccess(w, nil)
				return
			}
		}
	}
	query := bson.M{
		"userId":   reqBody.UserId,
		"disabled": bson.M{"$ne": true},
	}
	var result []*modelPaymentCard.PaymentCard
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE],
		query,
		bson.M{"shopperIP": 0, "userId": 0},
		&result)

	globalResponse.ResponseSuccess(w, result)
}

/*
 * @Description: Validate request data
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateGetListCards(reqBody *model.ApiRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.ISOCode == "" {
		return &lib.ERROR_ISO_CODE_REQUIRED
	}
	if reqBody.ISOCode != local.ISO_CODE {
		return &lib.ERROR_ISO_CODE_INCORRECT
	}
	return nil
}
