package groceryAssistant

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelStoreGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/storeGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func GetStores(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	decoder := json.NewDecoder(r.Body)
	var reqBody ApiRequestGroceryAssistant
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validateDataGetStores(reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var page int64 = 1
	var limit int64 = 20
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}

	var stores []*modelStoreGroceryAssistant.StoreGroceryAssistant
	globalDataAccess.GetAllByQueryPaging(
		globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
		bson.M{"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE, "place.city": reqBody.TaskPlace.City},
		bson.M{"_id": 1, "text": 1, "address": 1, "image": 1, "type": 1},
		page,
		limit,
		&stores,
	)

	if len(stores) == 0 {
		globalResponse.ResponseError(w, lib.ERROR_STORE_GROCERY_ASSISTANT_NOT_FOUND)
		return
	}

	var distance, estimateTime float32
	results := []map[string]interface{}{}
	for _, store := range stores {
		b, _ := json.Marshal(store)
		mapStore := map[string]interface{}{}
		json.Unmarshal(b, &mapStore)
		// TODO
		// Calc distance and estimateTime
		// distance := lib.CalcDistance(reqBody.Lat, reqBody.Lng, store.Lat, store.Lng)
		mapStore["distance"] = distance
		mapStore["estimateTime"] = estimateTime
		results = append(results, mapStore)
	}

	globalResponse.ResponseSuccess(w, results)
}

func validateDataGetStores(reqBody ApiRequestGroceryAssistant) *globalResponse.ResponseErrorCode {
	if reqBody.TaskPlace == nil {
		return &lib.ERROR_TASK_PLACE_REQUIRED
	}
	if reqBody.TaskPlace.City == "" {
		return &lib.ERROR_TASK_PLACE_CITY_REQUIRED
	}
	return nil
}
