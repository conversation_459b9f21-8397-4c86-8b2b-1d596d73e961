package groceryAssistant

import (
	"encoding/json"
	"fmt"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelProductGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/productGroceryAssistant"
	modelStoreGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/storeGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func FindProduct(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	decoder := json.NewDecoder(r.Body)
	var reqBody ApiRequestGroceryAssistant
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validateFindProduct(reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var page int64 = 1
	var limit int64 = 20
	if reqBody.Page > 0 {
		page = reqBody.Page
	}
	if reqBody.Limit > 0 {
		limit = reqBody.Limit
	}
	query := bson.M{"status": globalConstant.PRODUCT_GROCERY_ASSISTANT_STATUS_ACTIVE, "storeId": reqBody.StoreId}
	if reqBody.Text != "" {
		query = searchByText(reqBody.Text, reqBody.Language, query)
	}
	if reqBody.SubCategoryId != "" {
		query = searchBySubCategoryId(reqBody.SubCategoryId, query)
	}
	if reqBody.ProductId != "" {
		query = searchByProductId(reqBody.ProductId, query)
	}
	var products []*modelProductGroceryAssistant.ProductGroceryAssistant
	globalDataAccess.GetAllByQueryPaging(
		globalCollection.COLLECTION_PRODUCT_GROCERY_ASSISTANT[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "SKU": 1, "text": 1, "image": 1, "price": 1, "subCategoryIds": 1},
		page,
		limit,
		&products,
	)
	// Increase number of find popular in sub category
	increaseNumberOfFindPopular(products, reqBody.StoreId)
	globalResponse.ResponseSuccess(w, products)
}

// ======================================

func validateFindProduct(reqBody ApiRequestGroceryAssistant) *globalResponse.ResponseErrorCode {
	if reqBody.Text == "" && reqBody.SubCategoryId == "" && reqBody.ProductId == "" {
		return &lib.ERROR_QUERY_REQUIRED
	}
	if reqBody.Text != "" && reqBody.Language == "" {
		return &lib.ERROR_LANGUAGE_REQUIRED
	}
	if reqBody.StoreId == "" {
		return &lib.ERROR_STORE_ID_REQUIRED
	}
	return nil
}

func searchByText(text string, language string, query bson.M) bson.M {
	searchText := globalLib.RemoveUnicode(text)
	searchKey := "text.vi"
	switch language {
	case globalConstant.LANG_EN:
		searchKey = "text.en"
	case globalConstant.LANG_KO:
		searchKey = "text.ko"
	case globalConstant.LANG_TH:
		searchKey = "text.th"
	}
	query["$or"] = []bson.M{
		{searchKey: text},
		{searchKey: searchText},
		{searchKey: bson.M{"$regex": primitive.Regex{Pattern: fmt.Sprintf("^[%s].*$", text), Options: "i"}}},
		{searchKey: bson.M{"$regex": primitive.Regex{Pattern: fmt.Sprintf("^[%s].*$", searchText), Options: "i"}}},
	}
	return query
}

func searchBySubCategoryId(subCategoryId string, query bson.M) bson.M {
	query["subCategoryIds"] = subCategoryId
	return query
}

func searchByProductId(productId string, query bson.M) bson.M {
	query["_id"] = productId
	return query
}

func increaseNumberOfFindPopular(products []*modelProductGroceryAssistant.ProductGroceryAssistant, storeId string) {
	if len(products) > 0 {
		var subCategoryIds []string
		for _, p := range products {
			if len(p.SubCategoryIds) > 0 {
				subCategoryIds = globalLib.Union(subCategoryIds, p.SubCategoryIds)
			}
		}
		if len(subCategoryIds) > 0 {
			var store *modelStoreGroceryAssistant.StoreGroceryAssistant
			globalDataAccess.GetOneByQuery(
				globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
				bson.M{"categories.subCategories._id": bson.M{"$in": subCategoryIds}, "_id": storeId},
				bson.M{"categories.subCategories._id": 1},
				&store,
			)
			if store != nil && store.Categories != nil && len(store.Categories) > 0 {
				for catIndex, c := range store.Categories {
					if c.SubCategories != nil && len(c.SubCategories) > 0 {
						for subIndex, s := range c.SubCategories {
							if globalLib.FindStringInSlice(subCategoryIds, s.XId) >= 0 {
								updateField := fmt.Sprintf("categories.%d.subCategories.%d.numberOfFindPopular", catIndex, subIndex)
								globalDataAccess.UpdateOneByQuery(
									globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
									bson.M{"categories.subCategories._id": s.XId, "_id": storeId},
									bson.M{"$inc": bson.M{updateField: 1}},
								)
							}
						}
					}
				}
			}
		}
	}
}
