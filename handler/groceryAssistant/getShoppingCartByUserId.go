package groceryAssistant

import (
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelShoppingCartGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/shoppingCartGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func GetShoppingCartByUserId(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Validate data
	if reqBody.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_USER_ID_REQUIRED)
		return
	}

	var shoppingCart *modelShoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": reqBody.UserId}, bson.M{}, &shoppingCart)
	globalResponse.ResponseSuccess(w, shoppingCart)
}
