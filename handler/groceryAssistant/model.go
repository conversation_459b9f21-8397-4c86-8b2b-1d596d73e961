package groceryAssistant

import modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"

const INCREASE_PRODUCT_ACTION = "INCREASE"
const DECREASE_PRODUCT_ACTION = "DECREASE"
const REMOVE_PRODUCT_ACTION = "REMOVE"

var UPDATE_SHOPPING_CART_ACTION = []string{INCREASE_PRODUCT_ACTION, DECREASE_PRODUCT_ACTION, REMOVE_PRODUCT_ACTION}

type ApiRequestGroceryAssistant struct {
	StoreId        string     `json:"storeId,omitempty"`
	CategoryId     string     `json:"categoryId,omitempty"`
	SubCategoryId  string     `json:"subCategoryId,omitempty"`
	ProductId      string     `json:"productId,omitempty"`
	Language       string     `json:"language,omitempty"`
	Text           string     `json:"text,omitempty"`
	TaskPlace      *TaskPlace `json:"taskPlace,omitempty"`
	ShoppingCardId string     `json:"shoppingCardId,omitempty"`

	Page  int64 `json:"page,omitempty"`
	Limit int64 `json:"limit,omitempty"`
}

type UpdateShoppingCartRequest struct {
	UserId        string          `json:"userId,omitempty"`
	ProductId     string          `json:"productId,omitempty"`
	Action        string          `json:"action,omitempty"`
	IsUserCreated bool            `json:"isUserCreated,omitempty"`
	Quantity      int32           `json:"quantity,omitempty"`
	NewProduct    *UserNewProduct `json:"newProduct,omitempty"`
}

type UserNewProduct struct {
	Price      float64                   `json:"price,omitempty"`
	Text       *modelService.ServiceText `json:"text,omitempty"`
	CategoryId string                    `json:"categoryId,omitempty"`
}

type TaskPlace struct {
	Country  string  `json:"country,omitempty"`
	City     string  `json:"city,omitempty"`
	District string  `json:"district,omitempty"`
	Lat      float64 `json:"lat,omitempty"`
	Lng      float64 `json:"lng,omitempty"`
}
