package groceryAssistant

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/productGroceryAssistant"
	modelShoppingCartGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/shoppingCartGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func UpdateShoppingCart(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	decoder := json.NewDecoder(r.Body)
	var reqBody *UpdateShoppingCartRequest
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validateUpdateShoppingCart(reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	now := globalLib.GetCurrentTime(local.TimeZone)
	var shoppingCart *modelShoppingCartGroceryAssistant.ShoppingCartGroceryAssistant
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], bson.M{"userId": reqBody.UserId, "expiredAt": bson.M{"$gt": now}}, bson.M{}, &shoppingCart)

	result := make(map[string]interface{})
	switch reqBody.Action {
	case INCREASE_PRODUCT_ACTION:
		result, errCode = IncreaseProduct(reqBody, shoppingCart)
	case DECREASE_PRODUCT_ACTION:
		result, errCode = DecreaseProduct(reqBody, shoppingCart)
	case REMOVE_PRODUCT_ACTION:
		result, errCode = RemoveProduct(reqBody, shoppingCart)
	}

	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	globalResponse.ResponseSuccess(w, result)
}

func validateUpdateShoppingCart(reqBody *UpdateShoppingCartRequest) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if globalLib.FindStringInSlice(UPDATE_SHOPPING_CART_ACTION, reqBody.Action) < 0 {
		return &lib.ERROR_UPDATE_ACTION_INCORRECT
	}
	if reqBody.IsUserCreated {
		if reqBody.NewProduct == nil {
			return &lib.ERROR_PRODUCT_NAME_REQUIRED
		}
		if reqBody.NewProduct.Text == nil {
			return &lib.ERROR_PRODUCT_NAME_REQUIRED
		}
		if reqBody.NewProduct.Price == 0 {
			return &lib.ERROR_PRODUCT_PRICE_REQUIRED
		}
		if reqBody.NewProduct.CategoryId == "" {
			return &lib.ERROR_CATEGORY_ID_REQUIRED
		}
		if reqBody.Action != INCREASE_PRODUCT_ACTION {
			return &lib.ERROR_UPDATE_ACTION_INCORRECT
		}
	}
	if !reqBody.IsUserCreated && reqBody.ProductId == "" {
		return &lib.ERROR_PRODUCT_ID_REQUIRED
	}

	return nil
}

func IncreaseProduct(reqBody *UpdateShoppingCartRequest, shoppingCart *modelShoppingCartGroceryAssistant.ShoppingCartGroceryAssistant) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	result := make(map[string]interface{})
	var increaseQuantity int32 = 1
	if reqBody.Quantity > 0 {
		increaseQuantity = reqBody.Quantity
	}

	// Check if user already has shopping cart
	var lineItem *modelShoppingCartGroceryAssistant.LineItem
	if reqBody.IsUserCreated {
		lineItem = &modelShoppingCartGroceryAssistant.LineItem{
			XId:           globalLib.GenerateObjectId(),
			Text:          reqBody.NewProduct.Text,
			Price:         reqBody.NewProduct.Price,
			Quantity:      increaseQuantity,
			CategoryIds:   []string{reqBody.NewProduct.CategoryId},
			IsUserCreated: true,
		}
	}

	// Check product in cart
	isProductInCart := false
	if shoppingCart != nil {
		for _, product := range shoppingCart.LineItems {
			if product.XId == reqBody.ProductId {
				isProductInCart = true
				break
			}
		}
	}

	if !reqBody.IsUserCreated && (shoppingCart == nil || !isProductInCart) {
		var product *productGroceryAssistant.ProductGroceryAssistant
		globalDataAccess.GetOneById(globalCollection.COLLECTION_PRODUCT_GROCERY_ASSISTANT[local.ISO_CODE], reqBody.ProductId, bson.M{}, &product)
		if product == nil {
			return nil, &lib.ERROR_PRODUCT_NOT_FOUND
		}
		lineItem = &modelShoppingCartGroceryAssistant.LineItem{
			XId:         product.XId,
			SKU:         product.SKU,
			Text:        product.Text,
			Image:       product.Image,
			Price:       product.Price,
			Quantity:    increaseQuantity,
			CategoryIds: product.CategoryIds,
		}
	}

	if shoppingCart == nil {
		// Check if user already has shopping cart
		now := globalLib.GetCurrentTime(local.TimeZone)
		now_ts := globalLib.ParseTimestampFromDate(now)
		expiredAt := now.AddDate(0, 0, 7)
		expiredAt_ts := globalLib.ParseTimestampFromDate(expiredAt)

		// Insert a new shoppingCart for user
		newShoppingCart := &modelShoppingCartGroceryAssistant.ShoppingCartGroceryAssistant{
			XId:       globalLib.GenerateObjectId(),
			UserId:    reqBody.UserId,
			LineItems: []*modelShoppingCartGroceryAssistant.LineItem{lineItem},
			Discount:  0,
			CreatedAt: now_ts,
			UpdatedAt: now_ts,
			ExpiredAt: expiredAt_ts,
		}
		err := globalDataAccess.InsertOne(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], newShoppingCart)
		if err != nil {
			return nil, &lib.ERROR_INSERT_FAILED
		}
	} else {
		if isProductInCart {
			_, err := globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE],
				bson.M{"_id": shoppingCart.XId, "lineItems._id": reqBody.ProductId},
				bson.M{"$inc": bson.M{"lineItems.$.quantity": increaseQuantity}},
			)
			if err != nil {
				return nil, &lib.ERROR_UPDATE_FAILED
			}
		} else {
			_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE],
				shoppingCart.XId,
				bson.M{"$push": bson.M{"lineItems": lineItem}},
			)
			if err != nil {
				return nil, &lib.ERROR_UPDATE_FAILED
			}
		}
	}

	if reqBody.IsUserCreated {
		result = map[string]interface{}{
			"productId": lineItem.XId,
		}
	}
	return result, nil
}

func DecreaseProduct(reqBody *UpdateShoppingCartRequest, shoppingCart *modelShoppingCartGroceryAssistant.ShoppingCartGroceryAssistant) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	if shoppingCart == nil {
		return nil, &lib.ERROR_UPDATE_ACTION_INCORRECT
	}

	// Check product in cart
	isProductInCart := false
	var quantity int32 = 0
	if shoppingCart != nil {
		for _, product := range shoppingCart.LineItems {
			if product.XId == reqBody.ProductId {
				isProductInCart = true
				quantity = product.Quantity
				break
			}
		}
	}

	if !isProductInCart {
		return nil, &lib.ERROR_UPDATE_ACTION_INCORRECT
	}

	if quantity == 1 {
		if len(shoppingCart.LineItems) > 1 {
			// Remove the product from lineItems
			_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE],
				shoppingCart.XId,
				bson.M{"$pull": bson.M{"lineItems": bson.M{"_id": reqBody.ProductId}}},
			)
			if err != nil {
				return nil, &lib.ERROR_UPDATE_FAILED
			}
		} else {
			err := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], shoppingCart.XId)
			if err != nil {
				return nil, &lib.ERROR_UPDATE_FAILED
			}
		}
	} else {
		_, err := globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE],
			bson.M{"_id": shoppingCart.XId, "lineItems._id": reqBody.ProductId},
			bson.M{"$inc": bson.M{"lineItems.$.quantity": -1}},
		)
		if err != nil {
			return nil, &lib.ERROR_UPDATE_FAILED
		}
	}
	return nil, nil
}

func RemoveProduct(reqBody *UpdateShoppingCartRequest, shoppingCart *modelShoppingCartGroceryAssistant.ShoppingCartGroceryAssistant) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	if shoppingCart == nil {
		return nil, &lib.ERROR_UPDATE_ACTION_INCORRECT
	}

	// Check product in cart
	isProductInCart := false
	if shoppingCart != nil {
		for _, product := range shoppingCart.LineItems {
			if product.XId == reqBody.ProductId {
				isProductInCart = true
				break
			}
		}
	}

	if !isProductInCart {
		return nil, &lib.ERROR_UPDATE_ACTION_INCORRECT
	}

	if len(shoppingCart.LineItems) > 1 {
		// Remove the product from lineItems
		_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE],
			shoppingCart.XId,
			bson.M{"$pull": bson.M{"lineItems": bson.M{"_id": reqBody.ProductId}}},
		)
		if err != nil {
			return nil, &lib.ERROR_UPDATE_FAILED
		}
	} else {
		err := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], shoppingCart.XId)
		if err != nil {
			return nil, &lib.ERROR_UPDATE_FAILED
		}
	}

	return nil, nil
}
