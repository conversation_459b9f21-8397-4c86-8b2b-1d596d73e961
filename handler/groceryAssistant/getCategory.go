package groceryAssistant

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelProductGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/productGroceryAssistant"
	modelStoreGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/storeGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func GetCategory(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	decoder := json.NewDecoder(r.Body)
	var reqBody ApiRequestGroceryAssistant
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validateGetCategory(reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var store *modelStoreGroceryAssistant.StoreGroceryAssistant
	globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
		bson.M{"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE, "_id": reqBody.StoreId},
		bson.M{"categories._id": 1, "categories.text": 1, "categories.status": 1, "categories.subCategories._id": 1, "categories.subCategories.text": 1, "categories.subCategories.status": 1},
		&store,
	)
	if store == nil {
		globalResponse.ResponseError(w, lib.ERROR_STORE_GROCERY_ASSISTANT_NOT_FOUND)
		return
	}
	result := make(map[string]interface{})
	var resultCategories []map[string]interface{}
	var resultSubCategories []map[string]interface{}
	if store.Categories != nil && len(store.Categories) > 0 {
		for index, category := range store.Categories {
			if category.Status == globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE {
				// Lấy danh sách danh mục
				resultCategories = append(resultCategories, map[string]interface{}{
					"_id":  category.XId,
					"text": category.Text,
				})
				// Lấy danh sách danh mục con và danh sách sản phẩm trong danh mục con
				// Lần đầu client gọi sẽ không có categoryId nên default lấy category đầu tiên
				if reqBody.CategoryId == "" && index > 0 {
					continue
				}
				if reqBody.CategoryId != "" && category.XId != reqBody.CategoryId {
					continue
				}
				if category.SubCategories != nil && len(category.SubCategories) > 0 {
					var page int64 = 1
					var limit int64 = 20
					if reqBody.Page > 0 {
						page = reqBody.Page
					}
					if reqBody.Limit > 0 {
						limit = reqBody.Limit
					}
					var products []*modelProductGroceryAssistant.ProductGroceryAssistant
					globalDataAccess.GetAllByQueryPaging(
						globalCollection.COLLECTION_PRODUCT_GROCERY_ASSISTANT[local.ISO_CODE],
						bson.M{"categoryIds": category.XId, "status": globalConstant.PRODUCT_GROCERY_ASSISTANT_STATUS_ACTIVE},
						bson.M{"_id": 1, "SKU": 1, "text": 1, "image": 1, "price": 1, "subCategoryIds": 1},
						page,
						limit,
						&products,
					)
					if len(products) > 0 {
						for _, subCategory := range category.SubCategories {
							if subCategory.Status == globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE {
								resultSubCategory := make(map[string]interface{})
								b, _ := json.Marshal(subCategory)
								json.Unmarshal(b, &resultSubCategory)
								delete(resultSubCategory, "status")
								var listProduct []map[string]interface{}
								for _, product := range products {
									if globalLib.FindStringInSlice(product.SubCategoryIds, subCategory.XId) >= 0 {
										p := make(map[string]interface{})
										b, _ := json.Marshal(product)
										json.Unmarshal(b, &p)
										delete(p, "subCategoryIds")
										listProduct = append(listProduct, p)
									}
								}
								if len(listProduct) > 0 {
									resultSubCategory["products"] = listProduct
									resultSubCategories = append(resultSubCategories, resultSubCategory)
								}
							}
						}
					}
				}
			}
		}
	}
	result["categories"] = resultCategories
	result["subCategories"] = resultSubCategories
	globalResponse.ResponseSuccess(w, result)
}

// =====================================

func validateGetCategory(reqBody ApiRequestGroceryAssistant) *globalResponse.ResponseErrorCode {
	if reqBody.StoreId == "" {
		return &lib.ERROR_STORE_ID_REQUIRED
	}
	// if reqBody.CategoryId == "" {
	// 	return &lib.ERROR_CATEGORY_ID_REQUIRED
	// }
	return nil
}
