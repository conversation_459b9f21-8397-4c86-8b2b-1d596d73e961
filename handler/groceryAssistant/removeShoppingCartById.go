package groceryAssistant

import (
	"encoding/json"
	"net/http"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

func RemoveShoppingCardById(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Parse api params
	decoder := json.NewDecoder(r.Body)
	var reqBody ApiRequestGroceryAssistant
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validateRemoveShoppingCard(reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	err = globalDataAccess.DeleteOneById(globalCollection.COLLECTION_SHOPPING_CART[local.ISO_CODE], reqBody.ShoppingCardId)
	if err != nil {
		globalResponse.ResponseError(w, lib.ERROR_REMOVE_FAILED)
	}
	globalResponse.ResponseSuccess(w, nil)
}

func validateRemoveShoppingCard(reqBody ApiRequestGroceryAssistant) *globalResponse.ResponseErrorCode {
	if reqBody.ShoppingCardId == "" {
		return &lib.ERROR_SHOPPING_CARD_ID_REQUIRED
	}
	return nil
}
