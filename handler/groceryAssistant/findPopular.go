package groceryAssistant

import (
	"encoding/json"
	"net/http"
	"sort"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelStoreGroceryAssistant "gitlab.com/btaskee/go-services-model-v2/grpcmodel/storeGroceryAssistant"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func FindPopular(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	decoder := json.NewDecoder(r.Body)
	var reqBody ApiRequestGroceryAssistant
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}

	errCode := validateFindPopular(reqBody)
	if errCode != nil {
		globalResponse.ResponseError(w, *errCode)
		return
	}

	var store *modelStoreGroceryAssistant.StoreGroceryAssistant
	globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_STORE_GROCERY_ASSISTANT[local.ISO_CODE],
		bson.M{"status": globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE, "_id": reqBody.StoreId},
		bson.M{"categories.status": 1, "categories.subCategories._id": 1, "categories.subCategories.text": 1, "categories.subCategories.status": 1, "categories.subCategories.numberOfFindPopular": 1},
		&store,
	)
	if store == nil {
		globalResponse.ResponseError(w, lib.ERROR_STORE_GROCERY_ASSISTANT_NOT_FOUND)
		return
	}
	var result []*modelStoreGroceryAssistant.StoreGroceryAssistantCategoriesSubCategories
	if store.Categories != nil && len(store.Categories) > 0 {
		var isBreak bool
		for _, category := range store.Categories {
			if category.Status == globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE && category.SubCategories != nil && len(category.SubCategories) > 0 {
				for _, subCategory := range category.SubCategories {
					if subCategory.Status == globalConstant.STORE_GROCERY_ASSISTANT_STATUS_ACTIVE && subCategory.NumberOfFindPopular > 0 {
						result = append(result, subCategory)
						if len(result) == 5 {
							isBreak = true
							break
						}
					}
				}
			}
			if isBreak {
				break
			}
		}
	}
	if len(result) > 0 {
		sort.SliceStable(result, func(i, j int) bool { return result[i].NumberOfFindPopular > result[j].NumberOfFindPopular })
	}
	globalResponse.ResponseSuccess(w, result)
}

func validateFindPopular(reqBody ApiRequestGroceryAssistant) *globalResponse.ResponseErrorCode {
	if reqBody.StoreId == "" {
		return &lib.ERROR_STORE_ID_REQUIRED
	}
	return nil
}
