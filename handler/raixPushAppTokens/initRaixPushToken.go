/*
 * @File: initRaixPushToken.go
 * @Description: Handle, parse api params and route to handler function
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package raixPushAppTokens

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"

	"github.com/golang/protobuf/jsonpb"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRaixPushAppTokens "gitlab.com/btaskee/go-services-model-v2/grpcmodel/raixPushAppTokens"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Init Raix Push Token (_raix_push_app_tokens)
 * @CreatedAt: 08/10/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func InitRaixPushToken(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()
	// Decode request body
	var reqBody modelRaixPushAppTokens.RaixPushAppTokens
	err := jsonpb.Unmarshal(r.Body, &reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Validate data
	errCode := validateInitRaixPushToken(&reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	// Set data
	var existToken *modelRaixPushAppTokens.RaixPushAppTokens
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
		bson.M{"metadata.uuid": reqBody.Metadata.Uuid,
			"$or": []bson.M{
				{"token.gcm": reqBody.Token.Gcm},
				{"token.apn": reqBody.Token.Apn},
			}},
		bson.M{"_id": 1, "userId": 1, "token": 1, "metadata": 1},
		&existToken,
	)
	if existToken == nil {
		globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, bson.M{"metadata.uuid": reqBody.Metadata.Uuid, "appName": reqBody.AppName})
		doc := raixPushUpdate(&reqBody)
		if doc != nil && doc.XId != "" {
			// If fcm token is not exist, create one
			if reqBody.Token != nil && reqBody.Token.Apn != "" && reqBody.Metadata != nil && reqBody.Metadata.FCMToken == "" {
				reqBody.Metadata.FCMToken, errCode = tokenAPNToFCM(reqBody.Token.Apn, reqBody.AppName)
				if errCode != nil {
					local.Logger.Warn(lib.ERROR_APN_TO_FCM_FAILED.ErrorCode,
						zap.String("url", r.RequestURI),
						zap.Error(err),
						zap.String("apnToken", reqBody.Token.Apn),
						zap.String("appName", reqBody.AppName),
					)
				}
			}
			dataUpdate := bson.M{
				"metadata": reqBody.Metadata,
			}
			if reqBody.UserId != "" {
				dataUpdate["userId"] = reqBody.UserId
			}
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, doc.XId, bson.M{"$set": dataUpdate})
		}
	} else {
		// Remove empty tokens
		wg := &sync.WaitGroup{}
		wg.Add(1)
		go func() {
			defer wg.Done()
			globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
				bson.M{
					"metadata.uuid": reqBody.Metadata.Uuid,
					"appName":       reqBody.AppName,
					"$or": []bson.M{
						{"token": nil},
						{"token": bson.M{"$exists": false}},
					},
				},
			)
		}()
		// If not exists userId, or login with other account on device
		updateData := make(bson.M)
		if reqBody.UserId != "" && existToken.UserId != reqBody.UserId {
			updateData["userId"] = reqBody.UserId
		}
		metadata := make(bson.M)
		// Update new app version
		if reqBody.Metadata.AppVersion != "" && existToken.Metadata != nil && existToken.Metadata.AppVersion != reqBody.Metadata.AppVersion {
			metadata["appVersion"] = reqBody.Metadata.AppVersion
		}
		// Update new build number
		if reqBody.Metadata.BuildNumber != "" && existToken.Metadata != nil && existToken.Metadata.BuildNumber != reqBody.Metadata.BuildNumber {
			metadata["buildNumber"] = reqBody.Metadata.BuildNumber
		}
		// Update FCM token if not exist
		if existToken.Token != nil && existToken.Token.Apn != "" && existToken.Metadata != nil && existToken.Metadata.FCMToken == "" {
			metadata["FCMToken"], errCode = tokenAPNToFCM(existToken.Token.Apn, reqBody.AppName)
			if errCode != nil {
				local.Logger.Warn(lib.ERROR_APN_TO_FCM_FAILED.ErrorCode,
					zap.String("url", r.RequestURI),
					zap.Error(err),
					zap.String("apnToken", reqBody.Token.Apn),
					zap.String("appName", reqBody.AppName),
				)
			}
		}
		if metadata != nil && (metadata["appVersion"] != nil || metadata["buildNumber"] != nil || metadata["FCMToken"] != nil) {
			updateData["metadata"] = metadata
		}
		wg.Wait()
		// Update with valid updateData
		if updateData != nil && (updateData["userId"] != nil || updateData["metadata"] != nil) {
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, existToken.XId, bson.M{"$set": updateData})
		}
	}
	if reqBody.UserId != "" {
		updateDeviceUser(reqBody.UserId, reqBody.Metadata)
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: Update Raix Push(_raix_push_app_tokens)
 * @CreatedAt: 08/10/2020
 * @Author: linhnh
 * @UpdatedAt: 26/11/2020
 * @UpdatedBy: vinhnt
 */
func raixPushUpdate(data *modelRaixPushAppTokens.RaixPushAppTokens) *modelRaixPushAppTokens.RaixPushAppTokens {
	fields := bson.M{"_id": 1}
	var doc *modelRaixPushAppTokens.RaixPushAppTokens
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, bson.M{"userId": data.UserId}, fields, &doc)
	if doc == nil {
		var doc *modelRaixPushAppTokens.RaixPushAppTokens
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
			bson.M{
				"appName": data.AppName,
				"token":   bson.M{"$exists": true},
				"$or": []bson.M{
					{"token.gcm": data.Token.Gcm},
					{"token.apn": data.Token.Apn},
				},
			},
			fields,
			&doc,
		)
	}
	if doc == nil {
		doc = &modelRaixPushAppTokens.RaixPushAppTokens{
			XId:       globalLib.GenerateObjectId(),
			Token:     data.Token,
			AppName:   data.AppName,
			UserId:    data.UserId,
			Enabled:   true,
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, doc)
	} else {
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
			doc.XId,
			bson.M{"$set": bson.M{"token": data.Token, "updatedAt": globalLib.GetCurrentTime(local.TimeZone)}},
		)
	}
	if doc != nil {
		if doc.Token != nil {
			globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
				bson.M{
					"_id": bson.M{"$ne": doc.XId},
					"$or": []bson.M{
						{"token.gcm": data.Token.Gcm},
						{"token.apn": data.Token.Apn},
					},
					"appName": doc.AppName,
					"token":   bson.M{"$exists": true},
				},
			)
		}
	}
	return doc
}

/*
 * @Description: token APN To FCM
 * @CreatedAt: 08/10/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func tokenAPNToFCM(apnToken string, appName string) (string, *globalResponse.ResponseErrorCode) {
	var fcmAPIKey string
	if appName == globalConstant.APP_NAME_ASKER {
		fcmAPIKey = cfg.FcmAskerServerKey
	} else if appName == globalConstant.APP_NAME_TASKER {
		fcmAPIKey = cfg.FcmTaskerServerKey
	}
	if fcmAPIKey != "" {
		body := map[string]interface{}{
			"application": "com.lanterns.btaskee",
			"sandbox":     false,
			"apns_tokens": [1]string{apnToken},
		}
		reqBody, err := json.Marshal(body)
		if err != nil {
			return "", &lib.ERROR_APN_TO_FCM_FAILED
		}
		req, err := http.NewRequest("POST", "https://iid.googleapis.com/iid/v1:batchImport", bytes.NewBuffer(reqBody))
		req.Header.Set("Authorization", fmt.Sprintf("key=%s", fcmAPIKey))
		req.Header.Set("Content-Type", "application/json")
		if err != nil {
			return "", &lib.ERROR_APN_TO_FCM_FAILED
		}

		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			return "", &lib.ERROR_APN_TO_FCM_FAILED
		}
		if resp != nil && resp.StatusCode == http.StatusOK {
			b, _ := io.ReadAll(resp.Body)
			data := make(map[string]interface{})
			json.Unmarshal(b, &data)
			if data != nil && data["results"] != nil {
				b, _ = json.Marshal(data["results"])
				var results []map[string]interface{}
				json.Unmarshal(b, &results)
				if len(results) > 0 && results[0]["registration_token"] != nil {
					return results[0]["registration_token"].(string), nil
				}
			}
		}
	}
	return "", nil
}

/*
 * @Description: Update Device User
 * @CreatedAt: 08/10/2020
 * @Author: linhnh
 * @UpdatedAt: 26/11/2020
 * @UpdatedBy: vinhnt
 */
func updateDeviceUser(userId string, metadata *modelRaixPushAppTokens.RaixPushAppTokensMetadata) {
	existsUser, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"devices": 1, "appVersion": 1, "buildNumber": 1})
	if existsUser != nil && metadata != nil {
		updateData := make(bson.M)
		var isExistsDevice bool
		if existsUser.Devices != nil && len(existsUser.Devices) > 0 {
			for _, v := range existsUser.Devices {
				if v.Platform == metadata.Platform && v.Version == metadata.Version && v.Model == metadata.Model && v.Manufacturer == metadata.Manufacturer {
					isExistsDevice = true
					break
				}
			}
		}
		// Device is not exists => addToSet this device into user
		if !isExistsDevice {
			updateData["$addToSet"] = bson.M{
				"devices": bson.M{
					"platform":     metadata.Platform,
					"version":      metadata.Version,
					"model":        metadata.Model,
					"manufacturer": metadata.Manufacturer,
				},
			}
		}
		// Check for update appVersion, buildNumber
		if metadata.AppVersion != "" && metadata.BuildNumber != "" && (existsUser.AppVersion != metadata.AppVersion || existsUser.BuildNumber != metadata.BuildNumber) {
			updateData["$set"] = bson.M{
				"appVersion":  metadata.AppVersion,
				"buildNumber": metadata.BuildNumber,
			}
		}
		// Update users when updateData is valid
		if updateData["$addToSet"] != nil || updateData["$set"] != nil {
			modelUser.UpdateOneById(local.ISO_CODE, userId, updateData)
		}
	}
}

/*
 * @Description: Validate request params
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateInitRaixPushToken(reqBody *modelRaixPushAppTokens.RaixPushAppTokens) *globalResponse.ResponseErrorCode {
	if reqBody.Token == nil {
		return &lib.ERROR_TOKEN_REQUIRED
	}
	if reqBody.Metadata == nil {
		return &lib.ERROR_DATA_UPDATE_REQUIRED
	}
	if reqBody.AppName == "" {
		return &lib.ERROR_APP_NAME_REQUIRED
	}
	return nil
}
