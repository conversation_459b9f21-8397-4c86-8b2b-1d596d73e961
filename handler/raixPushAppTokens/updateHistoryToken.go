/*
 * @File: updateHistoryToken.go
 * @Description: <PERSON>le, parse api params and route to handler function
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
package raixPushAppTokens

import (
	"net/http"

	"github.com/golang/protobuf/jsonpb"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelRaixPushAppTokens "gitlab.com/btaskee/go-services-model-v2/grpcmodel/raixPushAppTokens"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Update History Token (_raix_push_app_tokens)
 * @CreatedAt: 29/09/2020
 * @Author: linhnh
 * @UpdatedAt: 03/03/2021
 * @UpdatedBy: ngoctb3
 */
func UpdateHistoryToken(w http.ResponseWriter, r *http.Request) {
	defer local.Logger.Sync()

	// Decode request body
	var reqBody modelRaixPushAppTokens.RaixPushAppTokens
	err := jsonpb.Unmarshal(r.Body, &reqBody)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, lib.ERROR_CAN_NOT_PARSE_API_PARAMS)
		return
	}
	// Validate data
	errCode := validateUpdateHistoryToken(&reqBody)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", &reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}

	// Set data
	var exist *modelRaixPushAppTokens.RaixPushAppTokens
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS,
		bson.M{"metadata.uuid": reqBody.Metadata.Uuid, "token.gcm": reqBody.Token.Gcm, "token.apn": reqBody.Token.Apn, "userId": reqBody.UserId},
		bson.M{"_id": 1, "metadata.installedLocation": 1},
		&exist,
	)

	if exist != nil {
		updateHistory(&reqBody, exist)
	}
	globalResponse.ResponseSuccess(w, nil)
}

/*
 * @Description: update push history token
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func updateHistory(reqBody *modelRaixPushAppTokens.RaixPushAppTokens, exist *modelRaixPushAppTokens.RaixPushAppTokens) {
	updateData := bson.M{}

	if reqBody.Metadata.History != nil && len(reqBody.Metadata.History) > 0 {
		updateData["$push"] = bson.M{
			"metadata.history": bson.M{"$each": reqBody.Metadata.History, "$slice": -3},
		}
	}
	dataSet := bson.M{}

	if (exist.Metadata == nil || exist.Metadata.InstalledLocation == nil) && reqBody.Metadata.InstalledLocation != nil {
		dataSet["metadata.installedLocation"] = reqBody.Metadata.InstalledLocation
	}

	if reqBody.Metadata.AppVersion != "" {
		dataSet["metadata.appVersion"] = reqBody.Metadata.AppVersion
	}
	updateData["$set"] = dataSet
	if updateData["$push"] != nil || updateData["$set"] != nil {
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_RAIX_PUSH_APP_TOKENS, exist.XId, updateData)
	}
}

/*
 * @Description: Validate request data
 * @CreatedAt: 03/03/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func validateUpdateHistoryToken(reqBody *modelRaixPushAppTokens.RaixPushAppTokens) *globalResponse.ResponseErrorCode {
	if reqBody.UserId == "" {
		return &lib.ERROR_USER_ID_REQUIRED
	}
	if reqBody.Token == nil {
		return &lib.ERROR_TOKEN_REQUIRED
	}
	if reqBody.Metadata == nil {
		return &lib.ERROR_DATA_UPDATE_REQUIRED
	}
	return nil
}
