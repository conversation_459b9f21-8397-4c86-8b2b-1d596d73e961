package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-api-asker-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var cfg = config.GetConfig()

func main() {
	// Create new server
	httpServer := service.NewServer(cfg.APIPort)
	grpcServer := service.NewGRPCServer()
	// Start HTTP Server
	go func() {
		err := service.StartHTTPServer(httpServer)
		if err != nil {
			log.Println(err.Error())
		}
	}()

	go func() {
		err := service.StartGRPCServer(grpcServer, cfg.GRPCPort)
		if err != nil {
			log.Println(err.Error())
		}
	}()

	// Graceful shutdown
	handleSigterm(httpServer)
}

func handleSigterm(srv *http.Server) {
	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE RUNNING] - %s", local.SERVICE_NAME))

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	signal.Notify(c, syscall.SIGTERM)
	<-c

	// Shutdown HTTP server
	service.StopHTTPServer(srv)

	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE DOWN] - %s", local.SERVICE_NAME))

	close(c)
}
