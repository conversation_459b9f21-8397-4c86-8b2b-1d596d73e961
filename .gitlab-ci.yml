variables:
  RELEASE_VERSION: v3.7.13-asker-vn
  DOCKER_HUB: btaskeehub
  DOCKER_REPO: go-api-service
  SLACK_CHANNEL: go-deploy-histories
  SLACK_USERNAME: bTaskee System

stages:
  - test
  - build
  - docker-sandbox
  - docker
  - deploy
  - alert-slack
  - merge

####################################################################
# Template
####################################################################

.test_template: &test_template
  stage: test
  image: golang:1.24.0-alpine3.21
  services:
    - mongo:6.0.20
  cache:
    paths:
      - .cache
  before_script:
    - mkdir -p .cache
    - export GOPATH="$CI_PROJECT_DIR/.cache"
    - apk add --no-cache git mongodb-tools
    - git config --global url."https://gitlab-ci-token:$<EMAIL>/".insteadOf "https://gitlab.com/"
    - git clone --single-branch --branch $SERVICE_MODEL_BRANCH https://gitlab.com/btaskee/go-services-model-v2.git
    - >
      mongorestore --host mongo --port 27017 go-services-model-v2/dbTest
  script:
    - git clone --single-branch --branch dev https://gitlab.com/btaskee/go-data-access-vn-v3.git
    - git clone --single-branch --branch dev https://gitlab.com/btaskee/go-data-access-th-v3.git
    - git clone --single-branch --branch $OTHER_SERVICE_BRANCH https://gitlab.com/btaskee/go-push-notification-vn-v3.git
    - git clone --single-branch --branch dev https://gitlab.com/btaskee/go-pricing-vn-v3.git
    - git clone --single-branch --branch $OTHER_SERVICE_BRANCH https://gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3.git
    - git clone --single-branch --branch $OTHER_SERVICE_BRANCH https://gitlab.com/btaskee/btaskee-go/go-payment-vn-v3.git
    - git clone --single-branch --branch $OTHER_SERVICE_BRANCH https://gitlab.com/btaskee/go-event-vn-v3.git
    - git clone --single-branch --branch $OTHER_SERVICE_BRANCH https://gitlab.com/btaskee/btaskee-go/go-bpoint-vn-v3.git
    - export APPLICATION_MODE=$APP_MODE
    - export MONGO_CONNECTION_TEST="mongodb://mongo:27017"
    - >
      cd go-data-access-vn-v3 && ./run.sh &
      cd go-data-access-th-v3 && ./run.sh &
      cd go-push-notification-vn-v3 && ./run.sh &
      cd go-pricing-vn-v3 && ./run.sh &
      cd go-send-task-vn-v3 && ./run.sh &
      cd go-payment-vn-v3 && ./run.sh &
      cd go-event-vn-v3 && ./run.sh &
      cd go-bpoint-vn-v3 && ./run.sh &
      sleep 10 &&
      ./test.sh

####################################################################
# Test
####################################################################

test_dev:
  variables:
    APP_MODE: test
    SERVICE_MODEL_BRANCH: dev
    OTHER_SERVICE_BRANCH: dev
  only:
    - /^feature-.*$/i
    - /^hotfix-.*$/i
    - /^improve-.*$/i
    # - sandbox
  <<: *test_template

test_production:
  variables:
    APP_MODE: test
    SERVICE_MODEL_BRANCH: master
    OTHER_SERVICE_BRANCH: master
  only:
    - /^release-.*$/i
  <<: *test_template

####################################################################
# Build
####################################################################

build:
  stage: build
  image: golang:1.24.0-alpine3.21
  only:
    - /^release-.*$/i
    - sandbox
  script:
    - apk add git
    - git config --global url."https://gitlab-ci-token:$<EMAIL>/".insteadOf "https://gitlab.com/"
    - export GOOS=linux
    - export CGO_ENABLED=0
    - ./build.sh
  artifacts: # cache binary file for 1 hour
    paths:
      - api-asker-vn-v3-linux-amd64
    expire_in: 1 hour

####################################################################
# Docker Sandbox
####################################################################

docker-sandbox:
  stage: docker-sandbox
  image: docker:27.5.1
  services:
    - docker:27.5.1-dind
  only:
    - sandbox
  variables:
    DOCKER_REPO: go-staging
    DOCKER_TAG: go-api-asker-vn-v3-jwt-1
  when: manual
  script:
    - echo $DOCKER_PASS | docker login -u $DOCKER_USERNAME --password-stdin
    - >
      docker build
      --build-arg MODE=dev
      -t $DOCKER_HUB/$DOCKER_REPO:$DOCKER_TAG .
    - docker push $DOCKER_HUB/$DOCKER_REPO:$DOCKER_TAG

####################################################################
# Docker
####################################################################

docker:
  stage: docker
  image: docker:27.5.1
  services:
    - docker:27.5.1-dind
  only:
    - /^release-.*$/i
  variables:
    DOCKER_DRIVER: overlay2
  script:
    - export DOCKER_TAG=$RELEASE_VERSION
    - echo $DOCKER_PASS | docker login -u $DOCKER_USERNAME --password-stdin
    - docker buildx create --use
    - >
      docker buildx build --push --sbom=true --provenance=true
      --build-arg MODE=prod
      -t $DOCKER_HUB/$DOCKER_REPO:$DOCKER_TAG .

####################################################################
# Deploy
####################################################################

deploy:
  stage: deploy
  image:
    name: bitnami/kubectl:1.26.9
    entrypoint: [""]
  only:
    - /^release-.*$/i
  when: manual
  script:
    - echo "$KUBE_CONFIG" | base64 -d > kube_config
    - kubectl apply -f deploy/k8s/deployment_prod.yml --kubeconfig ./kube_config

####################################################################
# Alert Slack
####################################################################

alert-slack:
  stage: alert-slack
  needs:
    - deploy
  image: bitnami/git:2.46.0
  only:
    - /^release-.*$/i
  before_script:
    - apt-get update
    - apt-get install -y jq
  script:
    - |
      SLACK_RESPONSE=$(curl -s -X POST "https://slack.com/api/chat.postMessage" \
      -H "Authorization: Bearer $SLACK_TOKEN" \
      -H "Content-type: application/json; charset=utf-8" \
      -d "{\"channel\":\"$SLACK_CHANNEL\", \"username\":\"$SLACK_USERNAME\", \"text\":\"✅ *Deployment successfully!* \n- Project: ${CI_PROJECT_NAME} \n- Version: ${RELEASE_VERSION}\"}")
      
      ERROR_MESSAGE=$(echo $SLACK_RESPONSE | jq -r '.ok')
      if [ "$ERROR_MESSAGE" == "false" ]; then exit 1; fi

####################################################################
# Merge
####################################################################

merge:
  stage: merge
  needs:
    - deploy
  image:
    name: bitnami/git:2.46.0
  only:
    - /^release-.*$/i
  before_script:
    - apt-get update
    - apt-get install -y jq
  script:
    # Tạo merge request vào master
    - >
      API_RESPONSE_MASTER=$(curl
      --silent
      --header "PRIVATE-TOKEN: $GITLAB_TOKEN"
      --header "Content-Type: application/json"
      --data "{\"source_branch\":\"$CI_COMMIT_REF_NAME\", \"target_branch\":\"master\", \"title\":\"Release $RELEASE_VERSION\"}" "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests")
    - sleep 5
    # Lấy ID của merge request và merge vào master
    - >
      MR_IID_MASTER=$(echo "$API_RESPONSE_MASTER" | jq -r '.iid')
    - >
      curl
      --silent
      --header "PRIVATE-TOKEN: $GITLAB_TOKEN" -X PUT "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests/$MR_IID_MASTER/merge"
    - sleep 3
    # Tạo merge request vào dev
    - >
      API_RESPONSE_DEV=$(curl
      --silent
      --header "PRIVATE-TOKEN: $GITLAB_TOKEN"
      --header "Content-Type: application/json"
      --data "{\"source_branch\":\"$CI_COMMIT_REF_NAME\", \"target_branch\":\"dev\", \"title\":\"Release $RELEASE_VERSION\"}" "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests")
    - sleep 5
    # Lấy ID của merge request và merge vào dev
    - >
      MR_IID_DEV=$(echo "$API_RESPONSE_DEV" | jq -r '.iid')
    - >
      curl
      --silent
      --header "PRIVATE-TOKEN: $GITLAB_TOKEN" -X PUT "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests/$MR_IID_DEV/merge"