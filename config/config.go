package config

import (
	"fmt"
	"os"

	Viper "github.com/spf13/viper"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/redisCache"
)

type Config struct {
	APIPort                       string `mapstructure:"running_rest_api_asker_vn_v3_port"`
	GRPCPort                      string `mapstructure:"running_grpc_api_asker_vn_v3_port"`
	GRPCPricingPort               string `mapstructure:"grpc_pricing_vn_v3_url"`
	GRPC_Push_Notification_URL    string `mapstructure:"grpc_push_notification_vn_v3_url"`
	GRPC_Send_Task_URL            string `mapstructure:"grpc_send_task_vn_v3_url"`
	GRPC_Email_Service_URL        string `mapstructure:"grpc_email_vn_v3_url"`
	GRPC_Websocket_VN_V3_URL      string `mapstructure:"grpc_websocket_vn_v3_url"`
	GRPC_Event_URL                string `mapstructure:"grpc_event_vn_v3_url"`
	GrpcPaymentPort               string `mapstructure:"grpc_payment_vn_v3_url"`
	GRPC_Websocket_Service_V2_URL string `mapstructure:"grpc_websocket_service_v2_url"`
	GRPC_BPoint_Port              string `mapstructure:"grpc_bpoint_vn_v3_url"`
	GoogleMapApiKey               string
	SlackToken                    string
	CloudTranslationAPI           string
	FcmAskerServerKey             string
	FcmTaskerServerKey            string
	UrBoxConfig                   UrBoxConfig
	AWS3                          *AWS3Config
	MiDeskConfig                  *MiDeskConfig
	GRPC_Chat_Server_VN_V3_URL    string `mapstructure:"grpc_chat_server_vn_v3_url"`
	GRPC_Websocket_Service_URL    string `mapstructure:"grpc_websocket_vn_v3_url"`
	RedisAddress                  string `mapstructure:"redis_address"`
	RedisPassword                 string
	RedisCache                    *redisCache.RedisCache
	ApplicationMode               string
}

type UrBoxConfig struct {
	AppId             string
	AppSecret         string
	BTaskeePrivateKey string
	CartPayVoucherUrl string
}

type MiDeskConfig struct {
	Email           string
	Password        string
	LoginUrl        string
	CreateTicketUrl string
}

type AWS3Config struct {
	KeyPrefix string
	Bucket    string
	Region    string
	AccesKey  string
	SecretKey string
}

var cfg *Config

func init() {
	var folder string
	mode := os.Getenv("APPLICATION_MODE")

	switch mode {
	case "dev", "prod", "test", "ci":
		folder = mode
	default:
		folder = "local"
		os.Setenv("APPLICATION_MODE", folder)
	}

	cfgPath := os.Getenv("API_SERVICE_CONFIG")
	if cfgPath == "" {
		cfgPath = "config"
	}
	path := fmt.Sprintf("%v/%v", cfgPath, folder)

	cfg = new(Config)

	if mode == "" || mode == "dev" || mode == "ci" {
		cfg.GoogleMapApiKey = "AIzaSyDYoZJmJ79ynNd_4-gP9DQfiStVQRS8ScM"
		cfg.CloudTranslationAPI = "AIzaSyCb7jDbivK7CQsser0gbvAef19VzfV3gHU"
		// cfg.UrBoxAccess = "********************************"
		// cfg.UrBoxAgentSite = "50000006"
		cfg.UrBoxConfig = UrBoxConfig{
			AppId:     "500000082",
			AppSecret: "7c12b4badc819600ede80b3cd4465c5b",
			BTaskeePrivateKey: `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
			CartPayVoucherUrl: "https://sandapi.urbox.dev/2.0/cart/cartPayVoucher",
		}
		cfg.FcmAskerServerKey = "LocaltestAsker"
		cfg.FcmTaskerServerKey = "LocaltestTasker"
		cfg.AWS3 = &AWS3Config{
			KeyPrefix: "mystore/",
			Bucket:    "toanphambucket",
			Region:    "ap-southeast-1",
			AccesKey:  "********************",
			SecretKey: "22GrNGL32vZa9l47TjdTHw5MIbzeHba0md2V0xSC",
		}
		cfg.MiDeskConfig = &MiDeskConfig{}
	} else if mode != "test" {
		cfg.GoogleMapApiKey = os.Getenv("GOOGLE_MAP_API_KEY")
		cfg.SlackToken = os.Getenv("SLACK_TOKEN_VN")

		cfg.CloudTranslationAPI = os.Getenv("CLOUD_TRANSLATION_API")

		cfg.FcmAskerServerKey = os.Getenv("FCM_ASKER_SERVER_KEY")
		cfg.FcmTaskerServerKey = os.Getenv("FCM_TASKER_SERVER_KEY")

		cfg.UrBoxConfig = UrBoxConfig{
			AppId:             os.Getenv("URBOX_APP_ID"),
			AppSecret:         os.Getenv("URBOX_APP_SECRET"),
			BTaskeePrivateKey: os.Getenv("URBOX_BTASKEE_PRIVATE_KEY"),
			CartPayVoucherUrl: os.Getenv("URBOX_CART_PAY_VOUCHER_URL"),
		}

		cfg.AWS3 = &AWS3Config{
			KeyPrefix: os.Getenv("AWS3_KEY_PREFIX"),
			Bucket:    os.Getenv("AWS3_BUCKET"),
			Region:    os.Getenv("AWS3_REGION"),
			AccesKey:  os.Getenv("AWS3_ACCESS_KEY"),
			SecretKey: os.Getenv("AWS3_SECRET_KEY"),
		}
		cfg.MiDeskConfig = &MiDeskConfig{
			Email:           os.Getenv("MIDESK_EMAIL"),
			Password:        os.Getenv("MIDESK_PASSWORD"),
			LoginUrl:        os.Getenv("MIDESK_LOGIN_URL"),
			CreateTicketUrl: os.Getenv("MIDESK_CREATE_TICKET_URL"),
		}
		cfg.RedisPassword = os.Getenv("REDIS_PASSWORD")
	} else if mode == "test" {
		cfg.GoogleMapApiKey = ""
		cfg.AWS3 = &AWS3Config{
			KeyPrefix: "",
			Bucket:    "",
			Region:    "",
			AccesKey:  "",
			SecretKey: "",
		}
		cfg.MiDeskConfig = &MiDeskConfig{}
		cfg.UrBoxConfig = UrBoxConfig{
			AppId:     "500000082",
			AppSecret: "7c12b4badc819600ede80b3cd4465c5b",
			BTaskeePrivateKey: `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
			CartPayVoucherUrl: "https://sandapi.urbox.dev/2.0/cart/cartPayVoucher",
		}
	}

	initConfig(path, "base", &cfg)
	initRedisCache()
	cfg.ApplicationMode = mode
}

func initConfig(configPath, configName string, result interface{}) {
	viper := Viper.New()
	viper.AddConfigPath(configPath)
	viper.SetConfigName(configName)

	err := viper.ReadInConfig() // Find and read the config file
	if err == nil {             // Handle errors reading the config file
		err = viper.Unmarshal(result)
		if err != nil { // Handle errors reading the config file
			panic(fmt.Errorf("fatal error config file: %s", err))
		}
	}
}

func GetConfig() *Config {
	return cfg
}

// func readUrBoxPrivateKey(configPath string) string {
// 	// Get the JSON configuration from the environment variable
// 	fileContent, err := os.ReadFile(configPath)
// 	if err != nil {
// 		log.Println("Error reading fcm config file:", configPath, "error:", err)
// 		return ""
// 	}

// 	// Unmarshal the JSON data into the Config struct
// 	return string(fileContent)
// }

func initRedisCache() {
	redisConfig := map[string]interface{}{
		"address":  cfg.RedisAddress,
		"password": cfg.RedisPassword,
	}
	cfg.RedisCache = redisCache.NewRedisCache(redisConfig)
}
