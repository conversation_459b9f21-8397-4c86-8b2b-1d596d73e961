export API_SERVICE_CONFIG="../config"
cover="-coverprofile=cover -coverpkg=."
case $1 in
  "1")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/blog/... -run Blog
    else
      go test -v ./testing ${cover}/blog/... -run Blog/^$2$
    fi
    ;;
  "18")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/settings/... -run Settings
    else
      go test -v ./testing ${cover}/settings/... -run Settings/^$2$
    fi
    ;;
  "20")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/task -run T_ask
    else
      go test -v ./testing ${cover}/task -run T_ask/^$2$
    fi
    ;;
  "20.1")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/task/... -run _Task_1
    else
      go test -v ./testing ${cover}/task/... -run _Task_1/^$2$
    fi
    ;;
  "7")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/incentive -run Incentive
    else
      go test -v ./testing ${cover}/incentive -run Incentive/^$2$
    fi
    ;;
  "4")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/financialAccount -run FAccount
    else
      go test -v ./testing ${cover}/financialAccount -run FAccount/^$2$
    fi
    ;;
  "24")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/user -run User
    else
      go test -v ./testing ${cover}/user -run User/^$2$
    fi
    ;;
  "9")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/notification -run Notification
    else
      go test -v ./testing ${cover}/notification -run Notification/^$2$
    fi
    ;;
  "3")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/FATransaction -run FATransaction
    else
      go test -v ./testing ${cover}/FATransaction -run FATransaction/^$2$
    fi
    ;;
  "5")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/gift -run Gift
    else
      go test -v ./testing ${cover}/gift -run Gift/^$2$
    fi
    ;;
  "6")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/housekeeping -run Housekeeping
    else
      go test -v ./testing ${cover}/housekeeping -run Housekeeping/^$2$
    fi
    ;;
  "10")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/others -run Others
    else
      go test -v ./testing ${cover}/others -run Others/^$2$
    fi
    ;;
  "23")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/taskSchedule -run Schedule
    else
      go test -v ./testing ${cover}/taskSchedule -run Schedule/^$2$
    fi
    ;;
  "11")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/paymentCard -run PaymentCard
    else
      go test -v ./testing ${cover}/paymentCard -run PaymentCard/^$2$
    fi
    ;;
  "19")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/subscription -run Subscription
    else
      go test -v ./testing ${cover}/subscription -run Subscription/^$2$
    fi
    ;;
  "14")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/rating -run Rating
    else
      go test -v ./testing ${cover}/rating -run Rating/^$2$
    fi
    ;;
  "13")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/raixPushAppTokens -run RaixPushAppTokens
    else
      go test -v ./testing ${cover}/raixPushAppTokens -run RaixPushAppTokens/^$2$
    fi
    ;;
  "8")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/marketingCampaign/v3 -run MarketingCampaign
    else
      go test -v ./testing ${cover}/marketingCampaign/v3 -run MarketingCampaign/^$2$
    fi
    ;;
  "12")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/pointTransaction -run PointTransaction
    else
      go test -v ./testing ${cover}/pointTransaction -run PointTransaction/^$2$
    fi
    ;;
  "29")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/feedback -run Feedback
    else
      go test -v ./testing ${cover}/feedback -run Feedback/^$2$
    fi
    ;;
  "17")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/serviceChannel -run S_erviceChannel
    else
      go test -v ./testing ${cover}/serviceChannel -run S_erviceChannel/^$2$
    fi
    ;;
  "16")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/service -run Service
    else
      go test -v ./testing ${cover}/service -run Service/^$2$
    fi
    ;;
  "22")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/taskReportTasker -run TaskReportTasker
    else
      go test -v ./testing ${cover}/taskReportTasker -run TaskReportTasker/^$2$
    fi
    ;;
  "15")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/reward -run Reward
    else
      go test -v ./testing ${cover}/reward -run Reward/^$2$
    fi
    ;;
  "21")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/taskerTraining -run Training
    else
      go test -v ./testing ${cover}/taskerTraining -run Training/^$2$
    fi
    ;;
  "2")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/chatMessage -run ChatMessage
    else
      go test -v ./testing ${cover}/chatMessage -run ChatMessage/^$2$
    fi
    ;;
  "26")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/workingPlaces -run WorkingPlaces
    else
      go test -v ./testing ${cover}/workingPlaces -run WorkingPlaces/^$2$
    fi
    ;;
  "25")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/weeklyPayout -run WeeklyPayout
    else
      go test -v ./testing ${cover}/weeklyPayout -run WeeklyPayout/^$2$
    fi
    ;;
  "27")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/taskerTaskHistory -run T_History
    else
      go test -v ./testing ${cover}/taskerTaskHistory -run T_History/^$2$
    fi
    ;;
  "28")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/taskerSettings -run T_Setting
    else
      go test -v ./testing ${cover}/taskerSettings -run T_Setting/^$2$
    fi
    ;;
   "30")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/outstandingPayment -run OutstandingPayment
    else
      go test -v ./testing ${cover}/outstandingPayment -run OutstandingPayment/^$2$
    fi
    ;;
   "31")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/reportTransaction -run ReportTransaction
    else
      go test -v ./testing ${cover}/reportTransaction -run ReportTransaction/^$2$
    fi
    ;;
  "32")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/groceryAssistant -run GroceryAssistant
    else
      go test -v ./testing ${cover}/groceryAssistant -run GroceryAssistant/^$2$
    fi
    ;;
  "33")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/payment/v3 -run PaymentList
    else
      go test -v ./testing ${cover}/payment/v3 -run PaymentList/^$2$
    fi
    ;;
  "34")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/promotionPaymentMethod -run PromotionPaymentMethod
    else
      go test -v ./testing ${cover}/promotionPaymentMethod -run PromotionPaymentMethod/^$2$
    fi
    ;;
  "35")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/apiForBackEnd -run APIForBackend
    else
      go test -v ./testing ${cover}/apiForBackEnd -run APIForBackend/^$2$
    fi
    ;;
  "36")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/tasker -run _Tasker
    else
      go test -v ./testing ${cover}/tasker -run _Tasker/^$2$
    fi
    ;;
  "37")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/luckyDraw -run LuckyDraw
    else
      go test -v ./testing ${cover}/luckyDraw -run LuckyDraw/^$2$
    fi
    ;;
  "38")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/yearEndReport -run YearEndReport
    else
      go test -v ./testing ${cover}/yearEndReport -run YearEndReport/^$2$
    fi
    ;;
  "39")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/comboVoucher -run ComboVoucher
    else
      go test -v ./testing ${cover}/comboVoucher -run ComboVoucher/^$2$
    fi
    ;;
  "40")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/bReward/... -run bReward
    else
      go test -v ./testing ${cover} -run bReward/^$2$
    fi
    ;;
  "41")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/referral/... -run Referral
    else
      go test -v ./testing ${cover}/referral/... -run Referral/^$2$
    fi
    ;;
  "42")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/handler/gameCampaign/... -run GameCampaign
    else
      go test -v ./testing ${cover}/handler/gameCampaign/... -run GameCampaign/^$2$
    fi
    ;;
  "43")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/survey/... -run Survey
    else
      go test -v ./testing ${cover}/survey/... -run Survey/^$2$
    fi
    ;;
  "44")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/chatMessage/... -run ChatV2
    else
      go test -v ./testing ${cover}/chatMessage/... -run ChatV2/^$2$
    fi
    ;;
  "45")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/voteFavTasker/... -run VoteFavTasker
    else
      go test -v ./testing ${cover}/voteFavTasker/... -run VoteFavTasker/^$2$
    fi
    ;;
  "46")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/fairyTale/... -run FairyTale
    else
      go test -v ./testing ${cover}/fairyTale/... -run FairyTale/^$2$
    fi
    ;;
  "47")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/rollCall/dailyRollCall/... -run DailyRollCall
    else
      go test -v ./testing ${cover}/rollCall/dailyRollCall/... -run DailyRollCall/^$2$
    fi
    ;;
  "48")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/rollCall/getWeeklyRollCall/... -run GetWeeklyRollCall
    else
      go test -v ./testing ${cover}/rollCall/getWeeklyRollCall/... -run GetWeeklyRollCall/^$2$
    fi
    ;;
  "49")
    if [[ -z $2 ]]; then
      go test -v ./testing ${cover}/business/... -run Business
    else
      go test -v ./testing ${cover}/business/... -run Business/^$2$
    fi
  ;;
  "49")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/business/... -run Business2
    else
      go test -v ./testing ${cover}/business/... -run Business2/^$2$
    fi
    ;;
  "50")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/bReward/lixiTasker/... -run LixiTasker
    else
      go test -v ./testing ${cover}/bReward/lixiTasker/... -run LixiTasker/^$2$
    fi
    ;;
  "51")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/bundleVoucher/... -run GetListBundleVoucher
    else
      go test -v ./testing ${cover}/bundleVoucher/... -run GetListBundleVoucher/^$2$
    fi
    ;;
  "52")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/bundleVoucher/... -run RedeemBundleVoucher
    else
      go test -v ./testing ${cover}/bundleVoucher/... -run RedeemBundleVoucher/^$2$
    fi
    ;;
  *)
    go test -v ./testing ${cover}/... -timeout 1h
    ;;
esac
